#!/usr/bin/env node

/**
 * 线索详情页面Bug修复验证脚本
 * 
 * 用于测试线索详情页面的API调用是否正常工作
 */

const axios = require('axios');

// 配置
const BASE_URL = 'http://localhost:1025';
const TEST_LEAD_ID = 1; // 测试用的线索ID

// 测试用的登录信息
const LOGIN_DATA = {
  loginName: 'admin',
  password: '123456'
};

let authToken = '';

/**
 * 登录获取token
 */
async function login() {
  try {
    console.log('🔐 正在登录...');
    const response = await axios.post(`${BASE_URL}/api/login`, LOGIN_DATA);
    
    if (response.data.ok) {
      authToken = response.data.data.token;
      console.log('✅ 登录成功');
      return true;
    } else {
      console.error('❌ 登录失败:', response.data.msg);
      return false;
    }
  } catch (error) {
    console.error('❌ 登录异常:', error.message);
    return false;
  }
}

/**
 * 测试线索详情API
 */
async function testLeadDetailAPI() {
  try {
    console.log(`\n🔍 测试线索详情API (leadId: ${TEST_LEAD_ID})`);
    
    const response = await axios.get(`${BASE_URL}/api/lead/detail/${TEST_LEAD_ID}`, {
      headers: {
        'satoken': authToken
      }
    });
    
    if (response.data.ok) {
      console.log('✅ 线索详情API调用成功');
      console.log('📋 线索信息:', {
        leadId: response.data.data.leadId,
        customerName: response.data.data.customerName,
        leadStatus: response.data.data.leadStatus,
        leadStatusName: response.data.data.leadStatusName
      });
      return true;
    } else {
      console.error('❌ 线索详情API调用失败:', response.data.msg);
      return false;
    }
  } catch (error) {
    console.error('❌ 线索详情API调用异常:', error.message);
    if (error.response) {
      console.error('📄 响应状态:', error.response.status);
      console.error('📄 响应数据:', error.response.data);
    }
    return false;
  }
}

/**
 * 测试跟进记录API (旧路径)
 */
async function testFollowListAPILegacy() {
  try {
    console.log(`\n🔍 测试跟进记录API - 旧路径 (leadId: ${TEST_LEAD_ID})`);
    
    const response = await axios.get(`${BASE_URL}/api/lead-follow/queryByLeadId/${TEST_LEAD_ID}`, {
      headers: {
        'satoken': authToken
      }
    });
    
    if (response.data.ok) {
      console.log('✅ 跟进记录API (旧路径) 调用成功');
      console.log('📋 跟进记录数量:', response.data.data?.length || 0);
      return true;
    } else {
      console.error('❌ 跟进记录API (旧路径) 调用失败:', response.data.msg);
      return false;
    }
  } catch (error) {
    console.error('❌ 跟进记录API (旧路径) 调用异常:', error.message);
    if (error.response) {
      console.error('📄 响应状态:', error.response.status);
      console.error('📄 响应数据:', error.response.data);
    }
    return false;
  }
}

/**
 * 测试跟进记录API (新路径)
 */
async function testFollowListAPINew() {
  try {
    console.log(`\n🔍 测试跟进记录API - 新路径 (leadId: ${TEST_LEAD_ID})`);
    
    const response = await axios.get(`${BASE_URL}/api/lead-follow/query-by-lead/${TEST_LEAD_ID}`, {
      headers: {
        'satoken': authToken
      }
    });
    
    if (response.data.ok) {
      console.log('✅ 跟进记录API (新路径) 调用成功');
      console.log('📋 跟进记录数量:', response.data.data?.length || 0);
      return true;
    } else {
      console.error('❌ 跟进记录API (新路径) 调用失败:', response.data.msg);
      return false;
    }
  } catch (error) {
    console.error('❌ 跟进记录API (新路径) 调用异常:', error.message);
    if (error.response) {
      console.error('📄 响应状态:', error.response.status);
      console.error('📄 响应数据:', error.response.data);
    }
    return false;
  }
}

/**
 * 测试线索360度视图API
 */
async function testLead360API() {
  try {
    console.log(`\n🔍 测试线索360度视图API (leadId: ${TEST_LEAD_ID})`);
    
    const response = await axios.get(`${BASE_URL}/api/lead360/${TEST_LEAD_ID}`, {
      headers: {
        'satoken': authToken
      }
    });
    
    if (response.data.ok) {
      console.log('✅ 线索360度视图API调用成功');
      console.log('📋 360度数据:', {
        basicInfo: !!response.data.data?.basicInfo,
        chatRecords: response.data.data?.chatRecords?.length || 0,
        quickNote: !!response.data.data?.quickNote
      });
      return true;
    } else {
      console.error('❌ 线索360度视图API调用失败:', response.data.msg);
      return false;
    }
  } catch (error) {
    console.error('❌ 线索360度视图API调用异常:', error.message);
    if (error.response) {
      console.error('📄 响应状态:', error.response.status);
      console.error('📄 响应数据:', error.response.data);
    }
    return false;
  }
}

/**
 * 测试状态历史API
 */
async function testStatusHistoryAPI() {
  try {
    console.log(`\n🔍 测试状态历史API (leadId: ${TEST_LEAD_ID})`);
    
    const response = await axios.get(`${BASE_URL}/api/lead/status-flow/history/${TEST_LEAD_ID}`, {
      headers: {
        'satoken': authToken
      }
    });
    
    if (response.data.ok) {
      console.log('✅ 状态历史API调用成功');
      console.log('📋 状态历史记录数量:', response.data.data?.length || 0);
      return true;
    } else {
      console.error('❌ 状态历史API调用失败:', response.data.msg);
      return false;
    }
  } catch (error) {
    console.error('❌ 状态历史API调用异常:', error.message);
    if (error.response) {
      console.error('📄 响应状态:', error.response.status);
      console.error('📄 响应数据:', error.response.data);
    }
    return false;
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始线索详情页面Bug修复验证测试\n');
  
  // 登录
  const loginSuccess = await login();
  if (!loginSuccess) {
    console.log('\n❌ 测试终止：登录失败');
    return;
  }
  
  // 测试各个API
  const results = {
    leadDetail: await testLeadDetailAPI(),
    followListLegacy: await testFollowListAPILegacy(),
    followListNew: await testFollowListAPINew(),
    lead360: await testLead360API(),
    statusHistory: await testStatusHistoryAPI()
  };
  
  // 汇总结果
  console.log('\n📊 测试结果汇总:');
  console.log('='.repeat(50));
  Object.entries(results).forEach(([test, success]) => {
    console.log(`${success ? '✅' : '❌'} ${test}: ${success ? '通过' : '失败'}`);
  });
  
  const successCount = Object.values(results).filter(Boolean).length;
  const totalCount = Object.keys(results).length;
  
  console.log('='.repeat(50));
  console.log(`📈 总体结果: ${successCount}/${totalCount} 个API测试通过`);
  
  if (successCount === totalCount) {
    console.log('🎉 所有API测试通过！线索详情页面应该可以正常工作了。');
  } else {
    console.log('⚠️  部分API测试失败，请检查后端服务和权限配置。');
  }
}

// 运行测试
runTests().catch(error => {
  console.error('💥 测试执行异常:', error);
});

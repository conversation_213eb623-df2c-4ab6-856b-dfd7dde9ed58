# 医院营销与客户管理系统 - 部署文档

## 系统概述

医院营销与客户管理系统是基于SmartAdmin框架开发的企业级应用，采用前后端分离架构，支持线索管理、预约管理、客户管理等核心业务功能。

## 技术架构

### 后端技术栈
- **Java**: 17
- **框架**: SpringBoot 3.2.0
- **ORM**: MyBatis-Plus 3.5.4
- **数据库**: MySQL 8.0
- **缓存**: Redis 7.0
- **权限**: Sa-Token 1.37.0

### 前端技术栈
- **框架**: Vue 3.3.0
- **UI库**: Ant Design Vue 4.0
- **构建工具**: Vite 4.0
- **包管理**: npm/yarn

## 环境要求

### 服务器配置
- **CPU**: 4核心以上
- **内存**: 8GB以上
- **硬盘**: 100GB以上
- **操作系统**: Linux (推荐 CentOS 7+/Ubuntu 18+)

### 软件环境
- **JDK**: 17+
- **Node.js**: 16+
- **MySQL**: 8.0+
- **Redis**: 7.0+
- **Nginx**: 1.20+

## 部署步骤

### 1. 环境准备

#### 1.1 安装JDK 17
```bash
# 下载并安装JDK 17
wget https://download.oracle.com/java/17/latest/jdk-17_linux-x64_bin.tar.gz
tar -xzf jdk-17_linux-x64_bin.tar.gz
sudo mv jdk-17.0.x /opt/jdk-17

# 配置环境变量
echo 'export JAVA_HOME=/opt/jdk-17' >> ~/.bashrc
echo 'export PATH=$JAVA_HOME/bin:$PATH' >> ~/.bashrc
source ~/.bashrc

# 验证安装
java -version
```

#### 1.2 安装MySQL 8.0
```bash
# 安装MySQL
sudo apt update
sudo apt install mysql-server-8.0

# 启动MySQL服务
sudo systemctl start mysql
sudo systemctl enable mysql

# 安全配置
sudo mysql_secure_installation
```

#### 1.3 安装Redis
```bash
# 安装Redis
sudo apt install redis-server

# 启动Redis服务
sudo systemctl start redis
sudo systemctl enable redis

# 验证安装
redis-cli ping
```

#### 1.4 安装Node.js
```bash
# 安装Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node -v
npm -v
```

#### 1.5 安装Nginx
```bash
# 安装Nginx
sudo apt install nginx

# 启动Nginx服务
sudo systemctl start nginx
sudo systemctl enable nginx
```

### 2. 数据库初始化

#### 2.1 创建数据库
```sql
-- 连接MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE smart_admin_hospital DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'hospital_user'@'%' IDENTIFIED BY 'hospital_password_2024';
GRANT ALL PRIVILEGES ON smart_admin_hospital.* TO 'hospital_user'@'%';
FLUSH PRIVILEGES;
```

#### 2.2 导入数据表
```bash
# 导入基础表结构
mysql -u hospital_user -p smart_admin_hospital < sql/smart_admin_base.sql

# 导入医院业务表
mysql -u hospital_user -p smart_admin_hospital < sql/hospital_tables.sql

# 导入权限配置
mysql -u hospital_user -p smart_admin_hospital < sql/hospital_permissions.sql

# 导入初始数据
mysql -u hospital_user -p smart_admin_hospital < sql/hospital_init_data.sql
```

### 3. 后端部署

#### 3.1 配置文件修改
```yaml
# application-prod.yml
server:
  port: 8080

spring:
  datasource:
    url: ********************************************************************************************************************
    username: hospital_user
    password: hospital_password_2024
    driver-class-name: com.mysql.cj.jdbc.Driver

  redis:
    host: localhost
    port: 6379
    password: 
    database: 0

logging:
  level:
    net.lab1024.sa: INFO
  file:
    name: /var/log/hospital-system/application.log
```

#### 3.2 打包部署
```bash
# 进入后端项目目录
cd smart-admin-api-java17-springboot3

# 打包项目
./mvnw clean package -Pprod

# 创建部署目录
sudo mkdir -p /opt/hospital-system
sudo mkdir -p /var/log/hospital-system

# 复制jar包
sudo cp sa-admin/target/sa-admin-1.0.0.jar /opt/hospital-system/

# 创建启动脚本
sudo tee /opt/hospital-system/start.sh > /dev/null <<EOF
#!/bin/bash
cd /opt/hospital-system
nohup java -jar -Xms2g -Xmx4g -Dspring.profiles.active=prod sa-admin-1.0.0.jar > /var/log/hospital-system/startup.log 2>&1 &
echo \$! > /opt/hospital-system/app.pid
EOF

sudo chmod +x /opt/hospital-system/start.sh

# 创建停止脚本
sudo tee /opt/hospital-system/stop.sh > /dev/null <<EOF
#!/bin/bash
if [ -f /opt/hospital-system/app.pid ]; then
    kill \$(cat /opt/hospital-system/app.pid)
    rm -f /opt/hospital-system/app.pid
    echo "Application stopped"
else
    echo "Application is not running"
fi
EOF

sudo chmod +x /opt/hospital-system/stop.sh
```

#### 3.3 创建系统服务
```bash
# 创建systemd服务文件
sudo tee /etc/systemd/system/hospital-system.service > /dev/null <<EOF
[Unit]
Description=Hospital Management System
After=network.target

[Service]
Type=forking
User=root
ExecStart=/opt/hospital-system/start.sh
ExecStop=/opt/hospital-system/stop.sh
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 重载systemd配置
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start hospital-system
sudo systemctl enable hospital-system

# 检查服务状态
sudo systemctl status hospital-system
```

### 4. 前端部署

#### 4.1 构建前端项目
```bash
# 进入前端项目目录
cd smart-admin-web-javascript

# 安装依赖
npm install

# 修改生产环境配置
# .env.production
VITE_APP_API_BASE_URL=http://your-domain.com/api
VITE_APP_TITLE=医院营销与客户管理系统

# 构建生产版本
npm run build
```

#### 4.2 配置Nginx
```bash
# 创建网站目录
sudo mkdir -p /var/www/hospital-system

# 复制构建文件
sudo cp -r dist/* /var/www/hospital-system/

# 配置Nginx
sudo tee /etc/nginx/sites-available/hospital-system > /dev/null <<EOF
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/hospital-system;
    index index.html;

    # 前端路由支持
    location / {
        try_files \$uri \$uri/ /index.html;
    }

    # API代理
    location /api/ {
        proxy_pass http://localhost:8080/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Gzip压缩
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
}
EOF

# 启用站点
sudo ln -s /etc/nginx/sites-available/hospital-system /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx
```

### 5. SSL证书配置（可选）

```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

### 6. 监控和日志

#### 6.1 日志配置
```bash
# 创建日志轮转配置
sudo tee /etc/logrotate.d/hospital-system > /dev/null <<EOF
/var/log/hospital-system/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
}
EOF
```

#### 6.2 监控脚本
```bash
# 创建健康检查脚本
sudo tee /opt/hospital-system/health-check.sh > /dev/null <<EOF
#!/bin/bash
response=\$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/api/health)
if [ \$response -eq 200 ]; then
    echo "Application is healthy"
else
    echo "Application is unhealthy, restarting..."
    systemctl restart hospital-system
fi
EOF

sudo chmod +x /opt/hospital-system/health-check.sh

# 添加到crontab
sudo crontab -e
# 添加以下行（每5分钟检查一次）
*/5 * * * * /opt/hospital-system/health-check.sh
```

## 验证部署

### 1. 后端验证
```bash
# 检查服务状态
sudo systemctl status hospital-system

# 检查端口监听
sudo netstat -tlnp | grep 8080

# 检查日志
tail -f /var/log/hospital-system/application.log

# API测试
curl http://localhost:8080/api/health
```

### 2. 前端验证
```bash
# 检查Nginx状态
sudo systemctl status nginx

# 访问网站
curl http://your-domain.com

# 检查API代理
curl http://your-domain.com/api/health
```

### 3. 数据库验证
```bash
# 连接数据库
mysql -u hospital_user -p smart_admin_hospital

# 检查表结构
SHOW TABLES;

# 检查数据
SELECT COUNT(*) FROM t_menu WHERE menu_id BETWEEN 5001 AND 5200;
```

## 常见问题

### 1. 端口冲突
```bash
# 查看端口占用
sudo netstat -tlnp | grep :8080

# 修改应用端口
vim /opt/hospital-system/application-prod.yml
```

### 2. 内存不足
```bash
# 调整JVM参数
vim /opt/hospital-system/start.sh
# 修改 -Xms2g -Xmx4g 为合适的值
```

### 3. 数据库连接失败
```bash
# 检查数据库服务
sudo systemctl status mysql

# 检查用户权限
mysql -u root -p
SHOW GRANTS FOR 'hospital_user'@'%';
```

## 备份策略

### 1. 数据库备份
```bash
# 创建备份脚本
sudo tee /opt/hospital-system/backup.sh > /dev/null <<EOF
#!/bin/bash
DATE=\$(date +%Y%m%d_%H%M%S)
mysqldump -u hospital_user -p'hospital_password_2024' smart_admin_hospital > /opt/hospital-system/backup/db_backup_\$DATE.sql
find /opt/hospital-system/backup -name "db_backup_*.sql" -mtime +7 -delete
EOF

sudo chmod +x /opt/hospital-system/backup.sh

# 添加到crontab（每天凌晨2点备份）
sudo crontab -e
0 2 * * * /opt/hospital-system/backup.sh
```

### 2. 应用备份
```bash
# 备份应用文件
sudo mkdir -p /opt/hospital-system/backup
sudo cp sa-admin-1.0.0.jar /opt/hospital-system/backup/sa-admin-1.0.0.jar.bak
```

---

**部署完成后，系统将在 http://your-domain.com 上运行**

**默认管理员账号**: admin / 123456

**技术支持**: <EMAIL>

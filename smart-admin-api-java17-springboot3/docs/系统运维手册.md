# 医院营销与客户管理系统 - 运维手册

## 运维概述

本手册提供医院营销与客户管理系统的日常运维指导，包括系统监控、故障处理、性能优化、安全维护等方面的操作指南。

## 1. 系统架构概览

### 1.1 技术架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (Vue3)   │    │  后端 (Spring)  │    │ 数据库 (MySQL)  │
│                 │    │                 │    │                 │
│ - Nginx         │    │ - Java17        │    │ - MySQL 8.0     │
│ - Vue3          │◄──►│ - SpringBoot3   │◄──►│ - Redis 7.0     │
│ - Ant Design    │    │ - MyBatis-Plus  │    │ - 主从复制      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 1.2 部署架构
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  负载均衡   │    │  应用服务器 │    │  数据库服务 │
│             │    │             │    │             │
│ - Nginx     │    │ - App1      │    │ - MySQL主   │
│ - SSL证书   │◄──►│ - App2      │◄──►│ - MySQL从   │
│ - 反向代理  │    │ - Redis     │    │ - 备份服务  │
└─────────────┘    └─────────────┘    └─────────────┘
```

## 2. 系统监控

### 2.1 关键指标监控

#### 应用层监控
```bash
# 检查应用状态
curl http://localhost:8080/api/health

# 检查JVM内存使用
jstat -gc [PID]

# 检查线程状态
jstack [PID]

# 检查CPU使用率
top -p [PID]
```

#### 数据库监控
```sql
-- 检查数据库连接数
SHOW STATUS LIKE 'Threads_connected';

-- 检查慢查询
SHOW STATUS LIKE 'Slow_queries';

-- 检查表锁状态
SHOW STATUS LIKE 'Table_locks_waited';

-- 检查缓存命中率
SHOW STATUS LIKE 'Qcache_hits';
SHOW STATUS LIKE 'Qcache_inserts';
```

#### 系统资源监控
```bash
# CPU使用率
top
htop

# 内存使用情况
free -h
cat /proc/meminfo

# 磁盘使用情况
df -h
du -sh /var/log/hospital-system/

# 网络连接状态
netstat -an | grep :8080
ss -tuln
```

### 2.2 日志监控

#### 应用日志
```bash
# 实时查看应用日志
tail -f /var/log/hospital-system/application.log

# 查看错误日志
grep "ERROR" /var/log/hospital-system/application.log

# 查看最近的异常
grep -A 10 "Exception" /var/log/hospital-system/application.log

# 统计接口调用次数
grep "POST\|GET\|PUT\|DELETE" /var/log/hospital-system/application.log | wc -l
```

#### 系统日志
```bash
# 查看系统日志
journalctl -u hospital-system.service

# 查看最近的系统错误
journalctl -p err -since "1 hour ago"

# 查看Nginx访问日志
tail -f /var/log/nginx/access.log

# 查看Nginx错误日志
tail -f /var/log/nginx/error.log
```

### 2.3 性能监控

#### 响应时间监控
```bash
# 接口响应时间测试
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:8080/api/health

# curl-format.txt内容:
#     time_namelookup:  %{time_namelookup}\n
#        time_connect:  %{time_connect}\n
#     time_appconnect:  %{time_appconnect}\n
#    time_pretransfer:  %{time_pretransfer}\n
#       time_redirect:  %{time_redirect}\n
#  time_starttransfer:  %{time_starttransfer}\n
#                     ----------\n
#          time_total:  %{time_total}\n
```

#### 数据库性能监控
```sql
-- 查看正在执行的查询
SHOW PROCESSLIST;

-- 查看慢查询日志
SELECT * FROM mysql.slow_log ORDER BY start_time DESC LIMIT 10;

-- 查看表状态
SHOW TABLE STATUS LIKE 't_customer';

-- 查看索引使用情况
SHOW INDEX FROM t_customer;
```

## 3. 故障处理

### 3.1 常见故障及解决方案

#### 应用无法启动
```bash
# 检查端口占用
netstat -tlnp | grep :8080

# 检查Java进程
ps aux | grep java

# 检查配置文件
cat /opt/hospital-system/application-prod.yml

# 重启应用
systemctl restart hospital-system
```

#### 数据库连接失败
```bash
# 检查MySQL服务状态
systemctl status mysql

# 检查数据库连接
mysql -u hospital_user -p -h localhost

# 检查连接池配置
grep -A 5 "datasource" /opt/hospital-system/application-prod.yml

# 重启MySQL服务
systemctl restart mysql
```

#### 内存溢出
```bash
# 查看内存使用情况
free -h

# 查看Java堆内存
jmap -heap [PID]

# 生成堆转储文件
jmap -dump:format=b,file=heapdump.hprof [PID]

# 调整JVM参数
vim /opt/hospital-system/start.sh
# 修改 -Xms2g -Xmx4g 为合适的值
```

#### 磁盘空间不足
```bash
# 查看磁盘使用情况
df -h

# 查找大文件
find /var/log -type f -size +100M

# 清理日志文件
find /var/log/hospital-system -name "*.log" -mtime +7 -delete

# 压缩旧日志
gzip /var/log/hospital-system/*.log.1
```

### 3.2 紧急故障处理流程

#### 1. 故障发现
- 监控告警
- 用户反馈
- 主动巡检

#### 2. 故障评估
- 影响范围
- 严重程度
- 预计恢复时间

#### 3. 故障处理
- 立即响应
- 问题定位
- 解决方案实施

#### 4. 故障恢复
- 服务恢复验证
- 数据一致性检查
- 用户通知

#### 5. 故障总结
- 根因分析
- 改进措施
- 文档更新

## 4. 备份与恢复

### 4.1 数据备份

#### 数据库备份
```bash
# 全量备份
mysqldump -u root -p --single-transaction --routines --triggers smart_admin_hospital > backup_$(date +%Y%m%d_%H%M%S).sql

# 增量备份（基于binlog）
mysqlbinlog --start-datetime="2024-01-01 00:00:00" --stop-datetime="2024-01-01 23:59:59" mysql-bin.000001 > incremental_backup.sql

# 自动备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/mysql"
mysqldump -u backup_user -p'backup_password' --single-transaction smart_admin_hospital > $BACKUP_DIR/hospital_backup_$DATE.sql
find $BACKUP_DIR -name "hospital_backup_*.sql" -mtime +7 -delete
```

#### 应用备份
```bash
# 备份应用文件
tar -czf hospital-system-backup-$(date +%Y%m%d).tar.gz /opt/hospital-system/

# 备份配置文件
cp /opt/hospital-system/application-prod.yml /backup/config/application-prod.yml.$(date +%Y%m%d)

# 备份日志文件
tar -czf logs-backup-$(date +%Y%m%d).tar.gz /var/log/hospital-system/
```

### 4.2 数据恢复

#### 数据库恢复
```bash
# 全量恢复
mysql -u root -p smart_admin_hospital < backup_20240115_120000.sql

# 增量恢复
mysql -u root -p smart_admin_hospital < incremental_backup.sql

# 恢复到指定时间点
mysqlbinlog --stop-datetime="2024-01-15 12:00:00" mysql-bin.000001 | mysql -u root -p smart_admin_hospital
```

#### 应用恢复
```bash
# 停止应用
systemctl stop hospital-system

# 恢复应用文件
tar -xzf hospital-system-backup-20240115.tar.gz -C /

# 恢复配置文件
cp /backup/config/application-prod.yml.20240115 /opt/hospital-system/application-prod.yml

# 启动应用
systemctl start hospital-system
```

## 5. 性能优化

### 5.1 数据库优化

#### 索引优化
```sql
-- 分析慢查询
EXPLAIN SELECT * FROM t_customer WHERE customer_phone = '13800138000';

-- 添加索引
CREATE INDEX idx_customer_phone ON t_customer(customer_phone);
CREATE INDEX idx_appointment_date ON t_appointment(appointment_date);
CREATE INDEX idx_lead_status ON t_lead(lead_status);

-- 查看索引使用情况
SHOW INDEX FROM t_customer;
```

#### 查询优化
```sql
-- 优化分页查询
SELECT * FROM t_customer 
WHERE customer_id > 1000 
ORDER BY customer_id 
LIMIT 20;

-- 使用覆盖索引
SELECT customer_id, customer_name, customer_phone 
FROM t_customer 
WHERE customer_status = 1;

-- 避免全表扫描
SELECT * FROM t_appointment 
WHERE appointment_date BETWEEN '2024-01-01' AND '2024-01-31'
AND appointment_status = 2;
```

#### 配置优化
```ini
# my.cnf优化配置
[mysqld]
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
query_cache_size = 128M
max_connections = 500
thread_cache_size = 50
```

### 5.2 应用优化

#### JVM参数优化
```bash
# 启动参数优化
java -jar \
  -Xms4g -Xmx4g \
  -XX:+UseG1GC \
  -XX:MaxGCPauseMillis=200 \
  -XX:+HeapDumpOnOutOfMemoryError \
  -XX:HeapDumpPath=/var/log/hospital-system/ \
  sa-admin-1.0.0.jar
```

#### 缓存优化
```yaml
# Redis配置优化
spring:
  redis:
    host: localhost
    port: 6379
    timeout: 3000
    jedis:
      pool:
        max-active: 20
        max-wait: -1
        max-idle: 10
        min-idle: 0
```

### 5.3 网络优化

#### Nginx配置优化
```nginx
# nginx.conf优化
worker_processes auto;
worker_connections 1024;

gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css application/json application/javascript;

client_max_body_size 10M;
client_body_timeout 60;
client_header_timeout 60;

keepalive_timeout 65;
keepalive_requests 100;
```

## 6. 安全维护

### 6.1 系统安全

#### 防火墙配置
```bash
# 开放必要端口
ufw allow 22/tcp
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 8080/tcp

# 限制数据库访问
ufw allow from ***********/24 to any port 3306

# 启用防火墙
ufw enable
```

#### SSL证书管理
```bash
# 检查证书有效期
openssl x509 -in /etc/ssl/certs/hospital.crt -noout -dates

# 自动续期Let's Encrypt证书
certbot renew --dry-run

# 定时任务自动续期
0 12 * * * /usr/bin/certbot renew --quiet
```

### 6.2 数据安全

#### 数据库安全
```sql
-- 创建只读用户
CREATE USER 'readonly'@'%' IDENTIFIED BY 'readonly_password';
GRANT SELECT ON smart_admin_hospital.* TO 'readonly'@'%';

-- 定期更改密码
ALTER USER 'hospital_user'@'%' IDENTIFIED BY 'new_password';

-- 审计日志
SET GLOBAL general_log = 'ON';
SET GLOBAL general_log_file = '/var/log/mysql/general.log';
```

#### 应用安全
```bash
# 定期更新系统
apt update && apt upgrade

# 检查安全漏洞
nmap -sV localhost

# 监控登录日志
tail -f /var/log/auth.log
```

## 7. 运维自动化

### 7.1 监控脚本

#### 健康检查脚本
```bash
#!/bin/bash
# health_check.sh

APP_URL="http://localhost:8080/api/health"
LOG_FILE="/var/log/hospital-system/health_check.log"

response=$(curl -s -o /dev/null -w "%{http_code}" $APP_URL)

if [ $response -eq 200 ]; then
    echo "$(date): Application is healthy" >> $LOG_FILE
else
    echo "$(date): Application is unhealthy, response code: $response" >> $LOG_FILE
    systemctl restart hospital-system
fi
```

#### 资源监控脚本
```bash
#!/bin/bash
# resource_monitor.sh

CPU_THRESHOLD=80
MEMORY_THRESHOLD=80
DISK_THRESHOLD=90

# CPU检查
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')
if (( $(echo "$CPU_USAGE > $CPU_THRESHOLD" | bc -l) )); then
    echo "High CPU usage: $CPU_USAGE%" | mail -s "CPU Alert" <EMAIL>
fi

# 内存检查
MEMORY_USAGE=$(free | grep Mem | awk '{printf("%.1f", $3/$2 * 100.0)}')
if (( $(echo "$MEMORY_USAGE > $MEMORY_THRESHOLD" | bc -l) )); then
    echo "High memory usage: $MEMORY_USAGE%" | mail -s "Memory Alert" <EMAIL>
fi

# 磁盘检查
DISK_USAGE=$(df -h | grep '/dev/sda1' | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt $DISK_THRESHOLD ]; then
    echo "High disk usage: $DISK_USAGE%" | mail -s "Disk Alert" <EMAIL>
fi
```

### 7.2 定时任务

#### Crontab配置
```bash
# 编辑定时任务
crontab -e

# 添加以下任务
# 每5分钟检查应用健康状态
*/5 * * * * /opt/hospital-system/health_check.sh

# 每小时检查系统资源
0 * * * * /opt/hospital-system/resource_monitor.sh

# 每天凌晨2点备份数据库
0 2 * * * /opt/hospital-system/backup.sh

# 每周日凌晨3点清理日志
0 3 * * 0 find /var/log/hospital-system -name "*.log" -mtime +7 -delete

# 每月1号检查SSL证书
0 0 1 * * /usr/bin/certbot renew --quiet
```

## 8. 故障预防

### 8.1 预防措施

1. **定期巡检**
   - 每日系统状态检查
   - 每周性能分析
   - 每月安全审计

2. **容量规划**
   - 监控资源使用趋势
   - 提前扩容准备
   - 性能基线建立

3. **版本管理**
   - 定期更新补丁
   - 测试环境验证
   - 灰度发布策略

### 8.2 应急预案

1. **服务中断**
   - 快速切换备用服务
   - 数据恢复流程
   - 用户通知机制

2. **数据丢失**
   - 备份数据恢复
   - 数据一致性检查
   - 业务影响评估

3. **安全事件**
   - 立即隔离系统
   - 安全漏洞修复
   - 事件调查分析

---

**编写时间**: 2024年1月15日  
**编写团队**: 运维团队  
**版本**: v1.0  
**状态**: 已发布

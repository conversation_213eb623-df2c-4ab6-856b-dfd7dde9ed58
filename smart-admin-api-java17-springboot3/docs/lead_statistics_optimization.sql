-- 线索统计优化方案
-- 作者：AI Assistant
-- 日期：2025-01-29
-- 说明：为线索表添加统计友好的索引和优化方案

-- ========================================
-- 1. 统计索引优化
-- ========================================

-- 线索来源统计索引
CREATE INDEX IF NOT EXISTS idx_lead_source ON t_lead(lead_source);

-- 地区统计索引
CREATE INDEX IF NOT EXISTS idx_region ON t_lead(region);

-- 线索质量统计索引
CREATE INDEX IF NOT EXISTS idx_lead_quality ON t_lead(lead_quality);

-- 性别统计索引
CREATE INDEX IF NOT EXISTS idx_gender ON t_lead(gender);

-- 组合索引：状态+创建时间（用于时间段统计）
CREATE INDEX IF NOT EXISTS idx_status_create_time ON t_lead(lead_status, create_time);

-- 组合索引：删除标志+创建时间（用于有效数据统计）
CREATE INDEX IF NOT EXISTS idx_deleted_create_time ON t_lead(deleted_flag, create_time);

-- ========================================
-- 2. 常用统计查询示例
-- ========================================

-- 2.1 线索来源分布统计
SELECT 
    lead_source as '线索来源',
    COUNT(*) as '数量',
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM t_lead WHERE deleted_flag = 0), 2) as '占比(%)'
FROM t_lead 
WHERE deleted_flag = 0
GROUP BY lead_source
ORDER BY COUNT(*) DESC;

-- 2.2 地区分布统计
SELECT 
    region as '地区',
    COUNT(*) as '数量',
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM t_lead WHERE deleted_flag = 0), 2) as '占比(%)'
FROM t_lead 
WHERE deleted_flag = 0
GROUP BY region
ORDER BY COUNT(*) DESC;

-- 2.3 线索状态分布统计
SELECT 
    CASE lead_status
        WHEN 1 THEN '新线索'
        WHEN 2 THEN '跟进中'
        WHEN 3 THEN '已转化'
        WHEN 4 THEN '已失效'
        ELSE '未知状态'
    END as '线索状态',
    COUNT(*) as '数量',
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM t_lead WHERE deleted_flag = 0), 2) as '占比(%)'
FROM t_lead 
WHERE deleted_flag = 0
GROUP BY lead_status
ORDER BY COUNT(*) DESC;

-- 2.4 性别分布统计
SELECT 
    CASE gender
        WHEN 1 THEN '男'
        WHEN 2 THEN '女'
        ELSE '未知'
    END as '性别',
    COUNT(*) as '数量',
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM t_lead WHERE deleted_flag = 0), 2) as '占比(%)'
FROM t_lead 
WHERE deleted_flag = 0
GROUP BY gender
ORDER BY COUNT(*) DESC;

-- 2.5 按月统计线索数量趋势
SELECT 
    DATE_FORMAT(create_time, '%Y-%m') as '月份',
    COUNT(*) as '新增线索数',
    SUM(CASE WHEN lead_status = 3 THEN 1 ELSE 0 END) as '转化数',
    ROUND(SUM(CASE WHEN lead_status = 3 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as '转化率(%)'
FROM t_lead 
WHERE deleted_flag = 0
GROUP BY DATE_FORMAT(create_time, '%Y-%m')
ORDER BY DATE_FORMAT(create_time, '%Y-%m') DESC;

-- 2.6 员工线索分配统计
SELECT 
    e.actual_name as '员工姓名',
    COUNT(l.lead_id) as '分配线索数',
    SUM(CASE WHEN l.lead_status = 3 THEN 1 ELSE 0 END) as '转化数',
    ROUND(SUM(CASE WHEN l.lead_status = 3 THEN 1 ELSE 0 END) * 100.0 / COUNT(l.lead_id), 2) as '转化率(%)'
FROM t_lead l
LEFT JOIN t_employee e ON l.assigned_employee_id = e.employee_id
WHERE l.deleted_flag = 0
GROUP BY l.assigned_employee_id, e.actual_name
ORDER BY COUNT(l.lead_id) DESC;

-- ========================================
-- 3. 性能优化建议
-- ========================================

/*
3.1 索引使用建议：
- 统计查询时优先使用已创建的索引字段进行过滤
- 避免在TEXT字段（如remark、chat_record）上进行统计查询
- 时间范围查询时使用idx_deleted_create_time或idx_status_create_time

3.2 查询优化建议：
- 统计查询时始终添加deleted_flag = 0条件
- 大数据量统计时考虑分页或限制时间范围
- 复杂统计可考虑使用物化视图或定时任务预计算

3.3 数据规范化建议：
- 地区字段建议使用字典值而非自由文本
- 线索来源字段建议标准化，避免NULL值
- 症状字段考虑拆分为独立的关联表以支持更精确的统计

3.4 监控建议：
- 定期监控统计查询的执行计划
- 关注索引的使用率和效果
- 根据业务需求调整索引策略
*/

-- 测试数据同步功能
-- 首先查看预约记录
SELECT appointment_id, customer_name, customer_phone, appointment_status FROM t_appointment WHERE appointment_id = 9;

-- 查看是否已有对应的到诊患者记录
SELECT patient_id, patient_name, appointment_id FROM t_visit_patient WHERE appointment_id = 9;

-- 手动创建到诊患者记录来模拟数据同步
INSERT INTO t_visit_patient (
    patient_no,
    patient_name,
    patient_phone,
    gender,
    age,
    patient_wechat,
    visit_status,
    visit_date,
    registration_time,
    assigned_doctor_id,
    assigned_doctor_name,
    appointment_id,
    patient_level,
    patient_source,
    create_user_id,
    create_time,
    deleted_flag
) 
SELECT 
    CONCAT('P', DATE_FORMAT(NOW(), '%Y%m%d'), LPAD(appointment_id, 4, '0')) as patient_no,
    customer_name as patient_name,
    customer_phone as patient_phone,
    gender,
    age,
    wechat as patient_wechat,
    2 as visit_status, -- 已到诊
    appointment_date as visit_date,
    NOW() as registration_time,
    doctor_id as assigned_doctor_id,
    doctor_name as assigned_doctor_name,
    appointment_id,
    1 as patient_level, -- 普通患者
    '预约转入' as patient_source,
    1 as create_user_id,
    NOW() as create_time,
    0 as deleted_flag
FROM t_appointment 
WHERE appointment_id = 9 
AND NOT EXISTS (
    SELECT 1 FROM t_visit_patient WHERE appointment_id = 9
);

-- 验证创建结果
SELECT patient_id, patient_no, patient_name, appointment_id, visit_status FROM t_visit_patient WHERE appointment_id = 9;

[2025-07-29 17:10:09,647][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-29 17:10:09,726][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7063ed6c] was not registered for synchronization because synchronization is not active 
[2025-07-29 17:10:09,729][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-29 17:10:09,730][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7063ed6c] 
[2025-07-29 17:11:09,745][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-29 17:11:09,748][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@39df8e4e] was not registered for synchronization because synchronization is not active 
[2025-07-29 17:11:09,750][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-29 17:11:09,751][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@39df8e4e] 

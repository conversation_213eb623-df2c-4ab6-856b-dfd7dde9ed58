[2025-07-30 00:21:40,749][DEBUG][redisson-netty-2-24][i.n.b.PoolThreadCache:214] Freed 20 thread-local buffer(s) from thread: redisson-netty-2-24 
[2025-07-30 00:21:40,750][DEBUG][redisson-netty-2-4][i.n.b.PoolThreadCache:214] Freed 19 thread-local buffer(s) from thread: redisson-netty-2-4 
[2025-07-30 00:21:40,750][DEBUG][redisson-netty-2-6][i.n.b.PoolThreadCache:214] Freed 19 thread-local buffer(s) from thread: redisson-netty-2-6 
[2025-07-30 00:21:40,750][DEBUG][redisson-netty-2-20][i.n.b.PoolThreadCache:214] Freed 18 thread-local buffer(s) from thread: redisson-netty-2-20 
[2025-07-30 00:21:40,750][DEBUG][redisson-netty-2-30][i.n.b.PoolThreadCache:214] Freed 17 thread-local buffer(s) from thread: redisson-netty-2-30 
[2025-07-30 00:21:40,750][DEBUG][redisson-netty-2-22][i.n.b.PoolThreadCache:214] Freed 18 thread-local buffer(s) from thread: redisson-netty-2-22 
[2025-07-30 00:21:40,750][DEBUG][redisson-netty-2-2][i.n.b.PoolThreadCache:214] Freed 20 thread-local buffer(s) from thread: redisson-netty-2-2 
[2025-07-30 00:21:40,750][DEBUG][redisson-netty-2-26][i.n.b.PoolThreadCache:214] Freed 19 thread-local buffer(s) from thread: redisson-netty-2-26 
[2025-07-30 00:21:40,750][DEBUG][redisson-netty-2-16][i.n.b.PoolThreadCache:214] Freed 19 thread-local buffer(s) from thread: redisson-netty-2-16 
[2025-07-30 00:21:40,750][DEBUG][redisson-netty-2-8][i.n.b.PoolThreadCache:214] Freed 20 thread-local buffer(s) from thread: redisson-netty-2-8 
[2025-07-30 00:21:40,750][DEBUG][redisson-netty-2-12][i.n.b.PoolThreadCache:214] Freed 19 thread-local buffer(s) from thread: redisson-netty-2-12 
[2025-07-30 00:21:40,750][DEBUG][redisson-netty-2-18][i.n.b.PoolThreadCache:214] Freed 20 thread-local buffer(s) from thread: redisson-netty-2-18 
[2025-07-30 00:21:40,750][DEBUG][redisson-netty-2-10][i.n.b.PoolThreadCache:214] Freed 21 thread-local buffer(s) from thread: redisson-netty-2-10 
[2025-07-30 00:21:40,750][DEBUG][redisson-netty-2-14][i.n.b.PoolThreadCache:214] Freed 19 thread-local buffer(s) from thread: redisson-netty-2-14 
[2025-07-30 00:21:40,750][DEBUG][redisson-netty-2-28][i.n.b.PoolThreadCache:214] Freed 19 thread-local buffer(s) from thread: redisson-netty-2-28 
[2025-07-30 00:21:40,750][DEBUG][redisson-netty-2-3][i.n.b.PoolThreadCache:214] Freed 16 thread-local buffer(s) from thread: redisson-netty-2-3 
[2025-07-30 00:21:40,750][DEBUG][redisson-netty-2-1][i.n.b.PoolThreadCache:214] Freed 19 thread-local buffer(s) from thread: redisson-netty-2-1 
[2025-07-30 00:21:40,753][DEBUG][SpringApplicationShutdownHook][o.s.b.f.s.DisposableBeanAdapter:361] Custom destroy method 'shutdown' on bean with name 'redisson' completed 
[2025-07-30 00:21:40,754][DEBUG][SpringApplicationShutdownHook][o.s.b.f.s.DisposableBeanAdapter:361] Custom destroy method 'close' on bean with name 'simpleMeterRegistry' completed 
[2025-07-30 08:20:46,692][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:20:46,695][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3f99f8c9] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:20:46,698][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:20:46,699][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3f99f8c9] 
[2025-07-30 08:21:46,755][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:21:46,757][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f9c4b7] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:21:46,758][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:21:46,760][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@f9c4b7] 
[2025-07-30 08:22:46,793][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:22:46,797][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@32041ea1] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:22:46,799][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:22:46,799][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@32041ea1] 
[2025-07-30 08:23:46,808][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:23:46,813][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@35057b2f] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:23:46,815][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:23:46,815][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@35057b2f] 
[2025-07-30 08:24:46,825][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:24:46,830][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3c476441] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:24:46,832][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:24:46,832][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3c476441] 
[2025-07-30 08:25:46,836][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:25:46,838][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1efe22e4] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:25:46,839][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:25:46,839][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1efe22e4] 
[2025-07-30 08:26:46,848][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:26:46,852][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2d45ada0] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:26:46,853][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:26:46,854][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2d45ada0] 
[2025-07-30 08:27:46,872][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:27:46,878][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@22e46e4d] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:27:46,880][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:27:46,883][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@22e46e4d] 
[2025-07-30 08:28:46,902][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:28:46,906][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7c50ca26] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:28:46,908][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:28:46,909][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7c50ca26] 
[2025-07-30 08:29:46,926][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:29:46,931][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e45e86] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:29:46,933][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:29:46,933][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e45e86] 
[2025-07-30 08:30:46,948][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:30:46,953][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@300715b4] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:30:46,955][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:30:46,956][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@300715b4] 
[2025-07-30 08:31:46,970][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:31:46,978][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2bf34031] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:31:46,981][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:31:46,982][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2bf34031] 
[2025-07-30 08:32:46,997][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:32:47,001][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37d3c13d] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:32:47,003][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:32:47,003][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@37d3c13d] 
[2025-07-30 08:33:47,027][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:33:47,033][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3ffaa3a8] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:33:47,036][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:33:47,036][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3ffaa3a8] 
[2025-07-30 08:34:47,051][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:34:47,054][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e162953] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:34:47,055][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:34:47,056][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e162953] 
[2025-07-30 08:35:47,068][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:35:47,072][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@469682c3] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:35:47,075][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:35:47,076][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@469682c3] 
[2025-07-30 08:36:47,093][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:36:47,097][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3abd7340] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:36:47,107][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:36:47,115][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3abd7340] 
[2025-07-30 08:37:47,142][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:37:47,146][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@16a1662b] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:37:47,148][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:37:47,150][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@16a1662b] 
[2025-07-30 08:38:47,209][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:38:47,213][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@375fba5c] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:38:47,215][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:38:47,217][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@375fba5c] 
[2025-07-30 08:39:47,262][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:39:47,274][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6843a536] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:39:47,281][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:39:47,282][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6843a536] 
[2025-07-30 08:40:47,317][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:40:47,321][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@240bd595] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:40:47,322][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:40:47,323][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@240bd595] 
[2025-07-30 08:41:47,341][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:41:47,345][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5b28d39] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:41:47,346][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:41:47,347][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@5b28d39] 
[2025-07-30 08:42:47,361][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:42:47,365][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@18b110a0] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:42:47,367][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:42:47,367][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@18b110a0] 
[2025-07-30 08:43:47,373][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:43:47,378][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@76f366b0] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:43:47,379][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:43:47,379][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@76f366b0] 
[2025-07-30 08:44:47,392][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:44:47,397][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e5f5453] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:44:47,399][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:44:47,400][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e5f5453] 
[2025-07-30 08:45:47,411][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:45:47,413][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54453832] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:45:47,414][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:45:47,414][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@54453832] 
[2025-07-30 08:46:47,423][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:46:47,439][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@70996fc7] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:46:47,455][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:46:47,456][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@70996fc7] 
[2025-07-30 08:47:47,486][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:47:47,489][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7cd5b6a5] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:47:47,491][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:47:47,491][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@7cd5b6a5] 
[2025-07-30 08:48:47,515][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:48:47,518][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4fe9fff2] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:48:47,520][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:48:47,521][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4fe9fff2] 
[2025-07-30 08:49:47,526][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:49:47,531][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3c835ff0] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:49:47,532][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:49:47,532][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@3c835ff0] 
[2025-07-30 08:50:47,539][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:50:47,541][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e3ccf3e] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:50:47,542][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:50:47,543][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@6e3ccf3e] 
[2025-07-30 08:51:47,555][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:51:47,560][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2bf252bf] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:51:47,564][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:51:47,564][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2bf252bf] 
[2025-07-30 08:52:47,580][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:52:47,583][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@488ae680] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:52:47,584][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:52:47,585][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@488ae680] 
[2025-07-30 08:53:47,598][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:53:47,601][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@17caec08] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:53:47,603][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:53:47,603][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@17caec08] 
[2025-07-30 08:54:47,619][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:54:47,622][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@9776b18] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:54:47,624][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:54:47,625][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@9776b18] 
[2025-07-30 08:55:47,642][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:55:47,646][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1fc76123] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:55:47,648][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:55:47,649][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1fc76123] 
[2025-07-30 08:56:47,664][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:56:47,673][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2e163501] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:56:47,674][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:56:47,676][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2e163501] 
[2025-07-30 08:57:47,755][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:57:47,773][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@74df6db6] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:57:47,774][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:57:47,775][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@74df6db6] 
[2025-07-30 08:58:47,795][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:58:47,809][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@bf94c83] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:58:47,815][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:58:47,815][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@bf94c83] 
[2025-07-30 08:59:47,839][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 08:59:47,844][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1a61d0f8] was not registered for synchronization because synchronization is not active 
[2025-07-30 08:59:47,846][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 08:59:47,847][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@1a61d0f8] 
[2025-07-30 09:00:47,868][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 09:00:47,873][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@53e0d5b2] was not registered for synchronization because synchronization is not active 
[2025-07-30 09:00:47,874][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 09:00:47,875][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@53e0d5b2] 
[2025-07-30 09:01:47,896][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 09:01:47,898][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c6740b6] was not registered for synchronization because synchronization is not active 
[2025-07-30 09:01:47,899][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 09:01:47,900][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4c6740b6] 
[2025-07-30 09:02:47,910][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 09:02:47,912][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@41decee3] was not registered for synchronization because synchronization is not active 
[2025-07-30 09:02:47,912][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 09:02:47,913][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@41decee3] 
[2025-07-30 09:03:47,921][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 09:03:47,925][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@bf39c2d] was not registered for synchronization because synchronization is not active 
[2025-07-30 09:03:47,926][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 09:03:47,926][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@bf39c2d] 
[2025-07-30 09:04:47,940][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 09:04:47,942][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@497a3afc] was not registered for synchronization because synchronization is not active 
[2025-07-30 09:04:47,943][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 09:04:47,944][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@497a3afc] 
[2025-07-30 09:05:47,958][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 09:05:47,963][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@65225c3d] was not registered for synchronization because synchronization is not active 
[2025-07-30 09:05:47,964][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 09:05:47,965][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@65225c3d] 
[2025-07-30 09:06:47,981][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 09:06:47,984][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@473bcab5] was not registered for synchronization because synchronization is not active 
[2025-07-30 09:06:47,986][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 09:06:47,987][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@473bcab5] 
[2025-07-30 09:07:48,003][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 09:07:48,006][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2a8b865e] was not registered for synchronization because synchronization is not active 
[2025-07-30 09:07:48,008][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 09:07:48,008][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@2a8b865e] 
[2025-07-30 09:08:48,019][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 09:08:48,022][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ba087f] was not registered for synchronization because synchronization is not active 
[2025-07-30 09:08:48,023][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 09:08:48,024][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@ba087f] 
[2025-07-30 09:09:48,037][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 09:09:48,041][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@59e0f5e1] was not registered for synchronization because synchronization is not active 
[2025-07-30 09:09:48,043][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 09:09:48,043][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@59e0f5e1] 
[2025-07-30 09:10:48,061][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 09:10:48,065][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@747bb882] was not registered for synchronization because synchronization is not active 
[2025-07-30 09:10:48,066][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 09:10:48,066][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@747bb882] 
[2025-07-30 09:11:48,077][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 09:11:48,080][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4ad03f80] was not registered for synchronization because synchronization is not active 
[2025-07-30 09:11:48,082][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 09:11:48,082][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@4ad03f80] 
[2025-07-30 09:12:48,143][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Creating a new SqlSession 
[2025-07-30 09:12:48,145][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@d96d11f] was not registered for synchronization because synchronization is not active 
[2025-07-30 09:12:48,147][DEBUG][SmartJobLauncher-0][n.l.s.a.m.s.d.MyBatisPlugin:55] MyBatisPlugin intercept: id=net.lab1024.sa.base.module.support.job.repository.SmartJobDao.selectList, path=SmartJobDao.selectList 
[2025-07-30 09:12:48,148][DEBUG][SmartJobLauncher-0][o.m.s.SqlSessionUtils:49] Closing non transactional SqlSession [org.apache.ibatis.session.defaults.DefaultSqlSession@d96d11f] 

package net.lab1024.sa.admin.module.business.hospital.followup.service;

import net.lab1024.sa.admin.module.business.hospital.customer.dao.CustomerDao;
import net.lab1024.sa.admin.module.business.hospital.customer.domain.entity.CustomerEntity;
import net.lab1024.sa.admin.module.business.hospital.followup.dao.FollowUpRecordDao;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.entity.FollowUpRecordEntity;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.form.FollowUpRecordAddForm;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.form.FollowUpRecordCompleteForm;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.form.FollowUpRecordUpdateForm;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.vo.FollowUpRecordVO;
import net.lab1024.sa.admin.module.system.employee.domain.entity.EmployeeEntity;
import net.lab1024.sa.admin.module.system.employee.service.EmployeeService;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

/**
 * 回访记录Service测试类
 *
 * <AUTHOR>
 * @Date 2025-07-29 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@ExtendWith(MockitoExtension.class)
class FollowUpRecordServiceTest {

    @Mock
    private FollowUpRecordDao followUpRecordDao;

    @Mock
    private CustomerDao customerDao;

    @Mock
    private EmployeeService employeeService;

    @InjectMocks
    private FollowUpRecordService followUpRecordService;

    private FollowUpRecordAddForm addForm;
    private FollowUpRecordUpdateForm updateForm;
    private CustomerEntity customerEntity;
    private EmployeeEntity employeeEntity;
    private FollowUpRecordEntity recordEntity;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        addForm = new FollowUpRecordAddForm();
        addForm.setCustomerId(1L);
        addForm.setFollowUpType(1);
        addForm.setFollowUpMethod(1);
        addForm.setScheduledTime(LocalDateTime.now().plusDays(1));
        addForm.setFollowUpContent("测试回访内容");
        addForm.setFollowUpUserId(1L);

        updateForm = new FollowUpRecordUpdateForm();
        updateForm.setFollowUpId(1L);
        updateForm.setFollowUpType(2);
        updateForm.setFollowUpMethod(2);
        updateForm.setFollowUpContent("更新后的回访内容");
        updateForm.setFollowUpResult("回访成功");
        updateForm.setFollowUpStatus(2);

        customerEntity = new CustomerEntity();
        customerEntity.setCustomerId(1L);
        customerEntity.setCustomerName("测试客户");
        customerEntity.setCustomerPhone("13800138000");

        employeeEntity = new EmployeeEntity();
        employeeEntity.setEmployeeId(1L);
        employeeEntity.setActualName("测试员工");

        recordEntity = new FollowUpRecordEntity();
        recordEntity.setFollowUpId(1L);
        recordEntity.setCustomerId(1L);
        recordEntity.setFollowUpType(1);
        recordEntity.setFollowUpMethod(1);
        recordEntity.setFollowUpStatus(1);
        recordEntity.setCreateTime(LocalDateTime.now());
    }

    @Test
    void testAdd_Success() {
        // 准备测试数据
        when(customerDao.selectById(anyLong())).thenReturn(customerEntity);
        when(employeeService.getById(anyLong())).thenReturn(employeeEntity);
        when(followUpRecordDao.insert(any(FollowUpRecordEntity.class))).thenReturn(1);

        // 执行测试
        ResponseDTO<String> result = followUpRecordService.add(addForm);

        // 验证结果
        assertTrue(result.getOk());
        assertEquals("添加成功", result.getMsg());

        // 验证方法调用
        verify(customerDao, times(1)).selectById(1L);
        verify(employeeService, times(1)).getById(1L);
        verify(followUpRecordDao, times(1)).insert(any(FollowUpRecordEntity.class));
    }

    @Test
    void testAdd_CustomerNotFound() {
        // 准备测试数据
        when(customerDao.selectById(anyLong())).thenReturn(null);

        // 执行测试
        ResponseDTO<String> result = followUpRecordService.add(addForm);

        // 验证结果
        assertFalse(result.getOk());
        assertEquals("客户信息不存在", result.getMsg());

        // 验证方法调用
        verify(customerDao, times(1)).selectById(1L);
        verify(followUpRecordDao, never()).insert(any(FollowUpRecordEntity.class));
    }

    @Test
    void testAdd_EmployeeNotFound() {
        // 准备测试数据
        when(customerDao.selectById(anyLong())).thenReturn(customerEntity);
        when(employeeService.getById(anyLong())).thenReturn(null);

        // 执行测试
        ResponseDTO<String> result = followUpRecordService.add(addForm);

        // 验证结果
        assertFalse(result.getOk());
        assertEquals("回访人员信息不存在", result.getMsg());

        // 验证方法调用
        verify(customerDao, times(1)).selectById(1L);
        verify(employeeService, times(1)).getById(1L);
        verify(followUpRecordDao, never()).insert(any(FollowUpRecordEntity.class));
    }

    @Test
    void testUpdate_Success() {
        // 准备测试数据
        when(followUpRecordDao.selectById(anyLong())).thenReturn(recordEntity);
        when(followUpRecordDao.updateById(any(FollowUpRecordEntity.class))).thenReturn(1);

        // 执行测试
        ResponseDTO<String> result = followUpRecordService.update(updateForm);

        // 验证结果
        assertTrue(result.getOk());
        assertEquals("更新成功", result.getMsg());

        // 验证方法调用
        verify(followUpRecordDao, times(1)).selectById(1L);
        verify(followUpRecordDao, times(1)).updateById(any(FollowUpRecordEntity.class));
    }

    @Test
    void testUpdate_RecordNotFound() {
        // 准备测试数据
        when(followUpRecordDao.selectById(anyLong())).thenReturn(null);

        // 执行测试
        ResponseDTO<String> result = followUpRecordService.update(updateForm);

        // 验证结果
        assertFalse(result.getOk());
        assertEquals("回访记录不存在", result.getMsg());

        // 验证方法调用
        verify(followUpRecordDao, times(1)).selectById(1L);
        verify(followUpRecordDao, never()).updateById(any(FollowUpRecordEntity.class));
    }

    @Test
    void testDelete_Success() {
        // 准备测试数据
        when(followUpRecordDao.selectById(anyLong())).thenReturn(recordEntity);
        when(followUpRecordDao.deleteById(anyLong())).thenReturn(1);

        // 执行测试
        ResponseDTO<String> result = followUpRecordService.delete(1L);

        // 验证结果
        assertTrue(result.getOk());
        assertEquals("删除成功", result.getMsg());

        // 验证方法调用
        verify(followUpRecordDao, times(1)).selectById(1L);
        verify(followUpRecordDao, times(1)).deleteById(1L);
    }

    @Test
    void testDelete_RecordNotFound() {
        // 准备测试数据
        when(followUpRecordDao.selectById(anyLong())).thenReturn(null);

        // 执行测试
        ResponseDTO<String> result = followUpRecordService.delete(1L);

        // 验证结果
        assertFalse(result.getOk());
        assertEquals("回访记录不存在", result.getMsg());

        // 验证方法调用
        verify(followUpRecordDao, times(1)).selectById(1L);
        verify(followUpRecordDao, never()).deleteById(anyLong());
    }

    @Test
    void testDetail_Success() {
        // 准备测试数据
        when(followUpRecordDao.selectById(anyLong())).thenReturn(recordEntity);

        // 执行测试
        ResponseDTO<FollowUpRecordVO> result = followUpRecordService.detail(1L);

        // 验证结果
        assertTrue(result.getOk());
        assertNotNull(result.getData());

        // 验证方法调用
        verify(followUpRecordDao, times(1)).selectById(1L);
    }

    @Test
    void testDetail_RecordNotFound() {
        // 准备测试数据
        when(followUpRecordDao.selectById(anyLong())).thenReturn(null);

        // 执行测试
        ResponseDTO<FollowUpRecordVO> result = followUpRecordService.detail(1L);

        // 验证结果
        assertFalse(result.getOk());
        assertEquals("回访记录不存在", result.getMsg());

        // 验证方法调用
        verify(followUpRecordDao, times(1)).selectById(1L);
    }

    @Test
    void testComplete_Success() {
        // 准备测试数据
        when(followUpRecordDao.selectById(anyLong())).thenReturn(recordEntity);
        when(followUpRecordDao.updateById(any(FollowUpRecordEntity.class))).thenReturn(1);

        // 创建完成回访表单
        FollowUpRecordCompleteForm completeForm = new FollowUpRecordCompleteForm();
        completeForm.setFollowUpContent("回访成功");
        completeForm.setFollowUpResult("客户反馈良好");
        completeForm.setSatisfactionScore(5);

        // 执行测试
        ResponseDTO<String> result = followUpRecordService.complete(1L, completeForm);

        // 验证结果
        assertTrue(result.getOk());

        // 验证方法调用
        verify(followUpRecordDao, times(1)).selectById(1L);
        verify(followUpRecordDao, times(1)).updateById(any(FollowUpRecordEntity.class));
    }

    @Test
    void testComplete_RecordNotFound() {
        // 准备测试数据
        when(followUpRecordDao.selectById(anyLong())).thenReturn(null);

        // 创建完成回访表单
        FollowUpRecordCompleteForm completeForm = new FollowUpRecordCompleteForm();
        completeForm.setFollowUpContent("回访成功");
        completeForm.setFollowUpResult("客户反馈良好");

        // 执行测试
        ResponseDTO<String> result = followUpRecordService.complete(1L, completeForm);

        // 验证结果
        assertFalse(result.getOk());
        assertEquals("回访记录不存在", result.getMsg());

        // 验证方法调用
        verify(followUpRecordDao, times(1)).selectById(1L);
        verify(followUpRecordDao, never()).updateById(any(FollowUpRecordEntity.class));
    }

    @Test
    void testComplete_AlreadyCompleted() {
        // 准备测试数据
        recordEntity.setFollowUpStatus(2); // 已完成状态
        when(followUpRecordDao.selectById(anyLong())).thenReturn(recordEntity);

        // 创建完成回访表单
        FollowUpRecordCompleteForm completeForm = new FollowUpRecordCompleteForm();
        completeForm.setFollowUpContent("回访成功");
        completeForm.setFollowUpResult("客户反馈良好");

        // 执行测试
        ResponseDTO<String> result = followUpRecordService.complete(1L, completeForm);

        // 验证结果
        assertTrue(result.getOk()); // 我们的实现允许重复完成，只是更新数据

        // 验证方法调用
        verify(followUpRecordDao, times(1)).selectById(1L);
        verify(followUpRecordDao, times(1)).updateById(any(FollowUpRecordEntity.class));
    }
}

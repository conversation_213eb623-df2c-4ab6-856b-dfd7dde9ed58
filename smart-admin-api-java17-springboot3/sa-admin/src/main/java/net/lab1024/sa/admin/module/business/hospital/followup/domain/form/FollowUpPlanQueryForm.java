package net.lab1024.sa.admin.module.business.hospital.followup.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.domain.PageParam;

import java.time.LocalDate;

/**
 * 回访计划查询表单
 *
 * <AUTHOR>
 * @Date 2025-07-30 00:00:00
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@Schema(description = "回访计划查询表单")
public class FollowUpPlanQueryForm extends PageParam {

    @Schema(description = "计划名称")
    private String planName;

    @Schema(description = "计划类型")
    private Integer planType;

    @Schema(description = "目标客户类型")
    private Integer targetCustomerType;

    @Schema(description = "回访方式")
    private Integer followUpMethod;

    @Schema(description = "计划状态")
    private Integer planStatus;

    @Schema(description = "优先级")
    private Integer priorityLevel;

    @Schema(description = "负责人姓名")
    private String responsibleUserName;

    @Schema(description = "计划开始日期-开始")
    private LocalDate planStartDateBegin;

    @Schema(description = "计划开始日期-结束")
    private LocalDate planStartDateEnd;

    @Schema(description = "计划结束日期-开始")
    private LocalDate planEndDateBegin;

    @Schema(description = "计划结束日期-结束")
    private LocalDate planEndDateEnd;

    @Schema(description = "创建时间-开始")
    private LocalDate createTimeBegin;

    @Schema(description = "创建时间-结束")
    private LocalDate createTimeEnd;
}

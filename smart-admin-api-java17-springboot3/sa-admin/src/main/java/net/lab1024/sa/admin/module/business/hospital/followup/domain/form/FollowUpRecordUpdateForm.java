package net.lab1024.sa.admin.module.business.hospital.followup.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 回访记录更新表单
 *
 * <AUTHOR>
 * @Date 2025-07-30 00:00:00
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@Schema(description = "回访记录更新表单")
public class FollowUpRecordUpdateForm {

    @Schema(description = "回访记录ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "回访记录ID不能为空")
    private Long followUpId;

    @Schema(description = "客户ID")
    private Long customerId;

    @Schema(description = "客户姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "客户姓名不能为空")
    private String customerName;

    @Schema(description = "客户电话", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "客户电话不能为空")
    private String customerPhone;

    @Schema(description = "回访类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "回访类型不能为空")
    private Integer followUpType;

    @Schema(description = "回访方式")
    private Integer followUpMethod;

    @Schema(description = "计划回访时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "回访时间不能为空")
    private LocalDateTime scheduledTime;

    @Schema(description = "回访内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "回访内容不能为空")
    private String followUpContent;

    @Schema(description = "回访结果", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "回访结果不能为空")
    private String followUpResult;

    @Schema(description = "回访人员ID")
    private Long followUpUserId;

    @Schema(description = "回访人员姓名")
    private String followUpUserName;

    @Schema(description = "满意度评分")
    private Integer satisfactionScore;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "下次回访时间")
    private LocalDateTime nextFollowUpTime;

    @Schema(description = "优先级")
    private Integer priorityLevel;

    @Schema(description = "回访状态")
    private Integer followUpStatus;
}

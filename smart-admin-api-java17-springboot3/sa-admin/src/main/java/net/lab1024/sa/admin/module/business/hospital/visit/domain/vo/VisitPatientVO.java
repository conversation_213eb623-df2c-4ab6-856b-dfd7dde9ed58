package net.lab1024.sa.admin.module.business.hospital.visit.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.swagger.SchemaEnum;
import net.lab1024.sa.admin.module.business.hospital.visit.constant.VisitStatusEnum;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 到诊患者视图对象
 *
 * <AUTHOR>
 * @Date 2025-07-29 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class VisitPatientVO {

    @Schema(description = "患者ID")
    private Long patientId;

    @Schema(description = "患者编号")
    private String patientNo;

    @Schema(description = "患者姓名")
    private String patientName;

    @Schema(description = "患者电话")
    private String patientPhone;

    @Schema(description = "患者微信")
    private String patientWechat;

    @Schema(description = "患者邮箱")
    private String patientEmail;

    @Schema(description = "性别：1-男，2-女")
    private Integer gender;

    @Schema(description = "性别名称")
    private String genderName;

    @Schema(description = "年龄")
    private Integer age;

    @Schema(description = "出生日期")
    private LocalDate birthday;

    @Schema(description = "身份证号")
    private String idCard;

    @Schema(description = "患者地址")
    private String patientAddress;

    @Schema(description = "职业")
    private String occupation;

    @Schema(description = "到诊状态：1-未到诊，2-已到诊，3-诊疗中，4-已完成，5-已离院")
    @SchemaEnum(VisitStatusEnum.class)
    private Integer visitStatus;

    @Schema(description = "到诊状态名称")
    private String visitStatusName;

    @Schema(description = "关联预约ID")
    private Long appointmentId;

    @Schema(description = "分配医生ID")
    private Long assignedDoctorId;

    @Schema(description = "分配医生姓名")
    private String assignedDoctorName;

    @Schema(description = "分配治疗助理ID")
    private Long assignedAssistantId;

    @Schema(description = "分配治疗助理姓名")
    private String assignedAssistantName;

    @Schema(description = "到诊日期")
    private LocalDate visitDate;

    @Schema(description = "到诊时间")
    private LocalTime visitTime;

    @Schema(description = "登记时间")
    private LocalDateTime registrationTime;

    @Schema(description = "完成时间")
    private LocalDateTime completionTime;

    @Schema(description = "所属部门ID")
    private Long departmentId;

    @Schema(description = "所属部门名称")
    private String departmentName;

    @Schema(description = "患者来源")
    private String patientSource;

    @Schema(description = "患者等级：1-普通患者，2-VIP患者，3-SVIP患者")
    private Integer patientLevel;

    @Schema(description = "患者等级名称")
    private String patientLevelName;

    @Schema(description = "患者标签")
    private String patientTags;

    @Schema(description = "首次就诊日期")
    private LocalDate firstVisitDate;

    @Schema(description = "最后就诊日期")
    private LocalDate lastVisitDate;

    @Schema(description = "总消费金额")
    private BigDecimal totalConsumption;

    @Schema(description = "紧急联系人")
    private String emergencyContact;

    @Schema(description = "紧急联系人电话")
    private String emergencyPhone;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    // 扩展字段
    @Schema(description = "诊断记录数量")
    private Integer diagnosisCount;

    @Schema(description = "开单记录数量")
    private Integer prescriptionCount;

    @Schema(description = "跟进记录数量")
    private Integer followCount;

    @Schema(description = "最近诊断时间")
    private LocalDateTime lastDiagnosisTime;

    @Schema(description = "最近跟进时间")
    private LocalDateTime lastFollowTime;

    @Schema(description = "待处理事项数量")
    private Integer pendingTaskCount;
}

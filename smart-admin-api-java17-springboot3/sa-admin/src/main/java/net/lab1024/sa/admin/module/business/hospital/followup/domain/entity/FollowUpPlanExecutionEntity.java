package net.lab1024.sa.admin.module.business.hospital.followup.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 回访计划执行记录实体
 *
 * <AUTHOR>
 * @Date 2025-07-30 00:00:00
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@TableName("t_follow_up_plan_execution")
public class FollowUpPlanExecutionEntity {

    /**
     * 执行记录ID
     */
    @TableId(type = IdType.AUTO)
    private Long executionId;

    /**
     * 计划ID
     */
    private Long planId;

    /**
     * 执行日期
     */
    private LocalDate executionDate;

    /**
     * 执行时间
     */
    private LocalDateTime executionTime;

    /**
     * 目标客户数量
     */
    private Integer targetCustomerCount;

    /**
     * 生成记录数量
     */
    private Integer generatedRecordCount;

    /**
     * 执行状态：1-执行中，2-执行成功，3-执行失败
     */
    private Integer executionStatus;

    /**
     * 执行结果描述
     */
    private String executionResult;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 执行耗时（秒）
     */
    private Integer executionDuration;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}

package net.lab1024.sa.admin.module.business.hospital.followup.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.entity.FollowUpRecordEntity;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.form.FollowUpRecordQueryForm;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.vo.FollowUpRecordVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 回访记录DAO
 *
 * <AUTHOR>
 * @Date 2025-07-30 00:00:00
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Mapper
public interface FollowUpRecordDao extends BaseMapper<FollowUpRecordEntity> {

    /**
     * 分页查询回访记录
     */
    List<FollowUpRecordVO> queryPage(Page page, @Param("queryForm") FollowUpRecordQueryForm queryForm);

    /**
     * 获取客户回访历史
     */
    List<FollowUpRecordVO> getCustomerFollowUpHistory(@Param("customerId") Long customerId);
}

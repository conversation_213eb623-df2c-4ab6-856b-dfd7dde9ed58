package net.lab1024.sa.admin.module.business.hospital.visit.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 开单记录实体类
 *
 * <AUTHOR>
 * @Date 2025-07-29
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@TableName("t_prescription")
public class PrescriptionEntity {

    /**
     * 开单ID
     */
    @TableId(type = IdType.AUTO)
    private Long prescriptionId;

    /**
     * 诊断ID
     */
    private Long diagnosisId;

    /**
     * 患者ID
     */
    private Long patientId;

    /**
     * 患者编号
     */
    private String patientNo;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 开单编号
     */
    private String prescriptionNo;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 开单说明
     */
    private String prescriptionNote;

    /**
     * 开单状态：1-已开单，2-已收费，3-已完成
     */
    private Integer prescriptionStatus;

    /**
     * 创建人ID
     */
    private Long createUserId;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 更新人ID
     */
    private Long updateUserId;

    /**
     * 更新人姓名
     */
    private String updateUserName;

    /**
     * 删除标识：0-未删除，1-已删除
     */
    private Integer deletedFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}

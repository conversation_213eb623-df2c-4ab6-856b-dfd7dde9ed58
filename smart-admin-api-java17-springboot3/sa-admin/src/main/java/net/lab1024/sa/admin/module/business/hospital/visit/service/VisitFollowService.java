package net.lab1024.sa.admin.module.business.hospital.visit.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.hospital.followup.dao.FollowUpRecordDao;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.entity.FollowUpRecordEntity;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.form.FollowUpRecordAddForm;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.form.FollowUpRecordQueryForm;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.vo.FollowUpRecordVO;
import net.lab1024.sa.admin.module.business.hospital.visit.dao.VisitPatientDao;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.entity.VisitPatientEntity;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.form.VisitFollowAddForm;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.form.VisitFollowQueryForm;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.VisitFollowVO;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 到诊患者跟进服务
 *
 * <AUTHOR>
 * @Date 2025-07-30 00:00:00
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Slf4j
@Service
public class VisitFollowService {

    @Autowired
    private FollowUpRecordDao followUpRecordDao;

    @Autowired
    private VisitPatientDao visitPatientDao;

    /**
     * 分页查询患者跟进记录
     */
    public PageResult<VisitFollowVO> queryPage(VisitFollowQueryForm queryForm) {
        // 转换为回访记录查询表单
        FollowUpRecordQueryForm followUpQueryForm = new FollowUpRecordQueryForm();
        followUpQueryForm.setPageNum(queryForm.getPageNum());
        followUpQueryForm.setPageSize(queryForm.getPageSize());
        followUpQueryForm.setCustomerName(queryForm.getPatientName());
        followUpQueryForm.setCustomerPhone(queryForm.getPatientPhone());
        followUpQueryForm.setFollowUpType(queryForm.getFollowType());
        followUpQueryForm.setFollowUpStatus(queryForm.getFollowStatus());
        followUpQueryForm.setRelatedRecordType("VISIT_PATIENT");
        
        if (queryForm.getPatientId() != null) {
            followUpQueryForm.setRelatedRecordId(queryForm.getPatientId());
        }

        Page<?> page = SmartPageUtil.convert2PageQuery(followUpQueryForm);
        List<FollowUpRecordVO> list = followUpRecordDao.queryPage(page, followUpQueryForm);
        
        // 转换为到诊跟进VO
        List<VisitFollowVO> visitFollowList = list.stream()
                .map(this::convertToVisitFollowVO)
                .toList();
        
        PageResult<VisitFollowVO> pageResult = SmartPageUtil.convert2PageResult(page, visitFollowList);
        return pageResult;
    }

    /**
     * 根据患者ID查询跟进记录
     */
    public ResponseDTO<List<VisitFollowVO>> getByPatientId(Long patientId) {
        try {
            FollowUpRecordQueryForm queryForm = new FollowUpRecordQueryForm();
            queryForm.setRelatedRecordId(patientId);
            queryForm.setRelatedRecordType("VISIT_PATIENT");
            // 设置默认分页参数
            queryForm.setPageNum(1L);
            queryForm.setPageSize(100L);

            Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
            List<FollowUpRecordVO> list = followUpRecordDao.queryPage(page, queryForm);
            
            List<VisitFollowVO> visitFollowList = list.stream()
                    .map(this::convertToVisitFollowVO)
                    .toList();
            
            return ResponseDTO.ok(visitFollowList);
        } catch (Exception e) {
            log.error("查询患者跟进记录失败", e);
            return ResponseDTO.userErrorParam("查询患者跟进记录失败");
        }
    }

    /**
     * 新增患者跟进记录
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> add(VisitFollowAddForm addForm, Long operateUserId, String operateUserName) {
        try {
            // 1. 校验患者是否存在
            VisitPatientEntity patient = visitPatientDao.selectById(addForm.getPatientId());
            if (patient == null || patient.getDeletedFlag()) {
                return ResponseDTO.userErrorParam("患者不存在");
            }

            // 2. 转换为回访记录表单
            FollowUpRecordAddForm followUpAddForm = new FollowUpRecordAddForm();
            followUpAddForm.setCustomerName(patient.getPatientName());
            followUpAddForm.setCustomerPhone(patient.getPatientPhone());
            followUpAddForm.setFollowUpType(addForm.getFollowType());
            followUpAddForm.setFollowUpMethod(addForm.getFollowMethod());
            followUpAddForm.setScheduledTime(addForm.getPlannedFollowTime());
            followUpAddForm.setFollowUpContent(addForm.getFollowContent());
            followUpAddForm.setFollowUpResult(addForm.getFollowResult());
            followUpAddForm.setRemark(addForm.getRemark());
            followUpAddForm.setPriorityLevel(addForm.getPriorityLevel());
            followUpAddForm.setFollowUpStatus(addForm.getFollowStatus());

            // 3. 创建回访记录实体
            FollowUpRecordEntity followUpEntity = SmartBeanUtil.copy(followUpAddForm, FollowUpRecordEntity.class);
            followUpEntity.setRelatedRecordId(addForm.getPatientId());
            followUpEntity.setRelatedRecordType("VISIT_PATIENT");
            followUpEntity.setFollowUpUserId(operateUserId);
            followUpEntity.setFollowUpUserName(operateUserName);
            followUpEntity.setCreateUserId(operateUserId);
            followUpEntity.setCreateTime(LocalDateTime.now());
            followUpEntity.setDeletedFlag(0);

            // 4. 保存到数据库
            followUpRecordDao.insert(followUpEntity);

            return ResponseDTO.ok("跟进记录创建成功");
        } catch (Exception e) {
            log.error("创建患者跟进记录失败", e);
            return ResponseDTO.userErrorParam("创建患者跟进记录失败");
        }
    }

    /**
     * 自动创建患者跟进计划
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> createFollowUpPlan(Long patientId, Long operateUserId, String operateUserName) {
        try {
            // 1. 校验患者是否存在
            VisitPatientEntity patient = visitPatientDao.selectById(patientId);
            if (patient == null || patient.getDeletedFlag()) {
                return ResponseDTO.userErrorParam("患者不存在");
            }

            // 2. 根据患者状态创建不同的跟进计划
            if (patient.getDiagnosisStatus() >= 7) { // 已完成治疗
                // 创建术后跟进
                createPostTreatmentFollowUp(patient, operateUserId, operateUserName);
                
                // 创建满意度调查
                createSatisfactionSurvey(patient, operateUserId, operateUserName);
            }

            return ResponseDTO.ok("跟进计划创建成功");
        } catch (Exception e) {
            log.error("创建患者跟进计划失败", e);
            return ResponseDTO.userErrorParam("创建患者跟进计划失败");
        }
    }

    /**
     * 创建术后跟进记录
     */
    private void createPostTreatmentFollowUp(VisitPatientEntity patient, Long operateUserId, String operateUserName) {
        FollowUpRecordEntity followUpEntity = new FollowUpRecordEntity();
        followUpEntity.setCustomerName(patient.getPatientName());
        followUpEntity.setCustomerPhone(patient.getPatientPhone());
        followUpEntity.setFollowUpType(1); // 治疗回访
        followUpEntity.setFollowUpMethod(1); // 电话回访
        followUpEntity.setScheduledTime(LocalDateTime.now().plusDays(3)); // 3天后跟进
        followUpEntity.setFollowUpContent("询问患者术后恢复情况，是否有不适症状，提醒按时服药和注意事项");
        followUpEntity.setPriorityLevel(2); // 中等优先级
        followUpEntity.setFollowUpStatus(1); // 待回访
        followUpEntity.setRelatedRecordId(patient.getPatientId());
        followUpEntity.setRelatedRecordType("VISIT_PATIENT");
        followUpEntity.setFollowUpUserId(operateUserId);
        followUpEntity.setFollowUpUserName(operateUserName);
        followUpEntity.setCreateUserId(operateUserId);
        followUpEntity.setCreateTime(LocalDateTime.now());
        followUpEntity.setDeletedFlag(0);

        followUpRecordDao.insert(followUpEntity);
    }

    /**
     * 创建满意度调查记录
     */
    private void createSatisfactionSurvey(VisitPatientEntity patient, Long operateUserId, String operateUserName) {
        FollowUpRecordEntity followUpEntity = new FollowUpRecordEntity();
        followUpEntity.setCustomerName(patient.getPatientName());
        followUpEntity.setCustomerPhone(patient.getPatientPhone());
        followUpEntity.setFollowUpType(3); // 满意度调查
        followUpEntity.setFollowUpMethod(1); // 电话回访
        followUpEntity.setScheduledTime(LocalDateTime.now().plusDays(7)); // 7天后调查
        followUpEntity.setFollowUpContent("了解患者对本次诊疗服务的满意度，收集改进建议");
        followUpEntity.setPriorityLevel(3); // 低优先级
        followUpEntity.setFollowUpStatus(1); // 待回访
        followUpEntity.setRelatedRecordId(patient.getPatientId());
        followUpEntity.setRelatedRecordType("VISIT_PATIENT");
        followUpEntity.setFollowUpUserId(operateUserId);
        followUpEntity.setFollowUpUserName(operateUserName);
        followUpEntity.setCreateUserId(operateUserId);
        followUpEntity.setCreateTime(LocalDateTime.now());
        followUpEntity.setDeletedFlag(0);

        followUpRecordDao.insert(followUpEntity);
    }

    /**
     * 转换为到诊跟进VO
     */
    private VisitFollowVO convertToVisitFollowVO(FollowUpRecordVO followUpVO) {
        VisitFollowVO visitFollowVO = new VisitFollowVO();
        visitFollowVO.setFollowId(followUpVO.getFollowUpId());
        visitFollowVO.setPatientId(followUpVO.getRelatedRecordId());
        visitFollowVO.setPatientName(followUpVO.getCustomerName());
        visitFollowVO.setPatientPhone(followUpVO.getCustomerPhone());
        visitFollowVO.setFollowType(followUpVO.getFollowUpType());
        visitFollowVO.setFollowMethod(followUpVO.getFollowUpMethod());
        visitFollowVO.setFollowContent(followUpVO.getFollowUpContent());
        visitFollowVO.setFollowResult(followUpVO.getFollowUpResult());
        visitFollowVO.setPlannedFollowTime(followUpVO.getScheduledTime());
        visitFollowVO.setActualFollowTime(followUpVO.getActualTime());
        visitFollowVO.setFollowStatus(followUpVO.getFollowUpStatus());
        visitFollowVO.setPriorityLevel(followUpVO.getPriorityLevel());
        visitFollowVO.setSatisfactionScore(followUpVO.getSatisfactionScore());
        visitFollowVO.setFollowUserName(followUpVO.getFollowUpUserName());
        visitFollowVO.setRemark(followUpVO.getRemark());
        visitFollowVO.setCreateTime(followUpVO.getCreateTime());
        return visitFollowVO;
    }
}

package net.lab1024.sa.admin.module.business.hospital.lead.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 线索归属变更申请实体类
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@TableName("t_lead_ownership_change_request")
public class LeadOwnershipChangeRequestEntity {

    /**
     * 申请ID
     */
    @TableId(type = IdType.AUTO)
    private Long requestId;

    /**
     * 线索ID
     */
    private Long leadId;

    /**
     * 客户手机号（冗余字段，便于查询）
     */
    private String customerPhone;

    /**
     * 客户姓名（冗余字段，便于查询）
     */
    private String customerName;

    /**
     * 原归属人ID
     */
    private Long originalOwnerId;

    /**
     * 原归属人姓名
     */
    private String originalOwnerName;

    /**
     * 新归属人ID（申请人）
     */
    private Long newOwnerId;

    /**
     * 新归属人姓名
     */
    private String newOwnerName;

    /**
     * 申请人所在部门ID
     */
    private Long departmentId;

    /**
     * 申请人所在部门名称
     */
    private String departmentName;

    /**
     * 部门负责人ID
     */
    private Long departmentManagerId;

    /**
     * 部门负责人姓名
     */
    private String departmentManagerName;

    /**
     * 申请原因
     */
    private String requestReason;

    /**
     * 申请状态：1-待审批，2-已同意，3-已拒绝，4-已撤销
     */
    private Integer requestStatus;

    /**
     * 审批时间
     */
    private LocalDateTime approveTime;

    /**
     * 审批意见
     */
    private String approveReason;

    /**
     * 审批人ID
     */
    private Long approveUserId;

    /**
     * 审批人姓名
     */
    private String approveUserName;

    /**
     * 原线索最后跟进时间（申请时记录）
     */
    private LocalDateTime lastFollowTime;

    /**
     * 距离最后跟进天数（申请时记录）
     */
    private Integer daysSinceLastFollow;

    /**
     * 删除标志：0-未删除，1-已删除
     */
    private Boolean deletedFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人ID
     */
    private Long createUserId;

    /**
     * 更新人ID
     */
    private Long updateUserId;
}

package net.lab1024.sa.admin.module.business.hospital.followup.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.constant.AdminSwaggerTagConst;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.form.FollowUpRecordAddForm;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.form.FollowUpRecordBatchCompleteForm;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.form.FollowUpRecordCompleteForm;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.form.FollowUpRecordQueryForm;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.form.FollowUpRecordUpdateForm;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.vo.FollowUpRecordVO;
import net.lab1024.sa.admin.module.business.hospital.followup.service.FollowUpRecordService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.ValidateList;
import net.lab1024.sa.base.module.support.operatelog.annotation.OperateLog;
import net.lab1024.sa.base.module.support.repeatsubmit.annoation.RepeatSubmit;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 回访记录管理Controller
 *
 * <AUTHOR>
 * @Date 2025-07-30 00:00:00
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Tag(name = AdminSwaggerTagConst.Business.HOSPITAL_FOLLOW_UP)
@RestController
public class FollowUpRecordController {

    @Resource
    private FollowUpRecordService followUpRecordService;

    @Operation(summary = "分页查询回访记录")
    @PostMapping("/api/followup/record/queryPage")
    @SaCheckPermission("hospital:followup:record:query")
    public ResponseDTO<PageResult<FollowUpRecordVO>> queryPage(@RequestBody @Valid FollowUpRecordQueryForm queryForm) {
        return followUpRecordService.queryPage(queryForm);
    }

    @Operation(summary = "分页查询患者回访列表")
    @PostMapping("/api/hospital/followup/patient-list/queryPage")
    @SaCheckPermission("hospital:followup:patient-list:query")
    public ResponseDTO<PageResult<FollowUpRecordVO>> queryPatientList(@RequestBody @Valid FollowUpRecordQueryForm queryForm) {
        // 设置查询条件为患者相关的回访记录
        queryForm.setRelatedRecordType("VISIT_PATIENT");
        return followUpRecordService.queryPage(queryForm);
    }

    @Operation(summary = "添加回访记录")
    @PostMapping("/api/followup/record/add")
    @SaCheckPermission("hospital:followup:record:add")
    @RepeatSubmit
    @OperateLog
    public ResponseDTO<String> add(@RequestBody @Valid FollowUpRecordAddForm addForm) {
        return followUpRecordService.add(addForm);
    }

    @Operation(summary = "更新回访记录")
    @PostMapping("/api/followup/record/update")
    @SaCheckPermission("hospital:followup:record:update")
    @RepeatSubmit
    @OperateLog
    public ResponseDTO<String> update(@RequestBody @Valid FollowUpRecordUpdateForm updateForm) {
        return followUpRecordService.update(updateForm);
    }

    @Operation(summary = "删除回访记录")
    @GetMapping("/api/followup/record/delete/{recordId}")
    @SaCheckPermission("hospital:followup:record:delete")
    @OperateLog
    public ResponseDTO<String> delete(@PathVariable Long recordId) {
        return followUpRecordService.delete(recordId);
    }

    @Operation(summary = "批量删除回访记录")
    @PostMapping("/api/followup/record/batchDelete")
    @SaCheckPermission("hospital:followup:record:delete")
    @OperateLog
    public ResponseDTO<String> batchDelete(@RequestBody ValidateList<Long> recordIdList) {
        return followUpRecordService.batchDelete(recordIdList);
    }

    @Operation(summary = "获取回访记录详情")
    @GetMapping("/api/followup/record/detail/{recordId}")
    @SaCheckPermission("hospital:followup:record:query")
    public ResponseDTO<FollowUpRecordVO> detail(@PathVariable Long recordId) {
        return followUpRecordService.detail(recordId);
    }

    @Operation(summary = "导出回访记录")
    @PostMapping("/api/followup/record/export")
    @SaCheckPermission("hospital:followup:record:export")
    @OperateLog
    public ResponseDTO<String> export(@RequestBody @Valid FollowUpRecordQueryForm queryForm) {
        return followUpRecordService.export(queryForm);
    }

    @Operation(summary = "获取回访统计数据")
    @PostMapping("/api/followup/record/statistics")
    @SaCheckPermission("hospital:followup:record:query")
    public ResponseDTO<Object> getStatistics(@RequestBody @Valid FollowUpRecordQueryForm queryForm) {
        return followUpRecordService.getStatistics(queryForm);
    }

    @Operation(summary = "获取客户回访历史")
    @GetMapping("/api/followup/record/customer/{customerId}/history")
    @SaCheckPermission("hospital:followup:record:query")
    public ResponseDTO<List<FollowUpRecordVO>> getCustomerFollowUpHistory(@PathVariable Long customerId) {
        return followUpRecordService.getCustomerFollowUpHistory(customerId);
    }

    @Operation(summary = "完成回访记录")
    @PostMapping("/api/followup/record/complete/{recordId}")
    @SaCheckPermission("hospital:followup:record:update")
    @OperateLog
    public ResponseDTO<String> complete(@PathVariable Long recordId, @RequestBody(required = false) FollowUpRecordCompleteForm completeForm) {
        return followUpRecordService.complete(recordId, completeForm);
    }

    @Operation(summary = "批量完成回访记录")
    @PostMapping("/api/followup/record/batchComplete")
    @SaCheckPermission("hospital:followup:record:update")
    @OperateLog
    public ResponseDTO<String> batchComplete(@RequestBody FollowUpRecordBatchCompleteForm batchCompleteForm) {
        return followUpRecordService.batchComplete(batchCompleteForm);
    }
}

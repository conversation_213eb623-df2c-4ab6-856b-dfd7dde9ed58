package net.lab1024.sa.admin.module.business.hospital.followup.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.hospital.followup.dao.FollowUpPlanDao;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.entity.FollowUpPlanEntity;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.form.FollowUpPlanAddForm;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.form.FollowUpPlanQueryForm;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.form.FollowUpPlanUpdateForm;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.vo.FollowUpPlanVO;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.admin.util.AdminRequestUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 回访计划Service
 *
 * <AUTHOR>
 * @Date 2025-07-30 00:00:00
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Slf4j
@Service
public class FollowUpPlanService {

    @Resource
    private FollowUpPlanDao followUpPlanDao;

    /**
     * 分页查询回访计划
     */
    public ResponseDTO<PageResult<FollowUpPlanVO>> queryPage(FollowUpPlanQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<FollowUpPlanVO> list = followUpPlanDao.queryPage(page, queryForm);
        PageResult<FollowUpPlanVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return ResponseDTO.ok(pageResult);
    }

    /**
     * 添加回访计划
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> add(FollowUpPlanAddForm addForm) {
        FollowUpPlanEntity entity = SmartBeanUtil.copy(addForm, FollowUpPlanEntity.class);
        entity.setCreateUserId(AdminRequestUtil.getRequestUserId());
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateUserId(AdminRequestUtil.getRequestUserId());
        entity.setUpdateTime(LocalDateTime.now());

        // 设置默认值
        if (entity.getPriorityLevel() == null) {
            entity.setPriorityLevel(3); // 默认中等优先级
        }
        if (entity.getAutoGenerateRecords() == null) {
            entity.setAutoGenerateRecords(1); // 默认自动生成记录
        }
        if (entity.getPlanStatus() == null) {
            entity.setPlanStatus(1); // 默认待执行状态
        }
        if (entity.getExpectedCustomerCount() == null) {
            entity.setExpectedCustomerCount(0);
        }
        entity.setActualCustomerCount(0);
        entity.setCompletedCount(0);
        entity.setSuccessRate(BigDecimal.ZERO);
        entity.setDeletedFlag(0);

        // 设置负责人信息
        if (entity.getResponsibleUserId() == null) {
            entity.setResponsibleUserId(AdminRequestUtil.getRequestUserId());
        }

        // 计算下次执行时间
        entity.setNextExecutionTime(calculateNextExecutionTime(entity));

        followUpPlanDao.insert(entity);
        return ResponseDTO.ok();
    }

    /**
     * 更新回访计划
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> update(FollowUpPlanUpdateForm updateForm) {
        FollowUpPlanEntity entity = followUpPlanDao.selectById(updateForm.getPlanId());
        if (entity == null) {
            return ResponseDTO.userErrorParam("回访计划不存在");
        }

        SmartBeanUtil.copyProperties(updateForm, entity);
        entity.setUpdateUserId(AdminRequestUtil.getRequestUserId());
        entity.setUpdateTime(LocalDateTime.now());

        // 重新计算下次执行时间
        entity.setNextExecutionTime(calculateNextExecutionTime(entity));

        followUpPlanDao.updateById(entity);
        return ResponseDTO.ok();
    }

    /**
     * 删除回访计划
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> delete(Long planId) {
        FollowUpPlanEntity entity = followUpPlanDao.selectById(planId);
        if (entity == null) {
            return ResponseDTO.userErrorParam("回访计划不存在");
        }

        entity.setDeletedFlag(1);
        entity.setUpdateUserId(AdminRequestUtil.getRequestUserId());
        entity.setUpdateTime(LocalDateTime.now());

        followUpPlanDao.updateById(entity);
        return ResponseDTO.ok();
    }

    /**
     * 获取回访计划详情
     */
    public ResponseDTO<FollowUpPlanVO> detail(Long planId) {
        FollowUpPlanEntity entity = followUpPlanDao.selectById(planId);
        if (entity == null) {
            return ResponseDTO.userErrorParam("回访计划不存在");
        }

        FollowUpPlanVO vo = SmartBeanUtil.copy(entity, FollowUpPlanVO.class);
        return ResponseDTO.ok(vo);
    }

    /**
     * 启动回访计划
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> start(Long planId) {
        FollowUpPlanEntity entity = followUpPlanDao.selectById(planId);
        if (entity == null) {
            return ResponseDTO.userErrorParam("回访计划不存在");
        }

        if (entity.getPlanStatus() == 2) {
            return ResponseDTO.userErrorParam("计划已在执行中");
        }

        entity.setPlanStatus(2); // 执行中
        entity.setUpdateUserId(AdminRequestUtil.getRequestUserId());
        entity.setUpdateTime(LocalDateTime.now());

        followUpPlanDao.updateById(entity);
        return ResponseDTO.ok();
    }

    /**
     * 暂停回访计划
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> pause(Long planId) {
        FollowUpPlanEntity entity = followUpPlanDao.selectById(planId);
        if (entity == null) {
            return ResponseDTO.userErrorParam("回访计划不存在");
        }

        if (entity.getPlanStatus() != 2) {
            return ResponseDTO.userErrorParam("只有执行中的计划才能暂停");
        }

        entity.setPlanStatus(3); // 已暂停
        entity.setUpdateUserId(AdminRequestUtil.getRequestUserId());
        entity.setUpdateTime(LocalDateTime.now());

        followUpPlanDao.updateById(entity);
        return ResponseDTO.ok();
    }

    /**
     * 执行回访计划
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> execute(Long planId) {
        FollowUpPlanEntity entity = followUpPlanDao.selectById(planId);
        if (entity == null) {
            return ResponseDTO.userErrorParam("回访计划不存在");
        }

        if (entity.getPlanStatus() != 2) {
            return ResponseDTO.userErrorParam("只有执行中的计划才能执行");
        }

        try {
            // 1. 根据筛选条件获取目标客户（这里简化处理，实际应该根据customer_filter_conditions查询）
            // 实际项目中需要根据具体的客户表和筛选条件来实现

            // 2. 生成回访记录（这里模拟生成一些记录）
            int generatedCount = generateFollowUpRecords(entity);

            // 3. 更新计划统计信息
            entity.setActualCustomerCount(generatedCount);
            entity.setLastExecutionTime(LocalDateTime.now());
            entity.setNextExecutionTime(calculateNextExecutionTime(entity));
            entity.setUpdateUserId(AdminRequestUtil.getRequestUserId());
            entity.setUpdateTime(LocalDateTime.now());

            followUpPlanDao.updateById(entity);

            return ResponseDTO.ok("计划执行成功，生成" + generatedCount + "条回访记录");
        } catch (Exception e) {
            log.error("执行回访计划失败", e);
            return ResponseDTO.userErrorParam("执行失败：" + e.getMessage());
        }
    }

    /**
     * 获取计划统计数据
     */
    public ResponseDTO<Object> getStatistics(FollowUpPlanQueryForm queryForm) {
        try {
            Map<String, Object> statistics = new HashMap<>();

            // 总计划数
            Long totalCount = followUpPlanDao.selectCount(
                new LambdaQueryWrapper<FollowUpPlanEntity>()
                    .eq(FollowUpPlanEntity::getDeletedFlag, 0)
            );
            statistics.put("totalCount", totalCount);

            // 按状态统计
            Map<String, Long> statusStats = new HashMap<>();
            for (int status = 1; status <= 3; status++) {
                Long count = followUpPlanDao.selectCount(
                    new LambdaQueryWrapper<FollowUpPlanEntity>()
                        .eq(FollowUpPlanEntity::getDeletedFlag, 0)
                        .eq(FollowUpPlanEntity::getPlanStatus, status)
                );
                statusStats.put(getPlanStatusDesc(status), count);
            }
            statistics.put("statusStats", statusStats);

            // 按类型统计
            Map<String, Long> typeStats = new HashMap<>();
            for (int type = 1; type <= 3; type++) {
                Long count = followUpPlanDao.selectCount(
                    new LambdaQueryWrapper<FollowUpPlanEntity>()
                        .eq(FollowUpPlanEntity::getDeletedFlag, 0)
                        .eq(FollowUpPlanEntity::getPlanType, type)
                );
                typeStats.put(getPlanTypeDesc(type), count);
            }
            statistics.put("typeStats", typeStats);

            return ResponseDTO.ok(statistics);
        } catch (Exception e) {
            log.error("获取计划统计数据失败", e);
            return ResponseDTO.userErrorParam("获取统计数据失败");
        }
    }

    /**
     * 计算下次执行时间
     */
    private LocalDateTime calculateNextExecutionTime(FollowUpPlanEntity entity) {
        LocalDateTime now = LocalDateTime.now();
        Integer frequency = entity.getExecutionFrequency();

        if (frequency == null) {
            return now.plusDays(1); // 默认明天
        }

        return switch (frequency) {
            case 1 -> now.plusDays(1);    // 每日
            case 2 -> now.plusWeeks(1);   // 每周
            case 3 -> now.plusMonths(1);  // 每月
            case 4 -> now.plusMonths(3);  // 每季度
            case 5 -> now.plusYears(1);   // 每年
            default -> now.plusDays(1);   // 默认每日
        };
    }

    /**
     * 生成回访记录
     */
    private int generateFollowUpRecords(FollowUpPlanEntity entity) {
        // 这里简化处理，实际应该根据客户筛选条件查询目标客户
        // 然后为每个客户生成回访记录

        // 模拟生成记录数量
        int expectedCount = entity.getExpectedCustomerCount() != null ? entity.getExpectedCustomerCount() : 10;

        // 实际项目中，这里应该：
        // 1. 根据customer_filter_conditions解析筛选条件
        // 2. 查询符合条件的客户列表
        // 3. 为每个客户创建回访记录
        // 4. 批量插入回访记录表

        return Math.min(expectedCount, 50); // 限制最大生成数量
    }

    /**
     * 获取计划状态描述
     */
    private String getPlanStatusDesc(Integer status) {
        if (status == null) return "未知";
        return switch (status) {
            case 1 -> "草稿";
            case 2 -> "执行中";
            case 3 -> "已暂停";
            default -> "未知";
        };
    }

    /**
     * 获取计划类型描述
     */
    private String getPlanTypeDesc(Integer type) {
        if (type == null) return "未知";
        return switch (type) {
            case 1 -> "定期回访";
            case 2 -> "节日回访";
            case 3 -> "特殊回访";
            default -> "未知";
        };
    }
}

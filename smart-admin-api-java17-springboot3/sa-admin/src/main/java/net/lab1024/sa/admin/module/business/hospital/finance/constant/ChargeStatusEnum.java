package net.lab1024.sa.admin.module.business.hospital.finance.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 收费状态枚举
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Getter
@AllArgsConstructor
public enum ChargeStatusEnum implements BaseEnum {

    /**
     * 待收费
     */
    PENDING(1, "待收费"),

    /**
     * 已收费
     */
    PAID(2, "已收费"),

    /**
     * 部分收费
     */
    PARTIAL_PAID(3, "部分收费"),

    /**
     * 已退费
     */
    REFUNDED(4, "已退费");

    private final Integer value;

    private final String desc;

    /**
     * 根据值获取枚举
     */
    public static ChargeStatusEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (ChargeStatusEnum statusEnum : values()) {
            if (statusEnum.getValue().equals(value)) {
                return statusEnum;
            }
        }
        return null;
    }
}

package net.lab1024.sa.admin.module.business.hospital.lead.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 线索统计实体类
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@TableName("t_lead_statistics")
public class LeadStatisticsEntity {

    /**
     * 统计ID
     */
    @TableId(type = IdType.AUTO)
    private Long statId;

    /**
     * 线索ID
     */
    private Long leadId;

    /**
     * 跟进次数
     */
    private Integer followCount;

    /**
     * 响应次数
     */
    private Integer responseCount;

    /**
     * 响应率（%）
     */
    private BigDecimal responseRate;

    /**
     * 预约次数
     */
    private Integer appointmentCount;

    /**
     * 最后沟通时间
     */
    private LocalDateTime lastCommunicationTime;

    /**
     * 首次跟进时间
     */
    private LocalDateTime firstFollowTime;

    /**
     * 平均响应时间（分钟）
     */
    private Integer avgResponseTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}

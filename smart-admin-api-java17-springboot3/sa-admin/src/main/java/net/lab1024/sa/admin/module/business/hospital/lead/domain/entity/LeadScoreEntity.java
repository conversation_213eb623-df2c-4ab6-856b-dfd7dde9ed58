package net.lab1024.sa.admin.module.business.hospital.lead.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 线索评分实体类
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@TableName("t_lead_score")
public class LeadScoreEntity {

    /**
     * 评分ID
     */
    @TableId(type = IdType.AUTO)
    private Long scoreId;

    /**
     * 线索ID
     */
    private Long leadId;

    /**
     * 总分（0-100）
     */
    private Integer totalScore;

    /**
     * 跟进频率得分
     */
    private Integer followScore;

    /**
     * 响应度得分
     */
    private Integer responseScore;

    /**
     * 意向度得分
     */
    private Integer intentionScore;

    /**
     * 转化概率（0-100）
     */
    private BigDecimal conversionProbability;

    /**
     * 最后计算时间
     */
    private LocalDateTime lastCalculateTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}

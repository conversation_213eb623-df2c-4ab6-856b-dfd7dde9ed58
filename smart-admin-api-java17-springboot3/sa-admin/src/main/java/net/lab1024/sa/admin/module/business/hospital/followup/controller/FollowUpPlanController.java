package net.lab1024.sa.admin.module.business.hospital.followup.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.constant.AdminSwaggerTagConst;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.form.FollowUpPlanAddForm;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.form.FollowUpPlanQueryForm;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.form.FollowUpPlanUpdateForm;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.vo.FollowUpPlanVO;
import net.lab1024.sa.admin.module.business.hospital.followup.service.FollowUpPlanService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartRequestUtil;
import net.lab1024.sa.base.module.support.operatelog.annotation.OperateLog;
import net.lab1024.sa.base.module.support.repeatsubmit.annoation.RepeatSubmit;
import org.springframework.web.bind.annotation.*;

/**
 * 回访计划管理Controller
 *
 * <AUTHOR>
 * @Date 2025-07-30 00:00:00
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Tag(name = AdminSwaggerTagConst.Business.HOSPITAL_FOLLOW_UP)
@RestController
@RequestMapping("/api/followup/plan")
public class FollowUpPlanController {

    @Resource
    private FollowUpPlanService followUpPlanService;

    @Operation(summary = "分页查询回访计划")
    @PostMapping("/queryPage")
    @SaCheckPermission("hospital:followup:plan:query")
    public ResponseDTO<PageResult<FollowUpPlanVO>> queryPage(@RequestBody @Valid FollowUpPlanQueryForm queryForm) {
        return followUpPlanService.queryPage(queryForm);
    }

    @Operation(summary = "添加回访计划")
    @PostMapping("/add")
    @SaCheckPermission("hospital:followup:plan:add")
    @RepeatSubmit
    @OperateLog
    public ResponseDTO<String> add(@RequestBody @Valid FollowUpPlanAddForm addForm) {
        return followUpPlanService.add(addForm);
    }

    @Operation(summary = "更新回访计划")
    @PostMapping("/update")
    @SaCheckPermission("hospital:followup:plan:update")
    @RepeatSubmit
    @OperateLog
    public ResponseDTO<String> update(@RequestBody @Valid FollowUpPlanUpdateForm updateForm) {
        return followUpPlanService.update(updateForm);
    }

    @Operation(summary = "删除回访计划")
    @GetMapping("/delete/{planId}")
    @SaCheckPermission("hospital:followup:plan:delete")
    @OperateLog
    public ResponseDTO<String> delete(@PathVariable Long planId) {
        return followUpPlanService.delete(planId);
    }

    @Operation(summary = "获取回访计划详情")
    @GetMapping("/detail/{planId}")
    @SaCheckPermission("hospital:followup:plan:query")
    public ResponseDTO<FollowUpPlanVO> detail(@PathVariable Long planId) {
        return followUpPlanService.detail(planId);
    }

    @Operation(summary = "启动回访计划")
    @PostMapping("/start/{planId}")
    @SaCheckPermission("hospital:followup:plan:update")
    @OperateLog
    public ResponseDTO<String> start(@PathVariable Long planId) {
        return followUpPlanService.start(planId);
    }

    @Operation(summary = "暂停回访计划")
    @PostMapping("/pause/{planId}")
    @SaCheckPermission("hospital:followup:plan:update")
    @OperateLog
    public ResponseDTO<String> pause(@PathVariable Long planId) {
        return followUpPlanService.pause(planId);
    }

    @Operation(summary = "执行回访计划")
    @PostMapping("/execute/{planId}")
    @SaCheckPermission("hospital:followup:plan:execute")
    @OperateLog
    public ResponseDTO<String> execute(@PathVariable Long planId) {
        return followUpPlanService.execute(planId);
    }

    @Operation(summary = "获取计划统计数据")
    @PostMapping("/statistics")
    @SaCheckPermission("hospital:followup:plan:query")
    public ResponseDTO<Object> getStatistics(@RequestBody @Valid FollowUpPlanQueryForm queryForm) {
        return followUpPlanService.getStatistics(queryForm);
    }
}

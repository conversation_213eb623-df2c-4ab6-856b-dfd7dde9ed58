package net.lab1024.sa.admin.module.business.hospital.visit.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 诊断记录实体类
 *
 * <AUTHOR>
 * @Date 2025-07-29
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@TableName("t_diagnosis")
public class DiagnosisEntity {

    /**
     * 诊断ID
     */
    @TableId(type = IdType.AUTO)
    private Long diagnosisId;

    /**
     * 患者ID
     */
    private Long patientId;

    /**
     * 患者编号
     */
    private String patientNo;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 诊断结果
     */
    private String diagnosisResult;

    /**
     * 诊断类型：1-初诊，2-复诊，3-会诊
     */
    private Integer diagnosisType;

    /**
     * 诊断说明
     */
    private String diagnosisDescription;

    /**
     * 诊断医生ID
     */
    private Long diagnosisDoctorId;

    /**
     * 诊断医生姓名
     */
    private String diagnosisDoctorName;

    /**
     * 诊断时间
     */
    private LocalDateTime diagnosisTime;

    /**
     * 诊断状态：1-已诊断，2-已开单，3-已收费，4-已完成
     */
    private Integer diagnosisStatus;

    /**
     * 创建人ID
     */
    private Long createUserId;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 更新人ID
     */
    private Long updateUserId;

    /**
     * 更新人姓名
     */
    private String updateUserName;

    /**
     * 删除标识：0-未删除，1-已删除
     */
    private Integer deletedFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}

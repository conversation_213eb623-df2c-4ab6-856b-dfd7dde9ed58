package net.lab1024.sa.admin.module.business.hospital.visit.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.form.VisitFollowAddForm;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.form.VisitFollowQueryForm;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.VisitFollowVO;
import net.lab1024.sa.admin.module.business.hospital.visit.service.VisitFollowService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.admin.util.AdminRequestUtil;
import net.lab1024.sa.base.module.support.operatelog.annotation.OperateLog;
import net.lab1024.sa.base.module.support.repeatsubmit.annoation.RepeatSubmit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 到诊患者跟进控制器
 *
 * <AUTHOR>
 * @Date 2025-07-30 00:00:00
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Tag(name = "到诊患者跟进管理")
@RestController
@RequestMapping("/api/visit/follow")
public class VisitFollowController {

    @Autowired
    private VisitFollowService visitFollowService;

    @Operation(summary = "分页查询患者跟进记录")
    @PostMapping("/queryPage")
    @SaCheckPermission("hospital:visit:follow:query")
    public ResponseDTO<PageResult<VisitFollowVO>> queryPage(@RequestBody @Valid VisitFollowQueryForm queryForm) {
        return ResponseDTO.ok(visitFollowService.queryPage(queryForm));
    }

    @Operation(summary = "根据患者ID查询跟进记录")
    @GetMapping("/patient/{patientId}")
    @SaCheckPermission("hospital:visit:follow:query")
    public ResponseDTO<List<VisitFollowVO>> getByPatientId(@PathVariable Long patientId) {
        return visitFollowService.getByPatientId(patientId);
    }

    @Operation(summary = "新增患者跟进记录")
    @PostMapping("/add")
    @SaCheckPermission("hospital:visit:follow:add")
    @RepeatSubmit
    @OperateLog
    public ResponseDTO<String> add(@RequestBody @Valid VisitFollowAddForm addForm) {
        Long operateUserId = AdminRequestUtil.getRequestUserId();
        String operateUserName = AdminRequestUtil.getRequestUser().getUserName();
        return visitFollowService.add(addForm, operateUserId, operateUserName);
    }

    @Operation(summary = "自动创建患者跟进计划")
    @PostMapping("/createPlan/{patientId}")
    @SaCheckPermission("hospital:visit:follow:add")
    @RepeatSubmit
    @OperateLog
    public ResponseDTO<String> createFollowUpPlan(@PathVariable Long patientId) {
        Long operateUserId = AdminRequestUtil.getRequestUserId();
        String operateUserName = AdminRequestUtil.getRequestUser().getUserName();
        return visitFollowService.createFollowUpPlan(patientId, operateUserId, operateUserName);
    }
}

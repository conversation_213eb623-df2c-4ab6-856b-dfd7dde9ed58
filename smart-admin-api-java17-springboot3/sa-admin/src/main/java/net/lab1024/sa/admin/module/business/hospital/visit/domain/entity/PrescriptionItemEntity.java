package net.lab1024.sa.admin.module.business.hospital.visit.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 开单项目明细实体类
 *
 * <AUTHOR>
 * @Date 2025-07-29
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@TableName("t_prescription_item")
public class PrescriptionItemEntity {

    /**
     * 项目ID
     */
    @TableId(type = IdType.AUTO)
    private Long itemId;

    /**
     * 开单ID
     */
    private Long prescriptionId;

    /**
     * 项目名称
     */
    private String itemName;

    /**
     * 项目类型：treatment-治疗，medicine-药品，examination-检查，material-材料
     */
    private String itemType;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 小计
     */
    private BigDecimal subtotal;

    /**
     * 项目说明
     */
    private String itemDescription;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}

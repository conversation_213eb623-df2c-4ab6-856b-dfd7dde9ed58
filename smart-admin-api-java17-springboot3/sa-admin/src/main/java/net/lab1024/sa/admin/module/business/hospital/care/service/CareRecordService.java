package net.lab1024.sa.admin.module.business.hospital.care.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.hospital.care.constant.CareMethodEnum;
import net.lab1024.sa.admin.module.business.hospital.care.constant.CareStatusEnum;
import net.lab1024.sa.admin.module.business.hospital.care.constant.CareTypeEnum;
import net.lab1024.sa.admin.module.business.hospital.care.dao.CareRecordDao;
import net.lab1024.sa.admin.module.business.hospital.care.domain.entity.CareRecordEntity;
import net.lab1024.sa.admin.module.business.hospital.care.domain.form.CareRecordAddForm;
import net.lab1024.sa.admin.module.business.hospital.care.domain.form.CareRecordQueryForm;
import net.lab1024.sa.admin.module.business.hospital.care.domain.form.CareRecordUpdateForm;
import net.lab1024.sa.admin.module.business.hospital.care.domain.vo.CareRecordVO;
import net.lab1024.sa.admin.module.business.hospital.customer.dao.CustomerDao;
import net.lab1024.sa.admin.module.business.hospital.customer.domain.entity.CustomerEntity;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.module.support.datatracer.constant.DataTracerTypeEnum;
import net.lab1024.sa.base.module.support.datatracer.service.DataTracerService;
import net.lab1024.sa.base.module.support.operatelog.annotation.OperateLog;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 关怀记录Service
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Slf4j
@Service
public class CareRecordService {

    @Resource
    private CareRecordDao careRecordDao;

    @Resource
    private CustomerDao customerDao;

    @Resource
    private DataTracerService dataTracerService;

    /**
     * 分页查询关怀记录
     */
    public ResponseDTO<PageResult<CareRecordVO>> queryPage(CareRecordQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<CareRecordVO> list = careRecordDao.queryPage(page, queryForm);
        
        // 设置枚举显示名称
        list.forEach(this::setEnumDisplayNames);
        
        PageResult<CareRecordVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return ResponseDTO.ok(pageResult);
    }

    /**
     * 添加关怀记录
     */
    @Transactional(rollbackFor = Exception.class)
    @OperateLog
    public ResponseDTO<String> add(CareRecordAddForm addForm) {
        // 验证客户是否存在
        if (addForm.getCustomerId() != null) {
            CustomerEntity customer = customerDao.selectById(addForm.getCustomerId());
            if (customer == null) {
                return ResponseDTO.userErrorParam("客户不存在");
            }
        }

        CareRecordEntity careRecordEntity = SmartBeanUtil.copy(addForm, CareRecordEntity.class);
        
        // 设置默认值
        if (careRecordEntity.getExecuteStatus() == null) {
            careRecordEntity.setExecuteStatus(CareStatusEnum.PENDING.getValue());
        }
        if (careRecordEntity.getPlannedTime() == null) {
            careRecordEntity.setPlannedTime(LocalDateTime.now());
        }
        if (careRecordEntity.getNeedFollowUp() == null) {
            careRecordEntity.setNeedFollowUp(false);
        }
        if (careRecordEntity.getDeletedFlag() == null) {
            careRecordEntity.setDeletedFlag(false);
        }

        careRecordDao.insert(careRecordEntity);

        // 记录数据变动
        dataTracerService.insert(careRecordEntity.getCareId(), DataTracerTypeEnum.CARE_RECORD);

        return ResponseDTO.ok();
    }

    /**
     * 更新关怀记录
     */
    @Transactional(rollbackFor = Exception.class)
    @OperateLog
    public ResponseDTO<String> update(CareRecordUpdateForm updateForm) {
        CareRecordEntity oldEntity = careRecordDao.selectById(updateForm.getCareId());
        if (oldEntity == null) {
            return ResponseDTO.userErrorParam("关怀记录不存在");
        }

        CareRecordEntity careRecordEntity = SmartBeanUtil.copy(updateForm, CareRecordEntity.class);
        careRecordDao.updateById(careRecordEntity);

        // 记录数据变动
        dataTracerService.update(careRecordEntity.getCareId(), DataTracerTypeEnum.CARE_RECORD, oldEntity, careRecordEntity);

        return ResponseDTO.ok();
    }

    /**
     * 删除关怀记录
     */
    @Transactional(rollbackFor = Exception.class)
    @OperateLog
    public ResponseDTO<String> delete(Long careId) {
        CareRecordEntity careRecordEntity = careRecordDao.selectById(careId);
        if (careRecordEntity == null) {
            return ResponseDTO.userErrorParam("关怀记录不存在");
        }

        careRecordEntity.setDeletedFlag(true);
        careRecordDao.updateById(careRecordEntity);

        // 记录数据变动
        dataTracerService.delete(careId, DataTracerTypeEnum.CARE_RECORD);

        return ResponseDTO.ok();
    }

    /**
     * 根据客户ID查询关怀记录
     */
    public ResponseDTO<List<CareRecordVO>> getByCustomerId(Long customerId) {
        List<CareRecordVO> list = careRecordDao.selectByCustomerId(customerId);
        list.forEach(this::setEnumDisplayNames);
        return ResponseDTO.ok(list);
    }

    /**
     * 根据病历ID查询关怀记录
     */
    public ResponseDTO<List<CareRecordVO>> getByRecordId(Long recordId) {
        List<CareRecordEntity> entities = careRecordDao.selectByRecordId(recordId);
        List<CareRecordVO> list = SmartBeanUtil.copyList(entities, CareRecordVO.class);
        list.forEach(this::setEnumDisplayNames);
        return ResponseDTO.ok(list);
    }

    /**
     * 更新关怀状态
     */
    @Transactional(rollbackFor = Exception.class)
    @OperateLog
    public ResponseDTO<String> updateCareStatus(Long careId, Integer careStatus) {
        CareRecordEntity careRecord = careRecordDao.selectById(careId);
        if (careRecord == null) {
            return ResponseDTO.userErrorParam("关怀记录不存在");
        }

        LocalDateTime actualTime = null;
        if (CareStatusEnum.COMPLETED.getValue().equals(careStatus)) {
            actualTime = LocalDateTime.now();
        }

        careRecordDao.updateCareStatus(careId, careStatus, actualTime);

        // 记录数据变动
        CareRecordEntity updatedEntity = careRecordDao.selectById(careId);
        dataTracerService.update(careId, DataTracerTypeEnum.CARE_RECORD, careRecord, updatedEntity);

        return ResponseDTO.ok();
    }

    /**
     * 更新执行结果
     */
    @Transactional(rollbackFor = Exception.class)
    @OperateLog
    public ResponseDTO<String> updateExecutionResult(Long careId, String executionResult, 
                                                   String customerFeedback, Integer satisfactionScore) {
        CareRecordEntity careRecord = careRecordDao.selectById(careId);
        if (careRecord == null) {
            return ResponseDTO.userErrorParam("关怀记录不存在");
        }

        careRecordDao.updateExecutionResult(careId, executionResult, customerFeedback, satisfactionScore);

        // 记录数据变动
        CareRecordEntity updatedEntity = careRecordDao.selectById(careId);
        dataTracerService.update(careId, DataTracerTypeEnum.CARE_RECORD, careRecord, updatedEntity);

        return ResponseDTO.ok();
    }

    /**
     * 根据出院病历自动创建关怀记录
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> createCareFromDischarge(Long recordId) {
        try {
            int count = careRecordDao.createCareFromDischarge(recordId);
            if (count > 0) {
                log.info("为病历ID: {} 自动创建了 {} 条出院关怀记录", recordId, count);
                return ResponseDTO.ok("自动创建出院关怀记录成功");
            } else {
                return ResponseDTO.userErrorParam("该病历不符合创建出院关怀的条件或已存在关怀记录");
            }
        } catch (Exception e) {
            log.error("自动创建出院关怀记录失败: recordId={}", recordId, e);
            return ResponseDTO.userErrorParam("自动创建出院关怀记录失败");
        }
    }

    /**
     * 获取待执行的关怀记录
     */
    public ResponseDTO<List<CareRecordVO>> getPendingCareRecords() {
        List<CareRecordEntity> entities = careRecordDao.getPendingCareRecords(LocalDateTime.now());
        List<CareRecordVO> list = SmartBeanUtil.copyList(entities, CareRecordVO.class);
        list.forEach(this::setEnumDisplayNames);
        return ResponseDTO.ok(list);
    }

    /**
     * 获取需要跟进的关怀记录
     */
    public ResponseDTO<List<CareRecordVO>> getFollowUpCareRecords() {
        List<CareRecordEntity> entities = careRecordDao.getFollowUpCareRecords(LocalDateTime.now());
        List<CareRecordVO> list = SmartBeanUtil.copyList(entities, CareRecordVO.class);
        list.forEach(this::setEnumDisplayNames);
        return ResponseDTO.ok(list);
    }

    /**
     * 设置枚举显示名称
     */
    private void setEnumDisplayNames(CareRecordVO vo) {
        if (vo.getCareType() != null) {
            CareTypeEnum careType = CareTypeEnum.getByValue(vo.getCareType());
            if (careType != null) {
                vo.setCareTypeName(careType.getDesc());
            }
        }
        
        if (vo.getCareMethod() != null) {
            CareMethodEnum careMethod = CareMethodEnum.getByValue(vo.getCareMethod());
            if (careMethod != null) {
                vo.setCareMethodName(careMethod.getDesc());
            }
        }
        
        if (vo.getCareStatus() != null) {
            CareStatusEnum careStatus = CareStatusEnum.getByValue(vo.getCareStatus());
            if (careStatus != null) {
                vo.setCareStatusName(careStatus.getDesc());
            }
        }
    }
}

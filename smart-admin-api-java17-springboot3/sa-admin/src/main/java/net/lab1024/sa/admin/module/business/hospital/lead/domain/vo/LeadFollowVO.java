package net.lab1024.sa.admin.module.business.hospital.lead.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.admin.module.business.hospital.lead.constant.FollowResultEnum;
import net.lab1024.sa.admin.module.business.hospital.lead.constant.FollowTypeEnum;
import net.lab1024.sa.base.common.swagger.SchemaEnum;

import java.time.LocalDateTime;

/**
 * 线索跟进视图对象
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class LeadFollowVO {

    @Schema(description = "跟进记录ID")
    private Long followId;

    @Schema(description = "线索ID")
    private Long leadId;

    @Schema(description = "跟进员工ID")
    private Long employeeId;

    @Schema(description = "跟进员工姓名")
    private String employeeName;

    @Schema(description = "跟进方式：1-电话，2-微信，3-面谈，4-其他")
    @SchemaEnum(FollowTypeEnum.class)
    private Integer followType;

    @Schema(description = "跟进方式名称")
    private String followTypeName;

    @Schema(description = "跟进内容")
    private String followContent;

    @Schema(description = "跟进结果：1-有意向，2-无意向，3-需再次跟进")
    @SchemaEnum(FollowResultEnum.class)
    private Integer followResult;

    @Schema(description = "跟进结果名称")
    private String followResultName;

    @Schema(description = "下次跟进时间")
    private LocalDateTime nextFollowTime;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "客户姓名")
    private String customerName;

    @Schema(description = "客户电话")
    private String customerPhone;
}

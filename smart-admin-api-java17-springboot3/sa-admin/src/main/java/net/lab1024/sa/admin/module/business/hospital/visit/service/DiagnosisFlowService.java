package net.lab1024.sa.admin.module.business.hospital.visit.service;

import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.hospital.visit.dao.VisitPatientDao;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.entity.VisitPatientEntity;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.form.ChargeAddForm;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.form.DiagnosisAddForm;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.form.DiagnosisFlowForm;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.form.DiagnosisFlowCompleteForm;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.form.PrescriptionAddForm;
import net.lab1024.sa.admin.module.system.employee.dao.EmployeeDao;
import net.lab1024.sa.admin.module.system.employee.domain.entity.EmployeeEntity;
import net.lab1024.sa.base.common.domain.ResponseDTO;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 诊断流程服务
 *
 * <AUTHOR>
 * @Date 2025-07-29
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Slf4j
@Service
public class DiagnosisFlowService {

    @Autowired
    private DiagnosisService diagnosisService;

    @Autowired
    private PrescriptionService prescriptionService;

    @Autowired
    private ChargeService chargeService;

    @Autowired
    private VisitPatientDao visitPatientDao;

    @Autowired
    private EmployeeDao employeeDao;

    /**
     * 执行完整的诊断流程
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> executeFlow(DiagnosisFlowForm flowForm, Long operateUserId, String operateUserName) {
        try {
            // 1. 验证输入参数
            if (flowForm.getPatientId() == null) {
                return ResponseDTO.userErrorParam("患者ID不能为空");
            }
            if (flowForm.getAssignedAssistantId() == null) {
                return ResponseDTO.userErrorParam("分配助理ID不能为空");
            }

            // 2. 验证患者是否存在
            VisitPatientEntity patient = visitPatientDao.selectById(flowForm.getPatientId());
            if (patient == null) {
                return ResponseDTO.userErrorParam("患者不存在");
            }

            // 3. 验证分配助理是否存在
            EmployeeEntity assistant = employeeDao.selectById(flowForm.getAssignedAssistantId());
            if (assistant == null) {
                return ResponseDTO.userErrorParam("分配助理不存在");
            }

            // 4. 执行诊断步骤
            DiagnosisAddForm diagnosisForm = flowForm.getDiagnosisForm();
            if (diagnosisForm == null) {
                return ResponseDTO.userErrorParam("诊断信息不能为空");
            }
            // 确保设置患者ID
            diagnosisForm.setPatientId(flowForm.getPatientId());
            ResponseDTO<Long> diagnosisResult = diagnosisService.add(diagnosisForm, operateUserId, operateUserName);
            if (!diagnosisResult.getOk()) {
                return ResponseDTO.userErrorParam(diagnosisResult.getMsg());
            }
            Long diagnosisId = diagnosisResult.getData();

            // 5. 执行开单步骤
            PrescriptionAddForm prescriptionForm = flowForm.getPrescriptionForm();
            if (prescriptionForm == null) {
                return ResponseDTO.userErrorParam("开单信息不能为空");
            }
            // 确保设置必要的ID
            prescriptionForm.setDiagnosisId(diagnosisId);
            prescriptionForm.setPatientId(flowForm.getPatientId());
            ResponseDTO<Long> prescriptionResult = prescriptionService.add(prescriptionForm, operateUserId, operateUserName);
            if (!prescriptionResult.getOk()) {
                return ResponseDTO.userErrorParam(prescriptionResult.getMsg());
            }
            Long prescriptionId = prescriptionResult.getData();

            // 6. 执行收费步骤
            ChargeAddForm chargeForm = flowForm.getChargeForm();
            if (chargeForm == null) {
                return ResponseDTO.userErrorParam("收费信息不能为空");
            }
            // 确保设置必要的ID
            chargeForm.setPrescriptionId(prescriptionId);
            chargeForm.setDiagnosisId(diagnosisId);
            chargeForm.setPatientId(flowForm.getPatientId());
            ResponseDTO<String> chargeResult = chargeService.add(chargeForm, operateUserId, operateUserName);
            if (!chargeResult.getOk()) {
                return chargeResult;
            }

            // 7. 分配助理并更新患者状态
            ResponseDTO<String> assignResult = assignAssistant(flowForm.getPatientId(), flowForm.getAssignedAssistantId(),
                    flowForm.getAssignNote(), operateUserId, operateUserName);
            if (!assignResult.getOk()) {
                return assignResult;
            }

            log.info("诊断流程执行成功，患者ID：{}，操作人：{}", flowForm.getPatientId(), operateUserName);
            return ResponseDTO.ok("诊断流程执行成功");

        } catch (Exception e) {
            log.error("诊断流程执行失败，患者ID：{}，操作人：{}，错误信息：{}", flowForm.getPatientId(), operateUserName, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 完成诊断流程（仅分配助理）
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> completeFlow(DiagnosisFlowCompleteForm completeForm, Long operateUserId, String operateUserName) {
        try {
            // 1. 验证输入参数
            if (completeForm.getPatientId() == null) {
                return ResponseDTO.userErrorParam("患者ID不能为空");
            }
            if (completeForm.getDiagnosisId() == null) {
                return ResponseDTO.userErrorParam("诊断ID不能为空");
            }
            if (completeForm.getPrescriptionId() == null) {
                return ResponseDTO.userErrorParam("开单ID不能为空");
            }
            if (completeForm.getAssignedAssistantId() == null) {
                return ResponseDTO.userErrorParam("分配助理ID不能为空");
            }

            // 2. 验证患者是否存在
            VisitPatientEntity patient = visitPatientDao.selectById(completeForm.getPatientId());
            if (patient == null) {
                return ResponseDTO.userErrorParam("患者不存在");
            }

            // 3. 验证分配助理是否存在
            EmployeeEntity assistant = employeeDao.selectById(completeForm.getAssignedAssistantId());
            if (assistant == null) {
                return ResponseDTO.userErrorParam("分配助理不存在");
            }

            // 4. 分配助理并更新患者状态
            ResponseDTO<String> assignResult = assignAssistant(completeForm.getPatientId(),
                    completeForm.getAssignedAssistantId(), completeForm.getAssignNote(),
                    operateUserId, operateUserName);
            if (!assignResult.getOk()) {
                return assignResult;
            }

            log.info("诊断流程完成成功，患者ID：{}，操作人：{}", completeForm.getPatientId(), operateUserName);
            return ResponseDTO.ok("诊断流程完成成功");

        } catch (Exception e) {
            log.error("诊断流程完成失败，患者ID：{}，操作人：{}，错误信息：{}",
                    completeForm.getPatientId(), operateUserName, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 分配助理
     */
    @Transactional(rollbackFor = Exception.class)

    public ResponseDTO<String> assignAssistant(Long patientId, Long assistantId, String assignNote,
                                               Long operateUserId, String operateUserName) {
        // 1. 验证患者是否存在
        VisitPatientEntity patient = visitPatientDao.selectById(patientId);
        if (patient == null) {
            return ResponseDTO.userErrorParam("患者不存在");
        }

        // 2. 验证助理是否存在
        EmployeeEntity assistant = employeeDao.selectById(assistantId);
        if (assistant == null) {
            return ResponseDTO.userErrorParam("助理不存在");
        }

        // 3. 更新患者信息
        patient.setAssignedAssistantId(assistantId);
        patient.setAssignedAssistantName(assistant.getActualName());
        patient.setDiagnosisStatus(5); // 已分配助理
        patient.setVisitStatus(3); // 诊疗中
        patient.setUpdateUserId(operateUserId);
        patient.setUpdateTime(LocalDateTime.now());
        patient.setUpdateTime(LocalDateTime.now());

        visitPatientDao.updateById(patient);

        return ResponseDTO.ok();
    }




}

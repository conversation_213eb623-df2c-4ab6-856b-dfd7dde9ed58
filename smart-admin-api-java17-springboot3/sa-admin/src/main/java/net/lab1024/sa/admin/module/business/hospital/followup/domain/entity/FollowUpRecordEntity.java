package net.lab1024.sa.admin.module.business.hospital.followup.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 回访记录实体
 *
 * <AUTHOR>
 * @Date 2025-07-30 00:00:00
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@TableName("t_follow_up_record")
public class FollowUpRecordEntity {

    /**
     * 回访记录ID
     */
    @TableId(type = IdType.AUTO)
    private Long followUpId;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 客户姓名
     */
    private String customerName;

    /**
     * 客户电话
     */
    private String customerPhone;

    /**
     * 回访类型
     */
    private Integer followUpType;

    /**
     * 回访方式
     */
    private Integer followUpMethod;

    /**
     * 计划回访时间
     */
    private LocalDateTime scheduledTime;

    /**
     * 实际回访时间
     */
    private LocalDateTime actualTime;

    /**
     * 回访内容
     */
    private String followUpContent;

    /**
     * 回访结果
     */
    private String followUpResult;

    /**
     * 回访人员ID
     */
    private Long followUpUserId;

    /**
     * 回访人员姓名
     */
    private String followUpUserName;

    /**
     * 满意度评分
     */
    private Integer satisfactionScore;

    /**
     * 备注
     */
    private String remark;

    /**
     * 下次回访时间
     */
    private LocalDateTime nextFollowUpTime;

    /**
     * 优先级
     */
    private Integer priorityLevel;

    /**
     * 回访状态
     */
    private Integer followUpStatus;

    /**
     * 关联记录ID
     */
    private Long relatedRecordId;

    /**
     * 关联记录类型
     */
    private String relatedRecordType;

    /**
     * 删除标志
     */
    private Integer deletedFlag;

    /**
     * 创建人ID
     */
    private Long createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    private Long updateUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}

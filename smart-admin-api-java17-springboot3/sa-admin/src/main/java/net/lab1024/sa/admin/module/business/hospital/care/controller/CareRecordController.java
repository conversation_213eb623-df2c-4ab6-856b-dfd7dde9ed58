package net.lab1024.sa.admin.module.business.hospital.care.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.lab1024.sa.admin.constant.AdminSwaggerTagConst;
import net.lab1024.sa.admin.module.business.hospital.care.domain.form.CareRecordAddForm;
import net.lab1024.sa.admin.module.business.hospital.care.domain.form.CareRecordQueryForm;
import net.lab1024.sa.admin.module.business.hospital.care.domain.form.CareRecordUpdateForm;
import net.lab1024.sa.admin.module.business.hospital.care.domain.vo.CareRecordVO;
import net.lab1024.sa.admin.module.business.hospital.care.service.CareRecordService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.module.support.operatelog.annotation.OperateLog;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 关怀记录Controller
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Tag(name = AdminSwaggerTagConst.Business.HOSPITAL_CARE)
@RestController
@RequestMapping("/api/care-record")
public class CareRecordController {

    @Resource
    private CareRecordService careRecordService;

    @Operation(summary = "分页查询关怀记录")
    @PostMapping("/query")
    @SaCheckPermission("hospital:care:query")
    public ResponseDTO<PageResult<CareRecordVO>> queryPage(@RequestBody @Valid CareRecordQueryForm queryForm) {
        return careRecordService.queryPage(queryForm);
    }

    @Operation(summary = "添加关怀记录")
    @PostMapping("/add")
    @SaCheckPermission("hospital:care:add")
    @OperateLog
    public ResponseDTO<String> add(@RequestBody @Valid CareRecordAddForm addForm) {
        return careRecordService.add(addForm);
    }

    @Operation(summary = "更新关怀记录")
    @PostMapping("/update")
    @SaCheckPermission("hospital:care:update")
    @OperateLog
    public ResponseDTO<String> update(@RequestBody @Valid CareRecordUpdateForm updateForm) {
        return careRecordService.update(updateForm);
    }

    @Operation(summary = "删除关怀记录")
    @GetMapping("/delete/{careId}")
    @SaCheckPermission("hospital:care:delete")
    @OperateLog
    public ResponseDTO<String> delete(@PathVariable Long careId) {
        return careRecordService.delete(careId);
    }

    @Operation(summary = "根据客户ID查询关怀记录")
    @GetMapping("/customer/{customerId}")
    @SaCheckPermission("hospital:care:query")
    public ResponseDTO<List<CareRecordVO>> getByCustomerId(@PathVariable Long customerId) {
        return careRecordService.getByCustomerId(customerId);
    }

    @Operation(summary = "根据病历ID查询关怀记录")
    @GetMapping("/record/{recordId}")
    @SaCheckPermission("hospital:care:query")
    public ResponseDTO<List<CareRecordVO>> getByRecordId(@PathVariable Long recordId) {
        return careRecordService.getByRecordId(recordId);
    }

    @Operation(summary = "更新关怀状态")
    @PostMapping("/update-status/{careId}")
    @SaCheckPermission("hospital:care:update")
    @OperateLog
    public ResponseDTO<String> updateCareStatus(@PathVariable Long careId,
                                               @RequestParam Integer careStatus) {
        return careRecordService.updateCareStatus(careId, careStatus);
    }

    @Operation(summary = "更新执行结果")
    @PostMapping("/update-result/{careId}")
    @SaCheckPermission("hospital:care:update")
    @OperateLog
    public ResponseDTO<String> updateExecutionResult(@PathVariable Long careId,
                                                    @RequestParam(required = false) String executionResult,
                                                    @RequestParam(required = false) String customerFeedback,
                                                    @RequestParam(required = false) Integer satisfactionScore) {
        return careRecordService.updateExecutionResult(careId, executionResult, customerFeedback, satisfactionScore);
    }

    @Operation(summary = "根据出院病历自动创建关怀记录")
    @PostMapping("/create-from-discharge/{recordId}")
    @SaCheckPermission("hospital:care:add")
    @OperateLog
    public ResponseDTO<String> createCareFromDischarge(@PathVariable Long recordId) {
        return careRecordService.createCareFromDischarge(recordId);
    }

    @Operation(summary = "获取待执行的关怀记录")
    @GetMapping("/pending")
    @SaCheckPermission("hospital:care:query")
    public ResponseDTO<List<CareRecordVO>> getPendingCareRecords() {
        return careRecordService.getPendingCareRecords();
    }

    @Operation(summary = "获取需要跟进的关怀记录")
    @GetMapping("/follow-up")
    @SaCheckPermission("hospital:care:query")
    public ResponseDTO<List<CareRecordVO>> getFollowUpCareRecords() {
        return careRecordService.getFollowUpCareRecords();
    }
}

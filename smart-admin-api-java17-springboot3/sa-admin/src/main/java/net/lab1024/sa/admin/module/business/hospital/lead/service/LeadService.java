package net.lab1024.sa.admin.module.business.hospital.lead.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.hospital.lead.constant.LeadQualityEnum;
import net.lab1024.sa.admin.module.business.hospital.lead.constant.LeadStatusEnum;
import net.lab1024.sa.admin.module.business.hospital.lead.dao.LeadDao;
import net.lab1024.sa.admin.module.business.hospital.lead.dao.LeadFollowDao;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.entity.LeadEntity;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.form.*;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadExcelVO;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadVO;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadDuplicateCheckVO;
import net.lab1024.sa.admin.module.business.hospital.lead.strategy.LeadDataScopeStrategy;
import net.lab1024.sa.admin.module.system.datascope.DataScope;
import net.lab1024.sa.admin.module.system.datascope.constant.DataScopeTypeEnum;
import net.lab1024.sa.admin.module.system.datascope.constant.DataScopeWhereInTypeEnum;
import net.lab1024.sa.admin.module.system.datascope.constant.DataScopeViewTypeEnum;
import net.lab1024.sa.admin.module.system.datascope.service.DataScopeViewService;
import net.lab1024.sa.admin.module.system.employee.dao.EmployeeDao;
import net.lab1024.sa.admin.module.system.employee.domain.entity.EmployeeEntity;
import net.lab1024.sa.admin.module.system.employee.domain.vo.EmployeeVO;
import net.lab1024.sa.admin.util.AdminRequestUtil;
import net.lab1024.sa.base.common.domain.RequestUser;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartEnumUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.module.support.datatracer.constant.DataTracerTypeEnum;
import net.lab1024.sa.base.module.support.datatracer.service.DataTracerService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;

/**
 * 线索Service
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Slf4j
@Service
public class LeadService {

    @Resource
    private LeadDao leadDao;

    @Resource
    private LeadFollowDao leadFollowDao;

    @Resource
    private EmployeeDao employeeDao;

    @Resource
    private DataTracerService dataTracerService;

    @Resource
    private DataScopeViewService dataScopeViewService;

    /**
     * 分页查询线索
     */
    public ResponseDTO<PageResult<LeadVO>> queryPage(LeadQueryForm queryForm) {
        queryForm.setDeletedFlag(Boolean.FALSE);

        // 获取当前用户信息
        RequestUser requestUser = AdminRequestUtil.getRequestUser();
        if (requestUser == null) {
            return ResponseDTO.userErrorParam("用户未登录");
        }

        Long currentUserId = requestUser.getUserId();
        queryForm.setCurrentUserId(currentUserId);

        // 手动实现权限过滤
        applyDataScopeFilter(queryForm, currentUserId);

        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<LeadVO> leadList = leadDao.queryPage(page, queryForm);
        PageResult<LeadVO> pageResult = SmartPageUtil.convert2PageResult(page, leadList);
        return ResponseDTO.ok(pageResult);
    }

    /**
     * 添加线索
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> add(LeadAddForm addForm) {
        // 注意：不再在后端校验手机号重复，重复检测和申请转移逻辑由前端处理
        // 这样可以支持通过申请转移流程添加重复手机号的线索

        LeadEntity leadEntity = SmartBeanUtil.copy(addForm, LeadEntity.class);
        leadEntity.setDeletedFlag(Boolean.FALSE);
        leadEntity.setCreateTime(LocalDateTime.now());
        leadEntity.setUpdateTime(LocalDateTime.now());
        leadEntity.setCreateUserId(AdminRequestUtil.getRequestUserId());
        leadEntity.setUpdateUserId(AdminRequestUtil.getRequestUserId());

        // 如果没有设置状态，默认为新线索
        if (leadEntity.getLeadStatus() == null) {
            leadEntity.setLeadStatus(LeadStatusEnum.NEW.getValue());
        }

        leadDao.insert(leadEntity);

        // 记录数据变动
        dataTracerService.insert(leadEntity.getLeadId(), DataTracerTypeEnum.LEAD);

        return ResponseDTO.ok();
    }

    /**
     * 更新线索
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> update(LeadUpdateForm updateForm) {
        // 校验线索是否存在
        LeadEntity existLead = leadDao.selectById(updateForm.getLeadId());
        if (existLead == null || existLead.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("线索不存在");
        }

        // 校验手机号是否重复
        ResponseDTO<String> checkResult = this.checkPhoneDuplicate(updateForm.getCustomerPhone(), updateForm.getLeadId());
        if (!checkResult.getOk()) {
            return checkResult;
        }

        LeadEntity leadEntity = SmartBeanUtil.copy(updateForm, LeadEntity.class);
        leadEntity.setUpdateTime(LocalDateTime.now());
        leadEntity.setUpdateUserId(AdminRequestUtil.getRequestUserId());
        // 获取原实体用于数据变动记录
        LeadEntity originEntity = leadDao.selectById(leadEntity.getLeadId());

        leadDao.updateById(leadEntity);

        // 记录数据变动
        dataTracerService.update(leadEntity.getLeadId(), DataTracerTypeEnum.LEAD, originEntity, leadEntity);
        
        return ResponseDTO.ok();
    }

    /**
     * 删除线索
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> delete(Long leadId) {
        LeadEntity leadEntity = leadDao.selectById(leadId);
        if (leadEntity == null || leadEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("线索不存在");
        }

        leadEntity.setDeletedFlag(Boolean.TRUE);
        leadEntity.setUpdateTime(LocalDateTime.now());
        leadEntity.setUpdateUserId(AdminRequestUtil.getRequestUserId());
        leadDao.updateById(leadEntity);
        
        // 同时删除跟进记录
        leadFollowDao.batchDeleteByLeadIds(List.of(leadId));
        
        // 记录数据变动
        dataTracerService.delete(leadId, DataTracerTypeEnum.LEAD);
        
        return ResponseDTO.ok();
    }

    /**
     * 批量删除线索
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> batchDelete(List<Long> leadIdList) {
        if (leadIdList == null || leadIdList.isEmpty()) {
            return ResponseDTO.userErrorParam("线索ID列表不能为空");
        }

        leadDao.batchUpdateDeleted(leadIdList, Boolean.TRUE);
        leadFollowDao.batchDeleteByLeadIds(leadIdList);
        
        // 记录数据变动
        for (Long leadId : leadIdList) {
            dataTracerService.delete(leadId, DataTracerTypeEnum.LEAD);
        }
        
        return ResponseDTO.ok();
    }

    /**
     * 分配线索
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> assign(LeadAssignForm assignForm) {
        if (assignForm.getLeadIdList() == null || assignForm.getLeadIdList().isEmpty()) {
            return ResponseDTO.userErrorParam("线索ID列表不能为空");
        }

        Long currentUserId = AdminRequestUtil.getRequestUserId();

        // 检查用户是否有分配线索的权限
        if (!canAssignLead(currentUserId)) {
            return ResponseDTO.userErrorParam("您没有分配线索的权限");
        }

        // 检查被分配员工是否为当前用户的下属或自己
        if (!isSubordinate(currentUserId, assignForm.getAssignedEmployeeId())) {
            return ResponseDTO.userErrorParam("您只能将线索分配给自己或您的直接下属员工");
        }

        // 权限验证：检查当前用户是否有权限分配这些线索
        for (Long leadId : assignForm.getLeadIdList()) {
            if (!hasLeadPermissionInternal(leadId, currentUserId)) {
                return ResponseDTO.userErrorParam("无权限分配线索ID: " + leadId);
            }
        }

        leadDao.batchAssign(assignForm.getLeadIdList(), assignForm.getAssignedEmployeeId());
        
        // 记录数据变动
        for (Long leadId : assignForm.getLeadIdList()) {
            LeadEntity originEntity = leadDao.selectById(leadId);
            LeadEntity updatedEntity = leadDao.selectById(leadId);
            dataTracerService.update(leadId, DataTracerTypeEnum.LEAD, originEntity, updatedEntity);
        }
        
        return ResponseDTO.ok();
    }

    /**
     * 线索转客户
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> convertToCustomer(Long leadId) {
        LeadEntity leadEntity = leadDao.selectById(leadId);
        if (leadEntity == null || leadEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("线索不存在");
        }

        // 更新线索状态为已转化
        leadDao.updateStatus(leadId, LeadStatusEnum.CONVERTED.getValue());
        
        // TODO: 这里应该调用客户管理服务创建客户记录
        // customerService.createFromLead(leadEntity);
        
        // 获取原实体和更新后的实体用于数据变动记录
        LeadEntity originEntity = leadEntity;
        LeadEntity updatedEntity = leadDao.selectById(leadId);

        // 记录数据变动
        dataTracerService.update(leadId, DataTracerTypeEnum.LEAD, originEntity, updatedEntity);
        
        return ResponseDTO.ok();
    }

    /**
     * 获取导出数据
     */
    public List<LeadExcelVO> getExcelExportData(LeadQueryForm queryForm) {
        queryForm.setDeletedFlag(Boolean.FALSE);
        return leadDao.selectExcelExportData(queryForm);
    }

    /**
     * 校验手机号重复
     */
    private ResponseDTO<String> checkPhoneDuplicate(String customerPhone, Long excludeLeadId) {
        if (StringUtils.isBlank(customerPhone)) {
            return ResponseDTO.ok();
        }

        LeadEntity existLead = leadDao.selectByPhone(customerPhone, excludeLeadId);
        if (existLead != null) {
            return ResponseDTO.userErrorParam("手机号已存在，请检查是否重复录入");
        }

        return ResponseDTO.ok();
    }

    /**
     * 检查线索重复并返回详细信息
     */
    public ResponseDTO<LeadDuplicateCheckVO> checkLeadDuplicate(String customerPhone, Long excludeLeadId) {
        if (StringUtils.isBlank(customerPhone)) {
            LeadDuplicateCheckVO result = new LeadDuplicateCheckVO();
            result.setIsDuplicate(false);
            return ResponseDTO.ok(result);
        }

        LeadDuplicateCheckVO duplicateInfo = leadDao.selectDuplicateCheckByPhone(customerPhone, excludeLeadId);
        if (duplicateInfo == null) {
            LeadDuplicateCheckVO result = new LeadDuplicateCheckVO();
            result.setIsDuplicate(false);
            return ResponseDTO.ok(result);
        }

        return ResponseDTO.ok(duplicateInfo);
    }

    /**
     * 获取我的线索统计
     */
    public ResponseDTO<Integer> getMyLeadCount() {
        Long employeeId = AdminRequestUtil.getRequestUserId();
        Integer count = leadDao.countByEmployeeId(employeeId);
        return ResponseDTO.ok(count);
    }

    /**
     * 获取待跟进线索
     */
    public ResponseDTO<List<LeadVO>> getPendingFollowUp() {
        Long employeeId = AdminRequestUtil.getRequestUserId();
        List<LeadVO> leadList = leadDao.selectPendingFollowUp(employeeId);
        return ResponseDTO.ok(leadList);
    }

    /**
     * 获取线索详情
     */
    public ResponseDTO<LeadVO> getDetail(Long leadId) {
        if (leadId == null) {
            return ResponseDTO.userErrorParam("线索ID不能为空");
        }

        // 权限验证
        Long currentUserId = AdminRequestUtil.getRequestUserId();
        if (!hasLeadPermissionInternal(leadId, currentUserId)) {
            return ResponseDTO.userErrorParam("无权限查看该线索");
        }

        LeadEntity leadEntity = leadDao.selectById(leadId);
        if (leadEntity == null || leadEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("线索不存在");
        }

        LeadVO leadVO = SmartBeanUtil.copy(leadEntity, LeadVO.class);

        // 设置线索来源名称
        if (StringUtils.isNotBlank(leadVO.getLeadSource())) {
            leadVO.setLeadSourceName(convertLeadSourceToName(leadVO.getLeadSource()));
        }

        // 设置性别名称
        if (leadVO.getGender() != null) {
            switch (leadVO.getGender()) {
                case 1:
                    leadVO.setGenderName("男");
                    break;
                case 2:
                    leadVO.setGenderName("女");
                    break;
                default:
                    leadVO.setGenderName("未知");
                    break;
            }
        }

        // 设置线索状态名称
        if (leadVO.getLeadStatus() != null) {
            LeadStatusEnum statusEnum = SmartEnumUtil.getEnumByValue(leadVO.getLeadStatus(), LeadStatusEnum.class);
            if (statusEnum != null) {
                leadVO.setLeadStatusName(statusEnum.getDesc());
            }
        }

        // 设置线索质量名称
        if (leadVO.getLeadQuality() != null) {
            LeadQualityEnum qualityEnum = SmartEnumUtil.getEnumByValue(leadVO.getLeadQuality(), LeadQualityEnum.class);
            if (qualityEnum != null) {
                leadVO.setLeadQualityName(qualityEnum.getDesc());
            }
        }

        // 设置分配员工名称
        if (leadVO.getAssignedEmployeeId() != null) {
            EmployeeEntity employee = employeeDao.selectById(leadVO.getAssignedEmployeeId());
            if (employee != null) {
                leadVO.setAssignedEmployeeName(employee.getActualName());
            }
        }

        return ResponseDTO.ok(leadVO);
    }

    /**
     * 将线索来源字典值转换为中文名称
     */
    private String convertLeadSourceToName(String leadSourceValue) {
        if (StringUtils.isBlank(leadSourceValue)) {
            return "";
        }

        // 根据字典值映射到中文名称
        switch (leadSourceValue.trim()) {
            case "BAIDU":
                return "百度";
            case "DOUYIN":
                return "抖音";
            case "KUAISHOU":
                return "快手";
            case "SHANGWUTONG":
                return "商务通";
            case "WECHAT":
                return "微信推广";
            case "FRIEND_REFERRAL":
                return "朋友介绍";
            case "PHONE_INQUIRY":
                return "电话咨询";
            case "ONLINE_PROMOTION":
                return "网络推广";
            case "OTHER":
                return "其他";
            default:
                // 如果是中文名称本身，直接返回
                return leadSourceValue;
        }
    }

    /**
     * 应用数据权限过滤
     */
    private void applyDataScopeFilter(LeadQueryForm queryForm, Long currentUserId) {
        log.debug("开始应用线索数据权限过滤，当前用户ID: {}", currentUserId);

        // 获取当前用户信息
        EmployeeEntity currentEmployee = employeeDao.selectById(currentUserId);
        if (currentEmployee == null) {
            log.warn("用户不存在，用户ID: {}", currentUserId);
            // 如果用户不存在，设置一个不可能的条件，确保查不到任何数据
            queryForm.setDataScopeEmployeeIds(List.of(-1L));
            return;
        }

        log.debug("当前用户信息: 姓名={}, 部门ID={}, 是否管理员={}",
                 currentEmployee.getActualName(), currentEmployee.getDepartmentId(), currentEmployee.getAdministratorFlag());

        // 获取数据权限类型，优先使用前端传递的dataScopeType
        DataScopeViewTypeEnum viewType;
        if (queryForm.getDataScopeType() != null) {
            try {
                viewType = DataScopeViewTypeEnum.valueOf(queryForm.getDataScopeType());
                log.debug("使用前端指定的数据权限类型: {}", viewType);
            } catch (IllegalArgumentException e) {
                log.warn("无效的数据权限类型: {}, 使用默认权限", queryForm.getDataScopeType());
                viewType = DataScopeViewTypeEnum.ME;
            }
        } else {
            // 超级管理员默认可以查看所有线索
            if (currentEmployee.getAdministratorFlag()) {
                log.debug("超级管理员，默认可以查看所有线索");
                return;
            }

            // 获取用户的数据权限级别
            try {
                viewType = dataScopeViewService.getEmployeeDataScopeViewType(DataScopeTypeEnum.LEAD, currentUserId);
                log.debug("用户数据权限级别: {}", viewType);

                // 如果没有配置数据权限，默认为ME权限
                if (viewType == null) {
                    log.debug("没有配置数据权限，默认为ME权限");
                    viewType = DataScopeViewTypeEnum.ME;
                }
            } catch (Exception e) {
                log.warn("获取权限级别失败，可能是用户没有配置数据权限，默认为ME权限。用户ID: {}, 错误: {}", currentUserId, e.getMessage());
                viewType = DataScopeViewTypeEnum.ME;
            }
        }

        // 超级管理员在指定ALL权限时可以查看所有线索
        if (currentEmployee.getAdministratorFlag() && viewType == DataScopeViewTypeEnum.ALL) {
            log.debug("超级管理员指定ALL权限，可以查看所有线索");
            return;
        }

        List<Long> allowedEmployeeIds = new ArrayList<>();

        // 根据筛选类型设置不同的权限逻辑
        String filterType = queryForm.getFilterType();

        switch (viewType) {
            case ALL:
                // 全部权限，不设置过滤条件
                log.debug("ALL权限，可以查看所有线索");
                return;
            case DEPARTMENT_AND_SUB:
                // 本部门及下属部门
                List<Long> departmentAndSubEmployeeIds = dataScopeViewService.getCanViewEmployeeId(viewType, currentUserId);
                log.debug("DEPARTMENT_AND_SUB权限，可查看员工IDs: {}", departmentAndSubEmployeeIds);
                if (CollectionUtils.isNotEmpty(departmentAndSubEmployeeIds)) {
                    // 如果是"下属线索"筛选，排除当前用户
                    if ("subordinate".equals(filterType)) {
                        departmentAndSubEmployeeIds.remove(currentUserId);
                        log.debug("下属线索筛选，排除当前用户后的员工IDs: {}", departmentAndSubEmployeeIds);
                    }
                    allowedEmployeeIds.addAll(departmentAndSubEmployeeIds);
                }
                break;
            case DEPARTMENT:
                // 本部门
                List<Long> departmentEmployeeIds = dataScopeViewService.getCanViewEmployeeId(viewType, currentUserId);
                log.debug("DEPARTMENT权限，可查看员工IDs: {}", departmentEmployeeIds);
                if (CollectionUtils.isNotEmpty(departmentEmployeeIds)) {
                    // 如果是"下属线索"筛选，排除当前用户
                    if ("subordinate".equals(filterType)) {
                        departmentEmployeeIds.remove(currentUserId);
                        log.debug("下属线索筛选，排除当前用户后的员工IDs: {}", departmentEmployeeIds);
                    }
                    allowedEmployeeIds.addAll(departmentEmployeeIds);
                }
                break;
            case ME:
            default:
                // 仅本人：可以查看自己创建的或分配给自己的线索
                log.debug("ME权限，仅可查看本人数据");
                allowedEmployeeIds.add(currentUserId);
                break;
        }

        // 如果没有允许的员工ID，设置一个不可能的条件
        if (allowedEmployeeIds.isEmpty()) {
            log.warn("没有允许的员工ID，设置不可能的条件");
            allowedEmployeeIds.add(-1L);
        }

        log.debug("最终设置的数据权限过滤员工IDs: {}", allowedEmployeeIds);

        // 设置数据权限过滤条件和筛选类型
        queryForm.setDataScopeEmployeeIds(allowedEmployeeIds);
        queryForm.setFilterScope(filterType); // 新增字段，用于区分筛选范围
    }

    /**
     * 检查用户是否有权限访问指定线索（公共方法）
     */
    public boolean hasLeadPermission(Long leadId, Long currentUserId) {
        return hasLeadPermissionInternal(leadId, currentUserId);
    }

    /**
     * 检查用户是否有权限访问指定线索（内部方法）
     */
    private boolean hasLeadPermissionInternal(Long leadId, Long currentUserId) {
        log.debug("检查线索查看权限，线索ID: {}, 当前用户ID: {}", leadId, currentUserId);

        if (leadId == null || currentUserId == null) {
            log.debug("线索ID或用户ID为空");
            return false;
        }

        // 获取线索信息
        LeadEntity leadEntity = leadDao.selectById(leadId);
        if (leadEntity == null || leadEntity.getDeletedFlag()) {
            log.debug("线索不存在或已删除，线索ID: {}", leadId);
            return false;
        }

        log.debug("线索信息: 创建者ID={}, 分配员工ID={}", leadEntity.getCreateUserId(), leadEntity.getAssignedEmployeeId());

        // 获取当前用户信息
        EmployeeEntity currentEmployee = employeeDao.selectById(currentUserId);
        if (currentEmployee == null) {
            log.debug("当前用户不存在，用户ID: {}", currentUserId);
            return false;
        }

        // 超级管理员有所有权限
        if (currentEmployee.getAdministratorFlag()) {
            log.debug("超级管理员，允许查看");
            return true;
        }

        // 检查是否是线索创建者或分配给自己的线索
        boolean isCreator = Objects.equals(leadEntity.getCreateUserId(), currentUserId);
        boolean isAssignee = Objects.equals(leadEntity.getAssignedEmployeeId(), currentUserId);
        boolean isOwner = isCreator || isAssignee;

        log.debug("所有权检查: 是创建者={}, 是分配者={}, 是拥有者={}", isCreator, isAssignee, isOwner);

        // 如果是线索拥有者，直接允许访问（这是最重要的检查）
        if (isOwner) {
            log.debug("是线索拥有者，允许查看");
            return true;
        }

        // 对于非拥有者，检查数据权限级别
        DataScopeViewTypeEnum viewType = null;
        try {
            viewType = dataScopeViewService.getEmployeeDataScopeViewType(DataScopeTypeEnum.LEAD, currentUserId);
            log.debug("用户数据权限级别: {}", viewType);

            // 如果没有配置数据权限，默认为ME权限，只能查看自己的
            if (viewType == null) {
                log.debug("没有配置数据权限，默认为ME权限");
                viewType = DataScopeViewTypeEnum.ME;
            }
        } catch (Exception e) {
            log.warn("获取权限级别失败，可能是用户没有配置数据权限，默认为ME权限。用户ID: {}, 错误: {}", currentUserId, e.getMessage());
            viewType = DataScopeViewTypeEnum.ME;
        }

        switch (viewType) {
            case ALL:
                log.debug("ALL权限，允许查看");
                return true;
            case DEPARTMENT_AND_SUB:
            case DEPARTMENT:
                // 检查是否在同一部门或下属部门
                try {
                    List<Long> canViewEmployeeIds = dataScopeViewService.getCanViewEmployeeId(viewType, currentUserId);
                    log.debug("可查看的员工IDs: {}", canViewEmployeeIds);
                    if (CollectionUtils.isNotEmpty(canViewEmployeeIds)) {
                        // 检查线索创建者或分配者是否在可查看范围内
                        boolean canViewCreator = leadEntity.getCreateUserId() != null &&
                                               canViewEmployeeIds.contains(leadEntity.getCreateUserId());
                        boolean canViewAssignee = leadEntity.getAssignedEmployeeId() != null &&
                                                canViewEmployeeIds.contains(leadEntity.getAssignedEmployeeId());
                        boolean result = canViewCreator || canViewAssignee;
                        log.debug("部门权限检查结果: 可查看创建者={}, 可查看分配者={}, 最终结果={}",
                                canViewCreator, canViewAssignee, result);
                        return result;
                    }
                } catch (Exception e) {
                    log.error("权限检查异常", e);
                    return false;
                }
                break;
            case ME:
            default:
                // ME权限只能查看自己的线索，前面已经检查过了
                log.debug("ME权限，非拥有者，拒绝访问");
                return false;
        }

        // 默认拒绝访问
        log.debug("默认拒绝访问");
        return false;
    }

    /**
     * 检查用户是否有权限分配线索
     * 所有用户都可以分配线索，具体的分配范围由isSubordinate方法控制
     */
    private boolean canAssignLead(Long currentUserId) {
        if (currentUserId == null) {
            return false;
        }

        // 获取当前用户信息
        EmployeeEntity currentEmployee = employeeDao.selectById(currentUserId);
        if (currentEmployee == null) {
            return false;
        }

        // 所有有效用户都可以分配线索（具体分配范围由isSubordinate方法控制）
        return true;
    }

    /**
     * 检查被分配员工是否为当前用户的下属
     */
    private boolean isSubordinate(Long currentUserId, Long assigneeId) {
        log.debug("检查线索分配权限，被分配员工ID: {}, 当前用户ID: {}", assigneeId, currentUserId);

        if (currentUserId == null || assigneeId == null) {
            log.debug("用户ID或被分配员工ID为空");
            return false;
        }

        // 如果分配给自己，允许
        if (Objects.equals(currentUserId, assigneeId)) {
            log.debug("分配给自己，允许");
            return true;
        }

        // 获取当前用户信息
        EmployeeEntity currentEmployee = employeeDao.selectById(currentUserId);
        if (currentEmployee == null) {
            log.debug("当前用户不存在");
            return false;
        }

        // 超级管理员可以分配给任何人
        if (currentEmployee.getAdministratorFlag()) {
            log.debug("超级管理员，允许分配给任何人");
            return true;
        }

        // 获取被分配员工信息，确保该员工存在且未被删除
        EmployeeEntity assigneeEmployee = employeeDao.selectById(assigneeId);
        if (assigneeEmployee == null || assigneeEmployee.getDeletedFlag()) {
            log.debug("被分配员工不存在或已删除");
            return false;
        }

        try {
            // 获取当前用户的数据权限级别
            DataScopeViewTypeEnum viewType = dataScopeViewService.getEmployeeDataScopeViewType(DataScopeTypeEnum.LEAD, currentUserId);
            log.debug("当前用户数据权限级别: {}", viewType);

            // 如果没有配置数据权限，默认为ME权限
            if (viewType == null) {
                viewType = DataScopeViewTypeEnum.ME;
                log.debug("没有配置数据权限，默认为ME权限");
            }

            switch (viewType) {
                case ALL:
                    // 全部权限，可以分配给任何人
                    log.debug("ALL权限，允许分配给任何人");
                    return true;
                case DEPARTMENT_AND_SUB:
                case DEPARTMENT:
                    // 部门权限，获取可以管理的员工列表
                    List<Long> managedEmployeeIds = dataScopeViewService.getCanViewEmployeeId(viewType, currentUserId);
                    log.debug("可管理的员工IDs: {}", managedEmployeeIds);
                    // 检查被分配员工是否在管理范围内
                    boolean canAssign = CollectionUtils.isNotEmpty(managedEmployeeIds) && managedEmployeeIds.contains(assigneeId);
                    log.debug("部门权限分配检查结果: {}", canAssign);
                    return canAssign;
                case ME:
                default:
                    // ME权限只能分配给自己（前面已经检查过了）
                    log.debug("ME权限，只能分配给自己，拒绝分配给其他人");
                    return false;
            }

        } catch (Exception e) {
            log.error("权限检查异常", e);
            // 如果权限检查出现异常，默认不允许分配给其他人
            return false;
        }
    }

    /**
     * 获取可分配的员工列表
     */
    public ResponseDTO<List<EmployeeVO>> getAssignableEmployees() {
        Long currentUserId = AdminRequestUtil.getRequestUserId();
        log.debug("获取可分配员工列表，当前用户ID: {}", currentUserId);

        try {
            // 获取当前用户信息
            EmployeeEntity currentEmployee = employeeDao.selectById(currentUserId);
            if (currentEmployee == null) {
                log.debug("当前用户不存在");
                return ResponseDTO.ok(List.of());
            }

            List<Long> assignableEmployeeIds = new ArrayList<>();

            // 超级管理员可以分配给任何人
            if (currentEmployee.getAdministratorFlag()) {
                log.debug("超级管理员，获取所有员工");
                // 获取所有有效员工
                List<EmployeeEntity> allEmployees = employeeDao.selectList(null);
                return ResponseDTO.ok(allEmployees.stream()
                        .filter(emp -> !emp.getDeletedFlag())
                        .map(this::convertToEmployeeVO)
                        .collect(Collectors.toList()));
            }

            // 获取当前用户的数据权限级别
            DataScopeViewTypeEnum viewType;
            try {
                viewType = dataScopeViewService.getEmployeeDataScopeViewType(DataScopeTypeEnum.LEAD, currentUserId);
                log.debug("当前用户数据权限级别: {}", viewType);

                // 如果没有配置数据权限，默认为ME权限
                if (viewType == null) {
                    viewType = DataScopeViewTypeEnum.ME;
                    log.debug("没有配置数据权限，默认为ME权限");
                }
            } catch (Exception e) {
                log.warn("获取权限级别失败，可能是用户没有配置数据权限，默认为ME权限。用户ID: {}, 错误: {}", currentUserId, e.getMessage());
                viewType = DataScopeViewTypeEnum.ME;
            }

            switch (viewType) {
                case ALL:
                    // 全部权限，可以分配给任何人
                    log.debug("ALL权限，获取所有员工");
                    List<EmployeeEntity> allEmployees = employeeDao.selectList(null);
                    return ResponseDTO.ok(allEmployees.stream()
                            .filter(emp -> !emp.getDeletedFlag())
                            .map(this::convertToEmployeeVO)
                            .collect(Collectors.toList()));
                case DEPARTMENT_AND_SUB:
                case DEPARTMENT:
                    // 部门权限，获取可以管理的员工列表
                    assignableEmployeeIds = dataScopeViewService.getCanViewEmployeeId(viewType, currentUserId);
                    log.debug("可分配的员工IDs: {}", assignableEmployeeIds);
                    break;
                case ME:
                default:
                    // ME权限只能分配给自己
                    assignableEmployeeIds.add(currentUserId);
                    log.debug("ME权限，只能分配给自己");
                    break;
            }

            // 根据员工ID列表获取员工信息
            if (assignableEmployeeIds.isEmpty()) {
                return ResponseDTO.ok(List.of());
            }

            List<EmployeeEntity> assignableEmployees = employeeDao.selectBatchIds(assignableEmployeeIds);
            List<EmployeeVO> result = assignableEmployees.stream()
                    .filter(emp -> !emp.getDeletedFlag())
                    .map(this::convertToEmployeeVO)
                    .collect(Collectors.toList());

            log.debug("返回可分配员工数量: {}", result.size());
            return ResponseDTO.ok(result);

        } catch (Exception e) {
            log.error("获取可分配员工列表异常", e);
            return ResponseDTO.userErrorParam("获取可分配员工列表失败");
        }
    }

    /**
     * 转换员工实体为VO
     */
    private EmployeeVO convertToEmployeeVO(EmployeeEntity employee) {
        EmployeeVO vo = new EmployeeVO();
        vo.setEmployeeId(employee.getEmployeeId());
        vo.setActualName(employee.getActualName());
        vo.setLoginName(employee.getLoginName());
        vo.setPhone(employee.getPhone());
        vo.setDepartmentId(employee.getDepartmentId());
        vo.setDisabledFlag(employee.getDisabledFlag());
        vo.setAdministratorFlag(employee.getAdministratorFlag());
        // 部门名称需要通过部门服务获取，这里暂时设为空
        vo.setDepartmentName("");
        return vo;
    }
}

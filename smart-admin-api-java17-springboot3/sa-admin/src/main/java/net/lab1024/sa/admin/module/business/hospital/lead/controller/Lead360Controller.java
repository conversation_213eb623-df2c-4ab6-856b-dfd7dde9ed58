package net.lab1024.sa.admin.module.business.hospital.lead.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.Lead360VO;
import net.lab1024.sa.admin.module.business.hospital.lead.service.Lead360Service;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.ValidateList;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;

/**
 * 线索360度视图Controller
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@RestController
@Tag(name = "线索360度视图")
@RequestMapping("/api/lead360")
public class Lead360Controller {

    @Resource
    private Lead360Service lead360Service;

    @Operation(summary = "获取线索360度视图")
    @GetMapping("/{leadId}")
    public ResponseDTO<Lead360VO> getLead360View(@PathVariable Long leadId) {
        return lead360Service.getLead360View(leadId);
    }

    @Operation(summary = "保存快速笔记")
    @PostMapping("/quick-note/{leadId}")
    public ResponseDTO<String> saveQuickNote(@PathVariable Long leadId, @RequestBody String noteContent) {
        return lead360Service.saveQuickNote(leadId, noteContent);
    }

    @Operation(summary = "添加聊天记录")
    @PostMapping("/chat-record/{leadId}")
    public ResponseDTO<String> addChatRecord(@PathVariable Long leadId, 
                                           @RequestParam String sender,
                                           @RequestParam String content,
                                           @RequestParam String type) {
        return lead360Service.addChatRecord(leadId, sender, content, type);
    }
}

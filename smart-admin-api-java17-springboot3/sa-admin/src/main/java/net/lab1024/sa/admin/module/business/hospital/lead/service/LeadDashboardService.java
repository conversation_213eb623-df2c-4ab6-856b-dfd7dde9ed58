package net.lab1024.sa.admin.module.business.hospital.lead.service;

import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.hospital.lead.dao.LeadDao;
import net.lab1024.sa.admin.module.system.dict.dao.DictDataDao;
import net.lab1024.sa.admin.module.system.dict.domain.entity.DictDataEntity;
import net.lab1024.sa.base.common.util.SmartRequestUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 线索仪表盘服务
 *
 * <AUTHOR>
 * @Date 2025-08-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Slf4j
@Service
public class LeadDashboardService {

    @Autowired
    private LeadDao leadDao;

    @Autowired
    private DictDataDao dictDataDao;

    /**
     * 获取线索基础统计
     */
    public Map<String, Object> getLeadBasicStats(String startDate, String endDate) {
        try {
            Long currentUserId = SmartRequestUtil.getRequestUserId();
            
            Map<String, Object> result = new HashMap<>();
            
            // 总线索数
            Integer totalLeads = leadDao.countTotalLeads(currentUserId);
            result.put("totalLeads", totalLeads != null ? totalLeads : 0);
            
            // 今日新增线索
            Integer todayNewLeads = leadDao.countTodayNewLeads(currentUserId);
            result.put("todayNewLeads", todayNewLeads != null ? todayNewLeads : 0);
            
            // 跟进中线索
            Integer followingLeads = leadDao.countLeadsByStatus(currentUserId, 2); // 2-跟进中
            result.put("followingLeads", followingLeads != null ? followingLeads : 0);
            
            // 已转化线索
            Integer convertedLeads = leadDao.countLeadsByStatus(currentUserId, 5); // 5-已转化
            result.put("convertedLeads", convertedLeads != null ? convertedLeads : 0);
            
            // 待跟进线索
            Integer pendingLeads = leadDao.countLeadsByStatus(currentUserId, 1); // 1-新线索
            result.put("pendingLeads", pendingLeads != null ? pendingLeads : 0);
            
            // 今日待跟进
            Integer todayPendingLeads = leadDao.countTodayPendingLeads(currentUserId);
            result.put("todayPendingLeads", todayPendingLeads != null ? todayPendingLeads : 0);
            
            // 本月转化
            Integer monthlyConverted = leadDao.countMonthlyConverted(currentUserId);
            result.put("monthlyConverted", monthlyConverted != null ? monthlyConverted : 0);
            
            // 转化率计算
            if (totalLeads != null && totalLeads > 0 && convertedLeads != null) {
                double conversionRate = (convertedLeads.doubleValue() / totalLeads.doubleValue()) * 100;
                result.put("conversionRate", Math.round(conversionRate * 10) / 10.0);
            } else {
                result.put("conversionRate", 0.0);
            }
            
            return result;
        } catch (Exception e) {
            log.error("获取线索基础统计失败", e);
            return getDefaultBasicStats();
        }
    }

    /**
     * 获取个人工作统计
     */
    public Map<String, Object> getPersonalStats(String startDate, String endDate) {
        try {
            Long currentUserId = SmartRequestUtil.getRequestUserId();
            
            Map<String, Object> result = new HashMap<>();
            
            // 本月预约数量
            Integer monthlyAppointments = leadDao.countMonthlyAppointments(currentUserId);
            result.put("monthlyAppointments", monthlyAppointments != null ? monthlyAppointments : 0);
            
            // 本月到院数量
            Integer monthlyArrivals = leadDao.countMonthlyArrivals(currentUserId);
            result.put("monthlyArrivals", monthlyArrivals != null ? monthlyArrivals : 0);
            
            // 跟进中客户
            Integer followingCustomers = leadDao.countLeadsByStatus(currentUserId, 2);
            result.put("followingCustomers", followingCustomers != null ? followingCustomers : 0);
            
            // 本月转化率
            Integer monthlyTotal = leadDao.countMonthlyTotal(currentUserId);
            Integer monthlyConverted = leadDao.countMonthlyConverted(currentUserId);
            if (monthlyTotal != null && monthlyTotal > 0 && monthlyConverted != null) {
                double conversionRate = (monthlyConverted.doubleValue() / monthlyTotal.doubleValue()) * 100;
                result.put("monthlyConversionRate", Math.round(conversionRate * 10) / 10.0);
            } else {
                result.put("monthlyConversionRate", 0.0);
            }
            
            return result;
        } catch (Exception e) {
            log.error("获取个人工作统计失败", e);
            return getDefaultPersonalStats();
        }
    }

    /**
     * 获取线索来源分布
     */
    public Map<String, Object> getLeadSourceDistribution() {
        try {
            Long currentUserId = SmartRequestUtil.getRequestUserId();
            
            // 获取线索来源统计
            List<Map<String, Object>> sourceStats = leadDao.getLeadSourceStats(currentUserId);
            
            // 获取线索来源字典
            List<DictDataEntity> sourceDict = dictDataDao.selectByDictCode("LEAD_SOURCE");
            Map<String, String> sourceLabelMap = sourceDict.stream()
                    .collect(Collectors.toMap(
                            DictDataEntity::getDataValue,
                            DictDataEntity::getDataLabel,
                            (existing, replacement) -> existing
                    ));
            
            List<Map<String, Object>> chartData = new ArrayList<>();
            for (Map<String, Object> stat : sourceStats) {
                String sourceValue = (String) stat.get("leadSource");
                Integer count = (Integer) stat.get("count");
                
                Map<String, Object> item = new HashMap<>();
                item.put("name", sourceLabelMap.getOrDefault(sourceValue, sourceValue));
                item.put("value", count);
                chartData.add(item);
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("data", chartData);
            return result;
        } catch (Exception e) {
            log.error("获取线索来源分布失败", e);
            return getDefaultChartData();
        }
    }

    /**
     * 获取线索状态分布
     */
    public Map<String, Object> getLeadStatusDistribution() {
        try {
            Long currentUserId = SmartRequestUtil.getRequestUserId();
            
            // 获取线索状态统计
            List<Map<String, Object>> statusStats = leadDao.getLeadStatusStats(currentUserId);
            
            // 状态映射
            Map<Integer, String> statusLabelMap = new HashMap<>();
            statusLabelMap.put(1, "新线索");
            statusLabelMap.put(2, "跟进中");
            statusLabelMap.put(3, "已预约");
            statusLabelMap.put(4, "已到院");
            statusLabelMap.put(5, "已转化");
            statusLabelMap.put(6, "爽约");
            statusLabelMap.put(7, "已关闭");
            
            List<Map<String, Object>> chartData = new ArrayList<>();
            for (Map<String, Object> stat : statusStats) {
                Integer status = (Integer) stat.get("leadStatus");
                Integer count = (Integer) stat.get("count");
                
                Map<String, Object> item = new HashMap<>();
                item.put("name", statusLabelMap.getOrDefault(status, "未知状态"));
                item.put("value", count);
                chartData.add(item);
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("data", chartData);
            return result;
        } catch (Exception e) {
            log.error("获取线索状态分布失败", e);
            return getDefaultChartData();
        }
    }

    /**
     * 获取线索质量分布
     */
    public Map<String, Object> getLeadQualityDistribution() {
        try {
            Long currentUserId = SmartRequestUtil.getRequestUserId();
            
            // 获取线索质量统计
            List<Map<String, Object>> qualityStats = leadDao.getLeadQualityStats(currentUserId);
            
            // 质量映射
            Map<Integer, String> qualityLabelMap = new HashMap<>();
            qualityLabelMap.put(1, "A级");
            qualityLabelMap.put(2, "B级");
            qualityLabelMap.put(3, "C级");
            
            List<Map<String, Object>> chartData = new ArrayList<>();
            for (Map<String, Object> stat : qualityStats) {
                Integer quality = (Integer) stat.get("leadQuality");
                Integer count = (Integer) stat.get("count");
                
                Map<String, Object> item = new HashMap<>();
                item.put("name", qualityLabelMap.getOrDefault(quality, "未知质量"));
                item.put("value", count);
                chartData.add(item);
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("data", chartData);
            return result;
        } catch (Exception e) {
            log.error("获取线索质量分布失败", e);
            return getDefaultChartData();
        }
    }

    /**
     * 获取症状分布
     */
    public Map<String, Object> getSymptomDistribution() {
        try {
            Long currentUserId = SmartRequestUtil.getRequestUserId();
            
            // 获取症状统计
            List<Map<String, Object>> symptomStats = leadDao.getSymptomStats(currentUserId);
            
            // 获取症状字典
            List<DictDataEntity> symptomDict = dictDataDao.selectByDictCode("SYMPTOM_TYPE");
            Map<String, String> symptomLabelMap = symptomDict.stream()
                    .collect(Collectors.toMap(
                            DictDataEntity::getDataValue,
                            DictDataEntity::getDataLabel,
                            (existing, replacement) -> existing
                    ));
            
            List<Map<String, Object>> chartData = new ArrayList<>();
            for (Map<String, Object> stat : symptomStats) {
                String symptomValue = (String) stat.get("symptom");
                Integer count = (Integer) stat.get("count");
                
                if (symptomValue != null && !symptomValue.trim().isEmpty()) {
                    Map<String, Object> item = new HashMap<>();
                    item.put("name", symptomLabelMap.getOrDefault(symptomValue, symptomValue));
                    item.put("value", count);
                    chartData.add(item);
                }
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("data", chartData);
            return result;
        } catch (Exception e) {
            log.error("获取症状分布失败", e);
            return getDefaultChartData();
        }
    }

    /**
     * 获取地理分布统计
     */
    public Map<String, Object> getGeographicDistribution() {
        try {
            Long currentUserId = SmartRequestUtil.getRequestUserId();
            
            // 获取地区统计
            List<Map<String, Object>> regionStats = leadDao.getRegionStats(currentUserId);
            
            List<Map<String, Object>> chartData = new ArrayList<>();
            for (Map<String, Object> stat : regionStats) {
                String region = (String) stat.get("region");
                Integer count = (Integer) stat.get("count");
                
                if (region != null && !region.trim().isEmpty()) {
                    Map<String, Object> item = new HashMap<>();
                    item.put("name", region);
                    item.put("value", count);
                    chartData.add(item);
                }
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("data", chartData);
            return result;
        } catch (Exception e) {
            log.error("获取地理分布统计失败", e);
            return getDefaultChartData();
        }
    }

    /**
     * 获取人口统计分析
     */
    public Map<String, Object> getDemographicStats() {
        try {
            Long currentUserId = SmartRequestUtil.getRequestUserId();
            
            Map<String, Object> result = new HashMap<>();
            
            // 年龄分布
            List<Map<String, Object>> ageStats = leadDao.getAgeStats(currentUserId);
            result.put("ageDistribution", ageStats);
            
            // 性别分布
            List<Map<String, Object>> genderStats = leadDao.getGenderStats(currentUserId);
            List<Map<String, Object>> genderChartData = new ArrayList<>();
            for (Map<String, Object> stat : genderStats) {
                Integer gender = (Integer) stat.get("gender");
                Integer count = (Integer) stat.get("count");
                
                Map<String, Object> item = new HashMap<>();
                if (gender != null) {
                    item.put("name", gender == 1 ? "男" : "女");
                    item.put("value", count);
                    genderChartData.add(item);
                }
            }
            result.put("genderDistribution", genderChartData);
            
            return result;
        } catch (Exception e) {
            log.error("获取人口统计分析失败", e);
            return getDefaultDemographicStats();
        }
    }

    /**
     * 获取线索转化趋势
     */
    public Map<String, Object> getLeadTrend(String startDate, String endDate) {
        try {
            Long currentUserId = SmartRequestUtil.getRequestUserId();
            
            // 获取趋势数据
            List<Map<String, Object>> trendData = leadDao.getLeadTrendStats(currentUserId, startDate, endDate);
            
            Map<String, Object> result = new HashMap<>();
            result.put("data", trendData);
            return result;
        } catch (Exception e) {
            log.error("获取线索转化趋势失败", e);
            return getDefaultChartData();
        }
    }

    // 默认数据方法
    private Map<String, Object> getDefaultBasicStats() {
        Map<String, Object> result = new HashMap<>();
        result.put("totalLeads", 0);
        result.put("todayNewLeads", 0);
        result.put("followingLeads", 0);
        result.put("convertedLeads", 0);
        result.put("pendingLeads", 0);
        result.put("todayPendingLeads", 0);
        result.put("monthlyConverted", 0);
        result.put("conversionRate", 0.0);
        return result;
    }

    private Map<String, Object> getDefaultPersonalStats() {
        Map<String, Object> result = new HashMap<>();
        result.put("monthlyAppointments", 0);
        result.put("monthlyArrivals", 0);
        result.put("followingCustomers", 0);
        result.put("monthlyConversionRate", 0.0);
        return result;
    }

    private Map<String, Object> getDefaultChartData() {
        Map<String, Object> result = new HashMap<>();
        result.put("data", new ArrayList<>());
        return result;
    }

    private Map<String, Object> getDefaultDemographicStats() {
        Map<String, Object> result = new HashMap<>();
        result.put("ageDistribution", new ArrayList<>());
        result.put("genderDistribution", new ArrayList<>());
        return result;
    }
}

package net.lab1024.sa.admin.module.business.hospital.lead.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 线索归属变更历史实体类
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@TableName("t_lead_ownership_change_history")
public class LeadOwnershipChangeHistoryEntity {

    /**
     * 历史记录ID
     */
    @TableId(type = IdType.AUTO)
    private Long historyId;

    /**
     * 线索ID
     */
    private Long leadId;

    /**
     * 关联的申请ID（如果是通过申请变更的）
     */
    private Long requestId;

    /**
     * 变更类型：1-申请变更，2-管理员直接变更，3-系统自动分配
     */
    private Integer changeType;

    /**
     * 原归属人ID
     */
    private Long originalOwnerId;

    /**
     * 原归属人姓名
     */
    private String originalOwnerName;

    /**
     * 新归属人ID
     */
    private Long newOwnerId;

    /**
     * 新归属人姓名
     */
    private String newOwnerName;

    /**
     * 变更原因
     */
    private String changeReason;

    /**
     * 操作人ID
     */
    private Long operateUserId;

    /**
     * 操作人姓名
     */
    private String operateUserName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}

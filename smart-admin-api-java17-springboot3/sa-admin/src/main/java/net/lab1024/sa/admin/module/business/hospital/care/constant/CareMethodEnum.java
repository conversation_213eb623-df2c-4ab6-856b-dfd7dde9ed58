package net.lab1024.sa.admin.module.business.hospital.care.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 关怀方式枚举
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Getter
@AllArgsConstructor
public enum CareMethodEnum implements BaseEnum {

    /**
     * 电话
     */
    PHONE(1, "电话"),

    /**
     * 短信
     */
    SMS(2, "短信"),

    /**
     * 微信
     */
    WECHAT(3, "微信"),

    /**
     * 邮件
     */
    EMAIL(4, "邮件"),

    /**
     * 上门
     */
    HOME_VISIT(5, "上门");

    private final Integer value;

    private final String desc;

    /**
     * 根据值获取枚举
     */
    public static CareMethodEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (CareMethodEnum methodEnum : values()) {
            if (methodEnum.getValue().equals(value)) {
                return methodEnum;
            }
        }
        return null;
    }
}

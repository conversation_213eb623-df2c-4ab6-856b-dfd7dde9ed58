package net.lab1024.sa.admin.module.business.hospital.visit.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 诊断记录实体类
 *
 * <AUTHOR>
 * @Date 2025-07-29 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@TableName("t_diagnosis_record")
public class DiagnosisRecordEntity {

    /**
     * 诊断记录ID
     */
    @TableId(type = IdType.AUTO)
    private Long diagnosisId;

    /**
     * 诊断编号
     */
    private String diagnosisNo;

    /**
     * 患者ID
     */
    private Long patientId;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 患者电话
     */
    private String patientPhone;

    /**
     * 医生ID
     */
    private Long doctorId;

    /**
     * 医生姓名
     */
    private String doctorName;

    /**
     * 科室ID
     */
    private Long departmentId;

    /**
     * 科室名称
     */
    private String departmentName;

    /**
     * 诊断日期
     */
    private LocalDate diagnosisDate;

    /**
     * 诊断时间
     */
    private LocalTime diagnosisTime;

    /**
     * 主诉
     */
    private String chiefComplaint;

    /**
     * 现病史
     */
    private String presentIllness;

    /**
     * 既往史
     */
    private String pastHistory;

    /**
     * 家族史
     */
    private String familyHistory;

    /**
     * 个人史
     */
    private String personalHistory;

    /**
     * 体格检查
     */
    private String physicalExamination;

    /**
     * 辅助检查
     */
    private String auxiliaryExamination;

    /**
     * 初步诊断
     */
    private String preliminaryDiagnosis;

    /**
     * 最终诊断
     */
    private String finalDiagnosis;

    /**
     * 治疗方案
     */
    private String treatmentPlan;

    /**
     * 用药建议
     */
    private String medicationAdvice;

    /**
     * 生活建议
     */
    private String lifestyleAdvice;

    /**
     * 随访计划
     */
    private String followUpPlan;

    /**
     * 下次复诊日期
     */
    private LocalDate nextVisitDate;

    /**
     * 诊断状态：1-草稿，2-已完成，3-已审核
     */
    private Integer diagnosisStatus;

    /**
     * 诊断模板ID
     */
    private Long templateId;

    /**
     * 诊断模板名称
     */
    private String templateName;

    /**
     * 诊断时长(分钟)
     */
    private Integer diagnosisDuration;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志：0-未删除，1-已删除
     */
    private Boolean deletedFlag;

    /**
     * 创建人ID
     */
    private Long createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    private Long updateUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}

package net.lab1024.sa.admin.module.business.hospital.lead.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 线索建议实体类
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@TableName("t_lead_suggestion")
public class LeadSuggestionEntity {

    /**
     * 建议ID
     */
    @TableId(type = IdType.AUTO)
    private Long suggestionId;

    /**
     * 线索ID
     */
    private Long leadId;

    /**
     * 建议类型：FOLLOW_UP,APPOINTMENT,QUALITY_IMPROVE,RESPONSE_URGENT
     */
    private String suggestionType;

    /**
     * 建议标题
     */
    private String title;

    /**
     * 建议描述
     */
    private String description;

    /**
     * 优先级：1-高，2-中，3-低
     */
    private Integer priority;

    /**
     * 是否可执行：0-否，1-是
     */
    private Boolean actionable;

    /**
     * 操作按钮文字
     */
    private String actionText;

    /**
     * 操作数据（JSON格式）
     */
    private String actionData;

    /**
     * 状态：1-待处理，2-已处理，3-已忽略
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}

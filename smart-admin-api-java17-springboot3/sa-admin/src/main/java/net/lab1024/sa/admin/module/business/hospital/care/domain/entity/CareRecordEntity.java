package net.lab1024.sa.admin.module.business.hospital.care.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 关怀记录实体类
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@TableName("t_care_record")
public class CareRecordEntity {

    /**
     * 关怀记录ID
     */
    @TableId(type = IdType.AUTO)
    private Long careId;

    /**
     * 关怀计划ID
     */
    private Long carePlanId;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 客户姓名
     */
    private String customerName;

    /**
     * 客户电话
     */
    private String customerPhone;

    /**
     * 病历ID（关联出院病历）
     */
    private Long recordId;

    /**
     * 关怀类型：1-出院关怀，2-复诊提醒，3-生日关怀，4-节日关怀，5-满意度调查
     */
    private Integer careType;

    /**
     * 关怀方式：1-电话，2-短信，3-微信，4-邮件，5-上门
     */
    private Integer careMethod;

    /**
     * 关怀模板ID
     */
    private Long templateId;

    /**
     * 关怀内容
     */
    private String careContent;

    /**
     * 计划执行时间
     */
    private LocalDateTime plannedTime;

    /**
     * 实际执行时间
     */
    private LocalDateTime actualTime;

    /**
     * 执行状态：1-待执行，2-执行中，3-已完成，4-已取消，5-执行失败
     */
    private Integer executeStatus;

    /**
     * 执行结果
     */
    private String executeResult;

    /**
     * 客户反馈
     */
    private String customerFeedback;

    /**
     * 满意度评分（1-5分）
     */
    private Integer satisfactionScore;

    /**
     * 是否需要后续跟进
     */
    private Boolean needFollowUp;

    /**
     * 后续跟进计划
     */
    private String followUpPlan;

    /**
     * 执行人员ID
     */
    private Long executeUserId;

    /**
     * 执行人员姓名
     */
    private String executeUserName;

    /**
     * 执行部门ID
     */
    private Long executeDepartmentId;

    /**
     * 执行部门名称
     */
    private String executeDepartmentName;

    /**
     * 自动执行标识
     */
    private Boolean autoExecuteFlag;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    private Integer maxRetryCount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标识
     */
    private Boolean deletedFlag;

    /**
     * 创建人ID
     */
    private Long createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    private Long updateUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}

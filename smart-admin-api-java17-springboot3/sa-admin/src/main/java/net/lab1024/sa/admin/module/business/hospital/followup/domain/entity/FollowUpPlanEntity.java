package net.lab1024.sa.admin.module.business.hospital.followup.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 回访计划实体
 *
 * <AUTHOR>
 * @Date 2025-07-30 00:00:00
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@TableName("t_follow_up_plan")
public class FollowUpPlanEntity {

    /**
     * 计划ID
     */
    @TableId(type = IdType.AUTO)
    private Long planId;

    /**
     * 计划名称
     */
    private String planName;

    /**
     * 计划描述
     */
    private String planDescription;

    /**
     * 计划类型：1-定期回访，2-节日回访，3-生日回访，4-满意度调查，5-产品推广
     */
    private Integer planType;

    /**
     * 目标客户类型：1-全部客户，2-新客户，3-老客户，4-VIP客户，5-指定客户
     */
    private Integer targetCustomerType;

    /**
     * 客户筛选条件（JSON格式）
     */
    private String customerFilterConditions;

    /**
     * 回访方式：1-电话回访，2-短信回访，3-微信回访，4-邮件回访，5-上门回访
     */
    private Integer followUpMethod;

    /**
     * 回访内容模板
     */
    private String followUpContentTemplate;

    /**
     * 计划开始日期
     */
    private LocalDate planStartDate;

    /**
     * 计划结束日期
     */
    private LocalDate planEndDate;

    /**
     * 执行频率：1-一次性，2-每日，3-每周，4-每月，5-每季度，6-每年
     */
    private Integer executionFrequency;

    /**
     * 执行时间
     */
    private LocalTime executionTime;

    /**
     * 执行日期（如：周一到周五为1,2,3,4,5）
     */
    private String executionDays;

    /**
     * 优先级：1-低，2-中，3-高，4-紧急
     */
    private Integer priorityLevel;

    /**
     * 计划状态：1-待执行，2-执行中，3-已暂停，4-已完成，5-已取消
     */
    private Integer planStatus;

    /**
     * 是否自动生成回访记录：0-否，1-是
     */
    private Integer autoGenerateRecords;

    /**
     * 负责人ID
     */
    private Long responsibleUserId;

    /**
     * 负责人姓名
     */
    private String responsibleUserName;

    /**
     * 预计客户数量
     */
    private Integer expectedCustomerCount;

    /**
     * 实际客户数量
     */
    private Integer actualCustomerCount;

    /**
     * 已完成数量
     */
    private Integer completedCount;

    /**
     * 成功率（%）
     */
    private BigDecimal successRate;

    /**
     * 最后执行时间
     */
    private LocalDateTime lastExecutionTime;

    /**
     * 下次执行时间
     */
    private LocalDateTime nextExecutionTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志：0-未删除，1-已删除
     */
    private Integer deletedFlag;

    /**
     * 创建人ID
     */
    private Long createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    private Long updateUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}

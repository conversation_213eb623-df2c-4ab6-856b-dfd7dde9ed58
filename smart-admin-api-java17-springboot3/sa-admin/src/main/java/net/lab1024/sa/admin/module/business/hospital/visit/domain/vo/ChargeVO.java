package net.lab1024.sa.admin.module.business.hospital.visit.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 收费记录VO
 *
 * <AUTHOR>
 * @Date 2025-07-29
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@Schema(description = "收费记录VO")
public class ChargeVO {

    @Schema(description = "收费ID")
    private Long chargeId;

    @Schema(description = "开单ID")
    private Long prescriptionId;

    @Schema(description = "诊断ID")
    private Long diagnosisId;

    @Schema(description = "患者ID")
    private Long patientId;

    @Schema(description = "患者编号")
    private String patientNo;

    @Schema(description = "患者姓名")
    private String patientName;

    @Schema(description = "收费编号")
    private String chargeNo;

    @Schema(description = "应收金额")
    private BigDecimal totalAmount;

    @Schema(description = "实收金额")
    private BigDecimal actualAmount;

    @Schema(description = "优惠金额")
    private BigDecimal discountAmount;

    @Schema(description = "收款账户：cash-现金，alipay-支付宝，wechat-微信支付，bank-银行卡")
    private String paymentAccount;

    @Schema(description = "收款账户名称")
    private String paymentAccountName;

    @Schema(description = "收费状态：1-已收费，2-部分收费，3-未收费，4-已退费")
    private Integer chargeStatus;

    @Schema(description = "收费状态名称")
    private String chargeStatusName;

    @Schema(description = "收费时间")
    private LocalDateTime chargeTime;

    @Schema(description = "收费备注")
    private String chargeNote;

    @Schema(description = "收费员ID")
    private Long cashierId;

    @Schema(description = "收费员姓名")
    private String cashierName;

    @Schema(description = "创建人姓名")
    private String createUserName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人姓名")
    private String updateUserName;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}

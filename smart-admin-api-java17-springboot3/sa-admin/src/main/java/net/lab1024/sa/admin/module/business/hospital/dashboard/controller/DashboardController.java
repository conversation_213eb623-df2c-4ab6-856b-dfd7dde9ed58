package net.lab1024.sa.admin.module.business.hospital.dashboard.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.admin.module.business.hospital.dashboard.domain.vo.DashboardStatisticsVO;
import net.lab1024.sa.admin.module.business.hospital.dashboard.domain.vo.TodoItemVO;
import net.lab1024.sa.admin.module.business.hospital.dashboard.domain.vo.ActivityVO;
import net.lab1024.sa.admin.module.business.hospital.dashboard.domain.vo.ChartDataVO;
import net.lab1024.sa.admin.module.business.hospital.dashboard.service.DashboardService;
import net.lab1024.sa.admin.module.business.hospital.lead.service.LeadDashboardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 医院仪表板 Controller
 *
 * <AUTHOR>
 * @Date 2024-12-15 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright <a href="https://1024lab.net">1024创新实验室</a>
 */
@Tag(name = "医院仪表板")
@RestController
@RequestMapping("/dashboard")
public class DashboardController {

    @Autowired
    private DashboardService dashboardService;

    @Autowired
    private LeadDashboardService leadDashboardService;

    @Operation(summary = "获取总览数据")
    @GetMapping("/overview")
    public ResponseDTO<DashboardStatisticsVO> getOverview() {
        return ResponseDTO.ok(dashboardService.getStatistics());
    }

    @Operation(summary = "获取统计数据")
    @GetMapping("/statistics")
    public ResponseDTO<DashboardStatisticsVO> getStatistics() {
        return ResponseDTO.ok(dashboardService.getStatistics());
    }

    @Operation(summary = "获取今日待办事项")
    @GetMapping("/today-todos")
    public ResponseDTO<List<TodoItemVO>> getTodayTodos() {
        return ResponseDTO.ok(dashboardService.getTodayTodos());
    }

    @Operation(summary = "获取最近活动")
    @GetMapping("/recent-activities")
    public ResponseDTO<List<ActivityVO>> getRecentActivities() {
        return ResponseDTO.ok(dashboardService.getRecentActivities());
    }

    @Operation(summary = "获取线索转化趋势")
    @GetMapping("/lead-conversion-trend")
    public ResponseDTO<ChartDataVO> getLeadConversionTrend(@RequestParam(required = false) String startDate,
                                                          @RequestParam(required = false) String endDate) {
        return ResponseDTO.ok(dashboardService.getLeadConversionTrend(startDate, endDate));
    }

    @Operation(summary = "获取客户来源分布")
    @GetMapping("/customer-source-distribution")
    public ResponseDTO<ChartDataVO> getCustomerSourceDistribution() {
        return ResponseDTO.ok(dashboardService.getCustomerSourceDistribution());
    }

    @Operation(summary = "获取预约状态分布")
    @GetMapping("/appointment-status-distribution")
    public ResponseDTO<ChartDataVO> getAppointmentStatusDistribution() {
        return ResponseDTO.ok(dashboardService.getAppointmentStatusDistribution());
    }

    @Operation(summary = "获取月度收入趋势")
    @GetMapping("/revenue-trend")
    public ResponseDTO<ChartDataVO> getRevenueTrend(@RequestParam(required = false) String year) {
        return ResponseDTO.ok(dashboardService.getRevenueTrend(year));
    }

    @Operation(summary = "获取员工绩效统计")
    @GetMapping("/employee-performance")
    public ResponseDTO<ChartDataVO> getEmployeePerformance(@RequestParam(required = false) String startDate,
                                                          @RequestParam(required = false) String endDate) {
        return ResponseDTO.ok(dashboardService.getEmployeePerformance(startDate, endDate));
    }

    @Operation(summary = "获取部门统计")
    @GetMapping("/department-statistics")
    public ResponseDTO<ChartDataVO> getDepartmentStatistics() {
        return ResponseDTO.ok(dashboardService.getDepartmentStatistics());
    }

    // ==================== 线索仪表盘API ====================

    @Operation(summary = "获取线索基础统计")
    @GetMapping("/lead-basic-stats")
    public ResponseDTO<Map<String, Object>> getLeadBasicStats(@RequestParam(required = false) String startDate,
                                                              @RequestParam(required = false) String endDate) {
        return ResponseDTO.ok(leadDashboardService.getLeadBasicStats(startDate, endDate));
    }

    @Operation(summary = "获取个人工作统计")
    @GetMapping("/personal-work-stats")
    public ResponseDTO<Map<String, Object>> getPersonalWorkStats(@RequestParam(required = false) String startDate,
                                                                 @RequestParam(required = false) String endDate) {
        return ResponseDTO.ok(leadDashboardService.getPersonalStats(startDate, endDate));
    }

    @Operation(summary = "获取线索来源分析")
    @GetMapping("/lead-source-analysis")
    public ResponseDTO<Map<String, Object>> getLeadSourceAnalysis() {
        return ResponseDTO.ok(leadDashboardService.getLeadSourceDistribution());
    }

    @Operation(summary = "获取线索状态分布")
    @GetMapping("/lead-status-distribution")
    public ResponseDTO<Map<String, Object>> getLeadStatusDistribution() {
        return ResponseDTO.ok(leadDashboardService.getLeadStatusDistribution());
    }

    @Operation(summary = "获取线索质量分布")
    @GetMapping("/lead-quality-distribution")
    public ResponseDTO<Map<String, Object>> getLeadQualityDistribution() {
        return ResponseDTO.ok(leadDashboardService.getLeadQualityDistribution());
    }

    @Operation(summary = "获取症状分布")
    @GetMapping("/symptom-distribution")
    public ResponseDTO<Map<String, Object>> getSymptomDistribution() {
        return ResponseDTO.ok(leadDashboardService.getSymptomDistribution());
    }

    @Operation(summary = "获取地理分布")
    @GetMapping("/lead-geographic-distribution")
    public ResponseDTO<Map<String, Object>> getLeadGeographicDistribution() {
        return ResponseDTO.ok(leadDashboardService.getGeographicDistribution());
    }

    @Operation(summary = "获取人口统计分析")
    @GetMapping("/lead-demographic-stats")
    public ResponseDTO<Map<String, Object>> getLeadDemographicStats() {
        return ResponseDTO.ok(leadDashboardService.getDemographicStats());
    }

    @Operation(summary = "获取线索趋势分析")
    @GetMapping("/lead-trend-analysis")
    public ResponseDTO<Map<String, Object>> getLeadTrendAnalysis(@RequestParam(required = false) String startDate,
                                                                 @RequestParam(required = false) String endDate) {
        return ResponseDTO.ok(leadDashboardService.getLeadTrend(startDate, endDate));
    }
}

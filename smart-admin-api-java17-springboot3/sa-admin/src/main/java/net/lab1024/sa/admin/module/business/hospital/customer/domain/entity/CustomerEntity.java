package net.lab1024.sa.admin.module.business.hospital.customer.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 客户实体类
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@TableName("t_customer")
public class CustomerEntity {

    /**
     * 客户ID
     */
    @TableId(type = IdType.AUTO)
    private Long customerId;

    /**
     * 客户编号
     */
    private String customerNo;

    /**
     * 客户姓名
     */
    private String customerName;

    /**
     * 客户电话
     */
    private String customerPhone;

    /**
     * 微信号
     */
    private String customerWechat;

    /**
     * 邮箱
     */
    private String customerEmail;

    /**
     * 性别：使用GENDER字典
     */
    private Integer gender;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 生日
     */
    private LocalDate birthday;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 地址
     */
    private String customerAddress;

    /**
     * 客户等级：使用CUSTOMER_LEVEL字典
     */
    private Integer customerLevel;

    /**
     * 客户来源
     */
    private String customerSource;

    /**
     * 客户状态：1-潜在客户，2-意向客户，3-成交客户，4-流失客户
     */
    private Integer customerStatus;

    /**
     * 客户标签
     */
    private String customerTags;

    /**
     * 负责员工ID
     */
    private Long responsibleEmployeeId;

    /**
     * 负责员工姓名
     */
    private String responsibleEmployeeName;

    /**
     * 首次就诊日期
     */
    private LocalDate firstVisitDate;

    /**
     * 最后就诊日期
     */
    private LocalDate lastVisitDate;

    /**
     * 职业
     */
    private String occupation;

    /**
     * 总消费金额
     */
    private BigDecimal totalConsumption;



    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标志：0-未删除，1-已删除
     */
    private Boolean deletedFlag;

    /**
     * 创建人ID
     */
    private Long createUserId;

    /**
     * 更新人ID
     */
    private Long updateUserId;

    /**
     * 线索ID（从线索转换而来的客户）
     */
    private Long leadId;
}

package net.lab1024.sa.admin.module.business.hospital.visit.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 医生助理关系实体类
 *
 * <AUTHOR>
 * @Date 2025-07-29 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@TableName("t_doctor_assistant_relation")
public class DoctorAssistantRelationEntity {

    /**
     * 关系ID
     */
    @TableId(type = IdType.AUTO)
    private Long relationId;

    /**
     * 医生ID
     */
    private Long doctorId;

    /**
     * 医生姓名
     */
    private String doctorName;

    /**
     * 治疗助理ID
     */
    private Long assistantId;

    /**
     * 治疗助理姓名
     */
    private String assistantName;

    /**
     * 部门ID
     */
    private Long departmentId;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 关系类型：1-直接下属，2-协作关系
     */
    private Integer relationType;

    /**
     * 关系开始日期
     */
    private LocalDate startDate;

    /**
     * 关系结束日期
     */
    private LocalDate endDate;

    /**
     * 关系状态：1-有效，0-无效
     */
    private Integer relationStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志：0-未删除，1-已删除
     */
    private Boolean deletedFlag;

    /**
     * 创建人ID
     */
    private Long createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    private Long updateUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}

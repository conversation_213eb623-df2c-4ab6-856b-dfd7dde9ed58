package net.lab1024.sa.admin.module.business.hospital.followup.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 回访计划类型枚举
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Getter
@AllArgsConstructor
public enum FollowUpPlanTypeEnum implements BaseEnum {

    /**
     * 定期回访
     */
    REGULAR(1, "定期回访"),

    /**
     * 节日回访
     */
    HOLIDAY(2, "节日回访"),

    /**
     * 特殊回访
     */
    SPECIAL(3, "特殊回访"),

    /**
     * 术后回访
     */
    POST_SURGERY(4, "术后回访"),

    /**
     * 用药跟进
     */
    MEDICATION_FOLLOW(5, "用药跟进");

    private final Integer value;

    private final String desc;
}

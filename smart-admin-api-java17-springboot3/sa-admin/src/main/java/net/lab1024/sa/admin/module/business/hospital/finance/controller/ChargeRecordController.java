package net.lab1024.sa.admin.module.business.hospital.finance.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.lab1024.sa.admin.constant.AdminSwaggerTagConst;
import net.lab1024.sa.admin.module.business.hospital.finance.domain.form.ChargeRecordAddForm;
import net.lab1024.sa.admin.module.business.hospital.finance.domain.form.ChargeRecordQueryForm;
import net.lab1024.sa.admin.module.business.hospital.finance.domain.form.ChargeRecordUpdateForm;
import net.lab1024.sa.admin.module.business.hospital.finance.domain.vo.ChargeRecordVO;
import net.lab1024.sa.admin.module.business.hospital.finance.service.ChargeRecordService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.module.support.operatelog.annotation.OperateLog;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 收费记录Controller
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Tag(name = AdminSwaggerTagConst.Business.HOSPITAL_FINANCE)
@RestController
@RequestMapping("/api/charge-record")
public class ChargeRecordController {

    @Resource
    private ChargeRecordService chargeRecordService;

    @Operation(summary = "分页查询收费记录")
    @PostMapping("/query")
    @SaCheckPermission("hospital:charge:query")
    public ResponseDTO<PageResult<ChargeRecordVO>> queryPage(@RequestBody @Valid ChargeRecordQueryForm queryForm) {
        return chargeRecordService.queryPage(queryForm);
    }

    @Operation(summary = "添加收费记录")
    @PostMapping("/add")
    @SaCheckPermission("hospital:charge:add")
    @OperateLog
    public ResponseDTO<String> add(@RequestBody @Valid ChargeRecordAddForm addForm) {
        return chargeRecordService.add(addForm);
    }

    @Operation(summary = "更新收费记录")
    @PostMapping("/update")
    @SaCheckPermission("hospital:charge:update")
    @OperateLog
    public ResponseDTO<String> update(@RequestBody @Valid ChargeRecordUpdateForm updateForm) {
        return chargeRecordService.update(updateForm);
    }

    @Operation(summary = "删除收费记录")
    @GetMapping("/delete/{chargeId}")
    @SaCheckPermission("hospital:charge:delete")
    @OperateLog
    public ResponseDTO<String> delete(@PathVariable Long chargeId) {
        return chargeRecordService.delete(chargeId);
    }

    @Operation(summary = "根据预约创建收费记录")
    @PostMapping("/create-from-appointment/{appointmentId}")
    @SaCheckPermission("hospital:charge:add")
    @OperateLog
    public ResponseDTO<String> createFromAppointment(@PathVariable Long appointmentId) {
        return chargeRecordService.createFromAppointment(appointmentId);
    }

    @Operation(summary = "根据客户ID查询收费记录")
    @GetMapping("/customer/{customerId}")
    @SaCheckPermission("hospital:charge:query")
    public ResponseDTO<List<ChargeRecordVO>> getByCustomerId(@PathVariable Long customerId) {
        return chargeRecordService.getByCustomerId(customerId);
    }

    @Operation(summary = "根据预约ID查询收费记录")
    @GetMapping("/appointment/{appointmentId}")
    @SaCheckPermission("hospital:charge:query")
    public ResponseDTO<List<ChargeRecordVO>> getByAppointmentId(@PathVariable Long appointmentId) {
        return chargeRecordService.getByAppointmentId(appointmentId);
    }

    @Operation(summary = "更新收费状态")
    @PostMapping("/update-status/{chargeId}")
    @SaCheckPermission("hospital:charge:update")
    @OperateLog
    public ResponseDTO<String> updateChargeStatus(@PathVariable Long chargeId,
                                                 @RequestParam Integer chargeStatus,
                                                 @RequestParam(required = false) Integer paymentMethod,
                                                 @RequestParam(required = false) String paymentDetails) {
        return chargeRecordService.updateChargeStatus(chargeId, chargeStatus, paymentMethod, paymentDetails);
    }

    @Operation(summary = "更新发票信息")
    @PostMapping("/update-invoice/{chargeId}")
    @SaCheckPermission("hospital:charge:update")
    @OperateLog
    public ResponseDTO<String> updateInvoiceInfo(@PathVariable Long chargeId,
                                                @RequestParam(required = false) String invoiceNo,
                                                @RequestParam Boolean invoiceFlag) {
        return chargeRecordService.updateInvoiceInfo(chargeId, invoiceNo, invoiceFlag);
    }
}

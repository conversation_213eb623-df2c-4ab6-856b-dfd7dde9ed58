package net.lab1024.sa.admin.module.business.hospital.followup.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 回访计划添加表单
 *
 * <AUTHOR>
 * @Date 2025-07-30 00:00:00
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@Schema(description = "回访计划添加表单")
public class FollowUpPlanAddForm {

    @Schema(description = "计划名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "计划名称不能为空")
    @Size(max = 200, message = "计划名称不能超过200个字符")
    private String planName;

    @Schema(description = "计划描述")
    @Size(max = 2000, message = "计划描述不能超过2000个字符")
    private String planDescription;

    @Schema(description = "计划类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "计划类型不能为空")
    private Integer planType;

    @Schema(description = "目标客户类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "目标客户类型不能为空")
    private Integer targetCustomerType;

    @Schema(description = "客户筛选条件（JSON格式）")
    private String customerFilterConditions;

    @Schema(description = "回访方式", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "回访方式不能为空")
    private Integer followUpMethod;

    @Schema(description = "回访内容模板")
    @Size(max = 2000, message = "回访内容模板不能超过2000个字符")
    private String followUpContentTemplate;

    @Schema(description = "计划开始日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "计划开始日期不能为空")
    private LocalDate planStartDate;

    @Schema(description = "计划结束日期")
    private LocalDate planEndDate;

    @Schema(description = "执行频率", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "执行频率不能为空")
    private Integer executionFrequency;

    @Schema(description = "执行时间")
    private LocalTime executionTime;

    @Schema(description = "执行日期（如：周一到周五为1,2,3,4,5）")
    @Size(max = 50, message = "执行日期不能超过50个字符")
    private String executionDays;

    @Schema(description = "优先级")
    private Integer priorityLevel;

    @Schema(description = "是否自动生成回访记录")
    private Integer autoGenerateRecords;

    @Schema(description = "负责人ID")
    private Long responsibleUserId;

    @Schema(description = "负责人姓名")
    @Size(max = 64, message = "负责人姓名不能超过64个字符")
    private String responsibleUserName;

    @Schema(description = "预计客户数量")
    private Integer expectedCustomerCount;

    @Schema(description = "备注")
    @Size(max = 500, message = "备注不能超过500个字符")
    private String remark;
}

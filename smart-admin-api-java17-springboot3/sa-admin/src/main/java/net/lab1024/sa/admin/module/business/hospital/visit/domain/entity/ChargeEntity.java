package net.lab1024.sa.admin.module.business.hospital.visit.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 收费记录实体类
 *
 * <AUTHOR>
 * @Date 2025-07-29
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@TableName("t_charge")
public class ChargeEntity {

    /**
     * 收费ID
     */
    @TableId(type = IdType.AUTO)
    private Long chargeId;

    /**
     * 开单ID
     */
    private Long prescriptionId;

    /**
     * 诊断ID
     */
    private Long diagnosisId;

    /**
     * 患者ID
     */
    private Long patientId;

    /**
     * 患者编号
     */
    private String patientNo;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 收费编号
     */
    private String chargeNo;

    /**
     * 应收金额
     */
    private BigDecimal totalAmount;

    /**
     * 实收金额
     */
    private BigDecimal actualAmount;

    /**
     * 优惠金额
     */
    private BigDecimal discountAmount;

    /**
     * 收款账户：cash-现金，alipay-支付宝，wechat-微信支付，bank-银行卡
     */
    private String paymentAccount;

    /**
     * 收费状态：1-已收费，2-部分收费，3-未收费，4-已退费
     */
    private Integer chargeStatus;

    /**
     * 收费时间
     */
    private LocalDateTime chargeTime;

    /**
     * 收费备注
     */
    private String chargeNote;

    /**
     * 收费员ID
     */
    private Long cashierId;

    /**
     * 收费员姓名
     */
    private String cashierName;

    /**
     * 创建人ID
     */
    private Long createUserId;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 更新人ID
     */
    private Long updateUserId;

    /**
     * 更新人姓名
     */
    private String updateUserName;

    /**
     * 删除标识：0-未删除，1-已删除
     */
    private Integer deletedFlag;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}

package net.lab1024.sa.admin.module.business.hospital.lead.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;

import lombok.Data;
import net.lab1024.sa.admin.module.business.hospital.lead.constant.LeadQualityEnum;
import net.lab1024.sa.admin.module.business.hospital.lead.constant.LeadStatusEnum;
import net.lab1024.sa.base.common.swagger.SchemaEnum;
import net.lab1024.sa.base.common.validator.enumeration.CheckEnum;
import org.hibernate.validator.constraints.Length;

/**
 * 线索添加表单
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class LeadAddForm {

    @Schema(description = "线索来源")
    @NotBlank(message = "请选择线索来源")
    @Length(max = 50, message = "线索来源最多50字符")
    private String leadSource;

    @Schema(description = "客户姓名")
    @NotBlank(message = "客户姓名不能为空")
    @Length(max = 100, message = "客户姓名最多100字符")
    private String customerName;

    @Schema(description = "客户电话")
    @NotBlank(message = "客户电话不能为空")
    @Length(max = 20, message = "客户电话最多20字符")
    private String customerPhone;

    @Schema(description = "工作手机设备标识")
    @Length(max = 50, message = "工作手机设备标识最多50字符")
    private String workPhone;

    @Schema(description = "微信号")
    @Length(max = 50, message = "微信号最多50字符")
    private String customerWechat;

    @Schema(description = "地区")
    @Length(max = 50, message = "地区最多50字符")
    private String region;

    @Schema(description = "性别：1-男，2-女")
    private Integer gender;

    @Schema(description = "年龄")
    private Integer age;

    @Schema(description = "症状")
    @Length(max = 200, message = "症状最多200字符")
    private String symptom;

    @Schema(description = "线索状态：1-新线索，2-跟进中，3-已预约，4-已转化，5-已关闭")
    @SchemaEnum(LeadStatusEnum.class)
    @CheckEnum(message = "线索状态错误", value = LeadStatusEnum.class, required = false)
    private Integer leadStatus;

    @Schema(description = "线索质量：1-A级，2-B级，3-C级")
    @SchemaEnum(LeadQualityEnum.class)
    @CheckEnum(message = "线索质量错误", value = LeadQualityEnum.class, required = false)
    private Integer leadQuality;

    @Schema(description = "备注")
    @Length(max = 500, message = "备注最多500字符")
    private String remark;

    @Schema(description = "聊天记录")
    @Length(max = 65535, message = "聊天记录最多65535字符")
    private String chatRecord;
}

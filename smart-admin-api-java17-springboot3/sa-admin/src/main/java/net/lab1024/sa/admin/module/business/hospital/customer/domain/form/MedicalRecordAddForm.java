package net.lab1024.sa.admin.module.business.hospital.customer.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDate;

/**
 * 病历添加表单
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class MedicalRecordAddForm {

    @Schema(description = "关联线索ID")
    private Long leadId;

    @Schema(description = "客户ID")
    @NotNull(message = "客户ID不能为空")
    private Long customerId;

    @Schema(description = "关联预约ID")
    private Long appointmentId;

    @Schema(description = "就诊日期")
    @NotNull(message = "就诊日期不能为空")
    private LocalDate visitDate;

    @Schema(description = "主诉")
    @NotBlank(message = "主诉不能为空")
    @Length(max = 500, message = "主诉最多500字符")
    private String chiefComplaint;

    @Schema(description = "现病史")
    @Length(max = 1000, message = "现病史最多1000字符")
    private String presentIllness;

    @Schema(description = "既往史")
    @Length(max = 1000, message = "既往史最多1000字符")
    private String pastHistory;

    @Schema(description = "检查结果")
    @Length(max = 1000, message = "检查结果最多1000字符")
    private String examination;

    @Schema(description = "诊断结果")
    @NotBlank(message = "诊断结果不能为空")
    @Length(max = 500, message = "诊断结果最多500字符")
    private String diagnosis;

    @Schema(description = "治疗方案")
    @Length(max = 1000, message = "治疗方案最多1000字符")
    private String treatment;

    @Schema(description = "用药情况")
    @Length(max = 500, message = "用药情况最多500字符")
    private String medication;

    @Schema(description = "医嘱")
    @Length(max = 500, message = "医嘱最多500字符")
    private String doctorAdvice;

    @Schema(description = "下次复诊时间")
    private LocalDate nextVisitDate;

    @Schema(description = "主治医生ID")
    private Long doctorId;

    @Schema(description = "备注")
    @Length(max = 500, message = "备注最多500字符")
    private String remark;
}

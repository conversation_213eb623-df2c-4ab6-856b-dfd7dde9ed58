package net.lab1024.sa.admin.module.business.hospital.dashboard.service.impl;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.hospital.dashboard.domain.vo.DashboardStatisticsVO;
import net.lab1024.sa.admin.module.business.hospital.dashboard.domain.vo.TodoItemVO;
import net.lab1024.sa.admin.module.business.hospital.dashboard.domain.vo.ActivityVO;
import net.lab1024.sa.admin.module.business.hospital.dashboard.domain.vo.ChartDataVO;
import net.lab1024.sa.admin.module.business.hospital.dashboard.service.DashboardService;
import net.lab1024.sa.admin.module.business.hospital.lead.dao.LeadDao;
import net.lab1024.sa.admin.module.business.hospital.customer.dao.CustomerDao;
import net.lab1024.sa.admin.module.business.hospital.appointment.dao.AppointmentDao;
import net.lab1024.sa.admin.module.business.hospital.visit.dao.ChargeDao;
import net.lab1024.sa.admin.module.business.hospital.dashboard.dao.DashboardDao;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 医院仪表板 Service 实现
 *
 * <AUTHOR>
 * @Date 2024-12-15 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright <a href="https://1024lab.net">1024创新实验室</a>
 */
@Slf4j
@Service
public class DashboardServiceImpl implements DashboardService {

    @Resource
    private LeadDao leadDao;

    @Resource
    private CustomerDao customerDao;

    @Resource
    private AppointmentDao appointmentDao;

    @Resource
    private ChargeDao chargeDao;



    @Resource
    private DashboardDao dashboardDao;

    @Override
    public DashboardStatisticsVO getStatistics() {
        DashboardStatisticsVO statistics = new DashboardStatisticsVO();

        try {
            // 获取今日开始和结束时间
            LocalDateTime todayStart = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            LocalDateTime todayEnd = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);

            // 获取总数据
            statistics.setLeadCount(Long.valueOf(leadDao.countTotal()));
            statistics.setCustomerCount(Long.valueOf(customerDao.countTotal()));
            statistics.setAppointmentCount(Long.valueOf(appointmentDao.countTotal()));

            // 获取总收入（从收费记录表）
            BigDecimal totalRevenue = getTotalRevenueFromChargeRecord();
            statistics.setTotalRevenue(totalRevenue != null ? totalRevenue.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO);

            // 获取今日数据
            statistics.setTodayLeadCount(Long.valueOf(leadDao.countByDateRange(todayStart, todayEnd)));
            statistics.setTodayCustomerCount(Long.valueOf(customerDao.countByDateRange(todayStart, todayEnd)));
            statistics.setTodayAppointmentCount(Long.valueOf(appointmentDao.countByDateRange(todayStart, todayEnd)));

            // 获取今日收入
            BigDecimal todayRevenue = getTodayRevenueFromChargeRecord();
            statistics.setTodayRevenue(todayRevenue != null ? todayRevenue.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO);

            // 获取待处理数据
            statistics.setPendingLeadCount(leadDao.countActiveLeads().longValue());
            statistics.setPendingAppointmentCount(appointmentDao.getPendingConfirmCount().longValue());

            // 获取今日待办数量
            List<TodoItemVO> todos = dashboardDao.getTodayTodos();
            statistics.setTodayTodoCount(todos != null ? (long) todos.size() : 0L);

        } catch (Exception e) {
            log.error("获取仪表盘统计数据失败", e);
            // 如果查询出错，返回默认值
            statistics.setLeadCount(0L);
            statistics.setCustomerCount(0L);
            statistics.setAppointmentCount(0L);
            statistics.setTotalRevenue(BigDecimal.ZERO);
            statistics.setTodayLeadCount(0L);
            statistics.setTodayCustomerCount(0L);
            statistics.setTodayAppointmentCount(0L);
            statistics.setTodayRevenue(BigDecimal.ZERO);
            statistics.setPendingLeadCount(0L);
            statistics.setPendingAppointmentCount(0L);
            statistics.setTodayTodoCount(0L);
        }

        return statistics;
    }

    /**
     * 从收费记录表获取总收入
     */
    private BigDecimal getTotalRevenueFromChargeRecord() {
        try {
            // 这里需要添加对应的DAO方法，暂时返回0
            return BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("获取总收入失败", e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 从收费记录表获取今日收入
     */
    private BigDecimal getTodayRevenueFromChargeRecord() {
        try {
            // 这里需要添加对应的DAO方法，暂时返回0
            return BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("获取今日收入失败", e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    public List<TodoItemVO> getTodayTodos() {
        try {
            return dashboardDao.getTodayTodos();
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    @Override
    public List<ActivityVO> getRecentActivities() {
        try {
            return dashboardDao.getRecentActivities();
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    @Override
    public ChartDataVO getLeadConversionTrend(String startDate, String endDate) {
        try {
            return dashboardDao.getLeadConversionTrend(startDate, endDate);
        } catch (Exception e) {
            return new ChartDataVO();
        }
    }

    @Override
    public ChartDataVO getCustomerSourceDistribution() {
        try {
            return dashboardDao.getCustomerSourceDistribution();
        } catch (Exception e) {
            return new ChartDataVO();
        }
    }

    @Override
    public ChartDataVO getAppointmentStatusDistribution() {
        try {
            return dashboardDao.getAppointmentStatusDistribution();
        } catch (Exception e) {
            return new ChartDataVO();
        }
    }

    @Override
    public ChartDataVO getRevenueTrend(String year) {
        try {
            return dashboardDao.getRevenueTrend(year);
        } catch (Exception e) {
            return new ChartDataVO();
        }
    }

    @Override
    public ChartDataVO getEmployeePerformance(String startDate, String endDate) {
        try {
            return dashboardDao.getEmployeePerformance(startDate, endDate);
        } catch (Exception e) {
            return new ChartDataVO();
        }
    }

    @Override
    public ChartDataVO getDepartmentStatistics() {
        try {
            return dashboardDao.getDepartmentStatistics();
        } catch (Exception e) {
            return new ChartDataVO();
        }
    }
} 
package net.lab1024.sa.admin.module.business.hospital.appointment.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.entity.AppointmentEntity;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.form.AppointmentQueryForm;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.vo.AppointmentCalendarVO;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.vo.AppointmentVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * 预约DAO
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Mapper
@Component
public interface AppointmentDao extends BaseMapper<AppointmentEntity> {

    /**
     * 分页查询预约
     *
     * @param page 分页参数
     * @param queryForm 查询条件
     * @return 预约列表
     */
    List<AppointmentVO> queryPage(Page page, @Param("query") AppointmentQueryForm queryForm);

    /**
     * 检查时间冲突
     *
     * @param doctorId 医生ID
     * @param appointmentDate 预约日期
     * @param appointmentTime 预约时间
     * @param excludeAppointmentId 排除的预约ID（用于更新时排除自己）
     * @return 冲突的预约数量
     */
    Integer checkTimeConflict(@Param("doctorId") Long doctorId, 
                             @Param("appointmentDate") LocalDate appointmentDate,
                             @Param("appointmentTime") LocalTime appointmentTime,
                             @Param("excludeAppointmentId") Long excludeAppointmentId);

    /**
     * 更新预约状态
     *
     * @param appointmentId 预约ID
     * @param appointmentStatus 预约状态
     */
    void updateStatus(@Param("appointmentId") Long appointmentId, @Param("appointmentStatus") Integer appointmentStatus);

    /**
     * 更新预约状态和实际到院时间
     *
     * @param appointmentId 预约ID
     * @param appointmentStatus 预约状态
     * @param actualArrivalTime 实际到院时间
     */
    void updateStatusAndArrivalTime(@Param("appointmentId") Long appointmentId,
                                   @Param("appointmentStatus") Integer appointmentStatus,
                                   @Param("actualArrivalTime") LocalDateTime actualArrivalTime);

    /**
     * 批量更新删除状态
     *
     * @param appointmentIdList 预约ID列表
     * @param deletedFlag 删除标志
     */
    void batchUpdateDeleted(@Param("appointmentIdList") List<Long> appointmentIdList, @Param("deletedFlag") Boolean deletedFlag);

    /**
     * 获取预约日历数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param doctorId 医生ID（可选）
     * @return 预约日历数据
     */
    List<AppointmentCalendarVO> getCalendarData(@Param("startDate") LocalDate startDate,
                                               @Param("endDate") LocalDate endDate,
                                               @Param("doctorId") Long doctorId);

    /**
     * 根据医生和日期统计预约数量
     *
     * @param doctorId 医生ID
     * @param appointmentDate 预约日期
     * @param appointmentTime 预约时间
     * @return 预约数量
     */
    Integer countByDoctorAndDateTime(@Param("doctorId") Long doctorId,
                                    @Param("appointmentDate") LocalDate appointmentDate,
                                    @Param("appointmentTime") LocalTime appointmentTime);

    /**
     * 根据线索ID查询预约
     *
     * @param leadId 线索ID
     * @return 预约列表
     */
    List<AppointmentVO> selectByLeadId(@Param("leadId") Long leadId);

    /**
     * 根据客户ID查询预约
     *
     * @param customerId 客户ID
     * @return 预约列表
     */
    List<AppointmentVO> selectByCustomerId(@Param("customerId") Long customerId);

    /**
     * 获取今日预约统计
     *
     * @param appointmentDate 预约日期
     * @return 预约统计数据
     */
    Integer getTodayAppointmentCount(@Param("appointmentDate") LocalDate appointmentDate);

    /**
     * 获取待确认预约数量
     *
     * @return 待确认预约数量
     */
    Integer getPendingConfirmCount();

    /**
     * 统计总预约数
     *
     * @return 总预约数
     */
    Integer countTotal();

    /**
     * 统计指定时间范围内的预约数
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 预约数
     */
    Integer countByDateRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 统计已完成的预约数
     *
     * @return 已完成预约数
     */
    Integer countCompleted();

    /**
     * 统计已到院的预约数
     *
     * @return 已到院预约数
     */
    Integer countArrived();

    /**
     * 统计指定时间范围内已到院的预约数
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 已到院预约数
     */
    Integer countArrivedByDateRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 获取总收入
     *
     * @return 总收入
     */
    BigDecimal getTotalRevenue();

    /**
     * 获取指定时间范围内的收入
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 收入金额
     */
    BigDecimal getRevenueByDateRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}

package net.lab1024.sa.admin.module.business.hospital.lead.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.constant.AdminSwaggerTagConst;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.form.LeadFollowAddForm;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.form.LeadFollowEnhancedAddForm;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.form.LeadFollowQueryForm;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.form.LeadFollowUpdateForm;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadFollowVO;
import net.lab1024.sa.admin.module.business.hospital.lead.service.LeadFollowService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.module.support.operatelog.annotation.OperateLog;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 线索跟进Controller
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Tag(name = AdminSwaggerTagConst.Business.HOSPITAL_LEAD_FOLLOW)
@RestController
@RequestMapping("/api/lead-follow")
public class LeadFollowController {

    @Resource
    private LeadFollowService leadFollowService;

    @Operation(summary = "根据线索ID查询跟进记录")
    @GetMapping("/queryByLeadId/{leadId}")
    @SaCheckPermission("hospital:lead:follow:query")
    public ResponseDTO<List<LeadFollowVO>> queryByLeadId(@PathVariable Long leadId) {
        return leadFollowService.queryByLeadId(leadId);
    }

    @Operation(summary = "根据线索ID查询跟进记录（前端兼容接口）")
    @GetMapping("/query-by-lead/{leadId}")
    @SaCheckPermission("hospital:lead:follow:query")
    public ResponseDTO<List<LeadFollowVO>> queryByLeadIdCompat(@PathVariable Long leadId) {
        return leadFollowService.queryByLeadId(leadId);
    }

    @Operation(summary = "分页查询跟进记录")
    @PostMapping("/query")
    @SaCheckPermission("hospital:lead:follow:query")
    public ResponseDTO<PageResult<LeadFollowVO>> query(@RequestBody LeadFollowQueryForm queryForm) {
        return leadFollowService.queryPage(queryForm.getLeadId(), queryForm.getEmployeeId(),
                queryForm.getPageNum().intValue(), queryForm.getPageSize().intValue());
    }

    @Operation(summary = "分页查询跟进记录(GET)")
    @GetMapping("/queryPage")
    @SaCheckPermission("hospital:lead:follow:query")
    public ResponseDTO<PageResult<LeadFollowVO>> queryPage(
            @RequestParam(required = false) Long leadId,
            @RequestParam(required = false) Long employeeId,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        return leadFollowService.queryPage(leadId, employeeId, pageNum, pageSize);
    }

    @Operation(summary = "添加跟进记录")
    @PostMapping("/add")
    @SaCheckPermission("hospital:lead:follow:add")
    @OperateLog
    public ResponseDTO<String> add(@RequestBody @Valid LeadFollowAddForm addForm) {
        return leadFollowService.add(addForm);
    }

    @Operation(summary = "添加增强跟进记录（支持后续操作）")
    @PostMapping("/add-enhanced")
    @SaCheckPermission("hospital:lead:follow:add")
    @OperateLog
    public ResponseDTO<String> addEnhanced(@RequestBody @Valid LeadFollowEnhancedAddForm enhancedForm) {
        return leadFollowService.addEnhanced(enhancedForm);
    }

    @Operation(summary = "更新跟进记录")
    @PostMapping("/update")
    @SaCheckPermission("hospital:lead:follow:update")
    @OperateLog
    public ResponseDTO<String> update(@RequestBody @Valid LeadFollowUpdateForm updateForm) {
        return leadFollowService.update(updateForm);
    }

    @Operation(summary = "删除跟进记录")
    @GetMapping("/delete/{followId}")
    @SaCheckPermission("hospital:lead:follow:delete")
    @OperateLog
    public ResponseDTO<String> delete(@PathVariable Long followId) {
        return leadFollowService.delete(followId);
    }

    @Operation(summary = "批量删除跟进记录")
    @PostMapping("/batch-delete")
    @SaCheckPermission("hospital:lead:follow:delete")
    @OperateLog
    public ResponseDTO<String> batchDelete(@RequestBody List<Long> followIds) {
        return leadFollowService.batchDelete(followIds);
    }

    @Operation(summary = "获取我的跟进统计")
    @GetMapping("/getMyFollowCount")
    @SaCheckPermission("hospital:lead:follow:query")
    public ResponseDTO<Integer> getMyFollowCount() {
        return leadFollowService.getMyFollowCount();
    }

    @Operation(summary = "获取需要跟进提醒的记录")
    @GetMapping("/getNeedReminder")
    @SaCheckPermission("hospital:lead:follow:query")
    public ResponseDTO<List<LeadFollowVO>> getNeedReminder() {
        return leadFollowService.getNeedReminder();
    }
}

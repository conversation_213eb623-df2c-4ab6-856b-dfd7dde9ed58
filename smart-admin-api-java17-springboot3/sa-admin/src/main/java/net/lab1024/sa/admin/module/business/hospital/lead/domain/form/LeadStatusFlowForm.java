package net.lab1024.sa.admin.module.business.hospital.lead.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 线索状态流转表单
 * 
 * <AUTHOR>
 * @Date 2025-07-22
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@Schema(description = "线索状态流转表单")
public class LeadStatusFlowForm {

    @Schema(description = "线索ID")
    @NotNull(message = "线索ID不能为空")
    private Long leadId;

    @Schema(description = "目标状态：1新线索 2跟进中 3已预约 4已到院 5已转化 6已关闭")
    @NotNull(message = "目标状态不能为空")
    private Integer targetStatus;

    @Schema(description = "备注")
    @Length(max = 500, message = "备注长度不能超过500字符")
    private String remark;
}

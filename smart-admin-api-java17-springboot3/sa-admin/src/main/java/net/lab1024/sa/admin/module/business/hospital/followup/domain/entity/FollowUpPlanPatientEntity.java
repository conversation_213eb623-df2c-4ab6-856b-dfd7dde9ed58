package net.lab1024.sa.admin.module.business.hospital.followup.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 回访计划患者关联实体类
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@TableName("t_follow_up_plan_patient")
public class FollowUpPlanPatientEntity {

    /**
     * 关联ID
     */
    @TableId(type = IdType.AUTO)
    private Long relationId;

    /**
     * 计划ID
     */
    private Long planId;

    /**
     * 患者ID
     */
    private Long patientId;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 患者电话
     */
    private String patientPhone;

    /**
     * 分配医生ID
     */
    private Long assignedDoctorId;

    /**
     * 分配医生姓名
     */
    private String assignedDoctorName;

    /**
     * 科室ID
     */
    private Long departmentId;

    /**
     * 科室名称
     */
    private String departmentName;

    /**
     * 到诊日期
     */
    private LocalDate visitDate;

    /**
     * 诊断状态
     */
    private Integer diagnosisStatus;

    /**
     * 患者标签
     */
    private String patientTags;

    /**
     * 纳入原因
     */
    private String inclusionReason;

    /**
     * 执行状态：1-待执行，2-执行中，3-已完成，4-已跳过，5-执行失败
     */
    private Integer executionStatus;

    /**
     * 计划执行时间
     */
    private LocalDateTime scheduledTime;

    /**
     * 实际执行时间
     */
    private LocalDateTime actualExecutionTime;

    /**
     * 关联的回访记录ID
     */
    private Long followUpRecordId;

    /**
     * 优先级：1-高，2-中，3-低
     */
    private Integer priorityLevel;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    private Integer maxRetryCount;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志：0-未删除，1-已删除
     */
    private Boolean deletedFlag;

    /**
     * 创建人ID
     */
    private Long createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    private Long updateUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}

package net.lab1024.sa.admin.module.business.hospital.appointment.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 预约实体类
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@TableName("t_appointment")
public class AppointmentEntity {

    /**
     * 预约ID
     */
    @TableId(type = IdType.AUTO)
    private Long appointmentId;

    /**
     * 预约编号
     */
    private String appointmentNo;

    /**
     * 关联线索ID
     */
    private Long leadId;

    /**
     * 关联客户ID
     */
    private Long customerId;

    /**
     * 客户姓名
     */
    private String customerName;

    /**
     * 客户电话
     */
    private String customerPhone;

    /**
     * 微信号
     */
    private String wechat;

    /**
     * 性别：1-男，2-女
     */
    private Integer gender;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 症状描述
     */
    private String symptom;

    /**
     * 地区
     */
    private String region;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 医生ID
     */
    private Long doctorId;

    /**
     * 医生姓名
     */
    private String doctorName;

    /**
     * 分配员工ID
     */
    private Long assignedEmployeeId;

    /**
     * 预约日期
     */
    private LocalDate appointmentDate;

    /**
     * 预约时间
     */
    private LocalTime appointmentTime;

    /**
     * 预约状态：1-待确认，2-已确认，3-已完成，4-已取消，5-已改期
     */
    private Integer appointmentStatus;

    /**
     * 取消原因
     */
    private String cancelReason;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标志：0-未删除，1-已删除
     */
    private Boolean deletedFlag;

    /**
     * 创建人ID
     */
    private Long createUserId;

    /**
     * 创建人姓名
     */
    private String createName;

    /**
     * 更新人ID
     */
    private Long updateUserId;

    /**
     * 更新人姓名
     */
    private String updateName;
}

package net.lab1024.sa.admin.module.business.hospital.appointment.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.domain.PageParam;

import java.time.LocalDate;

/**
 * 医生排班查询表单
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class DoctorScheduleQueryForm extends PageParam {

    @Schema(description = "医生ID")
    private Long doctorId;

    @Schema(description = "排班日期-开始")
    private LocalDate scheduleDateStart;

    @Schema(description = "排班日期-结束")
    private LocalDate scheduleDateEnd;

    @Schema(description = "状态：1-正常，2-暂停")
    private Integer status;

    @Schema(hidden = true)
    private Boolean deletedFlag;
}

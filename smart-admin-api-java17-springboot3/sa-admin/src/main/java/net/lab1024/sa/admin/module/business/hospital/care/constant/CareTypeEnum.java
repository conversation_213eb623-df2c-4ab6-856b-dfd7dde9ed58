package net.lab1024.sa.admin.module.business.hospital.care.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 关怀类型枚举
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Getter
@AllArgsConstructor
public enum CareTypeEnum implements BaseEnum {

    /**
     * 出院关怀
     */
    DISCHARGE_CARE(1, "出院关怀"),

    /**
     * 复诊提醒
     */
    FOLLOW_UP_REMINDER(2, "复诊提醒"),

    /**
     * 生日关怀
     */
    BIRTHDAY_CARE(3, "生日关怀"),

    /**
     * 节日关怀
     */
    HOLIDAY_CARE(4, "节日关怀"),

    /**
     * 满意度调查
     */
    SATISFACTION_SURVEY(5, "满意度调查");

    private final Integer value;

    private final String desc;

    /**
     * 根据值获取枚举
     */
    public static CareTypeEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (CareTypeEnum typeEnum : values()) {
            if (typeEnum.getValue().equals(value)) {
                return typeEnum;
            }
        }
        return null;
    }
}

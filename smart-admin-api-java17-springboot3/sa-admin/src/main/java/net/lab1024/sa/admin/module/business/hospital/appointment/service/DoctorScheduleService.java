package net.lab1024.sa.admin.module.business.hospital.appointment.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.hospital.appointment.dao.DoctorScheduleDao;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.entity.DoctorScheduleEntity;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.form.DoctorScheduleAddForm;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.form.DoctorScheduleBatchAddForm;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.form.DoctorScheduleQueryForm;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.form.DoctorScheduleUpdateForm;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.vo.AvailableTimeVO;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.vo.DoctorScheduleVO;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.module.support.datatracer.constant.DataTracerTypeEnum;
import net.lab1024.sa.base.module.support.datatracer.service.DataTracerService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 医生排班Service
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Slf4j
@Service
public class DoctorScheduleService {

    @Resource
    private DoctorScheduleDao doctorScheduleDao;

    @Resource
    private DataTracerService dataTracerService;

    /**
     * 分页查询医生排班
     */
    public ResponseDTO<PageResult<DoctorScheduleVO>> queryPage(DoctorScheduleQueryForm queryForm) {
        queryForm.setDeletedFlag(Boolean.FALSE);
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<DoctorScheduleVO> scheduleList = doctorScheduleDao.queryPage(page, queryForm);
        PageResult<DoctorScheduleVO> pageResult = SmartPageUtil.convert2PageResult(page, scheduleList);
        return ResponseDTO.ok(pageResult);
    }

    /**
     * 添加医生排班
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> add(DoctorScheduleAddForm addForm) {
        // 校验时间合理性
        if (!addForm.getStartTime().isBefore(addForm.getEndTime())) {
            return ResponseDTO.userErrorParam("开始时间必须早于结束时间");
        }

        // 检查时间冲突
        DoctorScheduleEntity conflictSchedule = doctorScheduleDao.checkTimeConflict(
                addForm.getDoctorId(), addForm.getScheduleDate(), 
                addForm.getStartTime(), addForm.getEndTime(), null);
        if (conflictSchedule != null) {
            return ResponseDTO.userErrorParam("该时间段与已有排班冲突");
        }

        DoctorScheduleEntity scheduleEntity = SmartBeanUtil.copy(addForm, DoctorScheduleEntity.class);
        scheduleEntity.setCurrentAppointments(0);
        scheduleEntity.setDeletedFlag(Boolean.FALSE);
        scheduleEntity.setCreateTime(LocalDateTime.now());
        scheduleEntity.setUpdateTime(LocalDateTime.now());

        // 如果没有设置状态，默认为正常
        if (scheduleEntity.getScheduleStatus() == null) {
            scheduleEntity.setScheduleStatus(1);
        }

        doctorScheduleDao.insert(scheduleEntity);

        // 记录数据变动
        dataTracerService.insert(scheduleEntity.getScheduleId(), DataTracerTypeEnum.DOCTOR_SCHEDULE);

        return ResponseDTO.ok();
    }

    /**
     * 更新医生排班
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> update(DoctorScheduleUpdateForm updateForm) {
        // 校验排班是否存在
        DoctorScheduleEntity existSchedule = doctorScheduleDao.selectById(updateForm.getScheduleId());
        if (existSchedule == null || existSchedule.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("排班不存在");
        }

        // 校验时间合理性
        if (!updateForm.getStartTime().isBefore(updateForm.getEndTime())) {
            return ResponseDTO.userErrorParam("开始时间必须早于结束时间");
        }

        // 检查时间冲突
        DoctorScheduleEntity conflictSchedule = doctorScheduleDao.checkTimeConflict(
                updateForm.getDoctorId(), updateForm.getScheduleDate(),
                updateForm.getStartTime(), updateForm.getEndTime(), updateForm.getScheduleId());
        if (conflictSchedule != null) {
            return ResponseDTO.userErrorParam("该时间段与已有排班冲突");
        }

        // 如果减少了最大预约数，需要检查是否小于当前预约数
        if (updateForm.getMaxAppointments() < existSchedule.getCurrentAppointments()) {
            return ResponseDTO.userErrorParam("最大预约数不能小于当前预约数(" + existSchedule.getCurrentAppointments() + ")");
        }

        DoctorScheduleEntity scheduleEntity = SmartBeanUtil.copy(updateForm, DoctorScheduleEntity.class);
        scheduleEntity.setCurrentAppointments(existSchedule.getCurrentAppointments());
        scheduleEntity.setUpdateTime(LocalDateTime.now());
        doctorScheduleDao.updateById(scheduleEntity);

        // 获取原实体用于数据变动记录
        DoctorScheduleEntity originEntity = doctorScheduleDao.selectById(scheduleEntity.getScheduleId());

        // 记录数据变动
        dataTracerService.update(scheduleEntity.getScheduleId(), DataTracerTypeEnum.DOCTOR_SCHEDULE, originEntity, scheduleEntity);

        return ResponseDTO.ok();
    }

    /**
     * 删除医生排班
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> delete(Long scheduleId) {
        DoctorScheduleEntity scheduleEntity = doctorScheduleDao.selectById(scheduleId);
        if (scheduleEntity == null || scheduleEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("排班不存在");
        }

        // 检查是否有预约
        if (scheduleEntity.getCurrentAppointments() > 0) {
            return ResponseDTO.userErrorParam("该排班已有预约，无法删除");
        }

        scheduleEntity.setDeletedFlag(Boolean.TRUE);
        scheduleEntity.setUpdateTime(LocalDateTime.now());
        doctorScheduleDao.updateById(scheduleEntity);

        // 记录数据变动
        dataTracerService.delete(scheduleId, DataTracerTypeEnum.DOCTOR_SCHEDULE);

        return ResponseDTO.ok();
    }

    /**
     * 批量添加排班
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> batchAdd(DoctorScheduleBatchAddForm batchAddForm) {
        // 校验时间合理性
        if (!batchAddForm.getStartTime().isBefore(batchAddForm.getEndTime())) {
            return ResponseDTO.userErrorParam("开始时间必须早于结束时间");
        }

        // 如果需要覆盖，先删除已存在的排班
        if (Boolean.TRUE.equals(batchAddForm.getOverwrite())) {
            doctorScheduleDao.deleteByCondition(batchAddForm.getDoctorId(), 
                    batchAddForm.getScheduleDates(), 
                    batchAddForm.getStartTime(), 
                    batchAddForm.getEndTime());
        } else {
            // 检查是否有冲突
            for (LocalDate scheduleDate : batchAddForm.getScheduleDates()) {
                DoctorScheduleEntity conflictSchedule = doctorScheduleDao.checkTimeConflict(
                        batchAddForm.getDoctorId(), scheduleDate,
                        batchAddForm.getStartTime(), batchAddForm.getEndTime(), null);
                if (conflictSchedule != null) {
                    return ResponseDTO.userErrorParam("日期 " + scheduleDate + " 的时间段与已有排班冲突");
                }
            }
        }

        // 批量创建排班
        List<DoctorScheduleEntity> scheduleList = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        
        for (LocalDate scheduleDate : batchAddForm.getScheduleDates()) {
            DoctorScheduleEntity scheduleEntity = new DoctorScheduleEntity();
            scheduleEntity.setDoctorId(batchAddForm.getDoctorId());
            scheduleEntity.setScheduleDate(scheduleDate);
            scheduleEntity.setStartTime(batchAddForm.getStartTime());
            scheduleEntity.setEndTime(batchAddForm.getEndTime());
            scheduleEntity.setMaxAppointments(batchAddForm.getMaxAppointments());
            scheduleEntity.setCurrentAppointments(0);
            scheduleEntity.setScheduleStatus(batchAddForm.getStatus() != null ? batchAddForm.getStatus() : 1);
            scheduleEntity.setDeletedFlag(Boolean.FALSE);
            scheduleEntity.setCreateTime(now);
            scheduleEntity.setUpdateTime(now);
            
            scheduleList.add(scheduleEntity);
        }

        doctorScheduleDao.batchInsert(scheduleList);

        // 记录数据变动
        for (DoctorScheduleEntity schedule : scheduleList) {
            dataTracerService.insert(schedule.getScheduleId(), DataTracerTypeEnum.DOCTOR_SCHEDULE);
        }

        return ResponseDTO.ok("成功添加 " + scheduleList.size() + " 条排班记录");
    }

    /**
     * 获取医生可预约时间
     */
    public ResponseDTO<List<AvailableTimeVO>> getAvailableTime(Long doctorId, LocalDate scheduleDate) {
        List<AvailableTimeVO> availableTimeList = doctorScheduleDao.getAvailableTime(doctorId, scheduleDate);
        return ResponseDTO.ok(availableTimeList);
    }

    /**
     * 批量删除排班
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> batchDelete(List<Long> scheduleIdList) {
        if (scheduleIdList == null || scheduleIdList.isEmpty()) {
            return ResponseDTO.userErrorParam("排班ID列表不能为空");
        }

        // 检查是否有预约
        for (Long scheduleId : scheduleIdList) {
            DoctorScheduleEntity scheduleEntity = doctorScheduleDao.selectById(scheduleId);
            if (scheduleEntity != null && scheduleEntity.getCurrentAppointments() > 0) {
                return ResponseDTO.userErrorParam("部分排班已有预约，无法删除");
            }
        }

        doctorScheduleDao.batchUpdateDeleted(scheduleIdList, Boolean.TRUE);

        // 记录数据变动
        for (Long scheduleId : scheduleIdList) {
            dataTracerService.delete(scheduleId, DataTracerTypeEnum.DOCTOR_SCHEDULE);
        }

        return ResponseDTO.ok();
    }
}

package net.lab1024.sa.admin.module.business.hospital.followup.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.domain.ValidateList;
import net.lab1024.sa.base.common.swagger.SchemaEnum;
import net.lab1024.sa.base.common.validator.enumeration.CheckEnum;
import net.lab1024.sa.admin.module.business.hospital.followup.constant.FollowUpPlanTypeEnum;
import net.lab1024.sa.admin.module.business.hospital.followup.constant.TargetPatientTypeEnum;
import net.lab1024.sa.admin.module.business.hospital.followup.constant.FollowUpMethodEnum;
import net.lab1024.sa.admin.module.business.hospital.followup.constant.ExecutionFrequencyEnum;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * 回访计划增强版添加表单
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@Schema(description = "回访计划增强版添加表单")
public class FollowUpPlanEnhancedAddForm {

    @Schema(description = "计划名称")
    @NotBlank(message = "计划名称不能为空")
    @Size(max = 100, message = "计划名称长度不能超过100个字符")
    private String planName;

    @Schema(description = "计划描述")
    @Size(max = 1000, message = "计划描述长度不能超过1000个字符")
    private String planDescription;

    @Schema(description = "计划类型：1-定期回访，2-节日回访，3-特殊回访，4-术后回访，5-用药跟进")
    @SchemaEnum(FollowUpPlanTypeEnum.class)
    @CheckEnum(value = FollowUpPlanTypeEnum.class, message = "计划类型错误", required = true)
    @NotNull(message = "计划类型不能为空")
    private Integer planType;

    @Schema(description = "目标患者类型：1-所有已分配医生患者，2-指定医生患者，3-指定科室患者，4-指定标签患者")
    @SchemaEnum(TargetPatientTypeEnum.class)
    @CheckEnum(value = TargetPatientTypeEnum.class, message = "目标患者类型错误", required = true)
    @NotNull(message = "目标患者类型不能为空")
    private Integer targetPatientType;

    @Schema(description = "目标医生ID列表")
    private ValidateList<Long> targetDoctorIds;

    @Schema(description = "目标科室ID列表")
    private ValidateList<Long> targetDepartmentIds;

    @Schema(description = "目标患者标签")
    @Size(max = 500, message = "目标患者标签长度不能超过500个字符")
    private String targetPatientTags;

    @Schema(description = "到诊日期范围开始")
    private LocalDate visitDateRangeStart;

    @Schema(description = "到诊日期范围结束")
    private LocalDate visitDateRangeEnd;

    @Schema(description = "回访方式：1-电话，2-微信，3-短信，4-邮件，5-上门")
    @SchemaEnum(FollowUpMethodEnum.class)
    @CheckEnum(value = FollowUpMethodEnum.class, message = "回访方式错误", required = true)
    @NotNull(message = "回访方式不能为空")
    private Integer followUpMethod;

    @Schema(description = "回访内容模板")
    @NotBlank(message = "回访内容模板不能为空")
    @Size(max = 2000, message = "回访内容模板长度不能超过2000个字符")
    private String followUpContentTemplate;

    @Schema(description = "回访问题列表")
    private List<FollowUpQuestionForm> followUpQuestions;

    @Schema(description = "计划开始日期")
    @NotNull(message = "计划开始日期不能为空")
    private LocalDate planStartDate;

    @Schema(description = "计划结束日期")
    private LocalDate planEndDate;

    @Schema(description = "执行频率：1-每日，2-每周，3-每月，4-每季度，5-每年，6-一次性")
    @SchemaEnum(ExecutionFrequencyEnum.class)
    @CheckEnum(value = ExecutionFrequencyEnum.class, message = "执行频率错误", required = true)
    @NotNull(message = "执行频率不能为空")
    private Integer executionFrequency;

    @Schema(description = "执行时间")
    private LocalTime executionTime;

    @Schema(description = "执行日期(周几或月几号)")
    @Size(max = 20, message = "执行日期长度不能超过20个字符")
    private String executionDays;

    @Schema(description = "到诊后延迟天数")
    private Integer delayDaysAfterVisit;

    @Schema(description = "优先级：1-高，2-中，3-低")
    private Integer priorityLevel;

    @Schema(description = "是否自动生成回访记录：false-否，true-是")
    private Boolean autoGenerateRecords;

    @Schema(description = "负责人ID")
    @NotNull(message = "负责人不能为空")
    private Long responsibleUserId;

    @Schema(description = "预期患者数量")
    private Integer expectedPatientCount;

    @Schema(description = "备注")
    @Size(max = 1000, message = "备注长度不能超过1000个字符")
    private String remark;

    /**
     * 回访问题表单
     */
    @Data
    @Schema(description = "回访问题表单")
    public static class FollowUpQuestionForm {

        @Schema(description = "问题内容")
        @NotBlank(message = "问题内容不能为空")
        @Size(max = 500, message = "问题内容长度不能超过500个字符")
        private String question;

        @Schema(description = "问题类型：1-单选，2-多选，3-文本")
        @NotNull(message = "问题类型不能为空")
        private Integer questionType;

        @Schema(description = "选项列表(单选/多选时使用)")
        private List<String> options;

        @Schema(description = "是否必填")
        private Boolean required;

        @Schema(description = "排序")
        private Integer sortOrder;
    }
}

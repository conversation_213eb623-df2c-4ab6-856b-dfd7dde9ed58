package net.lab1024.sa.admin.module.business.hospital.appointment.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.admin.module.business.hospital.appointment.constant.AppointmentStatusEnum;
import net.lab1024.sa.base.common.domain.PageParam;
import net.lab1024.sa.base.common.swagger.SchemaEnum;
import net.lab1024.sa.base.common.validator.enumeration.CheckEnum;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 预约查询表单
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class AppointmentQueryForm extends PageParam {

    @Schema(description = "客户姓名")
    @Length(max = 100, message = "客户姓名最多100字符")
    private String customerName;

    @Schema(description = "客户电话")
    @Length(max = 20, message = "客户电话最多20字符")
    private String customerPhone;



    @Schema(description = "预约状态：1-未到诊，2-已到诊")
    @SchemaEnum(AppointmentStatusEnum.class)
    @CheckEnum(message = "预约状态错误", value = AppointmentStatusEnum.class, required = false)
    private Integer appointmentStatus;

    @Schema(description = "预约日期-开始")
    private LocalDate appointmentDateStart;

    @Schema(description = "预约日期-结束")
    private LocalDate appointmentDateEnd;

    @Schema(description = "创建时间-开始")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间-结束")
    private LocalDateTime createTimeEnd;

    @Schema(description = "搜索关键词")
    @Length(max = 50, message = "搜索关键词最多50字符")
    private String searchWord;

    @Schema(description = "筛选类型：today-预计今日到院，three_days-预计3日内到院，month_arrived-本月已到院")
    private String filterType;

    @Schema(hidden = true)
    private Boolean deletedFlag;
}

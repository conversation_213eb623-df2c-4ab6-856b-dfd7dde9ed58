package net.lab1024.sa.admin.module.business.hospital.finance.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 收费记录添加表单
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class ChargeRecordAddForm {

    @Schema(description = "客户ID")
    private Long customerId;

    @Schema(description = "客户姓名", required = true)
    @NotBlank(message = "客户姓名不能为空")
    @Length(max = 50, message = "客户姓名最多50字符")
    private String customerName;

    @Schema(description = "客户电话", required = true)
    @NotBlank(message = "客户电话不能为空")
    @Length(max = 20, message = "客户电话最多20字符")
    private String customerPhone;

    @Schema(description = "预约ID")
    private Long appointmentId;

    @Schema(description = "病历ID")
    private Long recordId;

    @Schema(description = "项目ID", required = true)
    @NotNull(message = "项目ID不能为空")
    private Long projectId;

    @Schema(description = "项目名称", required = true)
    @NotBlank(message = "项目名称不能为空")
    @Length(max = 100, message = "项目名称最多100字符")
    private String projectName;

    @Schema(description = "项目分类")
    @Length(max = 50, message = "项目分类最多50字符")
    private String projectCategory;

    @Schema(description = "标准价格", required = true)
    @NotNull(message = "标准价格不能为空")
    private BigDecimal standardPrice;

    @Schema(description = "实际收费金额", required = true)
    @NotNull(message = "实际收费金额不能为空")
    private BigDecimal chargeAmount;

    @Schema(description = "优惠金额")
    private BigDecimal discountAmount;

    @Schema(description = "优惠原因")
    @Length(max = 200, message = "优惠原因最多200字符")
    private String discountReason;

    @Schema(description = "收费日期", required = true)
    @NotNull(message = "收费日期不能为空")
    private LocalDate chargeDate;

    @Schema(description = "收费状态：1-待收费，2-已收费，3-部分收费，4-已退费")
    private Integer chargeStatus;

    @Schema(description = "支付方式：1-现金，2-微信，3-支付宝，4-银行卡，5-组合支付")
    private Integer paymentMethod;

    @Schema(description = "支付详情（JSON格式）")
    private String paymentDetails;

    @Schema(description = "收费员工ID")
    private Long chargeEmployeeId;

    @Schema(description = "收费员工姓名")
    @Length(max = 50, message = "收费员工姓名最多50字符")
    private String chargeEmployeeName;

    @Schema(description = "收费科室ID")
    private Long chargeDepartmentId;

    @Schema(description = "收费科室名称")
    @Length(max = 50, message = "收费科室名称最多50字符")
    private String chargeDepartmentName;

    @Schema(description = "发票号码")
    @Length(max = 50, message = "发票号码最多50字符")
    private String invoiceNo;

    @Schema(description = "是否开具发票")
    private Boolean invoiceFlag;

    @Schema(description = "备注")
    private String remark;
}

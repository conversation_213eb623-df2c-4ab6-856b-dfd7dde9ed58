package net.lab1024.sa.admin.module.business.hospital.customer.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 客户标签VO
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class CustomerTagVO {

    @Schema(description = "标签ID")
    private Long tagId;

    @Schema(description = "标签名称")
    private String tagName;

    @Schema(description = "标签描述")
    private String tagDescription;

    @Schema(description = "标签颜色")
    private String tagColor;

    @Schema(description = "标签分类ID")
    private Long categoryId;

    @Schema(description = "标签分类名称")
    private String categoryName;

    @Schema(description = "标签类型：1-手动标签，2-自动标签")
    private Integer tagType;

    @Schema(description = "标签类型名称")
    private String tagTypeName;

    @Schema(description = "自动打标规则")
    private String autoRules;

    @Schema(description = "标签状态：1-启用，0-禁用")
    private Integer tagStatus;

    @Schema(description = "标签状态名称")
    private String tagStatusName;

    @Schema(description = "使用次数")
    private Integer usageCount;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人姓名")
    private String createUserName;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}

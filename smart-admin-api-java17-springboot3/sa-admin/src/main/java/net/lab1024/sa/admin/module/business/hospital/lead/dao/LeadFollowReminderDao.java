package net.lab1024.sa.admin.module.business.hospital.lead.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.entity.LeadFollowReminderEntity;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadFollowReminderVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 线索跟进提醒DAO
 * 
 * <AUTHOR>
 * @Date 2025-07-22
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Mapper
public interface LeadFollowReminderDao extends BaseMapper<LeadFollowReminderEntity> {

    /**
     * 根据用户ID查询跟进提醒
     * 
     * @param userId 用户ID
     * @return 提醒列表
     */
    List<LeadFollowReminderVO> selectByUserId(@Param("userId") Long userId);

    /**
     * 查询待提醒的记录
     * 
     * @param currentTime 当前时间
     * @return 待提醒记录列表
     */
    List<LeadFollowReminderVO> selectPendingReminders(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 更新提醒状态
     * 
     * @param reminderId 提醒ID
     * @param status 状态
     * @param sentTime 发送时间
     */
    void updateReminderStatus(@Param("reminderId") Long reminderId, 
                             @Param("status") Integer status, 
                             @Param("sentTime") LocalDateTime sentTime);

    /**
     * 完成线索相关的所有提醒
     * 
     * @param leadId 线索ID
     * @param completeTime 完成时间
     */
    void completeRemindersByLeadId(@Param("leadId") Long leadId, 
                                  @Param("completeTime") LocalDateTime completeTime);

    /**
     * 查询用户今日待跟进的线索
     * 
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 待跟进线索列表
     */
    List<LeadFollowReminderVO> selectTodayFollowReminders(@Param("userId") Long userId,
                                                         @Param("startTime") LocalDateTime startTime,
                                                         @Param("endTime") LocalDateTime endTime);

    /**
     * 查询逾期未跟进的线索
     * 
     * @param userId 用户ID
     * @param currentTime 当前时间
     * @return 逾期线索列表
     */
    List<LeadFollowReminderVO> selectOverdueReminders(@Param("userId") Long userId,
                                                     @Param("currentTime") LocalDateTime currentTime);
}

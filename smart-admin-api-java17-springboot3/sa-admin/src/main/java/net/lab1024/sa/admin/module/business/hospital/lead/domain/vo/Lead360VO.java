package net.lab1024.sa.admin.module.business.hospital.lead.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 线索360度视图VO
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@Schema(description = "线索360度视图")
public class Lead360VO {

    @Schema(description = "基础信息")
    private LeadBasicInfoVO basicInfo;

    @Schema(description = "评分信息")
    private LeadScoreVO scoreInfo;

    @Schema(description = "统计信息")
    private LeadStatisticsVO statistics;

    @Schema(description = "客户旅程时间线")
    private List<CustomerJourneyEventVO> timeline;

    @Schema(description = "跟进记录")
    private List<LeadFollowVO> followRecords;

    @Schema(description = "聊天记录")
    private List<ChatRecordVO> chatRecords;

    @Schema(description = "预约记录")
    private List<AppointmentSimpleVO> appointments;

    @Schema(description = "智能建议")
    private List<LeadSuggestionVO> suggestions;

    @Schema(description = "快速笔记")
    private String quickNote;

    /**
     * 线索基础信息VO
     */
    @Data
    @Schema(description = "线索基础信息")
    public static class LeadBasicInfoVO {
        @Schema(description = "线索ID")
        private Long leadId;

        @Schema(description = "线索来源")
        private String leadSource;

        @Schema(description = "线索来源名称")
        private String leadSourceName;

        @Schema(description = "客户姓名")
        private String customerName;

        @Schema(description = "客户电话")
        private String customerPhone;

        @Schema(description = "微信号")
        private String customerWechat;

        @Schema(description = "性别")
        private Integer gender;

        @Schema(description = "性别名称")
        private String genderName;

        @Schema(description = "年龄")
        private Integer age;

        @Schema(description = "症状")
        private String symptom;

        @Schema(description = "地区")
        private String region;

        @Schema(description = "地区名称")
        private String regionName;

        @Schema(description = "线索状态")
        private Integer leadStatus;

        @Schema(description = "线索状态名称")
        private String leadStatusName;

        @Schema(description = "线索质量")
        private Integer leadQuality;

        @Schema(description = "线索质量名称")
        private String leadQualityName;

        @Schema(description = "分配员工ID")
        private Long assignedEmployeeId;

        @Schema(description = "分配员工姓名")
        private String assignedEmployeeName;

        @Schema(description = "备注")
        private String remark;

        @Schema(description = "创建时间")
        private LocalDateTime createTime;

        @Schema(description = "更新时间")
        private LocalDateTime updateTime;
    }

    /**
     * 线索评分VO
     */
    @Data
    @Schema(description = "线索评分信息")
    public static class LeadScoreVO {
        @Schema(description = "总分")
        private Integer totalScore;

        @Schema(description = "跟进频率得分")
        private Integer followScore;

        @Schema(description = "响应度得分")
        private Integer responseScore;

        @Schema(description = "意向度得分")
        private Integer intentionScore;

        @Schema(description = "转化概率")
        private Double conversionProbability;

        @Schema(description = "最后计算时间")
        private LocalDateTime lastCalculateTime;
    }

    /**
     * 线索统计VO
     */
    @Data
    @Schema(description = "线索统计信息")
    public static class LeadStatisticsVO {
        @Schema(description = "跟进次数")
        private Integer followCount;

        @Schema(description = "响应次数")
        private Integer responseCount;

        @Schema(description = "响应率")
        private Double responseRate;

        @Schema(description = "预约次数")
        private Integer appointmentCount;

        @Schema(description = "最后沟通时间")
        private LocalDateTime lastCommunicationTime;

        @Schema(description = "首次跟进时间")
        private LocalDateTime firstFollowTime;

        @Schema(description = "平均响应时间（分钟）")
        private Integer avgResponseTime;
    }

    /**
     * 客户旅程事件VO
     */
    @Data
    @Schema(description = "客户旅程事件")
    public static class CustomerJourneyEventVO {
        @Schema(description = "事件ID")
        private String eventId;

        @Schema(description = "事件类型")
        private String eventType;

        @Schema(description = "事件时间")
        private LocalDateTime eventTime;

        @Schema(description = "事件标题")
        private String title;

        @Schema(description = "事件描述")
        private String description;

        @Schema(description = "图标")
        private String icon;

        @Schema(description = "颜色")
        private String color;

        @Schema(description = "附件")
        private List<AttachmentVO> attachments;
    }

    /**
     * 聊天记录VO
     */
    @Data
    @Schema(description = "聊天记录")
    public static class ChatRecordVO {
        @Schema(description = "时间")
        private LocalDateTime time;

        @Schema(description = "发送方")
        private String sender;

        @Schema(description = "消息内容")
        private String content;

        @Schema(description = "消息类型：customer-客户，staff-员工")
        private String type;
    }

    /**
     * 预约简要信息VO
     */
    @Data
    @Schema(description = "预约简要信息")
    public static class AppointmentSimpleVO {
        @Schema(description = "预约ID")
        private Long appointmentId;

        @Schema(description = "预约编号")
        private String appointmentNo;

        @Schema(description = "预约日期")
        private String appointmentDate;

        @Schema(description = "预约时间")
        private String appointmentTime;

        @Schema(description = "预约状态")
        private Integer appointmentStatus;

        @Schema(description = "预约状态名称")
        private String appointmentStatusName;

        @Schema(description = "医生姓名")
        private String doctorName;

        @Schema(description = "项目名称")
        private String projectName;

        @Schema(description = "创建时间")
        private LocalDateTime createTime;
    }

    /**
     * 线索建议VO
     */
    @Data
    @Schema(description = "线索建议")
    public static class LeadSuggestionVO {
        @Schema(description = "建议ID")
        private Long suggestionId;

        @Schema(description = "建议类型")
        private String suggestionType;

        @Schema(description = "标题")
        private String title;

        @Schema(description = "描述")
        private String description;

        @Schema(description = "优先级")
        private Integer priority;

        @Schema(description = "是否可执行")
        private Boolean actionable;

        @Schema(description = "操作按钮文字")
        private String actionText;

        @Schema(description = "操作数据")
        private String actionData;

        @Schema(description = "图标")
        private String icon;
    }

    /**
     * 附件VO
     */
    @Data
    @Schema(description = "附件信息")
    public static class AttachmentVO {
        @Schema(description = "附件ID")
        private String id;

        @Schema(description = "附件名称")
        private String name;

        @Schema(description = "附件URL")
        private String url;

        @Schema(description = "附件类型")
        private String type;
    }
}

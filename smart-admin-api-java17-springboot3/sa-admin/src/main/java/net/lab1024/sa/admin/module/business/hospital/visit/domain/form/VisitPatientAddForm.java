package net.lab1024.sa.admin.module.business.hospital.visit.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.swagger.SchemaEnum;
import net.lab1024.sa.base.common.validator.enumeration.CheckEnum;
import net.lab1024.sa.admin.module.business.hospital.visit.constant.VisitStatusEnum;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 到诊患者添加表单
 *
 * <AUTHOR>
 * @Date 2025-07-29 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class VisitPatientAddForm {

    @Schema(description = "患者姓名")
    @NotBlank(message = "患者姓名不能为空")
    @Length(max = 50, message = "患者姓名最多50字符")
    private String patientName;

    @Schema(description = "患者电话")
    @NotBlank(message = "患者电话不能为空")
    @Length(max = 20, message = "患者电话最多20字符")
    private String patientPhone;

    @Schema(description = "患者微信")
    @Length(max = 50, message = "患者微信最多50字符")
    private String patientWechat;

    @Schema(description = "患者邮箱")
    @Length(max = 100, message = "患者邮箱最多100字符")
    private String patientEmail;

    @Schema(description = "性别：1-男，2-女")
    @Min(value = 1, message = "性别只能是1或2")
    @Max(value = 2, message = "性别只能是1或2")
    private Integer gender;

    @Schema(description = "年龄")
    @Min(value = 1, message = "年龄必须在1-120之间")
    @Max(value = 120, message = "年龄必须在1-120之间")
    private Integer age;

    @Schema(description = "出生日期")
    private LocalDate birthday;

    @Schema(description = "身份证号")
    @Length(max = 18, message = "身份证号最多18字符")
    private String idCard;

    @Schema(description = "患者地址")
    @Length(max = 200, message = "患者地址最多200字符")
    private String patientAddress;

    @Schema(description = "职业")
    @Length(max = 50, message = "职业最多50字符")
    private String occupation;

    @Schema(description = "到诊状态：1-未到诊，2-已到诊，3-诊疗中，4-已完成，5-已离院")
    @SchemaEnum(VisitStatusEnum.class)
    @CheckEnum(message = "到诊状态错误", value = VisitStatusEnum.class, required = false)
    private Integer visitStatus;

    @Schema(description = "关联预约ID")
    private Long appointmentId;

    @Schema(description = "分配医生ID")
    private Long assignedDoctorId;

    @Schema(description = "分配医生姓名")
    @Length(max = 50, message = "医生姓名最多50字符")
    private String assignedDoctorName;

    @Schema(description = "分配治疗助理ID")
    private Long assignedAssistantId;

    @Schema(description = "分配治疗助理姓名")
    @Length(max = 50, message = "助理姓名最多50字符")
    private String assignedAssistantName;

    @Schema(description = "到诊日期")
    private LocalDate visitDate;

    @Schema(description = "到诊时间")
    private LocalTime visitTime;

    @Schema(description = "登记时间")
    private LocalDateTime registrationTime;

    @Schema(description = "患者来源")
    @Length(max = 100, message = "患者来源最多100字符")
    private String patientSource;

    @Schema(description = "患者等级：1-普通患者，2-VIP患者，3-SVIP患者")
    @Min(value = 1, message = "患者等级只能是1、2或3")
    @Max(value = 3, message = "患者等级只能是1、2或3")
    private Integer patientLevel;

    @Schema(description = "患者标签")
    @Length(max = 500, message = "患者标签最多500字符")
    private String patientTags;

    @Schema(description = "首次就诊日期")
    private LocalDate firstVisitDate;

    @Schema(description = "最后就诊日期")
    private LocalDate lastVisitDate;

    @Schema(description = "总消费金额")
    private BigDecimal totalConsumption;

    @Schema(description = "紧急联系人")
    @Length(max = 50, message = "紧急联系人最多50字符")
    private String emergencyContact;

    @Schema(description = "紧急联系人电话")
    @Length(max = 20, message = "紧急联系人电话最多20字符")
    private String emergencyPhone;

    @Schema(description = "备注")
    @Length(max = 1000, message = "备注最多1000字符")
    private String remark;
}

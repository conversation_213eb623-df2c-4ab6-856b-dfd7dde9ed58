package net.lab1024.sa.admin.module.business.hospital.lead.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 线索质量枚举
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Getter
@AllArgsConstructor
public enum LeadQualityEnum implements BaseEnum {

    /**
     * A级线索
     */
    A_LEVEL(1, "A级"),

    /**
     * B级线索
     */
    B_LEVEL(2, "B级"),

    /**
     * C级线索
     */
    C_LEVEL(3, "C级");

    private final Integer value;

    private final String desc;
}

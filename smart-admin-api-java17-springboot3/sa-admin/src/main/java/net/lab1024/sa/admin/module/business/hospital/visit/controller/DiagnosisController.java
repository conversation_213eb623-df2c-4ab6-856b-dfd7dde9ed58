package net.lab1024.sa.admin.module.business.hospital.visit.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.form.DiagnosisAddForm;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.form.DiagnosisQueryForm;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.DiagnosisVO;
import net.lab1024.sa.admin.module.business.hospital.visit.service.DiagnosisService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.RequestUser;
import net.lab1024.sa.base.common.util.SmartRequestUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 诊断记录控制器
 *
 * <AUTHOR>
 * @Date 2025-07-29
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Tag(name = "诊断记录管理")
@RestController
@RequestMapping("/visit/diagnosis")
public class DiagnosisController {

    @Autowired
    private DiagnosisService diagnosisService;

    @Operation(summary = "分页查询诊断记录")
    @PostMapping("/queryPage")
    public ResponseDTO<PageResult<DiagnosisVO>> queryPage(@RequestBody @Valid DiagnosisQueryForm queryForm) {
        return ResponseDTO.ok(diagnosisService.queryPage(queryForm));
    }

    @Operation(summary = "根据患者ID查询诊断记录")
    @GetMapping("/patient/{patientId}")
    public ResponseDTO<List<DiagnosisVO>> getByPatientId(@PathVariable Long patientId) {
        return diagnosisService.getByPatientId(patientId);
    }

    @Operation(summary = "根据ID查询诊断记录详情")
    @GetMapping("/get/{diagnosisId}")
    public ResponseDTO<DiagnosisVO> getById(@PathVariable Long diagnosisId) {
        return diagnosisService.getById(diagnosisId);
    }

    @Operation(summary = "新增诊断记录")
    @PostMapping("/add")
    public ResponseDTO<Long> add(@RequestBody @Valid DiagnosisAddForm addForm) {
        RequestUser requestUser = SmartRequestUtil.getRequestUser();
        return diagnosisService.add(addForm, requestUser.getUserId(), requestUser.getUserName());
    }

    @Operation(summary = "更新诊断状态")
    @PostMapping("/updateStatus")
    public ResponseDTO<String> updateStatus(@RequestParam Long diagnosisId, @RequestParam Integer diagnosisStatus) {
        RequestUser requestUser = SmartRequestUtil.getRequestUser();
        return diagnosisService.updateStatus(diagnosisId, diagnosisStatus, requestUser.getUserId(), requestUser.getUserName());
    }

    @Operation(summary = "删除诊断记录")
    @PostMapping("/delete/{diagnosisId}")
    public ResponseDTO<String> delete(@PathVariable Long diagnosisId) {
        RequestUser requestUser = SmartRequestUtil.getRequestUser();
        return diagnosisService.delete(diagnosisId, requestUser.getUserId(), requestUser.getUserName());
    }
}

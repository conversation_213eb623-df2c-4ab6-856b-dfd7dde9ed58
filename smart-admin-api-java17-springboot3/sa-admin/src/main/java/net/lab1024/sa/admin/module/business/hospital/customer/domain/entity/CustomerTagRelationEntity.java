package net.lab1024.sa.admin.module.business.hospital.customer.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 客户标签关联实体类
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@TableName("t_customer_tag_relation")
public class CustomerTagRelationEntity {

    /**
     * 关联ID
     */
    @TableId(type = IdType.AUTO)
    private Long relationId;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 标签ID
     */
    private Long tagId;

    /**
     * 标签名称（冗余字段，便于查询）
     */
    private String tagName;

    /**
     * 标签颜色（冗余字段，便于显示）
     */
    private String tagColor;

    /**
     * 打标方式：1-手动打标，2-自动打标
     */
    private Integer tagMethod;

    /**
     * 打标原因
     */
    private String tagReason;

    /**
     * 删除标识
     */
    private Boolean deletedFlag;

    /**
     * 创建人ID
     */
    private Long createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    private Long updateUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}

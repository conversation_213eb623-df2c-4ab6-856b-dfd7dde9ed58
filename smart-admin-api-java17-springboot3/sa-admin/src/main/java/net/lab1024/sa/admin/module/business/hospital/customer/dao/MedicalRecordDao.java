package net.lab1024.sa.admin.module.business.hospital.customer.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.hospital.customer.domain.entity.MedicalRecordEntity;
import net.lab1024.sa.admin.module.business.hospital.customer.domain.form.MedicalRecordQueryForm;
import net.lab1024.sa.admin.module.business.hospital.customer.domain.vo.MedicalRecordVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;

/**
 * 病历DAO
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Mapper
@Component
public interface MedicalRecordDao extends BaseMapper<MedicalRecordEntity> {

    /**
     * 分页查询病历
     *
     * @param page 分页参数
     * @param queryForm 查询条件
     * @return 病历列表
     */
    List<MedicalRecordVO> queryPage(Page page, @Param("query") MedicalRecordQueryForm queryForm);

    /**
     * 根据客户ID查询病历
     *
     * @param customerId 客户ID
     * @return 病历列表
     */
    List<MedicalRecordVO> selectByCustomerId(@Param("customerId") Long customerId);

    /**
     * 根据预约ID查询病历
     *
     * @param appointmentId 预约ID
     * @return 病历实体
     */
    MedicalRecordEntity selectByAppointmentId(@Param("appointmentId") Long appointmentId);

    /**
     * 根据线索ID查询病历
     *
     * @param leadId 线索ID
     * @return 病历列表
     */
    List<MedicalRecordVO> selectByLeadId(@Param("leadId") Long leadId);

    /**
     * 批量更新删除状态
     *
     * @param recordIdList 病历ID列表
     * @param deletedFlag 删除标志
     */
    void batchUpdateDeleted(@Param("recordIdList") List<Long> recordIdList, @Param("deletedFlag") Boolean deletedFlag);

    /**
     * 获取客户最近的病历
     *
     * @param customerId 客户ID
     * @param limit 限制数量
     * @return 病历列表
     */
    List<MedicalRecordVO> getRecentRecords(@Param("customerId") Long customerId, @Param("limit") Integer limit);

    /**
     * 根据医生ID查询病历
     *
     * @param doctorId 医生ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 病历列表
     */
    List<MedicalRecordVO> selectByDoctorId(@Param("doctorId") Long doctorId, 
                                          @Param("startDate") LocalDate startDate, 
                                          @Param("endDate") LocalDate endDate);

    /**
     * 获取病历统计数据
     *
     * @return 病历统计数据
     */
    Integer getMedicalRecordCount();

    /**
     * 根据客户ID统计病历数量
     *
     * @param customerId 客户ID
     * @return 病历数量
     */
    Integer getRecordCountByCustomerId(@Param("customerId") Long customerId);

    /**
     * 根据医生ID统计病历数量
     *
     * @param doctorId 医生ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 病历数量
     */
    Integer getRecordCountByDoctorId(@Param("doctorId") Long doctorId, 
                                    @Param("startDate") LocalDate startDate, 
                                    @Param("endDate") LocalDate endDate);

    /**
     * 获取需要复诊的病历
     *
     * @param date 日期
     * @return 病历列表
     */
    List<MedicalRecordVO> getFollowUpRecords(@Param("date") LocalDate date);
}

package net.lab1024.sa.admin.module.business.hospital.dashboard.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 图表数据 VO
 *
 * <AUTHOR>
 * @Date 2024-12-15 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright <a href="https://1024lab.net">1024创新实验室</a>
 */
@Data
@Schema(description = "图表数据")
public class ChartDataVO {

    @Schema(description = "图表类型")
    private String chartType;

    @Schema(description = "图表标题")
    private String title;

    @Schema(description = "X轴数据")
    private List<String> xAxis;

    @Schema(description = "Y轴数据")
    private List<SeriesData> series;

    @Schema(description = "饼图数据")
    private List<PieData> pieData;

    @Schema(description = "其他数据")
    private Map<String, Object> extraData;

    @Data
    @Schema(description = "系列数据")
    public static class SeriesData {
        @Schema(description = "系列名称")
        private String name;

        @Schema(description = "系列数据")
        private List<Object> data;

        @Schema(description = "系列类型")
        private String type;
    }

    @Data
    @Schema(description = "饼图数据")
    public static class PieData {
        @Schema(description = "名称")
        private String name;

        @Schema(description = "数值")
        private Object value;

        @Schema(description = "百分比")
        private String percentage;
    }
} 
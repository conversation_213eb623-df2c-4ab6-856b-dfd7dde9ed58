package net.lab1024.sa.admin.module.business.hospital.visit.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.entity.ChargeEntity;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.form.ChargeQueryForm;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.ChargeVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 收费记录Dao
 *
 * <AUTHOR>
 * @Date 2025-07-29
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Mapper
public interface ChargeDao extends BaseMapper<ChargeEntity> {

    /**
     * 分页查询收费记录
     *
     * @param page      分页参数
     * @param queryForm 查询条件
     * @return 收费记录列表
     */
    List<ChargeVO> queryPage(Page page, @Param("queryForm") ChargeQueryForm queryForm);

    /**
     * 根据患者ID查询收费记录
     *
     * @param patientId 患者ID
     * @return 收费记录列表
     */
    List<ChargeVO> selectByPatientId(@Param("patientId") Long patientId);

    /**
     * 根据开单ID查询收费记录
     *
     * @param prescriptionId 开单ID
     * @return 收费记录
     */
    ChargeVO selectByPrescriptionId(@Param("prescriptionId") Long prescriptionId);

    /**
     * 根据诊断ID查询收费记录
     *
     * @param diagnosisId 诊断ID
     * @return 收费记录
     */
    ChargeVO selectByDiagnosisId(@Param("diagnosisId") Long diagnosisId);

    /**
     * 根据收费ID查询详情
     *
     * @param chargeId 收费ID
     * @return 收费记录详情
     */
    ChargeVO selectDetailById(@Param("chargeId") Long chargeId);

    /**
     * 更新收费状态
     *
     * @param chargeId       收费ID
     * @param chargeStatus   收费状态
     * @param updateUserId   更新人ID
     * @param updateUserName 更新人姓名
     * @return 更新行数
     */
    int updateChargeStatus(@Param("chargeId") Long chargeId,
                           @Param("chargeStatus") Integer chargeStatus,
                           @Param("updateUserId") Long updateUserId,
                           @Param("updateUserName") String updateUserName);
}

package net.lab1024.sa.admin.module.business.hospital.appointment.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.hospital.appointment.constant.AppointmentStatusEnum;
import net.lab1024.sa.admin.module.business.hospital.appointment.dao.AppointmentDao;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.entity.AppointmentEntity;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.form.AppointmentAddForm;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.form.AppointmentQueryForm;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.form.AppointmentUpdateForm;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.vo.AppointmentCalendarVO;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.vo.AppointmentVO;
import net.lab1024.sa.admin.module.business.hospital.lead.dao.LeadDao;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.entity.LeadEntity;
import net.lab1024.sa.admin.util.AdminRequestUtil;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.module.support.datatracer.constant.DataTracerTypeEnum;
import net.lab1024.sa.base.module.support.datatracer.service.DataTracerService;
import net.lab1024.sa.base.module.support.serialnumber.constant.SerialNumberIdEnum;
import net.lab1024.sa.base.module.support.serialnumber.service.SerialNumberService;
import net.lab1024.sa.admin.module.business.hospital.finance.service.ChargeRecordService;
import net.lab1024.sa.admin.module.business.hospital.lead.service.LeadStatusFlowService;
import net.lab1024.sa.admin.module.business.hospital.visit.service.VisitPatientService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * 预约Service
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Slf4j
@Service
public class AppointmentService {

    @Resource
    private AppointmentDao appointmentDao;



    @Resource
    private LeadDao leadDao;

    @Resource
    private DataTracerService dataTracerService;

    @Autowired
    private LeadStatusFlowService leadStatusFlowService;

    @Resource
    private SerialNumberService serialNumberService;

    @Resource
    private ChargeRecordService chargeRecordService;

    @Resource
    private VisitPatientService visitPatientService;

    /**
     * 分页查询预约
     */
    public ResponseDTO<PageResult<AppointmentVO>> queryPage(AppointmentQueryForm queryForm) {
        queryForm.setDeletedFlag(Boolean.FALSE);
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<AppointmentVO> appointmentList = appointmentDao.queryPage(page, queryForm);
        PageResult<AppointmentVO> pageResult = SmartPageUtil.convert2PageResult(page, appointmentList);
        return ResponseDTO.ok(pageResult);
    }

    /**
     * 添加预约
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> add(AppointmentAddForm addForm) {
        // 简化预约添加逻辑，不再校验医生排班和时间冲突

        AppointmentEntity appointmentEntity = SmartBeanUtil.copy(addForm, AppointmentEntity.class);

        // 生成预约编号
        String appointmentNo = serialNumberService.generate(SerialNumberIdEnum.APPOINTMENT);
        appointmentEntity.setAppointmentNo(appointmentNo);

        appointmentEntity.setDeletedFlag(Boolean.FALSE);
        appointmentEntity.setCreateTime(LocalDateTime.now());
        appointmentEntity.setUpdateTime(LocalDateTime.now());
        appointmentEntity.setCreateUserId(AdminRequestUtil.getRequestUserId());

        // 如果没有设置状态，默认为未到诊
        if (appointmentEntity.getAppointmentStatus() == null) {
            appointmentEntity.setAppointmentStatus(AppointmentStatusEnum.NOT_ARRIVED.getValue());
        }

        // 设置默认值，避免数据库字段为空错误
        if (appointmentEntity.getProjectId() == null) {
            appointmentEntity.setProjectId(0L);
        }
        if (appointmentEntity.getProjectName() == null || appointmentEntity.getProjectName().trim().isEmpty()) {
            appointmentEntity.setProjectName("未指定项目");
        }
        if (appointmentEntity.getDoctorId() == null) {
            appointmentEntity.setDoctorId(0L);
        }
        if (appointmentEntity.getDoctorName() == null || appointmentEntity.getDoctorName().trim().isEmpty()) {
            appointmentEntity.setDoctorName("未指定医生");
        }

        appointmentDao.insert(appointmentEntity);

        // 如果是从线索创建的预约，自动流转线索状态为已预约
        if (addForm.getLeadId() != null) {
            leadStatusFlowService.onAppointmentCreated(addForm.getLeadId());
        }

        // 记录数据变动
        dataTracerService.insert(appointmentEntity.getAppointmentId(), DataTracerTypeEnum.APPOINTMENT);

        return ResponseDTO.ok();
    }

    /**
     * 从线索创建预约
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> addFromLead(Long leadId, LocalDate appointmentDate,
                                          LocalTime appointmentTime, String remark) {
        // 获取线索信息
        LeadEntity leadEntity = leadDao.selectById(leadId);
        if (leadEntity == null || leadEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("线索不存在");
        }

        // 创建预约表单，映射线索字段到预约字段
        AppointmentAddForm addForm = new AppointmentAddForm();
        addForm.setLeadId(leadId);
        addForm.setCustomerName(leadEntity.getCustomerName());
        addForm.setCustomerPhone(leadEntity.getCustomerPhone());
        addForm.setWechat(leadEntity.getCustomerWechat());
        addForm.setGender(leadEntity.getGender());
        addForm.setAge(leadEntity.getAge());
        addForm.setSymptom(leadEntity.getSymptom());
        addForm.setRegion(leadEntity.getRegion());
        addForm.setAssignedEmployeeId(leadEntity.getAssignedEmployeeId());
        // 设置默认的项目和医生信息
        addForm.setProjectId(0L);
        addForm.setProjectName("未指定项目");
        addForm.setDoctorId(0L);
        addForm.setDoctorName("未指定医生");
        addForm.setAppointmentDate(appointmentDate);
        addForm.setAppointmentTime(appointmentTime);
        addForm.setAppointmentStatus(AppointmentStatusEnum.NOT_ARRIVED.getValue());
        addForm.setRemark(remark);

        // 调用添加预约方法
        ResponseDTO<String> result = this.add(addForm);

        // 如果预约创建成功，自动流转线索状态为已预约
        if (result.getOk()) {
            leadStatusFlowService.onAppointmentCreated(leadId);
        }

        return result;
    }

    /**
     * 更新预约
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> update(AppointmentUpdateForm updateForm) {
        // 校验预约是否存在
        AppointmentEntity existAppointment = appointmentDao.selectById(updateForm.getAppointmentId());
        if (existAppointment == null || existAppointment.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("预约不存在");
        }

        // 简化更新逻辑，不再校验医生排班

        // 获取原实体用于数据变动记录
        AppointmentEntity originEntity = appointmentDao.selectById(updateForm.getAppointmentId());

        AppointmentEntity appointmentEntity = SmartBeanUtil.copy(updateForm, AppointmentEntity.class);
        appointmentEntity.setUpdateTime(LocalDateTime.now());
        appointmentDao.updateById(appointmentEntity);

        // 记录数据变动
        dataTracerService.update(appointmentEntity.getAppointmentId(), DataTracerTypeEnum.APPOINTMENT, originEntity, appointmentEntity);

        return ResponseDTO.ok();
    }

    /**
     * 删除预约
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> delete(Long appointmentId) {
        AppointmentEntity appointmentEntity = appointmentDao.selectById(appointmentId);
        if (appointmentEntity == null || appointmentEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("预约不存在");
        }

        appointmentEntity.setDeletedFlag(Boolean.TRUE);
        appointmentEntity.setUpdateTime(LocalDateTime.now());
        appointmentDao.updateById(appointmentEntity);

        // 简化取消逻辑，不再更新医生排班

        // 记录数据变动
        dataTracerService.delete(appointmentId, DataTracerTypeEnum.APPOINTMENT);

        return ResponseDTO.ok();
    }

    /**
     * 确认到诊（直接从未到诊变为已到诊）
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> confirm(Long appointmentId) {
        AppointmentEntity appointmentEntity = appointmentDao.selectById(appointmentId);
        if (appointmentEntity == null || appointmentEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("预约不存在");
        }

        if (!AppointmentStatusEnum.NOT_ARRIVED.getValue().equals(appointmentEntity.getAppointmentStatus())) {
            return ResponseDTO.userErrorParam("只有未到诊的预约才能确认到诊");
        }

        // 获取原实体用于数据变动记录
        AppointmentEntity originEntity = appointmentDao.selectById(appointmentId);

        appointmentDao.updateStatus(appointmentId, AppointmentStatusEnum.ARRIVED.getValue());

        // 如果预约关联了线索，自动流转线索状态为已到院
        if (appointmentEntity.getLeadId() != null) {
            leadStatusFlowService.onArrivalConfirmed(appointmentEntity.getLeadId());
        }

        // 自动创建到诊患者记录
        try {
            ResponseDTO<String> createResult = visitPatientService.createFromAppointment(appointmentId);
            if (!createResult.getOk()) {
                log.warn("预约确认到诊后创建到诊患者记录失败: appointmentId={}, error={}", appointmentId, createResult.getMsg());
            } else {
                log.info("预约确认到诊后成功创建到诊患者记录: appointmentId={}", appointmentId);
            }
        } catch (Exception e) {
            log.error("预约确认到诊后创建到诊患者记录异常: appointmentId={}", appointmentId, e);
        }

        // 获取更新后的实体
        AppointmentEntity updatedEntity = appointmentDao.selectById(appointmentId);

        // 记录数据变动
        dataTracerService.update(appointmentId, DataTracerTypeEnum.APPOINTMENT, originEntity, updatedEntity);

        return ResponseDTO.ok();
    }

    /**
     * 取消预约
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> cancel(Long appointmentId, String reason) {
        AppointmentEntity appointmentEntity = appointmentDao.selectById(appointmentId);
        if (appointmentEntity == null || appointmentEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("预约不存在");
        }

        if (AppointmentStatusEnum.ARRIVED.getValue().equals(appointmentEntity.getAppointmentStatus())) {
            return ResponseDTO.userErrorParam("已到诊的预约无法取消");
        }

        // 直接删除预约记录
        appointmentDao.deleteById(appointmentId);

        return ResponseDTO.ok();
    }

    /**
     * 到院确认
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> arrive(Long appointmentId) {
        AppointmentEntity appointmentEntity = appointmentDao.selectById(appointmentId);
        if (appointmentEntity == null || appointmentEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("预约不存在");
        }

        if (!AppointmentStatusEnum.NOT_ARRIVED.getValue().equals(appointmentEntity.getAppointmentStatus())) {
            return ResponseDTO.userErrorParam("只有未到诊的预约才能确认到诊");
        }

        // 获取原实体用于数据变动记录
        AppointmentEntity originEntity = appointmentEntity;

        // 更新状态为已到院，并记录实际到院时间
        appointmentDao.updateStatusAndArrivalTime(appointmentId, AppointmentStatusEnum.ARRIVED.getValue(), LocalDateTime.now());

        // 如果预约关联了线索，自动流转线索状态为已到院
        if (appointmentEntity.getLeadId() != null) {
            leadStatusFlowService.onArrivalConfirmed(appointmentEntity.getLeadId());
        }

        // 自动创建到诊患者记录
        try {
            ResponseDTO<String> createResult = visitPatientService.createFromAppointment(appointmentId);
            if (!createResult.getOk()) {
                log.warn("预约到院确认后创建到诊患者记录失败: appointmentId={}, error={}", appointmentId, createResult.getMsg());
            } else {
                log.info("预约到院确认后成功创建到诊患者记录: appointmentId={}", appointmentId);
            }
        } catch (Exception e) {
            log.error("预约到院确认后创建到诊患者记录异常: appointmentId={}", appointmentId, e);
        }

        // 获取更新后的实体
        AppointmentEntity updatedEntity = appointmentDao.selectById(appointmentId);

        // 记录数据变动
        dataTracerService.update(appointmentId, DataTracerTypeEnum.APPOINTMENT, originEntity, updatedEntity);

        return ResponseDTO.ok();
    }

    /**
     * 完成预约（更新状态为已完成，并自动创建开单）
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> complete(Long appointmentId) {
        AppointmentEntity appointmentEntity = appointmentDao.selectById(appointmentId);
        if (appointmentEntity == null || appointmentEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("预约不存在");
        }

        if (!AppointmentStatusEnum.ARRIVED.getValue().equals(appointmentEntity.getAppointmentStatus())) {
            return ResponseDTO.userErrorParam("只有已到诊的预约才能完成");
        }

        // 获取原实体用于数据变动记录
        AppointmentEntity originEntity = SmartBeanUtil.copy(appointmentEntity, AppointmentEntity.class);

        // 更新预约状态为已完成
        appointmentDao.updateStatus(appointmentId, AppointmentStatusEnum.COMPLETED.getValue());

        // 如果预约关联了线索，自动流转线索状态为已完成
        if (appointmentEntity.getLeadId() != null) {
            // leadStatusFlowService.onAppointmentCompleted(appointmentEntity.getLeadId());
            // 暂时注释掉，因为该方法可能不存在
        }

        // 自动创建开单记录
        ResponseDTO<String> chargeResult = chargeRecordService.createFromAppointment(appointmentId);
        if (!chargeResult.getOk()) {
            // 如果创建开单失败，记录日志但不影响预约完成
            log.warn("预约完成后创建开单失败: appointmentId={}, error={}", appointmentId, chargeResult.getMsg());
        }

        // 获取更新后的实体
        AppointmentEntity updatedEntity = appointmentDao.selectById(appointmentId);

        // 记录数据变动
        dataTracerService.update(appointmentId, DataTracerTypeEnum.APPOINTMENT, originEntity, updatedEntity);

        return ResponseDTO.ok();
    }

    /**
     * 获取预约日历数据
     */
    public ResponseDTO<List<AppointmentCalendarVO>> getCalendarData(LocalDate startDate, LocalDate endDate, Long doctorId) {
        List<AppointmentCalendarVO> calendarData = appointmentDao.getCalendarData(startDate, endDate, doctorId);
        return ResponseDTO.ok(calendarData);
    }

    /**
     * 根据线索ID查询预约记录
     */
    public ResponseDTO<List<AppointmentVO>> queryByLeadId(Long leadId) {
        List<AppointmentVO> appointmentList = appointmentDao.selectByLeadId(leadId);
        return ResponseDTO.ok(appointmentList);
    }

    /**
     * 分配医生
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> assignDoctor(Long appointmentId, Long doctorId, String doctorName) {
        // 检查预约是否存在
        AppointmentEntity appointment = appointmentDao.selectById(appointmentId);
        if (appointment == null) {
            return ResponseDTO.userErrorParam("预约不存在");
        }

        // 检查预约状态，只有未取消的预约才能分配医生
        if (AppointmentStatusEnum.CANCELLED.getValue().equals(appointment.getAppointmentStatus())) {
            return ResponseDTO.userErrorParam("已取消的预约不能分配医生");
        }

        // 更新预约信息
        appointment.setDoctorId(doctorId);
        appointment.setDoctorName(doctorName);
        appointment.setUpdateTime(LocalDateTime.now());
        appointment.setUpdateName(AdminRequestUtil.getRequestUser().getActualName());

        appointmentDao.updateById(appointment);

        // 如果预约已经到诊，同时更新对应的患者记录中的医生信息
        if (AppointmentStatusEnum.ARRIVED.getValue().equals(appointment.getAppointmentStatus())) {
            try {
                visitPatientService.updateDoctorByAppointmentId(appointmentId, doctorId, doctorName);
            } catch (Exception e) {
                log.warn("更新患者记录中的医生信息失败: appointmentId={}, doctorId={}, doctorName={}",
                        appointmentId, doctorId, doctorName, e);
            }
        }

        // 记录数据变更
        dataTracerService.insert(appointmentId, DataTracerTypeEnum.APPOINTMENT);

        return ResponseDTO.ok();
    }

    /**
     * 设置爽约
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> setNoShow(Long appointmentId) {
        // 检查预约是否存在
        AppointmentEntity appointment = appointmentDao.selectById(appointmentId);
        if (appointment == null) {
            return ResponseDTO.userErrorParam("预约不存在");
        }

        // 检查预约状态是否为未到诊
        if (!AppointmentStatusEnum.NOT_ARRIVED.getValue().equals(appointment.getAppointmentStatus())) {
            return ResponseDTO.userErrorParam("只有未到诊的预约才能设置为爽约");
        }

        // 更新预约状态为爽约
        appointment.setAppointmentStatus(AppointmentStatusEnum.NO_SHOW.getValue());
        appointment.setUpdateTime(LocalDateTime.now());
        appointment.setUpdateName(AdminRequestUtil.getRequestUser().getActualName());

        appointmentDao.updateById(appointment);

        // 如果有关联的线索，同步更新线索状态
        if (appointment.getLeadId() != null) {
            try {
                leadStatusFlowService.onAppointmentNoShow(appointment.getLeadId());
            } catch (Exception e) {
                log.error("更新线索状态失败：leadId={}", appointment.getLeadId(), e);
            }
        }

        // 记录数据变更
        dataTracerService.insert(appointmentId, DataTracerTypeEnum.APPOINTMENT);

        return ResponseDTO.ok();
    }


}

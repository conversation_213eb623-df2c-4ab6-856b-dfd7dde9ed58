package net.lab1024.sa.admin.module.business.hospital.lead.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.form.LeadFollowEnhancedAddForm;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadFollowReminderVO;
import net.lab1024.sa.admin.module.business.hospital.lead.service.LeadFollowService;
import net.lab1024.sa.admin.module.business.hospital.lead.service.LeadFollowReminderService;
import net.lab1024.sa.admin.util.AdminRequestUtil;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * 增强的线索跟进控制器
 * 
 * <AUTHOR>
 * @Date 2025-07-22
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Tag(name = "增强的线索跟进")
@RestController
@RequestMapping("/api/lead/follow-enhanced")
public class LeadFollowEnhancedController {

    @Resource
    private LeadFollowService leadFollowService;

    @Resource
    private LeadFollowReminderService leadFollowReminderService;

    @Operation(summary = "添加增强跟进记录（支持后续操作）")
    @PostMapping("/add")
    @SaCheckPermission("hospital:lead:follow:add")
    public ResponseDTO<String> addEnhancedFollow(@RequestBody @Valid LeadFollowEnhancedAddForm enhancedForm) {
        return leadFollowService.addEnhanced(enhancedForm);
    }

    @Operation(summary = "获取我的跟进提醒列表")
    @GetMapping("/reminders/my")
    @SaCheckPermission("hospital:lead:follow:query")
    public ResponseDTO<List<LeadFollowReminderVO>> getMyReminders() {
        Long userId = AdminRequestUtil.getRequestUserId();
        return leadFollowReminderService.getUserReminders(userId);
    }

    @Operation(summary = "获取今日待跟进线索")
    @GetMapping("/reminders/today")
    @SaCheckPermission("hospital:lead:follow:query")
    public ResponseDTO<List<LeadFollowReminderVO>> getTodayFollowReminders() {
        Long userId = AdminRequestUtil.getRequestUserId();
        LocalDateTime startTime = LocalDate.now().atStartOfDay();
        LocalDateTime endTime = LocalDate.now().atTime(LocalTime.MAX);

        List<LeadFollowReminderVO> reminders = leadFollowReminderService.getTodayFollowReminders(userId, startTime, endTime);
        return ResponseDTO.ok(reminders);
    }

    @Operation(summary = "获取逾期未跟进线索")
    @GetMapping("/reminders/overdue")
    @SaCheckPermission("hospital:lead:follow:query")
    public ResponseDTO<List<LeadFollowReminderVO>> getOverdueReminders() {
        Long userId = AdminRequestUtil.getRequestUserId();
        List<LeadFollowReminderVO> reminders = leadFollowReminderService.getOverdueReminders(userId, LocalDateTime.now());
        return ResponseDTO.ok(reminders);
    }

    @Operation(summary = "取消跟进提醒")
    @PostMapping("/reminders/{reminderId}/cancel")
    @SaCheckPermission("hospital:lead:follow:update")
    public ResponseDTO<String> cancelReminder(@PathVariable Long reminderId) {
        return leadFollowReminderService.cancelReminder(reminderId);
    }

    @Operation(summary = "完成跟进（自动取消相关提醒）")
    @PostMapping("/complete/{leadId}")
    @SaCheckPermission("hospital:lead:follow:update")
    public ResponseDTO<String> completeFollow(@PathVariable Long leadId) {
        leadFollowReminderService.autoCompleteReminders(leadId);
        return ResponseDTO.ok("跟进已完成，相关提醒已取消");
    }
}

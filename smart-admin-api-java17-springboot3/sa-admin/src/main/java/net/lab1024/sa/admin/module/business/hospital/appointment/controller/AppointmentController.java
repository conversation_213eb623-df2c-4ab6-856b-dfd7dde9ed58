package net.lab1024.sa.admin.module.business.hospital.appointment.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.constant.AdminSwaggerTagConst;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.form.AppointmentAddForm;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.form.AppointmentQueryForm;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.form.AppointmentUpdateForm;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.vo.AppointmentCalendarVO;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.vo.AppointmentVO;
import net.lab1024.sa.admin.module.business.hospital.appointment.service.AppointmentService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.ValidateList;
import net.lab1024.sa.base.module.support.operatelog.annotation.OperateLog;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * 预约管理Controller
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Tag(name = AdminSwaggerTagConst.Business.HOSPITAL_APPOINTMENT)
@RestController
@RequestMapping("/api/appointment")
public class AppointmentController {

    @Resource
    private AppointmentService appointmentService;

    @Operation(summary = "分页查询预约")
    @PostMapping("/query")
    @SaCheckPermission("hospital:appointment:query")
    public ResponseDTO<PageResult<AppointmentVO>> queryPage(@RequestBody @Valid AppointmentQueryForm queryForm) {
        return appointmentService.queryPage(queryForm);
    }

    @Operation(summary = "添加预约")
    @PostMapping("/add")
    @SaCheckPermission("hospital:appointment:add")
    @OperateLog
    public ResponseDTO<String> add(@RequestBody @Valid AppointmentAddForm addForm) {
        return appointmentService.add(addForm);
    }

    @Operation(summary = "更新预约")
    @PostMapping("/update")
    @SaCheckPermission("hospital:appointment:update")
    @OperateLog
    public ResponseDTO<String> update(@RequestBody @Valid AppointmentUpdateForm updateForm) {
        return appointmentService.update(updateForm);
    }

    @Operation(summary = "删除预约")
    @GetMapping("/delete/{appointmentId}")
    @SaCheckPermission("hospital:appointment:delete")
    @OperateLog
    public ResponseDTO<String> delete(@PathVariable Long appointmentId) {
        return appointmentService.delete(appointmentId);
    }

    @Operation(summary = "批量删除预约")
    @PostMapping("/batchDelete")
    @SaCheckPermission("hospital:appointment:delete")
    @OperateLog
    public ResponseDTO<String> batchDelete(@RequestBody ValidateList<Long> appointmentIdList) {
        // TODO: 实现批量删除逻辑
        return ResponseDTO.ok();
    }

    @Operation(summary = "确认预约")
    @PostMapping("/confirm/{appointmentId}")
    @SaCheckPermission("hospital:appointment:confirm")
    @OperateLog
    public ResponseDTO<String> confirm(@PathVariable Long appointmentId) {
        return appointmentService.confirm(appointmentId);
    }

    @Operation(summary = "取消预约")
    @PostMapping("/cancel/{appointmentId}")
    @SaCheckPermission("hospital:appointment:cancel")
    @OperateLog
    public ResponseDTO<String> cancel(@PathVariable Long appointmentId, @RequestParam(required = false) String reason) {
        return appointmentService.cancel(appointmentId, reason);
    }

    @Operation(summary = "到院确认")
    @PostMapping("/arrive/{appointmentId}")
    @SaCheckPermission("hospital:appointment:arrive")
    @OperateLog
    public ResponseDTO<String> arrive(@PathVariable Long appointmentId) {
        return appointmentService.arrive(appointmentId);
    }

    @Operation(summary = "完成预约")
    @PostMapping("/complete/{appointmentId}")
    @SaCheckPermission("hospital:appointment:complete")
    @OperateLog
    public ResponseDTO<String> complete(@PathVariable Long appointmentId) {
        return appointmentService.complete(appointmentId);
    }

    @Operation(summary = "获取预约日历数据")
    @GetMapping("/calendar")
    @SaCheckPermission("hospital:appointment:query")
    public ResponseDTO<List<AppointmentCalendarVO>> getCalendarData(
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate,
            @RequestParam(required = false) Long doctorId) {
        return appointmentService.getCalendarData(startDate, endDate, doctorId);
    }

    @Operation(summary = "根据线索ID查询预约记录")
    @GetMapping("/queryByLeadId/{leadId}")
    @SaCheckPermission("hospital:appointment:query")
    public ResponseDTO<List<AppointmentVO>> queryByLeadId(@PathVariable Long leadId) {
        return appointmentService.queryByLeadId(leadId);
    }

    @Operation(summary = "分配医生")
    @PostMapping("/assignDoctor/{appointmentId}")
    @SaCheckPermission("hospital:appointment:assign")
    @OperateLog
    public ResponseDTO<String> assignDoctor(@PathVariable Long appointmentId, @RequestBody AssignDoctorForm form) {
        return appointmentService.assignDoctor(appointmentId, form.getDoctorId(), form.getDoctorName());
    }

    @Operation(summary = "设置爽约")
    @PostMapping("/setNoShow/{appointmentId}")
    @SaCheckPermission("hospital:appointment:noshow")
    @OperateLog
    public ResponseDTO<String> setNoShow(@PathVariable Long appointmentId) {
        return appointmentService.setNoShow(appointmentId);
    }

    /**
     * 分配医生表单
     */
    public static class AssignDoctorForm {
        private Long doctorId;
        private String doctorName;

        public Long getDoctorId() {
            return doctorId;
        }

        public void setDoctorId(Long doctorId) {
            this.doctorId = doctorId;
        }

        public String getDoctorName() {
            return doctorName;
        }

        public void setDoctorName(String doctorName) {
            this.doctorName = doctorName;
        }
    }
}

package net.lab1024.sa.admin.module.business.hospital.common.annotation;

import java.lang.annotation.*;

/**
 * 数据脱敏注解
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DataMasking {

    /**
     * 脱敏类型
     */
    MaskingType type() default MaskingType.CUSTOM;

    /**
     * 保留开始位数（自定义类型时使用）
     */
    int start() default 0;

    /**
     * 保留结束位数（自定义类型时使用）
     */
    int end() default 0;

    /**
     * 脱敏类型枚举
     */
    enum MaskingType {
        /**
         * 手机号
         */
        PHONE,
        /**
         * 身份证号
         */
        ID_CARD,
        /**
         * 邮箱
         */
        EMAIL,
        /**
         * 银行卡号
         */
        BANK_CARD,
        /**
         * 姓名
         */
        NAME,
        /**
         * 地址
         */
        ADDRESS,
        /**
         * 自定义
         */
        CUSTOM
    }
}

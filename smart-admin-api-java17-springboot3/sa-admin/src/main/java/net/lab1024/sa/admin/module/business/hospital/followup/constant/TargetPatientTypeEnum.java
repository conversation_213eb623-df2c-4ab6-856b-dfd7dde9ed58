package net.lab1024.sa.admin.module.business.hospital.followup.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 目标患者类型枚举
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Getter
@AllArgsConstructor
public enum TargetPatientTypeEnum implements BaseEnum {

    /**
     * 所有已分配医生患者
     */
    ALL_ASSIGNED(1, "所有已分配医生患者"),

    /**
     * 指定医生患者
     */
    SPECIFIC_DOCTOR(2, "指定医生患者"),

    /**
     * 指定科室患者
     */
    SPECIFIC_DEPARTMENT(3, "指定科室患者"),

    /**
     * 指定标签患者
     */
    SPECIFIC_TAGS(4, "指定标签患者");

    private final Integer value;

    private final String desc;
}

package net.lab1024.sa.admin.module.business.hospital.customer.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.hospital.customer.domain.entity.CustomerEntity;
import net.lab1024.sa.admin.module.business.hospital.customer.domain.form.CustomerQueryForm;
import net.lab1024.sa.admin.module.business.hospital.customer.domain.vo.CustomerVO;
import net.lab1024.sa.admin.module.business.hospital.customer.domain.vo.Customer360VO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户DAO
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Mapper
@Component
public interface CustomerDao extends BaseMapper<CustomerEntity> {

    /**
     * 分页查询客户
     *
     * @param page 分页参数
     * @param queryForm 查询条件
     * @return 客户列表
     */
    List<CustomerVO> queryPage(Page page, @Param("query") CustomerQueryForm queryForm);

    /**
     * 根据电话号码查询客户（用于查重）
     *
     * @param customerPhone 客户电话
     * @param excludeCustomerId 排除的客户ID（用于更新时排除自己）
     * @return 客户实体
     */
    CustomerEntity selectByPhone(@Param("customerPhone") String customerPhone, @Param("excludeCustomerId") Long excludeCustomerId);

    /**
     * 根据身份证号查询客户（用于查重）
     *
     * @param idCard 身份证号
     * @param excludeCustomerId 排除的客户ID（用于更新时排除自己）
     * @return 客户实体
     */
    CustomerEntity selectByIdCard(@Param("idCard") String idCard, @Param("excludeCustomerId") Long excludeCustomerId);

    /**
     * 根据线索ID查询客户
     *
     * @param leadId 线索ID
     * @return 客户实体
     */
    CustomerEntity selectByLeadId(@Param("leadId") Long leadId);

    /**
     * 更新客户状态
     *
     * @param customerId 客户ID
     * @param customerStatus 客户状态
     */
    void updateStatus(@Param("customerId") Long customerId, @Param("customerStatus") Integer customerStatus);

    /**
     * 批量更新删除状态
     *
     * @param customerIdList 客户ID列表
     * @param deletedFlag 删除标志
     */
    void batchUpdateDeleted(@Param("customerIdList") List<Long> customerIdList, @Param("deletedFlag") Boolean deletedFlag);

    /**
     * 获取客户统计数据
     *
     * @return 客户统计数据
     */
    Integer getCustomerCount();

    /**
     * 根据状态统计客户数量
     *
     * @param customerStatus 客户状态
     * @return 客户数量
     */
    Integer getCustomerCountByStatus(@Param("customerStatus") Integer customerStatus);

    /**
     * 获取客户详情（包含统计信息）
     *
     * @param customerId 客户ID
     * @return 客户详情
     */
    CustomerVO getCustomerDetail(@Param("customerId") Long customerId);

    /**
     * 获取客户基本详情
     *
     * @param customerId 客户ID
     * @return 客户基本详情
     */
    CustomerVO getDetail(@Param("customerId") Long customerId);

    /**
     * 根据标签查询客户
     *
     * @param customerTags 客户标签
     * @return 客户列表
     */
    List<CustomerVO> selectByTags(@Param("customerTags") String customerTags);

    /**
     * 获取最近创建的客户
     *
     * @param limit 限制数量
     * @return 客户列表
     */
    List<CustomerVO> getRecentCustomers(@Param("limit") Integer limit);

    /**
     * 获取客户360度视图信息
     *
     * @param customerId 客户ID
     * @return 客户360度视图
     */
    Customer360VO getCustomer360View(@Param("customerId") Long customerId);

    /**
     * 获取客户最近预约记录
     *
     * @param customerId 客户ID
     * @param limit 限制数量
     * @return 预约记录列表
     */
    List<Customer360VO.AppointmentSimpleVO> getRecentAppointments(@Param("customerId") Long customerId, @Param("limit") Integer limit);

    /**
     * 获取客户最近病历记录
     *
     * @param customerId 客户ID
     * @param limit 限制数量
     * @return 病历记录列表
     */
    List<Customer360VO.MedicalRecordSimpleVO> getRecentMedicalRecords(@Param("customerId") Long customerId, @Param("limit") Integer limit);

    /**
     * 获取客户最近跟进记录
     *
     * @param customerId 客户ID
     * @param limit 限制数量
     * @return 跟进记录列表
     */
    List<Customer360VO.FollowUpSimpleVO> getRecentFollowUps(@Param("customerId") Long customerId, @Param("limit") Integer limit);

    /**
     * 获取客户消费记录
     *
     * @param customerId 客户ID
     * @param limit 限制数量
     * @return 消费记录列表
     */
    List<Customer360VO.ConsumptionRecordVO> getConsumptionRecords(@Param("customerId") Long customerId, @Param("limit") Integer limit);

    /**
     * 统计总客户数
     *
     * @return 总客户数
     */
    Integer countTotal();

    /**
     * 统计指定时间范围内的客户数
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 客户数
     */
    Integer countByDateRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);
}

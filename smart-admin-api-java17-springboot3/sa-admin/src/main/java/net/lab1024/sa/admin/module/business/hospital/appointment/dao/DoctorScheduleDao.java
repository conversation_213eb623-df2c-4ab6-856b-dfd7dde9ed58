package net.lab1024.sa.admin.module.business.hospital.appointment.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.entity.DoctorScheduleEntity;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.form.DoctorScheduleQueryForm;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.vo.AvailableTimeVO;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.vo.DoctorScheduleVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * 医生排班DAO
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Mapper
@Component
public interface DoctorScheduleDao extends BaseMapper<DoctorScheduleEntity> {

    /**
     * 分页查询医生排班
     *
     * @param page 分页参数
     * @param queryForm 查询条件
     * @return 排班列表
     */
    List<DoctorScheduleVO> queryPage(Page page, @Param("query") DoctorScheduleQueryForm queryForm);

    /**
     * 检查排班时间冲突
     *
     * @param doctorId 医生ID
     * @param scheduleDate 排班日期
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param excludeScheduleId 排除的排班ID（用于更新时排除自己）
     * @return 冲突的排班实体
     */
    DoctorScheduleEntity checkTimeConflict(@Param("doctorId") Long doctorId,
                                          @Param("scheduleDate") LocalDate scheduleDate,
                                          @Param("startTime") LocalTime startTime,
                                          @Param("endTime") LocalTime endTime,
                                          @Param("excludeScheduleId") Long excludeScheduleId);

    /**
     * 更新当前预约数
     *
     * @param scheduleId 排班ID
     * @param increment 增量（正数为增加，负数为减少）
     */
    void updateCurrentAppointments(@Param("scheduleId") Long scheduleId, @Param("increment") Integer increment);

    /**
     * 批量更新删除状态
     *
     * @param scheduleIdList 排班ID列表
     * @param deletedFlag 删除标志
     */
    void batchUpdateDeleted(@Param("scheduleIdList") List<Long> scheduleIdList, @Param("deletedFlag") Boolean deletedFlag);

    /**
     * 获取医生可预约时间
     *
     * @param doctorId 医生ID
     * @param scheduleDate 排班日期
     * @return 可预约时间列表
     */
    List<AvailableTimeVO> getAvailableTime(@Param("doctorId") Long doctorId, @Param("scheduleDate") LocalDate scheduleDate);

    /**
     * 根据医生和日期时间查询排班
     *
     * @param doctorId 医生ID
     * @param scheduleDate 排班日期
     * @param appointmentTime 预约时间
     * @return 排班实体
     */
    DoctorScheduleEntity selectByDoctorAndDateTime(@Param("doctorId") Long doctorId,
                                                  @Param("scheduleDate") LocalDate scheduleDate,
                                                  @Param("appointmentTime") LocalTime appointmentTime);

    /**
     * 批量插入排班
     *
     * @param scheduleList 排班列表
     */
    void batchInsert(@Param("scheduleList") List<DoctorScheduleEntity> scheduleList);

    /**
     * 删除指定条件的排班（用于批量排班时覆盖）
     *
     * @param doctorId 医生ID
     * @param scheduleDates 排班日期列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    void deleteByCondition(@Param("doctorId") Long doctorId,
                          @Param("scheduleDates") List<LocalDate> scheduleDates,
                          @Param("startTime") LocalTime startTime,
                          @Param("endTime") LocalTime endTime);

    /**
     * 获取医生排班统计
     *
     * @param doctorId 医生ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 排班统计数据
     */
    Integer getScheduleCount(@Param("doctorId") Long doctorId,
                            @Param("startDate") LocalDate startDate,
                            @Param("endDate") LocalDate endDate);
}

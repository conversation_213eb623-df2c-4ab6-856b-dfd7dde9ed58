package net.lab1024.sa.admin.module.business.hospital.lead.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 跟进结果枚举
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Getter
@AllArgsConstructor
public enum FollowResultEnum implements BaseEnum {

    /**
     * 客户有预约意向
     */
    APPOINTMENT_INTERESTED(1, "客户有预约意向"),

    /**
     * 需要再次跟进
     */
    NEED_FOLLOW_UP(2, "需要再次跟进"),

    /**
     * 客户无意向/无效线索
     */
    NO_INTEREST_INVALID(3, "客户无意向/无效线索"),

    /**
     * 仅记录跟进（无后续操作）
     */
    RECORD_ONLY(4, "仅记录跟进");

    private final Integer value;

    private final String desc;

    /**
     * 根据值获取枚举
     */
    public static FollowResultEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (FollowResultEnum enumItem : values()) {
            if (enumItem.getValue().equals(value)) {
                return enumItem;
            }
        }
        return null;
    }
}

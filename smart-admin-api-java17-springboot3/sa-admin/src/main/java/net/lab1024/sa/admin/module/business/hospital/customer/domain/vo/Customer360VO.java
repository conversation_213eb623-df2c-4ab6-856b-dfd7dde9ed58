package net.lab1024.sa.admin.module.business.hospital.customer.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户360度视图对象
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class Customer360VO {

    // ==================== 基础信息 ====================
    
    @Schema(description = "客户ID")
    private Long customerId;

    @Schema(description = "客户姓名")
    private String customerName;

    @Schema(description = "客户电话")
    private String customerPhone;

    @Schema(description = "客户微信")
    private String customerWechat;

    @Schema(description = "客户邮箱")
    private String customerEmail;

    @Schema(description = "性别：1-男，2-女")
    private Integer gender;

    @Schema(description = "性别名称")
    private String genderName;

    @Schema(description = "年龄")
    private Integer age;

    @Schema(description = "出生日期")
    private LocalDate birthday;

    @Schema(description = "身份证号")
    private String idCard;

    @Schema(description = "客户地址")
    private String customerAddress;

    @Schema(description = "职业")
    private String occupation;

    @Schema(description = "客户来源")
    private String customerSource;

    @Schema(description = "客户状态")
    private Integer customerStatus;

    @Schema(description = "客户状态名称")
    private String customerStatusName;

    @Schema(description = "客户标签")
    private String customerTags;

    @Schema(description = "客户等级")
    private Integer customerLevel;

    @Schema(description = "客户等级名称")
    private String customerLevelName;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人姓名")
    private String createUserName;

    // ==================== 统计信息 ====================

    @Schema(description = "预约总次数")
    private Integer totalAppointments;

    @Schema(description = "已完成预约次数")
    private Integer completedAppointments;

    @Schema(description = "取消预约次数")
    private Integer cancelledAppointments;

    @Schema(description = "病历记录数")
    private Integer medicalRecordCount;

    @Schema(description = "跟进记录数")
    private Integer followUpCount;

    @Schema(description = "消费总金额")
    private BigDecimal totalAmount;

    @Schema(description = "平均消费金额")
    private BigDecimal avgAmount;

    @Schema(description = "最高单次消费")
    private BigDecimal maxAmount;

    // ==================== 时间信息 ====================

    @Schema(description = "最近预约时间")
    private LocalDateTime lastAppointmentTime;

    @Schema(description = "最近就诊时间")
    private LocalDateTime lastVisitTime;

    @Schema(description = "最近跟进时间")
    private LocalDateTime lastFollowUpTime;

    @Schema(description = "下次预约时间")
    private LocalDateTime nextAppointmentTime;

    @Schema(description = "下次复诊时间")
    private LocalDateTime nextVisitTime;

    @Schema(description = "首次就诊时间")
    private LocalDateTime firstVisitTime;

    // ==================== 评分信息 ====================

    @Schema(description = "满意度评分")
    private BigDecimal satisfactionScore;

    @Schema(description = "客户价值评分")
    private BigDecimal valueScore;

    @Schema(description = "流失风险评分")
    private BigDecimal churnRiskScore;

    @Schema(description = "活跃度评分")
    private BigDecimal activityScore;

    // ==================== 关联信息 ====================

    @Schema(description = "关联线索ID")
    private Long leadId;

    @Schema(description = "线索来源详情")
    private String leadSourceDetail;

    @Schema(description = "推荐人信息")
    private String referrerInfo;

    @Schema(description = "负责员工ID")
    private Long responsibleEmployeeId;

    @Schema(description = "负责员工姓名")
    private String responsibleEmployeeName;

    // ==================== 详细记录 ====================

    @Schema(description = "最近预约记录")
    private List<AppointmentSimpleVO> recentAppointments;

    @Schema(description = "最近病历记录")
    private List<MedicalRecordSimpleVO> recentMedicalRecords;

    @Schema(description = "最近跟进记录")
    private List<FollowUpSimpleVO> recentFollowUps;

    @Schema(description = "消费记录")
    private List<ConsumptionRecordVO> consumptionRecords;

    // ==================== 内部类：简化记录对象 ====================

    @Data
    public static class AppointmentSimpleVO {
        @Schema(description = "预约ID")
        private Long appointmentId;

        @Schema(description = "预约日期")
        private LocalDate appointmentDate;

        @Schema(description = "预约时间")
        private String appointmentTime;

        @Schema(description = "项目名称")
        private String projectName;

        @Schema(description = "医生姓名")
        private String doctorName;

        @Schema(description = "预约状态")
        private Integer appointmentStatus;

        @Schema(description = "预约状态名称")
        private String appointmentStatusName;
    }

    @Data
    public static class MedicalRecordSimpleVO {
        @Schema(description = "病历ID")
        private Long recordId;

        @Schema(description = "就诊日期")
        private LocalDate visitDate;

        @Schema(description = "主诉")
        private String chiefComplaint;

        @Schema(description = "诊断结果")
        private String diagnosis;

        @Schema(description = "医生姓名")
        private String doctorName;

        @Schema(description = "下次复诊时间")
        private LocalDate nextVisitDate;
    }

    @Data
    public static class FollowUpSimpleVO {
        @Schema(description = "跟进ID")
        private Long followUpId;

        @Schema(description = "跟进时间")
        private LocalDateTime followUpTime;

        @Schema(description = "跟进方式")
        private String followUpMethod;

        @Schema(description = "跟进内容")
        private String followUpContent;

        @Schema(description = "跟进结果")
        private String followUpResult;

        @Schema(description = "跟进人姓名")
        private String followUpUserName;
    }

    @Data
    public static class ConsumptionRecordVO {
        @Schema(description = "消费ID")
        private Long consumptionId;

        @Schema(description = "消费日期")
        private LocalDate consumptionDate;

        @Schema(description = "项目名称")
        private String projectName;

        @Schema(description = "消费金额")
        private BigDecimal amount;

        @Schema(description = "支付状态")
        private Integer paymentStatus;

        @Schema(description = "支付状态名称")
        private String paymentStatusName;
    }
}

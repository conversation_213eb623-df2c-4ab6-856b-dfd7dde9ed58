package net.lab1024.sa.admin.module.business.hospital.visit.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.form.PrescriptionAddForm;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.form.PrescriptionQueryForm;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.PrescriptionVO;
import net.lab1024.sa.admin.module.business.hospital.visit.service.PrescriptionService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.RequestUser;
import net.lab1024.sa.base.common.util.SmartRequestUtil;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 开单记录控制器
 *
 * <AUTHOR>
 * @Date 2025-07-29
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Tag(name = "开单记录管理")
@RestController
@RequestMapping("/visit/prescription")
public class PrescriptionController {

    @Autowired
    private PrescriptionService prescriptionService;

    @Operation(summary = "分页查询开单记录")
    @PostMapping("/queryPage")
    public ResponseDTO<PageResult<PrescriptionVO>> queryPage(@RequestBody @Valid PrescriptionQueryForm queryForm) {
        return ResponseDTO.ok(prescriptionService.queryPage(queryForm));
    }

    @Operation(summary = "根据患者ID查询开单记录")
    @GetMapping("/patient/{patientId}")
    public ResponseDTO<List<PrescriptionVO>> getByPatientId(@PathVariable Long patientId) {
        return prescriptionService.getByPatientId(patientId);
    }

    @Operation(summary = "根据诊断ID查询开单记录")
    @GetMapping("/diagnosis/{diagnosisId}")
    public ResponseDTO<PrescriptionVO> getByDiagnosisId(@PathVariable Long diagnosisId) {
        return prescriptionService.getByDiagnosisId(diagnosisId);
    }

    @Operation(summary = "根据ID查询开单记录详情")
    @GetMapping("/get/{prescriptionId}")
    public ResponseDTO<PrescriptionVO> getById(@PathVariable Long prescriptionId) {
        return prescriptionService.getById(prescriptionId);
    }

    @Operation(summary = "新增开单记录")
    @PostMapping("/add")
    public ResponseDTO<Long> add(@RequestBody @Valid PrescriptionAddForm addForm) {
        RequestUser requestUser = SmartRequestUtil.getRequestUser();
        return prescriptionService.add(addForm, requestUser.getUserId(), requestUser.getUserName());
    }

    @Operation(summary = "更新开单状态")
    @PostMapping("/updateStatus")
    public ResponseDTO<String> updateStatus(@RequestParam Long prescriptionId, @RequestParam Integer prescriptionStatus) {
        RequestUser requestUser = SmartRequestUtil.getRequestUser();
        return prescriptionService.updateStatus(prescriptionId, prescriptionStatus, requestUser.getUserId(), requestUser.getUserName());
    }

    @Operation(summary = "删除开单记录")
    @PostMapping("/delete/{prescriptionId}")
    public ResponseDTO<String> delete(@PathVariable Long prescriptionId) {
        RequestUser requestUser = SmartRequestUtil.getRequestUser();
        return prescriptionService.delete(prescriptionId, requestUser.getUserId(), requestUser.getUserName());
    }
}

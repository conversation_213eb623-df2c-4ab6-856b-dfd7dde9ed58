package net.lab1024.sa.admin.module.business.hospital.lead.service;

import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.hospital.lead.dao.LeadDao;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.entity.LeadEntity;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadVO;
import net.lab1024.sa.admin.module.business.hospital.lead.constant.LeadStatusEnum;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.admin.util.AdminRequestUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 线索状态流转服务
 * 
 * <AUTHOR>
 * @Date 2025-07-22
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Slf4j
@Service
public class LeadStatusFlowService {

    @Autowired
    private LeadDao leadDao;

    @Autowired
    private LeadStatusHistoryService leadStatusHistoryService;

    /**
     * 状态流转规则映射
     * key: 当前状态, value: 允许流转到的状态集合
     */
    private static final Map<Integer, Integer[]> STATUS_FLOW_RULES = new HashMap<>();

    static {
        // 新线索 -> 跟进中、已关闭
        STATUS_FLOW_RULES.put(LeadStatusEnum.NEW.getValue(), new Integer[]{
            LeadStatusEnum.FOLLOWING.getValue(), 
            LeadStatusEnum.CLOSED.getValue()
        });
        
        // 跟进中 -> 已预约、已关闭
        STATUS_FLOW_RULES.put(LeadStatusEnum.FOLLOWING.getValue(), new Integer[]{
            LeadStatusEnum.APPOINTED.getValue(), 
            LeadStatusEnum.CLOSED.getValue()
        });
        
        // 已预约 -> 已到院、已关闭
        STATUS_FLOW_RULES.put(LeadStatusEnum.APPOINTED.getValue(), new Integer[]{
            LeadStatusEnum.ARRIVED.getValue(), 
            LeadStatusEnum.CLOSED.getValue()
        });
        
        // 已到院 -> 已转化、已关闭
        STATUS_FLOW_RULES.put(LeadStatusEnum.ARRIVED.getValue(), new Integer[]{
            LeadStatusEnum.CONVERTED.getValue(), 
            LeadStatusEnum.CLOSED.getValue()
        });
        
        // 已转化 -> 已关闭（可选）
        STATUS_FLOW_RULES.put(LeadStatusEnum.CONVERTED.getValue(), new Integer[]{
            LeadStatusEnum.CLOSED.getValue()
        });
        
        // 已关闭 -> 无法流转到其他状态
        STATUS_FLOW_RULES.put(LeadStatusEnum.CLOSED.getValue(), new Integer[]{});
    }

    /**
     * 自动流转线索状态
     * 
     * @param leadId 线索ID
     * @param targetStatus 目标状态
     * @param triggerType 触发类型（FOLLOW_ADD, APPOINTMENT_CREATE, ARRIVAL_CONFIRM, CONVERSION_COMPLETE, MANUAL_CLOSE）
     * @param remark 备注
     * @return 流转结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<Boolean> autoFlowStatus(Long leadId, Integer targetStatus, String triggerType, String remark) {
        try {
            // 1. 获取当前线索信息
            LeadEntity leadEntity = leadDao.selectById(leadId);
            if (leadEntity == null) {
                return ResponseDTO.userErrorParam("线索不存在");
            }

            Integer currentStatus = leadEntity.getLeadStatus();
            
            // 2. 验证状态流转规则
            if (!isValidStatusFlow(currentStatus, targetStatus)) {
                log.warn("线索状态流转失败：不允许从状态{}流转到状态{}", currentStatus, targetStatus);
                return ResponseDTO.userErrorParam("不允许的状态流转");
            }

            // 3. 更新线索状态
            leadEntity.setLeadStatus(targetStatus);
            leadEntity.setUpdateTime(LocalDateTime.now());
            leadEntity.setUpdateUserId(AdminRequestUtil.getRequestUserId());
            
            int updateResult = leadDao.updateById(leadEntity);
            if (updateResult <= 0) {
                return ResponseDTO.userErrorParam("状态更新失败");
            }

            // 4. 记录状态变更历史
            leadStatusHistoryService.recordStatusChange(leadId, currentStatus, targetStatus, triggerType, remark);

            log.info("线索状态自动流转成功：线索ID={}, {}({}) -> {}({}), 触发类型={}", 
                leadId, 
                LeadStatusEnum.getByValue(currentStatus).getDesc(), currentStatus,
                LeadStatusEnum.getByValue(targetStatus).getDesc(), targetStatus,
                triggerType);

            return ResponseDTO.ok(true);
            
        } catch (Exception e) {
            log.error("线索状态自动流转异常：leadId={}, targetStatus={}", leadId, targetStatus, e);
            return ResponseDTO.userErrorParam("状态流转失败：" + e.getMessage());
        }
    }

    /**
     * 手动流转线索状态
     * 
     * @param leadId 线索ID
     * @param targetStatus 目标状态
     * @param remark 备注
     * @return 流转结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<Boolean> manualFlowStatus(Long leadId, Integer targetStatus, String remark) {
        return autoFlowStatus(leadId, targetStatus, "MANUAL", remark);
    }

    /**
     * 验证状态流转是否合法
     * 
     * @param currentStatus 当前状态
     * @param targetStatus 目标状态
     * @return 是否合法
     */
    private boolean isValidStatusFlow(Integer currentStatus, Integer targetStatus) {
        // 相同状态不需要流转
        if (currentStatus.equals(targetStatus)) {
            return false;
        }
        
        Integer[] allowedStatuses = STATUS_FLOW_RULES.get(currentStatus);
        if (allowedStatuses == null || allowedStatuses.length == 0) {
            return false;
        }
        
        for (Integer allowedStatus : allowedStatuses) {
            if (allowedStatus.equals(targetStatus)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 获取允许流转的状态列表
     * 
     * @param currentStatus 当前状态
     * @return 允许流转的状态列表
     */
    public Integer[] getAllowedFlowStatuses(Integer currentStatus) {
        return STATUS_FLOW_RULES.getOrDefault(currentStatus, new Integer[]{});
    }

    /**
     * 线索添加跟进记录后自动流转
     */
    public void onFollowAdded(Long leadId) {
        autoFlowStatus(leadId, LeadStatusEnum.FOLLOWING.getValue(), "FOLLOW_ADD", "添加跟进记录，自动流转到跟进中状态");
    }

    /**
     * 线索创建预约后自动流转
     */
    public void onAppointmentCreated(Long leadId) {
        autoFlowStatus(leadId, LeadStatusEnum.APPOINTED.getValue(), "APPOINTMENT_CREATE", "创建预约记录，自动流转到已预约状态");
    }

    /**
     * 客户到院确认后自动流转
     */
    public void onArrivalConfirmed(Long leadId) {
        autoFlowStatus(leadId, LeadStatusEnum.ARRIVED.getValue(), "ARRIVAL_CONFIRM", "确认客户到院，自动流转到已到院状态");
    }

    /**
     * 完成转化后自动流转
     */
    public void onConversionCompleted(Long leadId) {
        autoFlowStatus(leadId, LeadStatusEnum.CONVERTED.getValue(), "CONVERSION_COMPLETE", "完成开单转化，自动流转到已转化状态");
    }

    /**
     * 预约爽约后自动流转
     */
    public void onAppointmentNoShow(Long leadId) {
        autoFlowStatus(leadId, LeadStatusEnum.NO_SHOW.getValue(), "APPOINTMENT_NO_SHOW", "预约爽约，自动流转到爽约状态");
    }

    /**
     * 手动关闭线索
     */
    public ResponseDTO<Boolean> closeLeadManually(Long leadId, String reason) {
        return autoFlowStatus(leadId, LeadStatusEnum.CLOSED.getValue(), "MANUAL_CLOSE", "手动关闭线索：" + reason);
    }


}

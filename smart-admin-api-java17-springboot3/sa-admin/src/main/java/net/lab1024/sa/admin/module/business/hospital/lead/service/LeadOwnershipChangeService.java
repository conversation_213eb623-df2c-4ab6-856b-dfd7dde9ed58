package net.lab1024.sa.admin.module.business.hospital.lead.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.hospital.lead.constant.LeadOwnershipChangeStatusEnum;
import net.lab1024.sa.admin.module.business.hospital.lead.constant.LeadOwnershipChangeTypeEnum;
import net.lab1024.sa.admin.module.business.hospital.lead.dao.LeadDao;
import net.lab1024.sa.admin.module.business.hospital.lead.dao.LeadOwnershipChangeRequestDao;
import net.lab1024.sa.admin.module.business.hospital.lead.dao.LeadOwnershipChangeHistoryDao;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.entity.LeadEntity;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.entity.LeadOwnershipChangeRequestEntity;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.entity.LeadOwnershipChangeHistoryEntity;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.form.LeadOwnershipChangeApplyForm;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.form.LeadOwnershipChangeApproveForm;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.form.LeadOwnershipChangeQueryForm;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadOwnershipChangeRequestVO;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadOwnershipChangeHistoryVO;
import net.lab1024.sa.admin.module.system.department.service.DepartmentService;
import net.lab1024.sa.admin.module.system.employee.service.EmployeeService;
import net.lab1024.sa.admin.module.system.employee.domain.vo.EmployeeVO;
import net.lab1024.sa.admin.module.system.employee.domain.entity.EmployeeEntity;
import net.lab1024.sa.admin.module.system.department.domain.vo.DepartmentVO;
import net.lab1024.sa.admin.util.AdminRequestUtil;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.module.support.datatracer.constant.DataTracerTypeEnum;
import net.lab1024.sa.base.module.support.datatracer.service.DataTracerService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

/**
 * 线索归属变更申请服务
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Slf4j
@Service
public class LeadOwnershipChangeService {

    @Resource
    private LeadDao leadDao;

    @Resource
    private LeadOwnershipChangeRequestDao requestDao;

    @Resource
    private LeadOwnershipChangeHistoryDao historyDao;

    @Resource
    private EmployeeService employeeService;

    @Resource
    private DepartmentService departmentService;

    @Resource
    private DataTracerService dataTracerService;

    /**
     * 分页查询变更申请
     */
    public ResponseDTO<PageResult<LeadOwnershipChangeRequestVO>> queryPage(LeadOwnershipChangeQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<LeadOwnershipChangeRequestVO> list = requestDao.queryPage(page, queryForm);
        PageResult<LeadOwnershipChangeRequestVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return ResponseDTO.ok(pageResult);
    }

    /**
     * 提交变更申请
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> applyOwnershipChange(LeadOwnershipChangeApplyForm applyForm) {
        Long currentUserId = AdminRequestUtil.getRequestUserId();
        
        // 1. 校验线索是否存在
        LeadEntity leadEntity = leadDao.selectById(applyForm.getLeadId());
        if (leadEntity == null || leadEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("线索不存在");
        }

        // 2. 校验线索状态
        if (leadEntity.getLeadStatus() != null && (leadEntity.getLeadStatus() == 4 || leadEntity.getLeadStatus() == 5 || leadEntity.getLeadStatus() == 7)) {
            return ResponseDTO.userErrorParam("线索已关闭或已转化，不能申请变更");
        }

        // 3. 校验是否为当前归属人
        if (leadEntity.getAssignedEmployeeId() != null && leadEntity.getAssignedEmployeeId().equals(currentUserId)) {
            return ResponseDTO.userErrorParam("不能申请变更为自己");
        }

        // 4. 检查是否已有待审批的申请
        LeadOwnershipChangeRequestEntity existingRequest = requestDao.selectPendingByLeadId(applyForm.getLeadId());
        if (existingRequest != null) {
            return ResponseDTO.userErrorParam("该线索已有待审批的变更申请，请等待审批完成");
        }

        // 5. 获取申请人信息
        EmployeeEntity applicant = employeeService.getById(currentUserId);
        if (applicant == null) {
            return ResponseDTO.userErrorParam("申请人信息不存在");
        }

        // 6. 获取部门信息和部门负责人
        DepartmentVO department = departmentService.getDepartmentById(applicant.getDepartmentId());
        if (department == null) {
            return ResponseDTO.userErrorParam("申请人部门信息不存在");
        }

        // 7. 获取原归属人信息
        String originalOwnerName = "未分配";
        if (leadEntity.getAssignedEmployeeId() != null) {
            EmployeeEntity originalOwner = employeeService.getById(leadEntity.getAssignedEmployeeId());
            if (originalOwner != null) {
                originalOwnerName = originalOwner.getActualName();
            }
        }

        // 8. 计算距离最后跟进时间
        LocalDateTime lastFollowTime = null;
        Integer daysSinceLastFollow = null;
        // TODO: 这里需要查询最后跟进时间，暂时使用创建时间
        if (leadEntity.getCreateTime() != null) {
            lastFollowTime = leadEntity.getCreateTime();
            daysSinceLastFollow = (int) ChronoUnit.DAYS.between(lastFollowTime, LocalDateTime.now());
        }

        // 9. 创建申请记录
        LeadOwnershipChangeRequestEntity requestEntity = new LeadOwnershipChangeRequestEntity();
        requestEntity.setLeadId(applyForm.getLeadId());
        requestEntity.setCustomerPhone(leadEntity.getCustomerPhone());
        requestEntity.setCustomerName(leadEntity.getCustomerName());
        requestEntity.setOriginalOwnerId(leadEntity.getAssignedEmployeeId());
        requestEntity.setOriginalOwnerName(originalOwnerName);
        requestEntity.setNewOwnerId(currentUserId);
        requestEntity.setNewOwnerName(applicant.getActualName());
        requestEntity.setDepartmentId(applicant.getDepartmentId());
        requestEntity.setDepartmentName(department.getDepartmentName());
        requestEntity.setDepartmentManagerId(department.getManagerId());
        
        // 获取部门负责人姓名
        if (department.getManagerId() != null) {
            EmployeeEntity manager = employeeService.getById(department.getManagerId());
            if (manager != null) {
                requestEntity.setDepartmentManagerName(manager.getActualName());
            }
        }
        
        requestEntity.setRequestReason(applyForm.getRequestReason());
        requestEntity.setRequestStatus(LeadOwnershipChangeStatusEnum.PENDING.getValue());
        requestEntity.setLastFollowTime(lastFollowTime);
        requestEntity.setDaysSinceLastFollow(daysSinceLastFollow);
        requestEntity.setDeletedFlag(false);
        requestEntity.setCreateTime(LocalDateTime.now());
        requestEntity.setUpdateTime(LocalDateTime.now());
        requestEntity.setCreateUserId(currentUserId);
        requestEntity.setUpdateUserId(currentUserId);

        requestDao.insert(requestEntity);

        // 10. 记录数据变动
        dataTracerService.insert(requestEntity.getRequestId(), DataTracerTypeEnum.LEAD_OWNERSHIP_CHANGE_REQUEST);

        // TODO: 11. 发送通知给部门负责人
        // notificationService.sendApprovalNotification(department.getManagerId(), requestEntity);

        log.info("线索归属变更申请提交成功：申请ID={}, 线索ID={}, 申请人={}, 原归属人={}", 
            requestEntity.getRequestId(), applyForm.getLeadId(), applicant.getActualName(), originalOwnerName);

        return ResponseDTO.ok("申请提交成功，请等待部门负责人审批");
    }

    /**
     * 撤销申请
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> cancelRequest(Long requestId) {
        Long currentUserId = AdminRequestUtil.getRequestUserId();
        
        // 1. 校验申请是否存在
        LeadOwnershipChangeRequestEntity requestEntity = requestDao.selectById(requestId);
        if (requestEntity == null || requestEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("申请不存在");
        }

        // 2. 校验是否为申请人
        if (!requestEntity.getNewOwnerId().equals(currentUserId)) {
            return ResponseDTO.userErrorParam("只能撤销自己的申请");
        }

        // 3. 校验申请状态
        if (!LeadOwnershipChangeStatusEnum.PENDING.getValue().equals(requestEntity.getRequestStatus())) {
            return ResponseDTO.userErrorParam("只能撤销待审批的申请");
        }

        // 4. 更新申请状态
        requestEntity.setRequestStatus(LeadOwnershipChangeStatusEnum.CANCELLED.getValue());
        requestEntity.setUpdateTime(LocalDateTime.now());
        requestEntity.setUpdateUserId(currentUserId);
        
        requestDao.updateById(requestEntity);

        log.info("线索归属变更申请撤销成功：申请ID={}, 申请人={}", requestId, requestEntity.getNewOwnerName());

        return ResponseDTO.ok("申请撤销成功");
    }

    /**
     * 查询我的申请列表
     */
    public ResponseDTO<PageResult<LeadOwnershipChangeRequestVO>> queryMyRequests(LeadOwnershipChangeQueryForm queryForm) {
        Long currentUserId = AdminRequestUtil.getRequestUserId();
        queryForm.setOnlyMyRequests(true);
        // 这里需要在查询中设置当前用户ID，但由于QueryForm中没有这个字段，我们需要在Mapper中处理
        return queryPage(queryForm);
    }

    /**
     * 查询待我审批的申请列表
     */
    public ResponseDTO<PageResult<LeadOwnershipChangeRequestVO>> queryPendingApprovals(LeadOwnershipChangeQueryForm queryForm) {
        Long currentUserId = AdminRequestUtil.getRequestUserId();
        queryForm.setOnlyPendingApproval(true);
        // 这里需要在查询中设置当前用户ID，但由于QueryForm中没有这个字段，我们需要在Mapper中处理
        return queryPage(queryForm);
    }

    /**
     * 查询变更历史
     */
    public ResponseDTO<List<LeadOwnershipChangeHistoryVO>> queryChangeHistory(Long leadId) {
        List<LeadOwnershipChangeHistoryVO> historyList = historyDao.selectByLeadId(leadId);
        return ResponseDTO.ok(historyList);
    }

    /**
     * 审批变更申请
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> approveRequest(LeadOwnershipChangeApproveForm approveForm) {
        Long currentUserId = AdminRequestUtil.getRequestUserId();

        // 1. 校验申请是否存在
        LeadOwnershipChangeRequestEntity requestEntity = requestDao.selectById(approveForm.getRequestId());
        if (requestEntity == null || requestEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("申请不存在");
        }

        // 2. 校验是否为部门负责人
        if (!currentUserId.equals(requestEntity.getDepartmentManagerId())) {
            return ResponseDTO.userErrorParam("只有部门负责人才能审批");
        }

        // 3. 校验申请状态
        if (!LeadOwnershipChangeStatusEnum.PENDING.getValue().equals(requestEntity.getRequestStatus())) {
            return ResponseDTO.userErrorParam("只能审批待审批的申请");
        }

        // 4. 获取审批人信息
        EmployeeEntity approver = employeeService.getById(currentUserId);
        if (approver == null) {
            return ResponseDTO.userErrorParam("审批人信息不存在");
        }

        // 5. 更新申请状态
        Integer newStatus = approveForm.getApproved() ?
            LeadOwnershipChangeStatusEnum.APPROVED.getValue() :
            LeadOwnershipChangeStatusEnum.REJECTED.getValue();

        requestEntity.setRequestStatus(newStatus);
        requestEntity.setApproveTime(LocalDateTime.now());
        requestEntity.setApproveReason(approveForm.getApproveReason());
        requestEntity.setApproveUserId(currentUserId);
        requestEntity.setApproveUserName(approver.getActualName());
        requestEntity.setUpdateTime(LocalDateTime.now());
        requestEntity.setUpdateUserId(currentUserId);

        requestDao.updateById(requestEntity);

        // 6. 如果同意，则变更线索归属
        if (approveForm.getApproved()) {
            ResponseDTO<String> changeResult = changeLeadOwnership(requestEntity);
            if (!changeResult.getOk()) {
                // 如果变更失败，回滚申请状态
                requestEntity.setRequestStatus(LeadOwnershipChangeStatusEnum.PENDING.getValue());
                requestEntity.setApproveTime(null);
                requestEntity.setApproveReason(null);
                requestEntity.setApproveUserId(null);
                requestEntity.setApproveUserName(null);
                requestDao.updateById(requestEntity);
                return changeResult;
            }
        }

        // 7. 记录数据变动
        dataTracerService.insert(requestEntity.getRequestId(), DataTracerTypeEnum.LEAD_OWNERSHIP_CHANGE_REQUEST);

        // TODO: 8. 发送通知给申请人
        // notificationService.sendApprovalResultNotification(requestEntity.getNewOwnerId(), requestEntity, approveForm.getApproved());

        String resultMsg = approveForm.getApproved() ? "同意" : "拒绝";
        log.info("线索归属变更申请审批完成：申请ID={}, 审批结果={}, 审批人={}",
            approveForm.getRequestId(), resultMsg, approver.getActualName());

        return ResponseDTO.ok("审批完成");
    }

    /**
     * 变更线索归属
     */
    private ResponseDTO<String> changeLeadOwnership(LeadOwnershipChangeRequestEntity requestEntity) {
        try {
            // 1. 校验线索是否还存在
            LeadEntity leadEntity = leadDao.selectById(requestEntity.getLeadId());
            if (leadEntity == null || leadEntity.getDeletedFlag()) {
                return ResponseDTO.userErrorParam("线索不存在，无法变更归属");
            }

            // 2. 校验新归属人是否还存在
            EmployeeEntity newOwner = employeeService.getById(requestEntity.getNewOwnerId());
            if (newOwner == null || newOwner.getDisabledFlag()) {
                return ResponseDTO.userErrorParam("新归属人不存在或已禁用，无法变更归属");
            }

            // 3. 记录变更历史
            LeadOwnershipChangeHistoryEntity historyEntity = new LeadOwnershipChangeHistoryEntity();
            historyEntity.setLeadId(requestEntity.getLeadId());
            historyEntity.setRequestId(requestEntity.getRequestId());
            historyEntity.setChangeType(LeadOwnershipChangeTypeEnum.REQUEST_CHANGE.getValue());
            historyEntity.setOriginalOwnerId(requestEntity.getOriginalOwnerId());
            historyEntity.setOriginalOwnerName(requestEntity.getOriginalOwnerName());
            historyEntity.setNewOwnerId(requestEntity.getNewOwnerId());
            historyEntity.setNewOwnerName(requestEntity.getNewOwnerName());
            historyEntity.setChangeReason("通过申请审批变更：" + requestEntity.getRequestReason());
            historyEntity.setOperateUserId(requestEntity.getApproveUserId());
            historyEntity.setOperateUserName(requestEntity.getApproveUserName());
            historyEntity.setCreateTime(LocalDateTime.now());

            historyDao.insert(historyEntity);

            // 4. 更新线索归属
            leadEntity.setAssignedEmployeeId(requestEntity.getNewOwnerId());
            leadEntity.setUpdateTime(LocalDateTime.now());
            leadEntity.setUpdateUserId(requestEntity.getApproveUserId());

            leadDao.updateById(leadEntity);

            // 5. 记录线索数据变动
            dataTracerService.insert(leadEntity.getLeadId(), DataTracerTypeEnum.LEAD);

            log.info("线索归属变更成功：线索ID={}, 原归属人={}, 新归属人={}",
                requestEntity.getLeadId(), requestEntity.getOriginalOwnerName(), requestEntity.getNewOwnerName());

            return ResponseDTO.ok("线索归属变更成功");

        } catch (Exception e) {
            log.error("线索归属变更失败：申请ID={}, 错误信息={}", requestEntity.getRequestId(), e.getMessage(), e);
            return ResponseDTO.userErrorParam("线索归属变更失败：" + e.getMessage());
        }
    }
}

package net.lab1024.sa.admin.module.business.hospital.lead.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.form.LeadOwnershipChangeApplyForm;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.form.LeadOwnershipChangeApproveForm;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.form.LeadOwnershipChangeQueryForm;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadOwnershipChangeRequestVO;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadOwnershipChangeHistoryVO;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadDuplicateCheckVO;
import net.lab1024.sa.admin.module.business.hospital.lead.service.LeadOwnershipChangeService;
import net.lab1024.sa.admin.module.business.hospital.lead.service.LeadService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartRequestUtil;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 线索归属变更管理控制器
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Tag(name = "线索归属变更管理")
@RestController
@RequestMapping("/api/lead/ownership")
public class LeadOwnershipChangeController {

    @Resource
    private LeadOwnershipChangeService ownershipChangeService;

    @Resource
    private LeadService leadService;

    @Operation(summary = "检查线索重复")
    @PostMapping("/checkDuplicate")
    @SaCheckPermission("hospital:lead:query")
    public ResponseDTO<LeadDuplicateCheckVO> checkDuplicate(@RequestParam String customerPhone,
                                                           @RequestParam(required = false) Long excludeLeadId) {
        return leadService.checkLeadDuplicate(customerPhone, excludeLeadId);
    }

    @Operation(summary = "提交归属变更申请")
    @PostMapping("/apply")
    @SaCheckPermission("hospital:lead:ownership:apply")
    public ResponseDTO<String> applyOwnershipChange(@Valid @RequestBody LeadOwnershipChangeApplyForm applyForm) {
        return ownershipChangeService.applyOwnershipChange(applyForm);
    }

    @Operation(summary = "撤销变更申请")
    @PostMapping("/cancel/{requestId}")
    @SaCheckPermission("hospital:lead:ownership:apply")
    public ResponseDTO<String> cancelRequest(@PathVariable Long requestId) {
        return ownershipChangeService.cancelRequest(requestId);
    }

    @Operation(summary = "分页查询变更申请")
    @PostMapping("/query")
    @SaCheckPermission("hospital:lead:ownership:query")
    public ResponseDTO<PageResult<LeadOwnershipChangeRequestVO>> queryPage(@Valid @RequestBody LeadOwnershipChangeQueryForm queryForm) {
        // 设置当前用户ID用于权限过滤
        Long currentUserId = SmartRequestUtil.getRequestUserId();
        queryForm.setRequestUserId(currentUserId);
        return ownershipChangeService.queryPage(queryForm);
    }

    @Operation(summary = "查询我的申请")
    @PostMapping("/myRequests")
    @SaCheckPermission("hospital:lead:ownership:apply")
    public ResponseDTO<PageResult<LeadOwnershipChangeRequestVO>> queryMyRequests(@Valid @RequestBody LeadOwnershipChangeQueryForm queryForm) {
        return ownershipChangeService.queryMyRequests(queryForm);
    }

    @Operation(summary = "查询待我审批的申请")
    @PostMapping("/pendingApprovals")
    @SaCheckPermission("hospital:lead:ownership:approve")
    public ResponseDTO<PageResult<LeadOwnershipChangeRequestVO>> queryPendingApprovals(@Valid @RequestBody LeadOwnershipChangeQueryForm queryForm) {
        return ownershipChangeService.queryPendingApprovals(queryForm);
    }

    @Operation(summary = "审批变更申请")
    @PostMapping("/approve")
    @SaCheckPermission("hospital:lead:ownership:approve")
    public ResponseDTO<String> approveRequest(@Valid @RequestBody LeadOwnershipChangeApproveForm approveForm) {
        return ownershipChangeService.approveRequest(approveForm);
    }

    @Operation(summary = "查询线索变更历史")
    @GetMapping("/history/{leadId}")
    @SaCheckPermission("hospital:lead:ownership:history")
    public ResponseDTO<List<LeadOwnershipChangeHistoryVO>> queryChangeHistory(@PathVariable Long leadId) {
        return ownershipChangeService.queryChangeHistory(leadId);
    }
}

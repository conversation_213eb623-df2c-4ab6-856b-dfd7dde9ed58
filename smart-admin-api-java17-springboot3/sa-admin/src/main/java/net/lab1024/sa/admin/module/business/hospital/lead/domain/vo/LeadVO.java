package net.lab1024.sa.admin.module.business.hospital.lead.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.admin.module.business.hospital.lead.constant.LeadQualityEnum;
import net.lab1024.sa.admin.module.business.hospital.lead.constant.LeadStatusEnum;
import net.lab1024.sa.base.common.swagger.SchemaEnum;

import java.time.LocalDateTime;

/**
 * 线索视图对象
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class LeadVO {

    @Schema(description = "线索ID")
    private Long leadId;

    @Schema(description = "线索来源")
    private String leadSource;

    @Schema(description = "线索来源名称")
    private String leadSourceName;

    @Schema(description = "客户姓名")
    private String customerName;

    @Schema(description = "客户电话")
    private String customerPhone;

    @Schema(description = "工作手机设备标识")
    private String workPhone;

    @Schema(description = "微信号")
    private String customerWechat;

    @Schema(description = "地区")
    private String region;

    @Schema(description = "地区名称")
    private String regionName;

    @Schema(description = "性别：1-男，2-女")
    private Integer gender;

    @Schema(description = "性别名称")
    private String genderName;

    @Schema(description = "年龄")
    private Integer age;

    @Schema(description = "症状")
    private String symptom;

    @Schema(description = "线索状态：1-新线索，2-跟进中，3-已预约，4-已转化，5-已关闭")
    @SchemaEnum(LeadStatusEnum.class)
    private Integer leadStatus;

    @Schema(description = "线索状态名称")
    private String leadStatusName;

    @Schema(description = "线索质量：1-A级，2-B级，3-C级")
    @SchemaEnum(LeadQualityEnum.class)
    private Integer leadQuality;

    @Schema(description = "线索质量名称")
    private String leadQualityName;

    @Schema(description = "分配员工ID")
    private Long assignedEmployeeId;

    @Schema(description = "分配员工姓名")
    private String assignedEmployeeName;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "聊天记录")
    private String chatRecord;

    @Schema(description = "创建人姓名")
    private String createUserName;

    @Schema(description = "负责人姓名")
    private String responsiblePersonName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "最后跟进时间")
    private LocalDateTime lastFollowTime;

    @Schema(description = "跟进次数")
    private Integer followCount;
}

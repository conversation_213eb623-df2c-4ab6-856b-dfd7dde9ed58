package net.lab1024.sa.admin.module.business.hospital.customer.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 病历视图对象
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class MedicalRecordVO {

    @Schema(description = "病历ID")
    private Long recordId;

    @Schema(description = "关联线索ID")
    private Long leadId;

    @Schema(description = "客户ID")
    private Long customerId;

    @Schema(description = "客户姓名")
    private String customerName;

    @Schema(description = "关联预约ID")
    private Long appointmentId;

    @Schema(description = "就诊日期")
    private LocalDate visitDate;

    @Schema(description = "主诉")
    private String chiefComplaint;

    @Schema(description = "现病史")
    private String presentIllness;

    @Schema(description = "既往史")
    private String pastHistory;

    @Schema(description = "检查结果")
    private String examination;

    @Schema(description = "诊断结果")
    private String diagnosis;

    @Schema(description = "治疗方案")
    private String treatment;

    @Schema(description = "用药情况")
    private String medication;

    @Schema(description = "医嘱")
    private String doctorAdvice;

    @Schema(description = "下次复诊时间")
    private LocalDate nextVisitDate;

    @Schema(description = "主治医生ID")
    private Long doctorId;

    @Schema(description = "主治医生姓名")
    private String doctorName;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人姓名")
    private String createUserName;
}

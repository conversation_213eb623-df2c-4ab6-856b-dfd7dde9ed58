package net.lab1024.sa.admin.module.business.hospital.customer.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.domain.PageParam;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 病历查询表单
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class MedicalRecordQueryForm extends PageParam {

    @Schema(description = "客户ID")
    private Long customerId;

    @Schema(description = "客户姓名")
    @Length(max = 100, message = "客户姓名最多100字符")
    private String customerName;

    @Schema(description = "主治医生ID")
    private Long doctorId;

    @Schema(description = "就诊日期-开始")
    private LocalDate visitDateStart;

    @Schema(description = "就诊日期-结束")
    private LocalDate visitDateEnd;

    @Schema(description = "创建时间-开始")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间-结束")
    private LocalDateTime createTimeEnd;

    @Schema(description = "搜索关键词")
    @Length(max = 50, message = "搜索关键词最多50字符")
    private String searchWord;

    @Schema(hidden = true)
    private Boolean deletedFlag;
}

package net.lab1024.sa.admin.module.business.hospital.lead.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 线索跟进提醒VO
 * 
 * <AUTHOR>
 * @Date 2025-07-22
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@Schema(description = "线索跟进提醒")
public class LeadFollowReminderVO {

    @Schema(description = "提醒ID")
    private Long reminderId;

    @Schema(description = "线索ID")
    private Long leadId;

    @Schema(description = "客户姓名")
    private String customerName;

    @Schema(description = "客户电话")
    private String customerPhone;

    @Schema(description = "线索来源")
    private String leadSource;

    @Schema(description = "计划跟进时间")
    private LocalDateTime followTime;

    @Schema(description = "提醒时间")
    private LocalDateTime reminderTime;

    @Schema(description = "提醒方式")
    private Integer reminderType;

    @Schema(description = "提醒方式名称")
    private String reminderTypeName;

    @Schema(description = "提前提醒分钟数")
    private Integer reminderMinutes;

    @Schema(description = "提醒状态")
    private Integer reminderStatus;

    @Schema(description = "提醒状态名称")
    private String reminderStatusName;

    @Schema(description = "实际发送时间")
    private LocalDateTime sentTime;

    @Schema(description = "提醒内容")
    private String reminderContent;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人姓名")
    private String createUserName;

    @Schema(description = "是否逾期")
    private Boolean overdue;

    @Schema(description = "距离跟进时间（分钟）")
    private Long minutesToFollow;
}

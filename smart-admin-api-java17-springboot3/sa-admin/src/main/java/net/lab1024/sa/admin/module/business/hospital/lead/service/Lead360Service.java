package net.lab1024.sa.admin.module.business.hospital.lead.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.hospital.appointment.dao.AppointmentDao;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.vo.AppointmentVO;
import net.lab1024.sa.admin.module.business.hospital.lead.dao.*;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.entity.*;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.Lead360VO;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadFollowVO;
import net.lab1024.sa.admin.module.system.employee.dao.EmployeeDao;
import net.lab1024.sa.admin.module.system.employee.domain.entity.EmployeeEntity;
import net.lab1024.sa.admin.util.AdminRequestUtil;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 线索360度视图服务
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Slf4j
@Service
public class Lead360Service {

    @Resource
    private LeadDao leadDao;
    @Resource
    private LeadFollowDao leadFollowDao;
    @Resource
    private LeadScoreDao leadScoreDao;
    @Resource
    private LeadStatisticsDao leadStatisticsDao;
    @Resource
    private LeadSuggestionDao leadSuggestionDao;
    @Resource
    private LeadQuickNoteDao leadQuickNoteDao;
    @Resource
    private AppointmentDao appointmentDao;
    @Resource
    private EmployeeDao employeeDao;

    /**
     * 获取线索360度视图数据
     */
    public ResponseDTO<Lead360VO> getLead360View(Long leadId) {
        try {
            // 权限验证
            Long currentUserId = AdminRequestUtil.getRequestUserId();
            if (!hasLeadPermission(leadId, currentUserId)) {
                return ResponseDTO.userErrorParam("无权限查看该线索");
            }

            Lead360VO lead360VO = new Lead360VO();

            // 1. 基础信息
            Lead360VO.LeadBasicInfoVO basicInfo = buildBasicInfo(leadId);
            if (basicInfo == null) {
                return ResponseDTO.userErrorParam("线索不存在");
            }
            lead360VO.setBasicInfo(basicInfo);

            // 2. 评分信息
            Lead360VO.LeadScoreVO scoreInfo = buildScoreInfo(leadId);
            lead360VO.setScoreInfo(scoreInfo);

            // 3. 统计信息
            Lead360VO.LeadStatisticsVO statistics = buildStatistics(leadId);
            lead360VO.setStatistics(statistics);

            // 4. 客户旅程时间线
            List<Lead360VO.CustomerJourneyEventVO> timeline = buildCustomerJourneyTimeline(leadId);
            lead360VO.setTimeline(timeline);

            // 5. 跟进记录
            List<LeadFollowVO> followRecords = leadFollowDao.selectByLeadId(leadId);
            lead360VO.setFollowRecords(followRecords);

            // 6. 聊天记录
            List<Lead360VO.ChatRecordVO> chatRecords = buildChatRecords(leadId);
            lead360VO.setChatRecords(chatRecords);

            // 7. 预约记录
            List<Lead360VO.AppointmentSimpleVO> appointments = buildAppointments(leadId);
            lead360VO.setAppointments(appointments);

            // 8. 智能建议
            List<Lead360VO.LeadSuggestionVO> suggestions = buildSuggestions(leadId);
            lead360VO.setSuggestions(suggestions);

            // 9. 快速笔记
            String quickNote = getQuickNote(leadId);
            lead360VO.setQuickNote(quickNote);

            return ResponseDTO.ok(lead360VO);
        } catch (Exception e) {
            log.error("获取线索360度视图失败", e);
            return ResponseDTO.userErrorParam("获取数据失败");
        }
    }

    /**
     * 构建基础信息
     */
    private Lead360VO.LeadBasicInfoVO buildBasicInfo(Long leadId) {
        LeadEntity leadEntity = leadDao.selectById(leadId);
        if (leadEntity == null || leadEntity.getDeletedFlag()) {
            return null;
        }

        Lead360VO.LeadBasicInfoVO basicInfo = SmartBeanUtil.copy(leadEntity, Lead360VO.LeadBasicInfoVO.class);

        // 设置字典值名称
        basicInfo.setLeadSourceName(convertLeadSourceToName(leadEntity.getLeadSource()));
        basicInfo.setGenderName(convertGenderToName(leadEntity.getGender()));
        basicInfo.setRegionName(convertRegionToName(leadEntity.getRegion()));
        basicInfo.setLeadStatusName(convertLeadStatusToName(leadEntity.getLeadStatus()));
        basicInfo.setLeadQualityName(convertLeadQualityToName(leadEntity.getLeadQuality()));

        // 设置分配员工名称
        if (leadEntity.getAssignedEmployeeId() != null) {
            EmployeeEntity employee = employeeDao.selectById(leadEntity.getAssignedEmployeeId());
            if (employee != null) {
                basicInfo.setAssignedEmployeeName(employee.getActualName());
            }
        }

        return basicInfo;
    }

    /**
     * 构建评分信息
     */
    private Lead360VO.LeadScoreVO buildScoreInfo(Long leadId) {
        LeadScoreEntity scoreEntity = leadScoreDao.selectByLeadId(leadId);
        if (scoreEntity == null) {
            // 如果没有评分记录，创建默认评分
            scoreEntity = createDefaultScore(leadId);
        }

        Lead360VO.LeadScoreVO scoreVO = SmartBeanUtil.copy(scoreEntity, Lead360VO.LeadScoreVO.class);
        if (scoreEntity.getConversionProbability() != null) {
            scoreVO.setConversionProbability(scoreEntity.getConversionProbability().doubleValue());
        }

        return scoreVO;
    }

    /**
     * 构建统计信息
     */
    private Lead360VO.LeadStatisticsVO buildStatistics(Long leadId) {
        LeadStatisticsEntity statisticsEntity = leadStatisticsDao.selectByLeadId(leadId);
        if (statisticsEntity == null) {
            // 如果没有统计记录，创建默认统计
            statisticsEntity = createDefaultStatistics(leadId);
        }

        Lead360VO.LeadStatisticsVO statisticsVO = SmartBeanUtil.copy(statisticsEntity, Lead360VO.LeadStatisticsVO.class);
        if (statisticsEntity.getResponseRate() != null) {
            statisticsVO.setResponseRate(statisticsEntity.getResponseRate().doubleValue());
        }

        return statisticsVO;
    }

    /**
     * 构建客户旅程时间线
     */
    private List<Lead360VO.CustomerJourneyEventVO> buildCustomerJourneyTimeline(Long leadId) {
        List<Lead360VO.CustomerJourneyEventVO> timeline = new ArrayList<>();

        // 线索创建事件
        LeadEntity lead = leadDao.selectById(leadId);
        if (lead != null) {
            Lead360VO.CustomerJourneyEventVO createEvent = new Lead360VO.CustomerJourneyEventVO();
            createEvent.setEventId("lead_create_" + leadId);
            createEvent.setEventType("LEAD_CREATED");
            createEvent.setEventTime(lead.getCreateTime());
            createEvent.setTitle("线索创建");
            createEvent.setDescription("来源：" + convertLeadSourceToName(lead.getLeadSource()));
            createEvent.setIcon("user-add");
            createEvent.setColor("blue");
            timeline.add(createEvent);
        }

        // 跟进记录事件
        List<LeadFollowVO> follows = leadFollowDao.selectByLeadId(leadId);
        for (LeadFollowVO follow : follows) {
            Lead360VO.CustomerJourneyEventVO followEvent = new Lead360VO.CustomerJourneyEventVO();
            followEvent.setEventId("follow_" + follow.getFollowId());
            followEvent.setEventType("FOLLOW_RECORD");
            followEvent.setEventTime(follow.getCreateTime());
            followEvent.setTitle("跟进记录");
            followEvent.setDescription(StringUtils.isNotBlank(follow.getFollowContent()) ?
                (follow.getFollowContent().length() > 50 ? follow.getFollowContent().substring(0, 50) + "..." : follow.getFollowContent()) : "");
            followEvent.setIcon("message");
            followEvent.setColor("green");
            timeline.add(followEvent);
        }

        // 预约事件
        List<AppointmentVO> appointments = appointmentDao.selectByLeadId(leadId);
        for (AppointmentVO appointment : appointments) {
            Lead360VO.CustomerJourneyEventVO appointmentEvent = new Lead360VO.CustomerJourneyEventVO();
            appointmentEvent.setEventId("appointment_" + appointment.getAppointmentId());
            appointmentEvent.setEventType("APPOINTMENT");
            appointmentEvent.setEventTime(appointment.getCreateTime());
            appointmentEvent.setTitle("预约创建");
            appointmentEvent.setDescription("预约时间：" + appointment.getAppointmentDate() + " " + appointment.getAppointmentTime());
            appointmentEvent.setIcon("calendar");
            appointmentEvent.setColor("orange");
            timeline.add(appointmentEvent);
        }

        // 按时间倒序排列
        timeline.sort((a, b) -> b.getEventTime().compareTo(a.getEventTime()));

        return timeline;
    }

    /**
     * 构建聊天记录
     */
    private List<Lead360VO.ChatRecordVO> buildChatRecords(Long leadId) {
        List<Lead360VO.ChatRecordVO> chatRecords = new ArrayList<>();

        LeadEntity leadEntity = leadDao.selectById(leadId);
        if (leadEntity != null && StringUtils.isNotBlank(leadEntity.getChatRecord())) {
            try {
                // 解析JSON格式的聊天记录
                JSONArray chatArray = JSON.parseArray(leadEntity.getChatRecord());
                for (int i = 0; i < chatArray.size(); i++) {
                    JSONObject chatObj = chatArray.getJSONObject(i);
                    Lead360VO.ChatRecordVO chatRecord = new Lead360VO.ChatRecordVO();
                    
                    // 解析时间
                    String timeStr = chatObj.getString("time");
                    if (StringUtils.isNotBlank(timeStr)) {
                        try {
                            chatRecord.setTime(LocalDateTime.parse(timeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                        } catch (Exception e) {
                            chatRecord.setTime(LocalDateTime.now());
                        }
                    } else {
                        chatRecord.setTime(LocalDateTime.now());
                    }
                    
                    chatRecord.setSender(chatObj.getString("sender"));
                    chatRecord.setContent(chatObj.getString("content"));
                    chatRecord.setType(chatObj.getString("type"));
                    
                    chatRecords.add(chatRecord);
                }
            } catch (Exception e) {
                log.warn("解析聊天记录失败，线索ID：{}", leadId, e);
                // 如果解析失败，尝试作为纯文本处理
                Lead360VO.ChatRecordVO chatRecord = new Lead360VO.ChatRecordVO();
                chatRecord.setTime(leadEntity.getUpdateTime());
                chatRecord.setSender("系统");
                chatRecord.setContent(leadEntity.getChatRecord());
                chatRecord.setType("staff");
                chatRecords.add(chatRecord);
            }
        }

        // 按时间正序排列
        chatRecords.sort(Comparator.comparing(Lead360VO.ChatRecordVO::getTime));

        return chatRecords;
    }

    /**
     * 构建预约记录
     */
    private List<Lead360VO.AppointmentSimpleVO> buildAppointments(Long leadId) {
        List<AppointmentVO> appointments = appointmentDao.selectByLeadId(leadId);
        return appointments.stream().map(appointment -> {
            Lead360VO.AppointmentSimpleVO appointmentVO = new Lead360VO.AppointmentSimpleVO();
            appointmentVO.setAppointmentId(appointment.getAppointmentId());
            appointmentVO.setAppointmentNo("APT" + appointment.getAppointmentId()); // 生成预约编号
            appointmentVO.setAppointmentDate(appointment.getAppointmentDate() != null ? appointment.getAppointmentDate().toString() : "");
            appointmentVO.setAppointmentTime(appointment.getAppointmentTime() != null ? appointment.getAppointmentTime().toString() : "");
            appointmentVO.setAppointmentStatus(appointment.getAppointmentStatus());
            appointmentVO.setAppointmentStatusName(convertAppointmentStatusToName(appointment.getAppointmentStatus()));
            appointmentVO.setDoctorName(appointment.getDoctorName());
            appointmentVO.setProjectName(appointment.getProjectName());
            appointmentVO.setCreateTime(appointment.getCreateTime());
            return appointmentVO;
        }).collect(Collectors.toList());
    }

    /**
     * 构建智能建议
     */
    private List<Lead360VO.LeadSuggestionVO> buildSuggestions(Long leadId) {
        // 先生成建议（如果不存在）
        generateSuggestions(leadId);

        // 查询待处理的建议
        List<LeadSuggestionEntity> suggestions = leadSuggestionDao.selectByLeadIdAndStatus(leadId, 1);
        return suggestions.stream().map(suggestion -> {
            Lead360VO.LeadSuggestionVO suggestionVO = SmartBeanUtil.copy(suggestion, Lead360VO.LeadSuggestionVO.class);
            suggestionVO.setIcon(getSuggestionIcon(suggestion.getSuggestionType()));
            return suggestionVO;
        }).collect(Collectors.toList());
    }

    /**
     * 获取快速笔记
     */
    private String getQuickNote(Long leadId) {
        LeadQuickNoteEntity noteEntity = leadQuickNoteDao.selectLatestByLeadId(leadId);
        return noteEntity != null ? noteEntity.getNoteContent() : "";
    }

    /**
     * 保存快速笔记
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> saveQuickNote(Long leadId, String noteContent) {
        try {
            // 权限验证
            Long currentUserId = AdminRequestUtil.getRequestUserId();
            if (!hasLeadPermission(leadId, currentUserId)) {
                return ResponseDTO.userErrorParam("无权限操作该线索");
            }

            // 删除旧笔记
            leadQuickNoteDao.deleteByLeadId(leadId);

            // 保存新笔记
            if (StringUtils.isNotBlank(noteContent)) {
                LeadQuickNoteEntity noteEntity = new LeadQuickNoteEntity();
                noteEntity.setLeadId(leadId);
                noteEntity.setNoteContent(noteContent);
                noteEntity.setCreateUserId(currentUserId);
                noteEntity.setCreateUserName(AdminRequestUtil.getRequestUser().getActualName());
                noteEntity.setCreateTime(LocalDateTime.now());
                noteEntity.setUpdateTime(LocalDateTime.now());
                leadQuickNoteDao.insert(noteEntity);
            }

            return ResponseDTO.ok();
        } catch (Exception e) {
            log.error("保存快速笔记失败", e);
            return ResponseDTO.userErrorParam("保存失败");
        }
    }

    /**
     * 添加聊天记录
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> addChatRecord(Long leadId, String sender, String content, String type) {
        try {
            // 权限验证
            Long currentUserId = AdminRequestUtil.getRequestUserId();
            if (!hasLeadPermission(leadId, currentUserId)) {
                return ResponseDTO.userErrorParam("无权限操作该线索");
            }

            LeadEntity leadEntity = leadDao.selectById(leadId);
            if (leadEntity == null || leadEntity.getDeletedFlag()) {
                return ResponseDTO.userErrorParam("线索不存在");
            }

            // 构建新的聊天记录
            JSONObject newChatRecord = new JSONObject();
            newChatRecord.put("time", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            newChatRecord.put("sender", sender);
            newChatRecord.put("content", content);
            newChatRecord.put("type", type);

            // 获取现有聊天记录
            JSONArray chatArray;
            if (StringUtils.isNotBlank(leadEntity.getChatRecord())) {
                try {
                    chatArray = JSON.parseArray(leadEntity.getChatRecord());
                } catch (Exception e) {
                    chatArray = new JSONArray();
                }
            } else {
                chatArray = new JSONArray();
            }

            // 添加新记录
            chatArray.add(newChatRecord);

            // 更新线索
            leadEntity.setChatRecord(chatArray.toJSONString());
            leadEntity.setUpdateTime(LocalDateTime.now());
            leadEntity.setUpdateUserId(currentUserId);
            leadDao.updateById(leadEntity);

            return ResponseDTO.ok();
        } catch (Exception e) {
            log.error("添加聊天记录失败", e);
            return ResponseDTO.userErrorParam("添加失败");
        }
    }

    /**
     * 创建默认评分
     */
    private LeadScoreEntity createDefaultScore(Long leadId) {
        LeadEntity leadEntity = leadDao.selectById(leadId);
        LeadScoreEntity scoreEntity = new LeadScoreEntity();
        scoreEntity.setLeadId(leadId);

        // 根据线索质量设置默认分数
        if (leadEntity.getLeadQuality() != null) {
            switch (leadEntity.getLeadQuality()) {
                case 1: // A级
                    scoreEntity.setTotalScore(85);
                    scoreEntity.setConversionProbability(new BigDecimal("75.00"));
                    break;
                case 2: // B级
                    scoreEntity.setTotalScore(65);
                    scoreEntity.setConversionProbability(new BigDecimal("55.00"));
                    break;
                case 3: // C级
                    scoreEntity.setTotalScore(45);
                    scoreEntity.setConversionProbability(new BigDecimal("35.00"));
                    break;
                default:
                    scoreEntity.setTotalScore(50);
                    scoreEntity.setConversionProbability(new BigDecimal("40.00"));
            }
        } else {
            scoreEntity.setTotalScore(50);
            scoreEntity.setConversionProbability(new BigDecimal("40.00"));
        }

        scoreEntity.setFollowScore(0);
        scoreEntity.setResponseScore(0);
        scoreEntity.setIntentionScore(0);
        scoreEntity.setLastCalculateTime(LocalDateTime.now());
        scoreEntity.setCreateTime(LocalDateTime.now());
        scoreEntity.setUpdateTime(LocalDateTime.now());

        leadScoreDao.insert(scoreEntity);
        return scoreEntity;
    }

    /**
     * 创建默认统计
     */
    private LeadStatisticsEntity createDefaultStatistics(Long leadId) {
        LeadStatisticsEntity statisticsEntity = new LeadStatisticsEntity();
        statisticsEntity.setLeadId(leadId);
        statisticsEntity.setFollowCount(0);
        statisticsEntity.setResponseCount(0);
        statisticsEntity.setResponseRate(new BigDecimal("0.00"));
        statisticsEntity.setAppointmentCount(0);
        statisticsEntity.setCreateTime(LocalDateTime.now());
        statisticsEntity.setUpdateTime(LocalDateTime.now());

        leadStatisticsDao.insert(statisticsEntity);
        return statisticsEntity;
    }

    /**
     * 生成智能建议
     */
    private void generateSuggestions(Long leadId) {
        // 检查是否已有建议
        List<LeadSuggestionEntity> existingSuggestions = leadSuggestionDao.selectByLeadIdAndStatus(leadId, 1);
        if (!existingSuggestions.isEmpty()) {
            return;
        }

        LeadEntity leadEntity = leadDao.selectById(leadId);
        LeadStatisticsEntity statistics = leadStatisticsDao.selectByLeadId(leadId);

        List<LeadSuggestionEntity> suggestions = new ArrayList<>();

        // 根据跟进情况生成建议
        if (statistics != null && statistics.getFollowCount() == 0) {
            LeadSuggestionEntity suggestion = new LeadSuggestionEntity();
            suggestion.setLeadId(leadId);
            suggestion.setSuggestionType("FOLLOW_UP");
            suggestion.setTitle("建议立即跟进");
            suggestion.setDescription("该线索尚未进行跟进，建议尽快联系客户");
            suggestion.setPriority(1);
            suggestion.setActionable(true);
            suggestion.setActionText("立即跟进");
            suggestion.setStatus(1);
            suggestion.setCreateTime(LocalDateTime.now());
            suggestion.setUpdateTime(LocalDateTime.now());
            suggestions.add(suggestion);
        }

        // 根据线索状态生成建议
        if (leadEntity.getLeadStatus() == 2 && statistics != null && statistics.getAppointmentCount() == 0) {
            LeadSuggestionEntity suggestion = new LeadSuggestionEntity();
            suggestion.setLeadId(leadId);
            suggestion.setSuggestionType("APPOINTMENT");
            suggestion.setTitle("建议创建预约");
            suggestion.setDescription("客户已在跟进中，可以尝试邀请客户预约到院");
            suggestion.setPriority(2);
            suggestion.setActionable(true);
            suggestion.setActionText("创建预约");
            suggestion.setStatus(1);
            suggestion.setCreateTime(LocalDateTime.now());
            suggestion.setUpdateTime(LocalDateTime.now());
            suggestions.add(suggestion);
        }

        // 批量插入建议
        for (LeadSuggestionEntity suggestion : suggestions) {
            leadSuggestionDao.insert(suggestion);
        }
    }

    /**
     * 权限验证
     */
    private boolean hasLeadPermission(Long leadId, Long currentUserId) {
        if (leadId == null || currentUserId == null) {
            return false;
        }

        LeadEntity leadEntity = leadDao.selectById(leadId);
        if (leadEntity == null || leadEntity.getDeletedFlag()) {
            return false;
        }

        EmployeeEntity currentEmployee = employeeDao.selectById(currentUserId);
        if (currentEmployee == null) {
            return false;
        }

        // 管理员有全部权限
        if (currentEmployee.getAdministratorFlag() != null && currentEmployee.getAdministratorFlag()) {
            return true;
        }

        // 创建者有权限
        if (leadEntity.getCreateUserId().equals(currentUserId)) {
            return true;
        }

        // 分配的员工有权限
        if (leadEntity.getAssignedEmployeeId() != null && leadEntity.getAssignedEmployeeId().equals(currentUserId)) {
            return true;
        }

        return false;
    }

    /**
     * 获取建议图标
     */
    private String getSuggestionIcon(String suggestionType) {
        switch (suggestionType) {
            case "FOLLOW_UP":
                return "phone";
            case "APPOINTMENT":
                return "calendar";
            case "QUALITY_IMPROVE":
                return "star";
            case "RESPONSE_URGENT":
                return "clock-circle";
            default:
                return "bulb";
        }
    }

    /**
     * 字典值转换方法
     */
    private String convertLeadSourceToName(String leadSource) {
        if (StringUtils.isBlank(leadSource)) {
            return "";
        }
        // 这里应该从字典服务获取，简化处理
        switch (leadSource) {
            case "SHANGWUTONG": return "商务通";
            case "BAIDU": return "百度";
            case "DOUYIN": return "抖音";
            case "KUAISHOU": return "快手";
            case "WECHAT": return "微信";
            case "PHONE": return "电话";
            case "REFERRAL": return "转介绍";
            default: return leadSource;
        }
    }

    private String convertGenderToName(Integer gender) {
        if (gender == null) {
            return "";
        }
        switch (gender) {
            case 1: return "男";
            case 2: return "女";
            default: return "未知";
        }
    }

    private String convertRegionToName(String region) {
        // 简化处理，实际应该从字典获取
        return StringUtils.isNotBlank(region) ? region : "";
    }

    private String convertLeadStatusToName(Integer leadStatus) {
        if (leadStatus == null) {
            return "";
        }
        switch (leadStatus) {
            case 1: return "新线索";
            case 2: return "跟进中";
            case 3: return "已预约";
            case 4: return "已转化";
            case 5: return "已关闭";
            default: return "未知";
        }
    }

    private String convertLeadQualityToName(Integer leadQuality) {
        if (leadQuality == null) {
            return "";
        }
        switch (leadQuality) {
            case 1: return "A级";
            case 2: return "B级";
            case 3: return "C级";
            default: return "未评级";
        }
    }

    private String convertAppointmentStatusToName(Integer appointmentStatus) {
        if (appointmentStatus == null) {
            return "";
        }
        switch (appointmentStatus) {
            case 1: return "未到诊";
            case 2: return "已到诊";
            case 3: return "已完成";
            case 4: return "爽约";
            case 5: return "已取消";
            default: return "未知";
        }
    }
}

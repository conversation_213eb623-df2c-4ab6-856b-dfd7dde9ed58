package net.lab1024.sa.admin.module.business.hospital.customer.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 客户标签新增表单
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class CustomerTagAddForm {

    @Schema(description = "标签名称")
    @NotBlank(message = "标签名称不能为空")
    private String tagName;

    @Schema(description = "标签描述")
    private String tagDescription;

    @Schema(description = "标签颜色")
    private String tagColor;

    @Schema(description = "标签分类ID")
    private Long categoryId;

    @Schema(description = "标签分类名称")
    private String categoryName;

    @Schema(description = "标签类型：1-手动标签，2-自动标签")
    @NotNull(message = "标签类型不能为空")
    private Integer tagType;

    @Schema(description = "自动打标规则（JSON格式）")
    private String autoRules;

    @Schema(description = "标签状态：1-启用，0-禁用")
    private Integer tagStatus;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "备注")
    private String remark;
}

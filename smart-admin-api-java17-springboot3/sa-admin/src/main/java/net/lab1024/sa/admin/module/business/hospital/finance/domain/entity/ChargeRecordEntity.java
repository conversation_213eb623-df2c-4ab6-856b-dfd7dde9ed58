package net.lab1024.sa.admin.module.business.hospital.finance.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 收费记录实体类
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@TableName("t_charge_record")
public class ChargeRecordEntity {

    /**
     * 收费记录ID
     */
    @TableId(type = IdType.AUTO)
    private Long chargeId;

    /**
     * 收费单号
     */
    private String chargeNo;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 客户姓名
     */
    private String customerName;

    /**
     * 客户电话
     */
    private String customerPhone;

    /**
     * 预约ID（关联预约）
     */
    private Long appointmentId;

    /**
     * 病历ID（关联病历）
     */
    private Long recordId;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目分类
     */
    private String projectCategory;

    /**
     * 标准价格
     */
    private BigDecimal standardPrice;

    /**
     * 实际收费金额
     */
    private BigDecimal chargeAmount;

    /**
     * 优惠金额
     */
    private BigDecimal discountAmount;

    /**
     * 优惠原因
     */
    private String discountReason;

    /**
     * 收费日期
     */
    private LocalDate chargeDate;

    /**
     * 收费状态：1-待收费，2-已收费，3-部分收费，4-已退费
     */
    private Integer chargeStatus;

    /**
     * 支付方式：1-现金，2-微信，3-支付宝，4-银行卡，5-组合支付
     */
    private Integer paymentMethod;

    /**
     * 支付详情（JSON格式，记录组合支付的详细信息）
     */
    private String paymentDetails;

    /**
     * 收费员工ID
     */
    private Long chargeEmployeeId;

    /**
     * 收费员工姓名
     */
    private String chargeEmployeeName;

    /**
     * 收费科室ID
     */
    private Long chargeDepartmentId;

    /**
     * 收费科室名称
     */
    private String chargeDepartmentName;

    /**
     * 发票号码
     */
    private String invoiceNo;

    /**
     * 是否开具发票
     */
    private Boolean invoiceFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标识
     */
    private Boolean deletedFlag;

    /**
     * 创建人ID
     */
    private Long createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    private Long updateUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}

package net.lab1024.sa.admin.module.business.hospital.lead.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.entity.LeadStatusHistoryEntity;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadStatusHistoryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 线索状态变更历史DAO
 * 
 * <AUTHOR>
 * @Date 2025-07-22
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Mapper
public interface LeadStatusHistoryDao extends BaseMapper<LeadStatusHistoryEntity> {

    /**
     * 根据线索ID查询状态变更历史
     * 
     * @param leadId 线索ID
     * @return 状态变更历史列表
     */
    List<LeadStatusHistoryVO> selectByLeadId(@Param("leadId") Long leadId);

    /**
     * 根据线索ID查询状态变更历史（包含停留时长）
     * 
     * @param leadId 线索ID
     * @return 状态变更历史列表
     */
    List<LeadStatusHistoryVO> selectByLeadIdWithDuration(@Param("leadId") Long leadId);

    /**
     * 统计各状态的线索数量
     * 
     * @return 状态统计结果
     */
    List<LeadStatusHistoryVO> getStatusStatistics();

    /**
     * 查询最近的状态变更记录
     * 
     * @param leadId 线索ID
     * @return 最近的状态变更记录
     */
    LeadStatusHistoryVO selectLatestByLeadId(@Param("leadId") Long leadId);
}

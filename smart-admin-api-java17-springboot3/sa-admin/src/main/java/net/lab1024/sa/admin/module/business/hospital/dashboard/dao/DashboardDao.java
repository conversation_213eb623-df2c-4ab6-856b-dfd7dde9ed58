package net.lab1024.sa.admin.module.business.hospital.dashboard.dao;

import net.lab1024.sa.admin.module.business.hospital.dashboard.domain.vo.TodoItemVO;
import net.lab1024.sa.admin.module.business.hospital.dashboard.domain.vo.ActivityVO;
import net.lab1024.sa.admin.module.business.hospital.dashboard.domain.vo.ChartDataVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 医院仪表板 DAO
 *
 * <AUTHOR>
 * @Date 2024-12-15 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright <a href="https://1024lab.net">1024创新实验室</a>
 */
@Mapper
public interface DashboardDao {

    /**
     * 获取今日待办事项
     */
    List<TodoItemVO> getTodayTodos();

    /**
     * 获取最近活动
     */
    List<ActivityVO> getRecentActivities();

    /**
     * 获取线索转化趋势
     */
    ChartDataVO getLeadConversionTrend(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 获取客户来源分布
     */
    ChartDataVO getCustomerSourceDistribution();

    /**
     * 获取预约状态分布
     */
    ChartDataVO getAppointmentStatusDistribution();

    /**
     * 获取月度收入趋势
     */
    ChartDataVO getRevenueTrend(@Param("year") String year);

    /**
     * 获取员工绩效统计
     */
    ChartDataVO getEmployeePerformance(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 获取部门统计
     */
    ChartDataVO getDepartmentStatistics();
} 
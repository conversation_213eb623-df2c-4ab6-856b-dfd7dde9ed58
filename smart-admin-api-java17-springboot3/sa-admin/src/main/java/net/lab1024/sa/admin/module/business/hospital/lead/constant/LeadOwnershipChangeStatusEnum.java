package net.lab1024.sa.admin.module.business.hospital.lead.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 线索归属变更申请状态枚举
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Getter
@AllArgsConstructor
public enum LeadOwnershipChangeStatusEnum implements BaseEnum {

    /**
     * 待审批
     */
    PENDING(1, "待审批"),

    /**
     * 已同意
     */
    APPROVED(2, "已同意"),

    /**
     * 已拒绝
     */
    REJECTED(3, "已拒绝"),

    /**
     * 已撤销
     */
    CANCELLED(4, "已撤销");

    private final Integer value;

    private final String desc;

    public static LeadOwnershipChangeStatusEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (LeadOwnershipChangeStatusEnum statusEnum : values()) {
            if (statusEnum.getValue().equals(value)) {
                return statusEnum;
            }
        }
        return null;
    }
}

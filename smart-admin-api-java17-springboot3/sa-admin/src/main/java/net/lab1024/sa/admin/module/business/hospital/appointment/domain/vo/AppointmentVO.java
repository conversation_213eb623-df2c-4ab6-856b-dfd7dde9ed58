package net.lab1024.sa.admin.module.business.hospital.appointment.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.admin.module.business.hospital.appointment.constant.AppointmentStatusEnum;
import net.lab1024.sa.base.common.swagger.SchemaEnum;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 预约视图对象
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class AppointmentVO {

    @Schema(description = "预约ID")
    private Long appointmentId;

    @Schema(description = "关联线索ID")
    private Long leadId;

    @Schema(description = "关联客户ID")
    private Long customerId;

    @Schema(description = "客户姓名")
    private String customerName;

    @Schema(description = "客户电话")
    private String customerPhone;

    @Schema(description = "性别：1-男，2-女")
    private Integer gender;



    @Schema(description = "预约日期")
    private LocalDate appointmentDate;

    @Schema(description = "预约时间")
    private LocalTime appointmentTime;

    @Schema(description = "预约状态：1-未到诊，2-已到诊")
    @SchemaEnum(AppointmentStatusEnum.class)
    private Integer appointmentStatus;

    @Schema(description = "预约状态名称")
    private String appointmentStatusName;

    @Schema(description = "实际到院时间")
    private LocalDateTime actualArrivalTime;

    @Schema(description = "微信号")
    private String wechat;

    @Schema(description = "年龄")
    private Integer age;

    @Schema(description = "症状描述")
    private String symptom;

    @Schema(description = "地区")
    private String region;

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "医生ID")
    private Long doctorId;

    @Schema(description = "医生姓名")
    private String doctorName;

    @Schema(description = "分配员工ID")
    private Long assignedEmployeeId;

    @Schema(description = "分配员工姓名")
    private String assignedEmployeeName;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人姓名")
    private String createUserName;
}

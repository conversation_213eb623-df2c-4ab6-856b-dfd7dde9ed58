package net.lab1024.sa.admin.module.business.hospital.appointment.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 预约日历视图对象
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class AppointmentCalendarVO {

    @Schema(description = "预约ID")
    private Long appointmentId;

    @Schema(description = "标题（客户姓名 + 项目名称）")
    private String title;

    @Schema(description = "开始时间（日期+时间）")
    private String start;

    @Schema(description = "结束时间（日期+时间）")
    private String end;

    @Schema(description = "颜色（根据状态）")
    private String color;

    @Schema(description = "客户姓名")
    private String customerName;

    @Schema(description = "客户电话")
    private String customerPhone;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "医生姓名")
    private String doctorName;

    @Schema(description = "预约状态")
    private Integer appointmentStatus;

    @Schema(description = "预约状态名称")
    private String appointmentStatusName;

    @Schema(description = "预约日期")
    private LocalDate appointmentDate;

    @Schema(description = "预约时间")
    private LocalTime appointmentTime;

    @Schema(description = "备注")
    private String remark;
}

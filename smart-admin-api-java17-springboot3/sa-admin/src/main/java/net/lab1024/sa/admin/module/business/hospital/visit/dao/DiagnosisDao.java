package net.lab1024.sa.admin.module.business.hospital.visit.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.entity.DiagnosisEntity;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.form.DiagnosisQueryForm;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.DiagnosisVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 诊断记录Dao
 *
 * <AUTHOR>
 * @Date 2025-07-29
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Mapper
public interface DiagnosisDao extends BaseMapper<DiagnosisEntity> {

    /**
     * 分页查询诊断记录
     *
     * @param page      分页参数
     * @param queryForm 查询条件
     * @return 诊断记录列表
     */
    List<DiagnosisVO> queryPage(Page page, @Param("queryForm") DiagnosisQueryForm queryForm);

    /**
     * 根据患者ID查询诊断记录
     *
     * @param patientId 患者ID
     * @return 诊断记录列表
     */
    List<DiagnosisVO> selectByPatientId(@Param("patientId") Long patientId);

    /**
     * 根据诊断ID查询详情
     *
     * @param diagnosisId 诊断ID
     * @return 诊断记录详情
     */
    DiagnosisVO selectDetailById(@Param("diagnosisId") Long diagnosisId);

    /**
     * 更新诊断状态
     *
     * @param diagnosisId     诊断ID
     * @param diagnosisStatus 诊断状态
     * @param updateUserId    更新人ID
     * @param updateUserName  更新人姓名
     * @return 更新行数
     */
    int updateDiagnosisStatus(@Param("diagnosisId") Long diagnosisId,
                              @Param("diagnosisStatus") Integer diagnosisStatus,
                              @Param("updateUserId") Long updateUserId,
                              @Param("updateUserName") String updateUserName);
}

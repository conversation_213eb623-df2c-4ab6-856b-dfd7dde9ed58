package net.lab1024.sa.admin.module.business.hospital.visit.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.entity.PrescriptionEntity;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.form.PrescriptionQueryForm;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.PrescriptionVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 开单记录Dao
 *
 * <AUTHOR>
 * @Date 2025-07-29
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Mapper
public interface PrescriptionDao extends BaseMapper<PrescriptionEntity> {

    /**
     * 分页查询开单记录
     *
     * @param page      分页参数
     * @param queryForm 查询条件
     * @return 开单记录列表
     */
    List<PrescriptionVO> queryPage(Page page, @Param("queryForm") PrescriptionQueryForm queryForm);

    /**
     * 根据患者ID查询开单记录
     *
     * @param patientId 患者ID
     * @return 开单记录列表
     */
    List<PrescriptionVO> selectByPatientId(@Param("patientId") Long patientId);

    /**
     * 根据诊断ID查询开单记录
     *
     * @param diagnosisId 诊断ID
     * @return 开单记录
     */
    PrescriptionVO selectByDiagnosisId(@Param("diagnosisId") Long diagnosisId);

    /**
     * 根据开单ID查询详情
     *
     * @param prescriptionId 开单ID
     * @return 开单记录详情
     */
    PrescriptionVO selectDetailById(@Param("prescriptionId") Long prescriptionId);

    /**
     * 更新开单状态
     *
     * @param prescriptionId     开单ID
     * @param prescriptionStatus 开单状态
     * @param updateUserId       更新人ID
     * @param updateUserName     更新人姓名
     * @return 更新行数
     */
    int updatePrescriptionStatus(@Param("prescriptionId") Long prescriptionId,
                                 @Param("prescriptionStatus") Integer prescriptionStatus,
                                 @Param("updateUserId") Long updateUserId,
                                 @Param("updateUserName") String updateUserName);
}

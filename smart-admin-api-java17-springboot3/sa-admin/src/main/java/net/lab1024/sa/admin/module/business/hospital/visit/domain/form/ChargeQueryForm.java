package net.lab1024.sa.admin.module.business.hospital.visit.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.domain.PageParam;

import java.time.LocalDateTime;

/**
 * 收费记录查询表单
 *
 * <AUTHOR>
 * @Date 2025-07-29
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@Schema(description = "收费记录查询表单")
public class ChargeQueryForm extends PageParam {

    @Schema(description = "开单ID")
    private Long prescriptionId;

    @Schema(description = "诊断ID")
    private Long diagnosisId;

    @Schema(description = "患者ID")
    private Long patientId;

    @Schema(description = "患者编号")
    private String patientNo;

    @Schema(description = "患者姓名")
    private String patientName;

    @Schema(description = "收费编号")
    private String chargeNo;

    @Schema(description = "收款账户：cash-现金，alipay-支付宝，wechat-微信支付，bank-银行卡")
    private String paymentAccount;

    @Schema(description = "收费状态：1-已收费，2-部分收费，3-未收费，4-已退费")
    private Integer chargeStatus;

    @Schema(description = "收费员ID")
    private Long cashierId;

    @Schema(description = "收费员姓名")
    private String cashierName;

    @Schema(description = "收费开始时间")
    private LocalDateTime chargeTimeStart;

    @Schema(description = "收费结束时间")
    private LocalDateTime chargeTimeEnd;

    @Schema(description = "创建开始时间")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建结束时间")
    private LocalDateTime createTimeEnd;
}

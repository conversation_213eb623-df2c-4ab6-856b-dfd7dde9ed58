package net.lab1024.sa.admin.module.business.hospital.visit.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 诊断记录添加表单
 *
 * <AUTHOR>
 * @Date 2025-07-29
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@Schema(description = "诊断记录添加表单")
public class DiagnosisAddForm {

    @Schema(description = "患者ID")
    @NotNull(message = "患者ID不能为空")
    private Long patientId;

    @Schema(description = "诊断结果")
    @NotBlank(message = "诊断结果不能为空")
    private String diagnosisResult;

    @Schema(description = "诊断类型：1-初诊，2-复诊，3-会诊")
    @NotNull(message = "诊断类型不能为空")
    private Integer diagnosisType;

    @Schema(description = "诊断说明")
    private String diagnosisDescription;

    @Schema(description = "诊断医生ID（自动获取当前登录用户）")
    private Long diagnosisDoctorId;

    @Schema(description = "诊断时间")
    @NotNull(message = "诊断时间不能为空")
    private LocalDateTime diagnosisTime;
}

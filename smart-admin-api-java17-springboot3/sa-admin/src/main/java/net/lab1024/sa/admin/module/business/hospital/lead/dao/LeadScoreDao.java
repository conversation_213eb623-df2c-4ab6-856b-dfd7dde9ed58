package net.lab1024.sa.admin.module.business.hospital.lead.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.entity.LeadScoreEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 线索评分DAO
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Mapper
@Component
public interface LeadScoreDao extends BaseMapper<LeadScoreEntity> {

    /**
     * 根据线索ID查询评分
     *
     * @param leadId 线索ID
     * @return 线索评分
     */
    LeadScoreEntity selectByLeadId(@Param("leadId") Long leadId);

    /**
     * 更新线索评分
     *
     * @param leadId 线索ID
     * @param totalScore 总分
     * @param followScore 跟进得分
     * @param responseScore 响应得分
     * @param intentionScore 意向得分
     * @param conversionProbability 转化概率
     */
    void updateScore(@Param("leadId") Long leadId,
                    @Param("totalScore") Integer totalScore,
                    @Param("followScore") Integer followScore,
                    @Param("responseScore") Integer responseScore,
                    @Param("intentionScore") Integer intentionScore,
                    @Param("conversionProbability") Double conversionProbability);
}

package net.lab1024.sa.admin.module.business.hospital.customer.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 病历记录实体类
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@TableName("t_medical_record")
public class MedicalRecordEntity {

    /**
     * 病历记录ID
     */
    @TableId(type = IdType.AUTO)
    private Long recordId;

    /**
     * 病历编号
     */
    private String recordNo;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 客户姓名
     */
    private String customerName;

    /**
     * 医生ID
     */
    private Long doctorId;

    /**
     * 医生姓名
     */
    private String doctorName;

    /**
     * 科室
     */
    private String department;

    /**
     * 就诊日期
     */
    private LocalDate visitDate;

    /**
     * 主诉
     */
    private String chiefComplaint;

    /**
     * 现病史
     */
    private String presentIllness;

    /**
     * 既往史
     */
    private String pastHistory;

    /**
     * 体格检查
     */
    private String physicalExamination;

    /**
     * 辅助检查
     */
    private String auxiliaryExamination;

    /**
     * 诊断
     */
    private String diagnosis;

    /**
     * 治疗方案
     */
    private String treatmentPlan;

    /**
     * 处方
     */
    private String prescription;

    /**
     * 下次复诊日期
     */
    private LocalDate nextVisitDate;

    /**
     * 病历状态：1-草稿，2-已完成
     */
    private Integer recordStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标志：0-未删除，1-已删除
     */
    private Boolean deletedFlag;

    /**
     * 创建人ID
     */
    private Long createUserId;

    /**
     * 更新人ID
     */
    private Long updateUserId;
}

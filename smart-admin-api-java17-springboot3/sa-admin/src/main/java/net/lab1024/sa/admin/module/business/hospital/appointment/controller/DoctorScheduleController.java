package net.lab1024.sa.admin.module.business.hospital.appointment.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.constant.AdminSwaggerTagConst;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.form.DoctorScheduleAddForm;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.form.DoctorScheduleBatchAddForm;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.form.DoctorScheduleQueryForm;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.form.DoctorScheduleUpdateForm;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.vo.AvailableTimeVO;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.vo.DoctorScheduleVO;
import net.lab1024.sa.admin.module.business.hospital.appointment.service.DoctorScheduleService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.ValidateList;
import net.lab1024.sa.base.module.support.operatelog.annotation.OperateLog;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * 医生排班Controller
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Tag(name = AdminSwaggerTagConst.Business.HOSPITAL_DOCTOR_SCHEDULE)
@RestController
@RequestMapping("/api/doctorSchedule")
public class DoctorScheduleController {

    @Resource
    private DoctorScheduleService doctorScheduleService;

    @Operation(summary = "分页查询医生排班")
    @PostMapping("/query")
    @SaCheckPermission("hospital:schedule:query")
    public ResponseDTO<PageResult<DoctorScheduleVO>> queryPage(@RequestBody @Valid DoctorScheduleQueryForm queryForm) {
        return doctorScheduleService.queryPage(queryForm);
    }

    @Operation(summary = "添加医生排班")
    @PostMapping("/add")
    @SaCheckPermission("hospital:schedule:add")
    @OperateLog
    public ResponseDTO<String> add(@RequestBody @Valid DoctorScheduleAddForm addForm) {
        return doctorScheduleService.add(addForm);
    }

    @Operation(summary = "更新医生排班")
    @PostMapping("/update")
    @SaCheckPermission("hospital:schedule:update")
    @OperateLog
    public ResponseDTO<String> update(@RequestBody @Valid DoctorScheduleUpdateForm updateForm) {
        return doctorScheduleService.update(updateForm);
    }

    @Operation(summary = "删除医生排班")
    @GetMapping("/delete/{scheduleId}")
    @SaCheckPermission("hospital:schedule:delete")
    @OperateLog
    public ResponseDTO<String> delete(@PathVariable Long scheduleId) {
        return doctorScheduleService.delete(scheduleId);
    }

    @Operation(summary = "批量删除医生排班")
    @PostMapping("/batchDelete")
    @SaCheckPermission("hospital:schedule:delete")
    @OperateLog
    public ResponseDTO<String> batchDelete(@RequestBody ValidateList<Long> scheduleIdList) {
        return doctorScheduleService.batchDelete(scheduleIdList);
    }

    @Operation(summary = "批量添加医生排班")
    @PostMapping("/batchAdd")
    @SaCheckPermission("hospital:schedule:batch")
    @OperateLog
    public ResponseDTO<String> batchAdd(@RequestBody @Valid DoctorScheduleBatchAddForm batchAddForm) {
        return doctorScheduleService.batchAdd(batchAddForm);
    }

    @Operation(summary = "获取医生可预约时间")
    @GetMapping("/availableTime/{doctorId}/{date}")
    @SaCheckPermission("hospital:schedule:query")
    public ResponseDTO<List<AvailableTimeVO>> getAvailableTime(
            @PathVariable Long doctorId,
            @PathVariable @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate date) {
        return doctorScheduleService.getAvailableTime(doctorId, date);
    }
}

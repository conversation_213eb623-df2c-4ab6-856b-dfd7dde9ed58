package net.lab1024.sa.admin.module.business.hospital.care.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 关怀记录添加表单
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class CareRecordAddForm {

    @Schema(description = "关怀计划ID")
    private Long carePlanId;

    @Schema(description = "客户ID", required = true)
    @NotNull(message = "客户ID不能为空")
    private Long customerId;

    @Schema(description = "客户姓名", required = true)
    @NotBlank(message = "客户姓名不能为空")
    @Length(max = 50, message = "客户姓名最多50字符")
    private String customerName;

    @Schema(description = "客户电话", required = true)
    @NotBlank(message = "客户电话不能为空")
    @Length(max = 20, message = "客户电话最多20字符")
    private String customerPhone;

    @Schema(description = "病历ID")
    private Long recordId;

    @Schema(description = "关怀类型：1-出院关怀，2-复诊提醒，3-生日关怀，4-节日关怀，5-满意度调查", required = true)
    @NotNull(message = "关怀类型不能为空")
    private Integer careType;

    @Schema(description = "关怀方式：1-电话，2-短信，3-微信，4-邮件，5-上门", required = true)
    @NotNull(message = "关怀方式不能为空")
    private Integer careMethod;

    @Schema(description = "关怀模板ID")
    private Long templateId;

    @Schema(description = "关怀内容", required = true)
    @NotBlank(message = "关怀内容不能为空")
    private String careContent;

    @Schema(description = "计划执行时间")
    private LocalDateTime plannedTime;

    @Schema(description = "实际执行时间")
    private LocalDateTime actualTime;

    @Schema(description = "关怀状态：1-待执行，2-执行中，3-已完成，4-已取消")
    private Integer careStatus;

    @Schema(description = "执行人ID")
    private Long executorId;

    @Schema(description = "执行人姓名")
    @Length(max = 50, message = "执行人姓名最多50字符")
    private String executorName;

    @Schema(description = "执行结果")
    private String executionResult;

    @Schema(description = "客户反馈")
    private String customerFeedback;

    @Schema(description = "满意度评分：1-5分")
    private Integer satisfactionScore;

    @Schema(description = "是否需要跟进")
    private Boolean needFollowUp;

    @Schema(description = "下次跟进时间")
    private LocalDateTime nextFollowUpTime;

    @Schema(description = "备注")
    private String remark;
}

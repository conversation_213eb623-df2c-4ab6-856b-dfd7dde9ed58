package net.lab1024.sa.admin.module.business.hospital.lead.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.entity.LeadStatisticsEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 线索统计DAO
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Mapper
@Component
public interface LeadStatisticsDao extends BaseMapper<LeadStatisticsEntity> {

    /**
     * 根据线索ID查询统计信息
     *
     * @param leadId 线索ID
     * @return 线索统计
     */
    LeadStatisticsEntity selectByLeadId(@Param("leadId") Long leadId);

    /**
     * 更新跟进统计
     *
     * @param leadId 线索ID
     * @param followCount 跟进次数
     * @param lastCommunicationTime 最后沟通时间
     */
    void updateFollowStatistics(@Param("leadId") Long leadId,
                               @Param("followCount") Integer followCount,
                               @Param("lastCommunicationTime") LocalDateTime lastCommunicationTime);

    /**
     * 更新预约统计
     *
     * @param leadId 线索ID
     * @param appointmentCount 预约次数
     */
    void updateAppointmentStatistics(@Param("leadId") Long leadId,
                                   @Param("appointmentCount") Integer appointmentCount);
}

package net.lab1024.sa.admin.module.business.hospital.visit.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.hospital.visit.dao.ChargeDao;
import net.lab1024.sa.admin.module.business.hospital.visit.dao.DiagnosisDao;
import net.lab1024.sa.admin.module.business.hospital.visit.dao.PrescriptionDao;
import net.lab1024.sa.admin.module.business.hospital.visit.dao.VisitPatientDao;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.entity.ChargeEntity;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.entity.DiagnosisEntity;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.entity.PrescriptionEntity;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.entity.VisitPatientEntity;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.form.ChargeAddForm;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.form.ChargeQueryForm;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.ChargeVO;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartPageUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 收费记录服务
 *
 * <AUTHOR>
 * @Date 2025-07-29
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Slf4j
@Service
public class ChargeService {

    @Autowired
    private ChargeDao chargeDao;

    @Autowired
    private PrescriptionDao prescriptionDao;

    @Autowired
    private DiagnosisDao diagnosisDao;

    @Autowired
    private VisitPatientDao visitPatientDao;

    /**
     * 分页查询收费记录
     */
    public PageResult<ChargeVO> queryPage(ChargeQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<ChargeVO> list = chargeDao.queryPage(page, queryForm);
        PageResult<ChargeVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 根据患者ID查询收费记录
     */
    public ResponseDTO<List<ChargeVO>> getByPatientId(Long patientId) {
        List<ChargeVO> list = chargeDao.selectByPatientId(patientId);
        return ResponseDTO.ok(list);
    }

    /**
     * 根据开单ID查询收费记录
     */
    public ResponseDTO<ChargeVO> getByPrescriptionId(Long prescriptionId) {
        ChargeVO chargeVO = chargeDao.selectByPrescriptionId(prescriptionId);
        return ResponseDTO.ok(chargeVO);
    }

    /**
     * 根据诊断ID查询收费记录
     */
    public ResponseDTO<ChargeVO> getByDiagnosisId(Long diagnosisId) {
        ChargeVO chargeVO = chargeDao.selectByDiagnosisId(diagnosisId);
        return ResponseDTO.ok(chargeVO);
    }

    /**
     * 根据ID查询收费记录详情
     */
    public ResponseDTO<ChargeVO> getById(Long chargeId) {
        ChargeVO chargeVO = chargeDao.selectDetailById(chargeId);
        if (chargeVO == null) {
            return ResponseDTO.userErrorParam("收费记录不存在");
        }
        return ResponseDTO.ok(chargeVO);
    }

    /**
     * 新增收费记录
     */
    @Transactional(rollbackFor = Exception.class)

    public ResponseDTO<String> add(ChargeAddForm addForm, Long operateUserId, String operateUserName) {
        // 1. 验证开单记录是否存在
        PrescriptionEntity prescription = prescriptionDao.selectById(addForm.getPrescriptionId());
        if (prescription == null) {
            return ResponseDTO.userErrorParam("开单记录不存在");
        }

        // 2. 验证诊断记录是否存在
        DiagnosisEntity diagnosis = diagnosisDao.selectById(addForm.getDiagnosisId());
        if (diagnosis == null) {
            return ResponseDTO.userErrorParam("诊断记录不存在");
        }

        // 3. 验证患者是否存在
        VisitPatientEntity patient = visitPatientDao.selectById(addForm.getPatientId());
        if (patient == null) {
            return ResponseDTO.userErrorParam("患者不存在");
        }

        // 4. 计算优惠金额
        BigDecimal discountAmount = addForm.getDiscountAmount();
        if (discountAmount == null) {
            discountAmount = addForm.getTotalAmount().subtract(addForm.getActualAmount());
        }

        // 5. 生成收费编号
        String chargeNo = generateChargeNo();

        // 6. 创建收费记录
        ChargeEntity chargeEntity = new ChargeEntity();
        chargeEntity.setPrescriptionId(addForm.getPrescriptionId());
        chargeEntity.setDiagnosisId(addForm.getDiagnosisId());
        chargeEntity.setPatientId(addForm.getPatientId());
        chargeEntity.setPatientNo(patient.getPatientNo());
        chargeEntity.setPatientName(patient.getPatientName());
        chargeEntity.setChargeNo(chargeNo);
        chargeEntity.setTotalAmount(addForm.getTotalAmount());
        chargeEntity.setActualAmount(addForm.getActualAmount());
        chargeEntity.setDiscountAmount(discountAmount);
        chargeEntity.setPaymentAccount(addForm.getPaymentAccount());
        chargeEntity.setChargeStatus(addForm.getChargeStatus());
        chargeEntity.setChargeTime(addForm.getChargeTime());
        chargeEntity.setChargeNote(addForm.getChargeNote());
        chargeEntity.setCashierId(operateUserId);
        chargeEntity.setCashierName(operateUserName);
        chargeEntity.setCreateUserId(operateUserId);
        chargeEntity.setCreateUserName(operateUserName);
        chargeEntity.setCreateTime(LocalDateTime.now());
        chargeEntity.setDeletedFlag(0);

        chargeDao.insert(chargeEntity);

        // 7. 更新开单状态
        prescriptionDao.updatePrescriptionStatus(addForm.getPrescriptionId(), 2, operateUserId, operateUserName);

        // 8. 更新诊断状态
        diagnosisDao.updateDiagnosisStatus(addForm.getDiagnosisId(), 3, operateUserId, operateUserName);

        // 9. 更新患者诊断状态
        visitPatientDao.updateDiagnosisStatus(addForm.getPatientId(), 4, operateUserId, operateUserName);

        return ResponseDTO.ok();
    }

    /**
     * 生成收费编号
     */
    private String generateChargeNo() {
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String timeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HHmmss"));
        return "C" + dateStr + timeStr + String.format("%03d", (int) (Math.random() * 1000));
    }

    /**
     * 更新收费状态
     */
    @Transactional(rollbackFor = Exception.class)

    public ResponseDTO<String> updateStatus(Long chargeId, Integer chargeStatus, Long operateUserId, String operateUserName) {
        int result = chargeDao.updateChargeStatus(chargeId, chargeStatus, operateUserId, operateUserName);
        if (result == 0) {
            return ResponseDTO.userErrorParam("收费记录不存在");
        }
        return ResponseDTO.ok();
    }

    /**
     * 删除收费记录
     */
    @Transactional(rollbackFor = Exception.class)

    public ResponseDTO<String> delete(Long chargeId, Long operateUserId, String operateUserName) {
        ChargeEntity entity = chargeDao.selectById(chargeId);
        if (entity == null) {
            return ResponseDTO.userErrorParam("收费记录不存在");
        }

        entity.setDeletedFlag(1);
        entity.setUpdateUserId(operateUserId);
        entity.setUpdateUserName(operateUserName);
        entity.setUpdateTime(LocalDateTime.now());
        chargeDao.updateById(entity);

        return ResponseDTO.ok();
    }
}

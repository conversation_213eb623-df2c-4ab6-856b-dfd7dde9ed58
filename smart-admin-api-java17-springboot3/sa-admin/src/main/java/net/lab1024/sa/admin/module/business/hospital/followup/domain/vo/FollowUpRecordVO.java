package net.lab1024.sa.admin.module.business.hospital.followup.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 回访记录VO
 *
 * <AUTHOR>
 * @Date 2025-07-30 00:00:00
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@Schema(description = "回访记录VO")
public class FollowUpRecordVO {

    @Schema(description = "回访记录ID")
    private Long followUpId;

    @Schema(description = "客户ID")
    private Long customerId;

    @Schema(description = "客户姓名")
    private String customerName;

    @Schema(description = "客户电话")
    private String customerPhone;

    @Schema(description = "回访类型")
    private Integer followUpType;

    @Schema(description = "回访类型描述")
    private String followUpTypeDesc;

    @Schema(description = "回访方式")
    private Integer followUpMethod;

    @Schema(description = "回访方式描述")
    private String followUpMethodDesc;

    @Schema(description = "计划回访时间")
    private LocalDateTime scheduledTime;

    @Schema(description = "实际回访时间")
    private LocalDateTime actualTime;

    @Schema(description = "回访内容")
    private String followUpContent;

    @Schema(description = "回访结果")
    private String followUpResult;

    @Schema(description = "回访结果描述")
    private String followUpResultDesc;

    @Schema(description = "回访人员ID")
    private Long followUpUserId;

    @Schema(description = "回访人员姓名")
    private String followUpUserName;

    @Schema(description = "满意度评分")
    private Integer satisfactionScore;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "下次回访时间")
    private LocalDateTime nextFollowUpTime;

    @Schema(description = "优先级")
    private Integer priorityLevel;

    @Schema(description = "优先级描述")
    private String priorityDesc;

    @Schema(description = "回访状态")
    private Integer followUpStatus;

    @Schema(description = "回访状态描述")
    private String followUpStatusDesc;

    @Schema(description = "创建人ID")
    private Long createUserId;

    @Schema(description = "创建人姓名")
    private String createUserName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人ID")
    private Long updateUserId;

    @Schema(description = "更新人姓名")
    private String updateUserName;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "关联记录ID")
    private Long relatedRecordId;

    @Schema(description = "关联记录类型")
    private String relatedRecordType;
}

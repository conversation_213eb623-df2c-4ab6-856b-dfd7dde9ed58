package net.lab1024.sa.admin.module.business.hospital.customer.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 客户标签实体类
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@TableName("t_customer_tag")
public class CustomerTagEntity {

    /**
     * 标签ID
     */
    @TableId(type = IdType.AUTO)
    private Long tagId;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 标签描述
     */
    private String tagDescription;

    /**
     * 标签颜色
     */
    private String tagColor;

    /**
     * 标签分类ID
     */
    private Long categoryId;

    /**
     * 标签分类名称
     */
    private String categoryName;

    /**
     * 标签类型：1-手动标签，2-自动标签
     */
    private Integer tagType;

    /**
     * 自动打标规则（JSON格式）
     */
    private String autoRules;

    /**
     * 标签状态：1-启用，0-禁用
     */
    private Integer tagStatus;

    /**
     * 使用次数
     */
    private Integer usageCount;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标识
     */
    private Boolean deletedFlag;

    /**
     * 创建人ID
     */
    private Long createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    private Long updateUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}

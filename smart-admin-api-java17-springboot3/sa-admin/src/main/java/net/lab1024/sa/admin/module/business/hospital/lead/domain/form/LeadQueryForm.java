package net.lab1024.sa.admin.module.business.hospital.lead.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import net.lab1024.sa.base.common.domain.PageParam;

import org.hibernate.validator.constraints.Length;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 线索查询表单
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class LeadQueryForm extends PageParam {

    @Schema(description = "线索来源")
    @Length(max = 50, message = "线索来源最多50字符")
    private String leadSource;

    @Schema(description = "客户姓名")
    @Length(max = 100, message = "客户姓名最多100字符")
    private String customerName;

    @Schema(description = "客户电话")
    @Length(max = 20, message = "客户电话最多20字符")
    private String customerPhone;

    @Schema(description = "地区")
    @Length(max = 50, message = "地区最多50字符")
    private String region;

    @Schema(description = "筛选类型：all-全部线索，my-我的线索，subordinate-下属线索，today-今日待跟进，week-7日未跟进，month-30天未跟进")
    private String filterType;

    @Schema(description = "分配员工ID")
    private Long assignedEmployeeId;

    @Schema(description = "症状")
    @Length(max = 50, message = "症状最多50字符")
    private String symptom;

    @Schema(description = "工作手机")
    @Length(max = 20, message = "工作手机最多20字符")
    private String workPhone;

    @Schema(description = "线索状态")
    private Integer leadStatus;

    @Schema(description = "排除的线索状态列表")
    private List<Integer> excludeStatuses;

    @Schema(description = "数据权限类型：ME-仅本人，DEPARTMENT-本部门，DEPARTMENT_AND_SUB-本部门及下属部门，ALL-全部")
    private String dataScopeType;

    @Schema(description = "创建时间-开始")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间-结束")
    private LocalDateTime createTimeEnd;

    @Schema(description = "搜索关键词")
    @Length(max = 50, message = "搜索关键词最多50字符")
    private String searchWord;

    @Schema(hidden = true)
    private Boolean deletedFlag;

    @Schema(hidden = true)
    private Long currentUserId;

    @Schema(hidden = true)
    private List<Long> dataScopeEmployeeIds;

    @Schema(hidden = true)
    private String filterScope;
}

package net.lab1024.sa.admin.module.business.hospital.customer.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 病历状态枚举
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Getter
@AllArgsConstructor
public enum MedicalRecordStatusEnum implements BaseEnum {

    /**
     * 草稿
     */
    DRAFT(1, "草稿"),

    /**
     * 已完成
     */
    COMPLETED(2, "已完成"),

    /**
     * 已出院
     */
    DISCHARGED(3, "已出院");

    private final Integer value;

    private final String desc;

    /**
     * 根据值获取枚举
     */
    public static MedicalRecordStatusEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (MedicalRecordStatusEnum statusEnum : values()) {
            if (statusEnum.getValue().equals(value)) {
                return statusEnum;
            }
        }
        return null;
    }
}

package net.lab1024.sa.admin.module.business.hospital.followup.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 回访计划状态枚举
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Getter
@AllArgsConstructor
public enum FollowUpPlanStatusEnum implements BaseEnum {

    /**
     * 草稿
     */
    DRAFT(1, "草稿"),

    /**
     * 执行中
     */
    EXECUTING(2, "执行中"),

    /**
     * 已暂停
     */
    PAUSED(3, "已暂停"),

    /**
     * 已完成
     */
    COMPLETED(4, "已完成"),

    /**
     * 已取消
     */
    CANCELLED(5, "已取消");

    private final Integer value;

    private final String desc;
}

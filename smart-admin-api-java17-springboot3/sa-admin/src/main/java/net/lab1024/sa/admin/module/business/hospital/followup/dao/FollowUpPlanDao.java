package net.lab1024.sa.admin.module.business.hospital.followup.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.entity.FollowUpPlanEntity;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.form.FollowUpPlanQueryForm;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.vo.FollowUpPlanVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 回访计划DAO
 *
 * <AUTHOR>
 * @Date 2025-07-30 00:00:00
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Mapper
public interface FollowUpPlanDao extends BaseMapper<FollowUpPlanEntity> {

    /**
     * 分页查询回访计划
     */
    List<FollowUpPlanVO> queryPage(Page page, @Param("queryForm") FollowUpPlanQueryForm queryForm);

    /**
     * 获取需要执行的计划列表
     */
    List<FollowUpPlanEntity> getExecutablePlans(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 获取计划统计数据
     */
    List<FollowUpPlanVO> getPlanStatistics(@Param("queryForm") FollowUpPlanQueryForm queryForm);

    /**
     * 更新计划执行统计
     */
    int updatePlanStatistics(@Param("planId") Long planId, 
                           @Param("actualCustomerCount") Integer actualCustomerCount,
                           @Param("completedCount") Integer completedCount,
                           @Param("lastExecutionTime") LocalDateTime lastExecutionTime,
                           @Param("nextExecutionTime") LocalDateTime nextExecutionTime);
}

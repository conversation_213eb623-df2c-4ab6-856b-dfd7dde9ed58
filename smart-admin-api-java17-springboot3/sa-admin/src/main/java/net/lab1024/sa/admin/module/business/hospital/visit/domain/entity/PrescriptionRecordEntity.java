package net.lab1024.sa.admin.module.business.hospital.visit.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 开单记录实体类
 *
 * <AUTHOR>
 * @Date 2025-07-29 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@TableName("t_prescription_record")
public class PrescriptionRecordEntity {

    /**
     * 开单记录ID
     */
    @TableId(type = IdType.AUTO)
    private Long prescriptionId;

    /**
     * 开单编号
     */
    private String prescriptionNo;

    /**
     * 患者ID
     */
    private Long patientId;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 患者电话
     */
    private String patientPhone;

    /**
     * 医生ID
     */
    private Long doctorId;

    /**
     * 医生姓名
     */
    private String doctorName;

    /**
     * 科室ID
     */
    private Long departmentId;

    /**
     * 科室名称
     */
    private String departmentName;

    /**
     * 关联诊断记录ID
     */
    private Long diagnosisId;

    /**
     * 开单类型：1-药品处方，2-检查单，3-治疗单，4-手术单
     */
    private Integer prescriptionType;

    /**
     * 开单日期
     */
    private LocalDate prescriptionDate;

    /**
     * 开单时间
     */
    private LocalTime prescriptionTime;

    /**
     * 开单项目详情(JSON格式)
     */
    private String prescriptionItems;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 优惠金额
     */
    private BigDecimal discountAmount;

    /**
     * 最终金额
     */
    private BigDecimal finalAmount;

    /**
     * 开单状态：1-待审核，2-已审核，3-已执行，4-已取消
     */
    private Integer prescriptionStatus;

    /**
     * 审核人ID
     */
    private Long auditUserId;

    /**
     * 审核人姓名
     */
    private String auditUserName;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 执行人ID
     */
    private Long executeUserId;

    /**
     * 执行人姓名
     */
    private String executeUserName;

    /**
     * 执行时间
     */
    private LocalDateTime executeTime;

    /**
     * 执行备注
     */
    private String executeRemark;

    /**
     * 紧急程度：1-紧急，2-普通，3-延缓
     */
    private Integer urgencyLevel;

    /**
     * 有效天数
     */
    private Integer validDays;

    /**
     * 过期日期
     */
    private LocalDate expireDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志：0-未删除，1-已删除
     */
    private Boolean deletedFlag;

    /**
     * 创建人ID
     */
    private Long createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    private Long updateUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}

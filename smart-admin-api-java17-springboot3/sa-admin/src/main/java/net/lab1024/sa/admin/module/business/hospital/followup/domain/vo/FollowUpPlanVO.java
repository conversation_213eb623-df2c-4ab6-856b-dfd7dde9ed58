package net.lab1024.sa.admin.module.business.hospital.followup.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 回访计划VO
 *
 * <AUTHOR>
 * @Date 2025-07-30 00:00:00
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@Schema(description = "回访计划VO")
public class FollowUpPlanVO {

    @Schema(description = "计划ID")
    private Long planId;

    @Schema(description = "计划名称")
    private String planName;

    @Schema(description = "计划描述")
    private String planDescription;

    @Schema(description = "计划类型")
    private Integer planType;

    @Schema(description = "计划类型描述")
    private String planTypeDesc;

    @Schema(description = "目标客户类型")
    private Integer targetCustomerType;

    @Schema(description = "目标客户类型描述")
    private String targetCustomerTypeDesc;

    @Schema(description = "客户筛选条件")
    private String customerFilterConditions;

    @Schema(description = "回访方式")
    private Integer followUpMethod;

    @Schema(description = "回访方式描述")
    private String followUpMethodDesc;

    @Schema(description = "回访内容模板")
    private String followUpContentTemplate;

    @Schema(description = "计划开始日期")
    private LocalDate planStartDate;

    @Schema(description = "计划结束日期")
    private LocalDate planEndDate;

    @Schema(description = "执行频率")
    private Integer executionFrequency;

    @Schema(description = "执行频率描述")
    private String executionFrequencyDesc;

    @Schema(description = "执行时间")
    private LocalTime executionTime;

    @Schema(description = "执行日期")
    private String executionDays;

    @Schema(description = "优先级")
    private Integer priorityLevel;

    @Schema(description = "优先级描述")
    private String priorityLevelDesc;

    @Schema(description = "计划状态")
    private Integer planStatus;

    @Schema(description = "计划状态描述")
    private String planStatusDesc;

    @Schema(description = "是否自动生成回访记录")
    private Integer autoGenerateRecords;

    @Schema(description = "负责人ID")
    private Long responsibleUserId;

    @Schema(description = "负责人姓名")
    private String responsibleUserName;

    @Schema(description = "预计客户数量")
    private Integer expectedCustomerCount;

    @Schema(description = "实际客户数量")
    private Integer actualCustomerCount;

    @Schema(description = "已完成数量")
    private Integer completedCount;

    @Schema(description = "成功率")
    private BigDecimal successRate;

    @Schema(description = "最后执行时间")
    private LocalDateTime lastExecutionTime;

    @Schema(description = "下次执行时间")
    private LocalDateTime nextExecutionTime;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建人ID")
    private Long createUserId;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人ID")
    private Long updateUserId;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}

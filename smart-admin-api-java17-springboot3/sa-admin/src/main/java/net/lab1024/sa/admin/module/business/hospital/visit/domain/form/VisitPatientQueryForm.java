package net.lab1024.sa.admin.module.business.hospital.visit.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.lab1024.sa.base.common.domain.PageParam;
import net.lab1024.sa.base.common.swagger.SchemaEnum;
import net.lab1024.sa.base.common.validator.enumeration.CheckEnum;
import net.lab1024.sa.admin.module.business.hospital.visit.constant.VisitStatusEnum;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 到诊患者查询表单
 *
 * <AUTHOR>
 * @Date 2025-07-29 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class VisitPatientQueryForm extends PageParam {

    @Schema(description = "患者姓名")
    @Length(max = 50, message = "患者姓名最多50字符")
    private String patientName;

    @Schema(description = "患者电话")
    @Length(max = 20, message = "患者电话最多20字符")
    private String patientPhone;

    @Schema(description = "到诊状态：1-未到诊，2-已到诊，3-诊疗中，4-已完成，5-已离院")
    @SchemaEnum(VisitStatusEnum.class)
    @CheckEnum(message = "到诊状态错误", value = VisitStatusEnum.class, required = false)
    private Integer visitStatus;

    @Schema(description = "分配医生ID")
    private Long assignedDoctorId;

    @Schema(description = "分配医生姓名")
    @Length(max = 50, message = "医生姓名最多50字符")
    private String assignedDoctorName;

    @Schema(description = "分配治疗助理ID")
    private Long assignedAssistantId;

    @Schema(description = "分配治疗助理姓名")
    @Length(max = 50, message = "助理姓名最多50字符")
    private String assignedAssistantName;

    @Schema(description = "所属部门ID")
    private Long departmentId;

    @Schema(description = "所属部门名称")
    @Length(max = 50, message = "部门名称最多50字符")
    private String departmentName;

    @Schema(description = "到诊日期-开始")
    private LocalDate visitDateStart;

    @Schema(description = "到诊日期-结束")
    private LocalDate visitDateEnd;

    @Schema(description = "登记时间-开始")
    private LocalDateTime registrationTimeStart;

    @Schema(description = "登记时间-结束")
    private LocalDateTime registrationTimeEnd;

    @Schema(description = "患者来源")
    @Length(max = 100, message = "患者来源最多100字符")
    private String patientSource;

    @Schema(description = "患者等级：1-普通患者，2-VIP患者，3-SVIP患者")
    private Integer patientLevel;

    @Schema(description = "患者标签")
    @Length(max = 200, message = "患者标签最多200字符")
    private String patientTags;

    @Schema(description = "搜索关键词")
    @Length(max = 50, message = "搜索关键词最多50字符")
    private String searchWord;

    @Schema(hidden = true)
    private Boolean deletedFlag;

    /**
     * 数据权限过滤 - 允许查看的员工ID列表
     */
    @Schema(hidden = true)
    private List<Long> dataScopeEmployeeIds;

    /**
     * 数据权限过滤 - 允许查看的部门ID列表
     */
    @Schema(hidden = true)
    private List<Long> dataScopeDepartmentIds;
}

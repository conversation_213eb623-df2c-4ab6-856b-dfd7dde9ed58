package net.lab1024.sa.admin.module.business.hospital.customer.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.hospital.customer.constant.CustomerStatusEnum;
import net.lab1024.sa.admin.module.business.hospital.customer.dao.CustomerDao;
import net.lab1024.sa.admin.module.business.hospital.customer.domain.entity.CustomerEntity;
import net.lab1024.sa.admin.module.business.hospital.customer.domain.form.CustomerAddForm;
import net.lab1024.sa.admin.module.business.hospital.customer.domain.form.CustomerQueryForm;
import net.lab1024.sa.admin.module.business.hospital.customer.domain.form.CustomerUpdateForm;
import net.lab1024.sa.admin.module.business.hospital.customer.domain.form.LeadToCustomerForm;
import net.lab1024.sa.admin.module.business.hospital.customer.domain.vo.CustomerVO;
import net.lab1024.sa.admin.module.business.hospital.customer.domain.vo.Customer360VO;
import net.lab1024.sa.admin.module.business.hospital.lead.constant.LeadStatusEnum;
import net.lab1024.sa.admin.module.business.hospital.lead.dao.LeadDao;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.entity.LeadEntity;
import net.lab1024.sa.admin.module.system.datascope.constant.DataScopeTypeEnum;
import net.lab1024.sa.admin.module.system.datascope.constant.DataScopeViewTypeEnum;
import net.lab1024.sa.admin.module.system.datascope.service.DataScopeViewService;
import net.lab1024.sa.admin.module.system.employee.dao.EmployeeDao;
import net.lab1024.sa.admin.module.system.employee.domain.entity.EmployeeEntity;
import net.lab1024.sa.admin.module.system.employee.domain.vo.EmployeeVO;
import net.lab1024.sa.admin.util.AdminRequestUtil;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.module.support.datatracer.constant.DataTracerTypeEnum;
import net.lab1024.sa.base.module.support.datatracer.service.DataTracerService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 客户Service
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Slf4j
@Service
public class CustomerService {

    @Resource
    private CustomerDao customerDao;

    @Resource
    private LeadDao leadDao;

    @Resource
    private DataTracerService dataTracerService;

    @Resource
    private DataScopeViewService dataScopeViewService;

    @Resource
    private EmployeeDao employeeDao;

    /**
     * 分页查询客户
     */
    public ResponseDTO<PageResult<CustomerVO>> queryPage(CustomerQueryForm queryForm) {
        queryForm.setDeletedFlag(Boolean.FALSE);

        // 应用数据权限过滤
        Long currentUserId = AdminRequestUtil.getRequestUserId();
        applyDataScopeFilter(queryForm, currentUserId);

        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<CustomerVO> customerList = customerDao.queryPage(page, queryForm);
        PageResult<CustomerVO> pageResult = SmartPageUtil.convert2PageResult(page, customerList);
        return ResponseDTO.ok(pageResult);
    }

    /**
     * 添加客户
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> add(CustomerAddForm addForm) {
        // 校验客户电话是否重复
        CustomerEntity existCustomer = customerDao.selectByPhone(addForm.getCustomerPhone(), null);
        if (existCustomer != null) {
            return ResponseDTO.userErrorParam("客户电话已存在");
        }

        // 校验身份证号是否重复
        if (StringUtils.isNotBlank(addForm.getIdCard())) {
            CustomerEntity existIdCardCustomer = customerDao.selectByIdCard(addForm.getIdCard(), null);
            if (existIdCardCustomer != null) {
                return ResponseDTO.userErrorParam("身份证号已存在");
            }
        }

        CustomerEntity customerEntity = SmartBeanUtil.copy(addForm, CustomerEntity.class);
        customerEntity.setDeletedFlag(Boolean.FALSE);
        customerEntity.setCreateTime(LocalDateTime.now());
        customerEntity.setUpdateTime(LocalDateTime.now());
        customerEntity.setCreateUserId(AdminRequestUtil.getRequestUserId());

        // 如果没有设置状态，默认为潜在客户
        if (customerEntity.getCustomerStatus() == null) {
            customerEntity.setCustomerStatus(CustomerStatusEnum.POTENTIAL.getValue());
        }

        customerDao.insert(customerEntity);

        // 记录数据变动
        dataTracerService.insert(customerEntity.getCustomerId(), DataTracerTypeEnum.CUSTOMER);

        return ResponseDTO.ok();
    }

    /**
     * 更新客户
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> update(CustomerUpdateForm updateForm) {
        // 校验客户是否存在
        CustomerEntity existCustomer = customerDao.selectById(updateForm.getCustomerId());
        if (existCustomer == null || existCustomer.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("客户不存在");
        }

        // 检查权限
        Long currentUserId = AdminRequestUtil.getRequestUserId();
        if (!hasPermissionToViewCustomer(updateForm.getCustomerId(), currentUserId)) {
            return ResponseDTO.userErrorParam("无权限操作此客户");
        }

        // 校验客户电话是否重复
        CustomerEntity duplicatePhoneCustomer = customerDao.selectByPhone(updateForm.getCustomerPhone(), updateForm.getCustomerId());
        if (duplicatePhoneCustomer != null) {
            return ResponseDTO.userErrorParam("客户电话已存在");
        }

        // 校验身份证号是否重复
        if (StringUtils.isNotBlank(updateForm.getIdCard())) {
            CustomerEntity duplicateIdCardCustomer = customerDao.selectByIdCard(updateForm.getIdCard(), updateForm.getCustomerId());
            if (duplicateIdCardCustomer != null) {
                return ResponseDTO.userErrorParam("身份证号已存在");
            }
        }

        CustomerEntity customerEntity = SmartBeanUtil.copy(updateForm, CustomerEntity.class);
        customerEntity.setUpdateTime(LocalDateTime.now());
        // 获取原实体用于数据变动记录
        CustomerEntity originEntity = customerDao.selectById(customerEntity.getCustomerId());

        customerDao.updateById(customerEntity);

        // 记录数据变动
        dataTracerService.update(customerEntity.getCustomerId(), DataTracerTypeEnum.CUSTOMER, originEntity, customerEntity);

        return ResponseDTO.ok();
    }

    /**
     * 删除客户
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> delete(Long customerId) {
        CustomerEntity customerEntity = customerDao.selectById(customerId);
        if (customerEntity == null || customerEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("客户不存在");
        }

        // 检查权限
        Long currentUserId = AdminRequestUtil.getRequestUserId();
        if (!hasPermissionToViewCustomer(customerId, currentUserId)) {
            return ResponseDTO.userErrorParam("无权限操作此客户");
        }

        // TODO: 检查是否有关联的预约记录或病历记录
        // 如果有关联记录，不允许删除

        customerEntity.setDeletedFlag(Boolean.TRUE);
        customerEntity.setUpdateTime(LocalDateTime.now());
        customerDao.updateById(customerEntity);

        // 记录数据变动
        dataTracerService.delete(customerId, DataTracerTypeEnum.CUSTOMER);

        return ResponseDTO.ok();
    }

    /**
     * 批量删除客户
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> batchDelete(List<Long> customerIdList) {
        if (customerIdList == null || customerIdList.isEmpty()) {
            return ResponseDTO.userErrorParam("客户ID列表不能为空");
        }

        // TODO: 检查是否有关联的预约记录或病历记录

        customerDao.batchUpdateDeleted(customerIdList, Boolean.TRUE);

        // 记录数据变动
        for (Long customerId : customerIdList) {
            dataTracerService.delete(customerId, DataTracerTypeEnum.CUSTOMER);
        }

        return ResponseDTO.ok();
    }

    /**
     * 更新客户状态
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> updateStatus(Long customerId, Integer customerStatus) {
        CustomerEntity customerEntity = customerDao.selectById(customerId);
        if (customerEntity == null || customerEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("客户不存在");
        }

        // 校验状态值
        boolean validStatus = false;
        for (CustomerStatusEnum statusEnum : CustomerStatusEnum.values()) {
            if (statusEnum.getValue().equals(customerStatus)) {
                validStatus = true;
                break;
            }
        }
        if (!validStatus) {
            return ResponseDTO.userErrorParam("客户状态错误");
        }

        // 获取原实体用于数据变动记录
        CustomerEntity originEntity = customerDao.selectById(customerId);

        customerDao.updateStatus(customerId, customerStatus);

        // 获取更新后的实体
        CustomerEntity updatedEntity = customerDao.selectById(customerId);

        // 记录数据变动
        dataTracerService.update(customerId, DataTracerTypeEnum.CUSTOMER, originEntity, updatedEntity);

        return ResponseDTO.ok();
    }

    /**
     * 获取客户详情
     */
    public ResponseDTO<CustomerVO> getDetail(Long customerId) {
        // 检查权限
        Long currentUserId = AdminRequestUtil.getRequestUserId();
        if (!hasPermissionToViewCustomer(customerId, currentUserId)) {
            return ResponseDTO.userErrorParam("无权限查看此客户");
        }

        CustomerVO customerDetail = customerDao.getCustomerDetail(customerId);
        if (customerDetail == null) {
            return ResponseDTO.userErrorParam("客户不存在");
        }
        return ResponseDTO.ok(customerDetail);
    }

    /**
     * 线索转客户
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> leadToCustomer(LeadToCustomerForm leadToCustomerForm) {
        // 校验线索是否存在
        LeadEntity leadEntity = leadDao.selectById(leadToCustomerForm.getLeadId());
        if (leadEntity == null || leadEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("线索不存在");
        }

        // 检查该线索是否已经转为客户
        CustomerEntity existCustomer = customerDao.selectByLeadId(leadToCustomerForm.getLeadId());
        if (existCustomer != null) {
            return ResponseDTO.userErrorParam("该线索已转为客户");
        }

        // 校验客户电话是否重复
        CustomerEntity duplicatePhoneCustomer = customerDao.selectByPhone(leadEntity.getCustomerPhone(), null);
        if (duplicatePhoneCustomer != null) {
            return ResponseDTO.userErrorParam("客户电话已存在");
        }

        // 校验身份证号是否重复
        if (StringUtils.isNotBlank(leadToCustomerForm.getIdCard())) {
            CustomerEntity duplicateIdCardCustomer = customerDao.selectByIdCard(leadToCustomerForm.getIdCard(), null);
            if (duplicateIdCardCustomer != null) {
                return ResponseDTO.userErrorParam("身份证号已存在");
            }
        }

        // 创建客户记录
        CustomerEntity customerEntity = new CustomerEntity();
        customerEntity.setLeadId(leadToCustomerForm.getLeadId());
        customerEntity.setCustomerName(leadEntity.getCustomerName());
        customerEntity.setCustomerPhone(leadEntity.getCustomerPhone());
        customerEntity.setCustomerWechat(leadToCustomerForm.getCustomerWechat());
        customerEntity.setCustomerEmail(leadToCustomerForm.getCustomerEmail());
        customerEntity.setGender(leadToCustomerForm.getGender());
        customerEntity.setAge(leadToCustomerForm.getAge());
        customerEntity.setBirthday(leadToCustomerForm.getBirthday());
        customerEntity.setIdCard(leadToCustomerForm.getIdCard());
        customerEntity.setCustomerAddress(leadToCustomerForm.getCustomerAddress());
        customerEntity.setOccupation(leadToCustomerForm.getOccupation());
        customerEntity.setCustomerSource(leadEntity.getLeadSource());
        customerEntity.setCustomerStatus(leadToCustomerForm.getCustomerStatus() != null ? 
                leadToCustomerForm.getCustomerStatus() : CustomerStatusEnum.INTERESTED.getValue());
        customerEntity.setCustomerTags(leadToCustomerForm.getCustomerTags());
        customerEntity.setRemark(leadToCustomerForm.getRemark());
        customerEntity.setDeletedFlag(Boolean.FALSE);
        customerEntity.setCreateTime(LocalDateTime.now());
        customerEntity.setUpdateTime(LocalDateTime.now());
        customerEntity.setCreateUserId(AdminRequestUtil.getRequestUserId());

        customerDao.insert(customerEntity);

        // 更新线索状态为已转客户
        leadDao.updateStatus(leadToCustomerForm.getLeadId(), LeadStatusEnum.CONVERTED.getValue());

        // 获取原线索实体用于数据变动记录
        LeadEntity originLeadEntity = leadDao.selectById(leadToCustomerForm.getLeadId());
        LeadEntity updatedLeadEntity = leadDao.selectById(leadToCustomerForm.getLeadId());

        // 记录数据变动
        dataTracerService.insert(customerEntity.getCustomerId(), DataTracerTypeEnum.CUSTOMER);
        dataTracerService.update(leadToCustomerForm.getLeadId(), DataTracerTypeEnum.LEAD, originLeadEntity, updatedLeadEntity);

        return ResponseDTO.ok();
    }

    /**
     * 根据标签查询客户
     */
    public ResponseDTO<List<CustomerVO>> queryByTags(String customerTags) {
        List<CustomerVO> customerList = customerDao.selectByTags(customerTags);
        return ResponseDTO.ok(customerList);
    }

    /**
     * 获取最近创建的客户
     */
    public ResponseDTO<List<CustomerVO>> getRecentCustomers(Integer limit) {
        if (limit == null || limit <= 0) {
            limit = 10;
        }
        List<CustomerVO> customerList = customerDao.getRecentCustomers(limit);
        return ResponseDTO.ok(customerList);
    }

    /**
     * 获取客户360度视图
     */
    public ResponseDTO<Customer360VO> getCustomer360View(Long customerId) {
        // 检查权限
        Long currentUserId = AdminRequestUtil.getRequestUserId();
        if (!hasPermissionToViewCustomer(customerId, currentUserId)) {
            return ResponseDTO.userErrorParam("无权限查看此客户");
        }

        // 获取客户基础信息和统计数据
        Customer360VO customer360 = customerDao.getCustomer360View(customerId);
        if (customer360 == null) {
            return ResponseDTO.userErrorParam("客户不存在");
        }

        // 获取最近预约记录
        List<Customer360VO.AppointmentSimpleVO> recentAppointments = customerDao.getRecentAppointments(customerId, 5);
        customer360.setRecentAppointments(recentAppointments);

        // 获取最近病历记录
        List<Customer360VO.MedicalRecordSimpleVO> recentMedicalRecords = customerDao.getRecentMedicalRecords(customerId, 5);
        customer360.setRecentMedicalRecords(recentMedicalRecords);

        // 获取最近跟进记录
        List<Customer360VO.FollowUpSimpleVO> recentFollowUps = customerDao.getRecentFollowUps(customerId, 5);
        customer360.setRecentFollowUps(recentFollowUps);

        // 获取消费记录
        List<Customer360VO.ConsumptionRecordVO> consumptionRecords = customerDao.getConsumptionRecords(customerId, 10);
        customer360.setConsumptionRecords(consumptionRecords);

        return ResponseDTO.ok(customer360);
    }

    /**
     * 分配客户
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> assignCustomer(Long customerId, Long assigneeId) {
        // 校验客户是否存在
        CustomerEntity customerEntity = customerDao.selectById(customerId);
        if (customerEntity == null || customerEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("客户不存在");
        }

        // 检查权限
        Long currentUserId = AdminRequestUtil.getRequestUserId();
        if (!hasPermissionToViewCustomer(customerId, currentUserId)) {
            return ResponseDTO.userErrorParam("无权限操作此客户");
        }

        // 检查是否有权限分配给指定员工
        if (!hasPermissionToAssignCustomer(assigneeId, currentUserId)) {
            return ResponseDTO.userErrorParam("无权限分配给该员工");
        }

        // 校验被分配员工是否存在
        EmployeeEntity assigneeEmployee = employeeDao.selectById(assigneeId);
        if (assigneeEmployee == null || assigneeEmployee.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("被分配员工不存在");
        }

        // 获取原实体用于数据变动记录
        CustomerEntity originEntity = customerDao.selectById(customerId);

        // 更新负责员工
        customerEntity.setResponsibleEmployeeId(assigneeId);
        customerEntity.setResponsibleEmployeeName(assigneeEmployee.getActualName());
        customerEntity.setUpdateTime(LocalDateTime.now());
        customerDao.updateById(customerEntity);

        // 获取更新后的实体
        CustomerEntity updatedEntity = customerDao.selectById(customerId);

        // 记录数据变动
        dataTracerService.update(customerId, DataTracerTypeEnum.CUSTOMER, originEntity, updatedEntity);

        return ResponseDTO.ok();
    }

    /**
     * 应用数据权限过滤
     */
    private void applyDataScopeFilter(CustomerQueryForm queryForm, Long currentUserId) {
        log.debug("开始应用客户数据权限过滤，当前用户ID: {}", currentUserId);

        // 获取当前用户信息
        EmployeeEntity currentEmployee = employeeDao.selectById(currentUserId);
        if (currentEmployee == null) {
            log.warn("用户不存在，用户ID: {}", currentUserId);
            // 如果用户不存在，设置一个不可能的条件，确保查不到任何数据
            queryForm.setDataScopeEmployeeIds(List.of(-1L));
            return;
        }

        log.debug("当前用户信息: 姓名={}, 部门ID={}, 是否管理员={}",
                 currentEmployee.getActualName(), currentEmployee.getDepartmentId(), currentEmployee.getAdministratorFlag());

        // 超级管理员可以查看所有客户
        if (currentEmployee.getAdministratorFlag()) {
            log.debug("超级管理员，可以查看所有客户");
            // 不设置任何过滤条件，可以查看所有客户
            return;
        }

        // 获取用户的数据权限级别
        DataScopeViewTypeEnum viewType = dataScopeViewService.getEmployeeDataScopeViewType(DataScopeTypeEnum.CUSTOMER, currentUserId);
        log.debug("用户数据权限级别: {}", viewType);

        List<Long> allowedEmployeeIds = new ArrayList<>();

        switch (viewType) {
            case ALL:
                // 全部权限，不设置过滤条件
                log.debug("ALL权限，可以查看所有客户");
                return;
            case DEPARTMENT_AND_SUB:
                // 本部门及下属部门
                List<Long> departmentAndSubEmployeeIds = dataScopeViewService.getCanViewEmployeeId(viewType, currentUserId);
                log.debug("DEPARTMENT_AND_SUB权限，可查看员工IDs: {}", departmentAndSubEmployeeIds);
                if (CollectionUtils.isNotEmpty(departmentAndSubEmployeeIds)) {
                    allowedEmployeeIds.addAll(departmentAndSubEmployeeIds);
                }
                break;
            case DEPARTMENT:
                // 本部门
                List<Long> departmentEmployeeIds = dataScopeViewService.getCanViewEmployeeId(viewType, currentUserId);
                log.debug("DEPARTMENT权限，可查看员工IDs: {}", departmentEmployeeIds);
                if (CollectionUtils.isNotEmpty(departmentEmployeeIds)) {
                    allowedEmployeeIds.addAll(departmentEmployeeIds);
                }
                break;
            case ME:
            default:
                // 仅本人
                log.debug("ME权限，仅可查看本人数据");
                allowedEmployeeIds.add(currentUserId);
                break;
        }

        // 如果没有允许的员工ID，设置一个不可能的条件
        if (allowedEmployeeIds.isEmpty()) {
            log.warn("没有允许的员工ID，设置不可能的条件");
            allowedEmployeeIds.add(-1L);
        }

        log.debug("最终设置的数据权限过滤员工IDs: {}", allowedEmployeeIds);
        queryForm.setDataScopeEmployeeIds(allowedEmployeeIds);
    }

    /**
     * 检查是否有权限查看指定客户
     */
    private boolean hasPermissionToViewCustomer(Long customerId, Long currentUserId) {
        log.debug("检查客户查看权限，客户ID: {}, 当前用户ID: {}", customerId, currentUserId);

        // 获取客户信息
        CustomerEntity customerEntity = customerDao.selectById(customerId);
        if (customerEntity == null || customerEntity.getDeletedFlag()) {
            log.debug("客户不存在或已删除，客户ID: {}", customerId);
            return false;
        }

        log.debug("客户信息: 创建者ID={}, 负责人ID={}", customerEntity.getCreateUserId(), customerEntity.getResponsibleEmployeeId());

        // 获取当前用户信息
        EmployeeEntity currentEmployee = employeeDao.selectById(currentUserId);
        if (currentEmployee == null) {
            log.debug("当前用户不存在，用户ID: {}", currentUserId);
            return false;
        }

        // 超级管理员可以查看所有客户
        if (currentEmployee.getAdministratorFlag()) {
            log.debug("超级管理员，允许查看");
            return true;
        }

        // 获取用户的数据权限级别
        DataScopeViewTypeEnum viewType = dataScopeViewService.getEmployeeDataScopeViewType(DataScopeTypeEnum.CUSTOMER, currentUserId);
        log.debug("用户数据权限级别: {}", viewType);

        switch (viewType) {
            case ALL:
                log.debug("ALL权限，允许查看");
                return true;
            case DEPARTMENT_AND_SUB:
            case DEPARTMENT:
                // 检查是否在同一部门或下属部门
                try {
                    List<Long> canViewEmployeeIds = dataScopeViewService.getCanViewEmployeeId(viewType, currentUserId);
                    log.debug("可查看的员工IDs: {}", canViewEmployeeIds);
                    if (CollectionUtils.isNotEmpty(canViewEmployeeIds)) {
                        // 检查客户创建者或负责人是否在可查看范围内
                        boolean canViewCreator = customerEntity.getCreateUserId() != null &&
                                               canViewEmployeeIds.contains(customerEntity.getCreateUserId());
                        boolean canViewResponsible = customerEntity.getResponsibleEmployeeId() != null &&
                                                   canViewEmployeeIds.contains(customerEntity.getResponsibleEmployeeId());
                        boolean result = canViewCreator || canViewResponsible;
                        log.debug("部门权限检查结果: 可查看创建者={}, 可查看负责人={}, 最终结果={}",
                                canViewCreator, canViewResponsible, result);
                        return result;
                    }
                } catch (Exception e) {
                    log.error("权限检查异常", e);
                    return false;
                }
                break;
            case ME:
            default:
                // ME权限只能查看自己创建的或负责的客户
                boolean isCreator = customerEntity.getCreateUserId() != null && customerEntity.getCreateUserId().equals(currentUserId);
                boolean isResponsible = customerEntity.getResponsibleEmployeeId() != null && customerEntity.getResponsibleEmployeeId().equals(currentUserId);
                boolean result = isCreator || isResponsible;
                log.debug("ME权限检查结果: 是创建者={}, 是负责人={}, 最终结果={}", isCreator, isResponsible, result);
                return result;
        }

        // 默认拒绝访问
        log.debug("默认拒绝访问");
        return false;
    }

    /**
     * 检查是否有权限分配客户给指定员工
     */
    private boolean hasPermissionToAssignCustomer(Long assigneeId, Long currentUserId) {
        log.debug("检查客户分配权限，被分配员工ID: {}, 当前用户ID: {}", assigneeId, currentUserId);

        // 可以分配给自己
        if (assigneeId.equals(currentUserId)) {
            log.debug("分配给自己，允许");
            return true;
        }

        try {
            // 获取当前用户信息
            EmployeeEntity currentEmployee = employeeDao.selectById(currentUserId);
            if (currentEmployee == null) {
                log.debug("当前用户不存在，拒绝分配");
                return false;
            }

            // 超级管理员可以分配给任何人
            if (currentEmployee.getAdministratorFlag()) {
                log.debug("超级管理员，允许分配给任何人");
                return true;
            }

            // 获取被分配员工信息
            EmployeeEntity assigneeEmployee = employeeDao.selectById(assigneeId);
            if (assigneeEmployee == null || assigneeEmployee.getDeletedFlag()) {
                log.debug("被分配员工不存在或已删除，拒绝分配");
                return false;
            }

            // 获取当前用户的数据权限级别
            DataScopeViewTypeEnum viewType = dataScopeViewService.getEmployeeDataScopeViewType(DataScopeTypeEnum.CUSTOMER, currentUserId);
            log.debug("当前用户数据权限级别: {}", viewType);

            // 如果没有配置数据权限，默认为ME权限
            if (viewType == null) {
                viewType = DataScopeViewTypeEnum.ME;
                log.debug("没有配置数据权限，默认为ME权限");
            }

            switch (viewType) {
                case ALL:
                    // 全部权限，可以分配给任何人
                    log.debug("ALL权限，允许分配给任何人");
                    return true;
                case DEPARTMENT_AND_SUB:
                case DEPARTMENT:
                    // 部门权限，获取可以管理的员工列表
                    List<Long> managedEmployeeIds = dataScopeViewService.getCanViewEmployeeId(viewType, currentUserId);
                    log.debug("可管理的员工IDs: {}", managedEmployeeIds);
                    // 检查被分配员工是否在管理范围内
                    boolean canAssign = CollectionUtils.isNotEmpty(managedEmployeeIds) && managedEmployeeIds.contains(assigneeId);
                    log.debug("部门权限分配检查结果: {}", canAssign);
                    return canAssign;
                case ME:
                default:
                    // ME权限只能分配给自己（前面已经检查过了）
                    log.debug("ME权限，只能分配给自己，拒绝分配给其他人");
                    return false;
            }

        } catch (Exception e) {
            log.error("权限检查异常", e);
            // 如果权限检查出现异常，默认不允许分配给其他人
            return false;
        }
    }

    /**
     * 获取可分配的员工列表
     */
    public ResponseDTO<List<EmployeeVO>> getAssignableEmployees() {
        Long currentUserId = AdminRequestUtil.getRequestUserId();
        log.debug("获取可分配员工列表，当前用户ID: {}", currentUserId);

        try {
            // 获取当前用户信息
            EmployeeEntity currentEmployee = employeeDao.selectById(currentUserId);
            if (currentEmployee == null) {
                log.debug("当前用户不存在");
                return ResponseDTO.ok(List.of());
            }

            List<Long> assignableEmployeeIds = new ArrayList<>();

            // 超级管理员可以分配给任何人
            if (currentEmployee.getAdministratorFlag()) {
                log.debug("超级管理员，获取所有员工");
                // 获取所有有效员工
                List<EmployeeEntity> allEmployees = employeeDao.selectList(null);
                return ResponseDTO.ok(allEmployees.stream()
                        .filter(emp -> !emp.getDeletedFlag())
                        .map(this::convertToEmployeeVO)
                        .collect(Collectors.toList()));
            }

            // 获取当前用户的数据权限级别
            DataScopeViewTypeEnum viewType = dataScopeViewService.getEmployeeDataScopeViewType(DataScopeTypeEnum.CUSTOMER, currentUserId);
            log.debug("当前用户数据权限级别: {}", viewType);

            // 如果没有配置数据权限，默认为ME权限
            if (viewType == null) {
                viewType = DataScopeViewTypeEnum.ME;
                log.debug("没有配置数据权限，默认为ME权限");
            }

            switch (viewType) {
                case ALL:
                    // 全部权限，可以分配给任何人
                    log.debug("ALL权限，获取所有员工");
                    List<EmployeeEntity> allEmployees = employeeDao.selectList(null);
                    return ResponseDTO.ok(allEmployees.stream()
                            .filter(emp -> !emp.getDeletedFlag())
                            .map(this::convertToEmployeeVO)
                            .collect(Collectors.toList()));
                case DEPARTMENT_AND_SUB:
                case DEPARTMENT:
                    // 部门权限，获取可以管理的员工列表
                    assignableEmployeeIds = dataScopeViewService.getCanViewEmployeeId(viewType, currentUserId);
                    log.debug("可分配的员工IDs: {}", assignableEmployeeIds);
                    break;
                case ME:
                default:
                    // ME权限只能分配给自己
                    assignableEmployeeIds.add(currentUserId);
                    log.debug("ME权限，只能分配给自己");
                    break;
            }

            // 根据员工ID列表获取员工信息
            if (assignableEmployeeIds.isEmpty()) {
                return ResponseDTO.ok(List.of());
            }

            List<EmployeeEntity> assignableEmployees = employeeDao.selectBatchIds(assignableEmployeeIds);
            List<EmployeeVO> result = assignableEmployees.stream()
                    .filter(emp -> !emp.getDeletedFlag())
                    .map(this::convertToEmployeeVO)
                    .collect(Collectors.toList());

            log.debug("返回可分配员工数量: {}", result.size());
            return ResponseDTO.ok(result);

        } catch (Exception e) {
            log.error("获取可分配员工列表异常", e);
            return ResponseDTO.userErrorParam("获取可分配员工列表失败");
        }
    }

    /**
     * 转换员工实体为VO
     */
    private EmployeeVO convertToEmployeeVO(EmployeeEntity employee) {
        EmployeeVO vo = new EmployeeVO();
        vo.setEmployeeId(employee.getEmployeeId());
        vo.setActualName(employee.getActualName());
        vo.setLoginName(employee.getLoginName());
        vo.setPhone(employee.getPhone());
        vo.setDepartmentId(employee.getDepartmentId());
        vo.setDisabledFlag(employee.getDisabledFlag());
        vo.setAdministratorFlag(employee.getAdministratorFlag());
        // 部门名称需要通过部门服务获取，这里暂时设为空
        vo.setDepartmentName("");
        return vo;
    }
}

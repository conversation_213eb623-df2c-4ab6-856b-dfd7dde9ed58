package net.lab1024.sa.admin.module.business.hospital.visit.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.hospital.visit.dao.DiagnosisDao;
import net.lab1024.sa.admin.module.business.hospital.visit.dao.VisitPatientDao;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.entity.DiagnosisEntity;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.entity.VisitPatientEntity;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.form.DiagnosisAddForm;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.form.DiagnosisQueryForm;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.DiagnosisVO;

import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 诊断记录服务
 *
 * <AUTHOR>
 * @Date 2025-07-29
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Slf4j
@Service
public class DiagnosisService {

    @Autowired
    private DiagnosisDao diagnosisDao;

    @Autowired
    private VisitPatientDao visitPatientDao;



    /**
     * 分页查询诊断记录
     */
    public PageResult<DiagnosisVO> queryPage(DiagnosisQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<DiagnosisVO> list = diagnosisDao.queryPage(page, queryForm);
        PageResult<DiagnosisVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 根据患者ID查询诊断记录
     */
    public ResponseDTO<List<DiagnosisVO>> getByPatientId(Long patientId) {
        List<DiagnosisVO> list = diagnosisDao.selectByPatientId(patientId);
        return ResponseDTO.ok(list);
    }

    /**
     * 根据ID查询诊断记录详情
     */
    public ResponseDTO<DiagnosisVO> getById(Long diagnosisId) {
        DiagnosisVO diagnosisVO = diagnosisDao.selectDetailById(diagnosisId);
        if (diagnosisVO == null) {
            return ResponseDTO.userErrorParam("诊断记录不存在");
        }
        return ResponseDTO.ok(diagnosisVO);
    }

    /**
     * 新增诊断记录
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<Long> add(DiagnosisAddForm addForm, Long operateUserId, String operateUserName) {
        // 1. 验证患者是否存在
        VisitPatientEntity patient = visitPatientDao.selectById(addForm.getPatientId());
        if (patient == null) {
            return ResponseDTO.userErrorParam("患者不存在");
        }

        // 2. 创建诊断记录
        DiagnosisEntity diagnosisEntity = SmartBeanUtil.copy(addForm, DiagnosisEntity.class);
        diagnosisEntity.setPatientNo(patient.getPatientNo());
        diagnosisEntity.setPatientName(patient.getPatientName());

        // 3. 使用患者记录中分配的医生作为诊断医生（保持预约分诊时指定的医生）
        if (patient.getAssignedDoctorId() != null && patient.getAssignedDoctorId() > 0L) {
            diagnosisEntity.setDiagnosisDoctorId(patient.getAssignedDoctorId());
            diagnosisEntity.setDiagnosisDoctorName(patient.getAssignedDoctorName());
        } else {
            // 如果患者记录中没有分配医生，则使用当前操作用户
            diagnosisEntity.setDiagnosisDoctorId(operateUserId);
            diagnosisEntity.setDiagnosisDoctorName(operateUserName);
        }
        diagnosisEntity.setDiagnosisStatus(1); // 已诊断
        diagnosisEntity.setCreateUserId(operateUserId);
        diagnosisEntity.setCreateUserName(operateUserName);
        diagnosisEntity.setCreateTime(LocalDateTime.now());
        diagnosisEntity.setDeletedFlag(0);

        diagnosisDao.insert(diagnosisEntity);

        // 4. 更新患者诊断状态
        visitPatientDao.updateDiagnosisStatus(addForm.getPatientId(), 2, operateUserId, operateUserName);

        return ResponseDTO.ok(diagnosisEntity.getDiagnosisId());
    }

    /**
     * 更新诊断状态
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> updateStatus(Long diagnosisId, Integer diagnosisStatus, Long operateUserId, String operateUserName) {
        int result = diagnosisDao.updateDiagnosisStatus(diagnosisId, diagnosisStatus, operateUserId, operateUserName);
        if (result == 0) {
            return ResponseDTO.userErrorParam("诊断记录不存在");
        }
        return ResponseDTO.ok();
    }

    /**
     * 删除诊断记录
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> delete(Long diagnosisId, Long operateUserId, String operateUserName) {
        DiagnosisEntity entity = diagnosisDao.selectById(diagnosisId);
        if (entity == null) {
            return ResponseDTO.userErrorParam("诊断记录不存在");
        }

        entity.setDeletedFlag(1);
        entity.setUpdateUserId(operateUserId);
        entity.setUpdateUserName(operateUserName);
        entity.setUpdateTime(LocalDateTime.now());
        diagnosisDao.updateById(entity);

        return ResponseDTO.ok();
    }
}

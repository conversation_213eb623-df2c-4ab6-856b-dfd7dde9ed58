package net.lab1024.sa.admin.module.business.hospital.lead.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.module.business.hospital.lead.service.LeadStatusFlowService;
import net.lab1024.sa.admin.module.business.hospital.lead.service.LeadStatusHistoryService;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadStatusHistoryVO;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.form.LeadStatusFlowForm;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.ValidateData;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 线索状态流转控制器
 * 
 * <AUTHOR>
 * @Date 2025-07-22
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Tag(name = "线索状态流转")
@RestController
@RequestMapping("/api/lead/status-flow")
public class LeadStatusFlowController {

    @Resource
    private LeadStatusFlowService leadStatusFlowService;

    @Resource
    private LeadStatusHistoryService leadStatusHistoryService;

    @Operation(summary = "手动流转线索状态")
    @PostMapping("/manual-flow")
    public ResponseDTO<Boolean> manualFlowStatus(@RequestBody @Valid LeadStatusFlowForm flowForm) {
        return leadStatusFlowService.manualFlowStatus(flowForm.getLeadId(), flowForm.getTargetStatus(), flowForm.getRemark());
    }

    @Operation(summary = "手动关闭线索")
    @PostMapping("/close")
    public ResponseDTO<Boolean> closeLeadManually(@RequestBody @Valid ValidateData<LeadStatusFlowForm> validForm) {
        LeadStatusFlowForm flowForm = validForm.getData();
        return leadStatusFlowService.closeLeadManually(flowForm.getLeadId(), flowForm.getRemark());
    }

    @Operation(summary = "获取允许流转的状态列表")
    @GetMapping("/allowed-statuses/{currentStatus}")
    public ResponseDTO<Integer[]> getAllowedFlowStatuses(@PathVariable Integer currentStatus) {
        Integer[] allowedStatuses = leadStatusFlowService.getAllowedFlowStatuses(currentStatus);
        return ResponseDTO.ok(allowedStatuses);
    }

    @Operation(summary = "查询线索状态变更历史")
    @GetMapping("/history/{leadId}")
    public ResponseDTO<List<LeadStatusHistoryVO>> getStatusHistory(@PathVariable Long leadId) {
        return leadStatusHistoryService.getStatusHistory(leadId);
    }

    @Operation(summary = "统计线索状态停留时长")
    @GetMapping("/duration-stats/{leadId}")
    public ResponseDTO<List<LeadStatusHistoryVO>> getStatusDurationStats(@PathVariable Long leadId) {
        return leadStatusHistoryService.getStatusDurationStats(leadId);
    }

    @Operation(summary = "确认客户到院")
    @PostMapping("/confirm-arrival")
    public ResponseDTO<Boolean> confirmArrival(@RequestBody @Valid ValidateData<Long> validData) {
        Long leadId = validData.getData();
        leadStatusFlowService.onArrivalConfirmed(leadId);
        return ResponseDTO.ok(true);
    }

    @Operation(summary = "完成客户转化")
    @PostMapping("/complete-conversion")
    public ResponseDTO<Boolean> completeConversion(@RequestBody @Valid ValidateData<Long> validData) {
        Long leadId = validData.getData();
        leadStatusFlowService.onConversionCompleted(leadId);
        return ResponseDTO.ok(true);
    }
}

package net.lab1024.sa.admin.module.business.hospital.appointment.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * 医生批量排班表单
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class DoctorScheduleBatchAddForm {

    @Schema(description = "医生ID")
    @NotNull(message = "医生ID不能为空")
    private Long doctorId;

    @Schema(description = "排班日期列表")
    @NotEmpty(message = "排班日期列表不能为空")
    private List<LocalDate> scheduleDates;

    @Schema(description = "开始时间")
    @NotNull(message = "开始时间不能为空")
    private LocalTime startTime;

    @Schema(description = "结束时间")
    @NotNull(message = "结束时间不能为空")
    private LocalTime endTime;

    @Schema(description = "最大预约数")
    @NotNull(message = "最大预约数不能为空")
    @Range(min = 1, max = 100, message = "最大预约数必须在1-100之间")
    private Integer maxAppointments;

    @Schema(description = "状态：1-正常，2-暂停")
    private Integer status;

    @Schema(description = "是否覆盖已存在的排班")
    private Boolean overwrite;
}

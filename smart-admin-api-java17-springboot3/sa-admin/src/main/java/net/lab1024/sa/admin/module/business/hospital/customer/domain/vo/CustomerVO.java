package net.lab1024.sa.admin.module.business.hospital.customer.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.admin.module.business.hospital.customer.constant.CustomerStatusEnum;
import net.lab1024.sa.base.common.swagger.SchemaEnum;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 客户视图对象
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class CustomerVO {

    @Schema(description = "客户ID")
    private Long customerId;

    @Schema(description = "关联线索ID")
    private Long leadId;

    @Schema(description = "客户姓名")
    private String customerName;

    @Schema(description = "客户电话")
    private String customerPhone;

    @Schema(description = "客户微信")
    private String customerWechat;

    @Schema(description = "客户邮箱")
    private String customerEmail;

    @Schema(description = "性别：1-男，2-女")
    private Integer gender;

    @Schema(description = "性别名称")
    private String genderName;

    @Schema(description = "年龄")
    private Integer age;

    @Schema(description = "出生日期")
    private LocalDate birthday;

    @Schema(description = "身份证号")
    private String idCard;

    @Schema(description = "客户地址")
    private String customerAddress;

    @Schema(description = "职业")
    private String occupation;

    @Schema(description = "客户来源")
    private String customerSource;

    @Schema(description = "客户状态：1-潜在客户，2-意向客户，3-成交客户，4-流失客户")
    @SchemaEnum(CustomerStatusEnum.class)
    private Integer customerStatus;

    @Schema(description = "客户状态名称")
    private String customerStatusName;

    @Schema(description = "客户标签")
    private String customerTags;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人姓名")
    private String createUserName;

    @Schema(description = "预约次数")
    private Integer appointmentCount;

    @Schema(description = "最近预约时间")
    private LocalDateTime lastAppointmentTime;

    @Schema(description = "消费总金额")
    private java.math.BigDecimal totalAmount;

    // ==================== 360度视图统计信息 ====================

    @Schema(description = "预约总次数")
    private Integer totalAppointments;

    @Schema(description = "已完成预约次数")
    private Integer completedAppointments;

    @Schema(description = "取消预约次数")
    private Integer cancelledAppointments;

    @Schema(description = "病历记录数")
    private Integer medicalRecordCount;

    @Schema(description = "跟进记录数")
    private Integer followUpCount;

    @Schema(description = "最近就诊时间")
    private LocalDateTime lastVisitTime;

    @Schema(description = "最近跟进时间")
    private LocalDateTime lastFollowUpTime;

    @Schema(description = "下次预约时间")
    private LocalDateTime nextAppointmentTime;

    @Schema(description = "下次复诊时间")
    private LocalDateTime nextVisitTime;

    @Schema(description = "客户来源详情")
    private String sourceDetail;

    @Schema(description = "推荐人信息")
    private String referrerInfo;

    @Schema(description = "客户等级")
    private Integer customerLevel;

    @Schema(description = "客户等级名称")
    private String customerLevelName;

    @Schema(description = "满意度评分")
    private java.math.BigDecimal satisfactionScore;

    @Schema(description = "客户价值评分")
    private java.math.BigDecimal valueScore;

    @Schema(description = "流失风险评分")
    private java.math.BigDecimal churnRiskScore;

    @Schema(description = "活跃度评分")
    private java.math.BigDecimal activityScore;
}

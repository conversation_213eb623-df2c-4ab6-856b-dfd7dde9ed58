package net.lab1024.sa.admin.module.business.hospital.dashboard.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 待办事项 VO
 *
 * <AUTHOR>
 * @Date 2024-12-15 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright <a href="https://1024lab.net">1024创新实验室</a>
 */
@Data
@Schema(description = "待办事项")
public class TodoItemVO {

    @Schema(description = "待办ID")
    private Long todoId;

    @Schema(description = "待办类型")
    private String todoType;

    @Schema(description = "待办标题")
    private String todoTitle;

    @Schema(description = "待办内容")
    private String todoContent;

    @Schema(description = "优先级")
    private Integer priorityLevel;

    @Schema(description = "待办状态")
    private Integer todoStatus;

    @Schema(description = "业务类型")
    private String businessType;

    @Schema(description = "业务ID")
    private Long businessId;

    @Schema(description = "截止时间")
    private LocalDateTime dueTime;

    @Schema(description = "提醒时间")
    private LocalDateTime remindTime;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "完成时间")
    private LocalDateTime completeTime;

    @Schema(description = "分配员工ID")
    private Long assignedEmployeeId;

    @Schema(description = "分配员工姓名")
    private String assignedEmployeeName;

    @Schema(description = "处理员工ID")
    private Long handlerEmployeeId;

    @Schema(description = "处理员工姓名")
    private String handlerEmployeeName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
} 
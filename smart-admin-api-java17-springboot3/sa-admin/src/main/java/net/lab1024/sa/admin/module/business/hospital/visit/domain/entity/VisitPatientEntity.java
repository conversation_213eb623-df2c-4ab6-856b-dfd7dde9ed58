package net.lab1024.sa.admin.module.business.hospital.visit.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 到诊患者实体类
 *
 * <AUTHOR>
 * @Date 2025-07-29 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@TableName("t_visit_patient")
public class VisitPatientEntity {

    /**
     * 患者ID
     */
    @TableId(type = IdType.AUTO)
    private Long patientId;

    /**
     * 患者编号
     */
    private String patientNo;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 患者电话
     */
    private String patientPhone;

    /**
     * 微信号
     */
    private String patientWechat;

    /**
     * 邮箱
     */
    private String patientEmail;

    /**
     * 性别：1-男，2-女
     */
    private Integer gender;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 生日
     */
    private LocalDate birthday;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 地址
     */
    private String patientAddress;

    /**
     * 职业
     */
    private String occupation;

    /**
     * 到诊状态：1-未到诊，2-已到诊，3-诊疗中，4-已完成，5-已离院
     */
    private Integer visitStatus;

    /**
     * 诊断流程状态：1-待诊断，2-已诊断，3-已开单，4-已收费，5-已分配助理，6-治疗中，7-已完成
     */
    private Integer diagnosisStatus;

    /**
     * 关联预约ID
     */
    private Long appointmentId;

    /**
     * 分配医生ID
     */
    private Long assignedDoctorId;

    /**
     * 分配医生姓名
     */
    private String assignedDoctorName;

    /**
     * 分配治疗助理ID
     */
    private Long assignedAssistantId;

    /**
     * 分配治疗助理姓名
     */
    private String assignedAssistantName;

    /**
     * 到诊日期
     */
    private LocalDate visitDate;

    /**
     * 到诊时间
     */
    private LocalTime visitTime;

    /**
     * 登记时间
     */
    private LocalDateTime registrationTime;

    /**
     * 完成时间
     */
    private LocalDateTime completionTime;

    /**
     * 所属部门ID
     */
    private Long departmentId;

    /**
     * 所属部门名称
     */
    private String departmentName;

    /**
     * 数据权限员工ID列表(JSON格式)
     */
    private String dataScopeEmployeeIds;

    /**
     * 患者来源
     */
    private String patientSource;

    /**
     * 患者等级：1-普通患者，2-VIP患者，3-SVIP患者
     */
    private Integer patientLevel;

    /**
     * 患者标签
     */
    private String patientTags;

    /**
     * 首次就诊日期
     */
    private LocalDate firstVisitDate;

    /**
     * 最后就诊日期
     */
    private LocalDate lastVisitDate;

    /**
     * 总消费金额
     */
    private BigDecimal totalConsumption;

    /**
     * 紧急联系人
     */
    private String emergencyContact;

    /**
     * 紧急联系人电话
     */
    private String emergencyPhone;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志：0-未删除，1-已删除
     */
    private Boolean deletedFlag;

    /**
     * 创建人ID
     */
    private Long createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    private Long updateUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}

package net.lab1024.sa.admin.module.business.hospital.visit.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 诊断状态枚举
 *
 * <AUTHOR>
 * @Date 2025-07-29 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Getter
@AllArgsConstructor
public enum DiagnosisStatusEnum implements BaseEnum {

    /**
     * 草稿
     */
    DRAFT(1, "草稿"),

    /**
     * 已完成
     */
    COMPLETED(2, "已完成"),

    /**
     * 已审核
     */
    AUDITED(3, "已审核");

    private final Integer value;

    private final String desc;
}

package net.lab1024.sa.admin.module.business.hospital.lead.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.domain.PageParam;

/**
 * 线索跟进查询表单
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@Schema(description = "线索跟进查询表单")
public class LeadFollowQueryForm extends PageParam {

    @Schema(description = "线索ID")
    private Long leadId;

    @Schema(description = "跟进员工ID")
    private Long employeeId;

    @Schema(description = "跟进类型")
    private Integer followType;

    @Schema(description = "跟进结果")
    private String followResult;

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "结束时间")
    private String endTime;
}

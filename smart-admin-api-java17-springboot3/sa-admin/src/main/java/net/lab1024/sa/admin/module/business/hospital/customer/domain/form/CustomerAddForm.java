package net.lab1024.sa.admin.module.business.hospital.customer.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import net.lab1024.sa.admin.module.business.hospital.customer.constant.CustomerStatusEnum;
import net.lab1024.sa.base.common.swagger.SchemaEnum;
import net.lab1024.sa.base.common.validator.enumeration.CheckEnum;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import java.time.LocalDate;

/**
 * 客户添加表单
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class CustomerAddForm {

    @Schema(description = "关联线索ID")
    private Long leadId;

    @Schema(description = "客户姓名")
    @NotBlank(message = "客户姓名不能为空")
    @Length(max = 100, message = "客户姓名最多100字符")
    private String customerName;

    @Schema(description = "客户电话")
    @NotBlank(message = "客户电话不能为空")
    @Length(max = 20, message = "客户电话最多20字符")
    private String customerPhone;

    @Schema(description = "客户微信")
    @Length(max = 50, message = "客户微信最多50字符")
    private String customerWechat;

    @Schema(description = "客户邮箱")
    @Length(max = 100, message = "客户邮箱最多100字符")
    private String customerEmail;

    @Schema(description = "性别：1-男，2-女")
    @Range(min = 1, max = 2, message = "性别只能是1或2")
    private Integer gender;

    @Schema(description = "年龄")
    @Range(min = 1, max = 120, message = "年龄必须在1-120之间")
    private Integer age;

    @Schema(description = "出生日期")
    private LocalDate birthday;

    @Schema(description = "身份证号")
    @Length(max = 18, message = "身份证号最多18字符")
    private String idCard;

    @Schema(description = "客户地址")
    @Length(max = 200, message = "客户地址最多200字符")
    private String customerAddress;

    @Schema(description = "职业")
    @Length(max = 50, message = "职业最多50字符")
    private String occupation;

    @Schema(description = "客户来源")
    @Length(max = 50, message = "客户来源最多50字符")
    private String customerSource;

    @Schema(description = "客户状态：1-潜在客户，2-意向客户，3-成交客户，4-流失客户")
    @SchemaEnum(CustomerStatusEnum.class)
    @CheckEnum(message = "客户状态错误", value = CustomerStatusEnum.class, required = false)
    private Integer customerStatus;

    @Schema(description = "客户标签")
    @Length(max = 200, message = "客户标签最多200字符")
    private String customerTags;

    @Schema(description = "备注")
    @Length(max = 500, message = "备注最多500字符")
    private String remark;
}

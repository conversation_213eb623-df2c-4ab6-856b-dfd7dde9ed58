package net.lab1024.sa.admin.module.business.hospital.lead.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.entity.LeadOwnershipChangeRequestEntity;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.form.LeadOwnershipChangeQueryForm;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadOwnershipChangeRequestVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 线索归属变更申请DAO
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Mapper
@Component
public interface LeadOwnershipChangeRequestDao extends BaseMapper<LeadOwnershipChangeRequestEntity> {

    /**
     * 分页查询线索归属变更申请
     *
     * @param page 分页参数
     * @param queryForm 查询条件
     * @return 申请列表
     */
    List<LeadOwnershipChangeRequestVO> queryPage(Page page, @Param("query") LeadOwnershipChangeQueryForm queryForm);

    /**
     * 根据线索ID查询待审批的申请
     *
     * @param leadId 线索ID
     * @return 待审批的申请
     */
    LeadOwnershipChangeRequestEntity selectPendingByLeadId(@Param("leadId") Long leadId);

    /**
     * 根据申请人ID查询申请列表
     *
     * @param newOwnerId 申请人ID
     * @param requestStatus 申请状态
     * @return 申请列表
     */
    List<LeadOwnershipChangeRequestEntity> selectByNewOwnerId(@Param("newOwnerId") Long newOwnerId, 
                                                              @Param("requestStatus") Integer requestStatus);

    /**
     * 根据部门负责人ID查询待审批申请
     *
     * @param departmentManagerId 部门负责人ID
     * @return 待审批申请列表
     */
    List<LeadOwnershipChangeRequestEntity> selectPendingByManagerId(@Param("departmentManagerId") Long departmentManagerId);

    /**
     * 统计某个线索的申请次数
     *
     * @param leadId 线索ID
     * @return 申请次数
     */
    Integer countByLeadId(@Param("leadId") Long leadId);

    /**
     * 统计某个用户的申请次数
     *
     * @param newOwnerId 申请人ID
     * @param requestStatus 申请状态
     * @return 申请次数
     */
    Integer countByNewOwnerIdAndStatus(@Param("newOwnerId") Long newOwnerId, 
                                       @Param("requestStatus") Integer requestStatus);
}

package net.lab1024.sa.admin.module.business.hospital.followup.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.domain.PageParam;

import java.time.LocalDateTime;

/**
 * 回访记录查询表单
 *
 * <AUTHOR>
 * @Date 2025-07-30 00:00:00
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@Schema(description = "回访记录查询表单")
public class FollowUpRecordQueryForm extends PageParam {

    @Schema(description = "客户姓名")
    private String customerName;

    @Schema(description = "客户电话")
    private String customerPhone;

    @Schema(description = "回访类型")
    private Integer followUpType;

    @Schema(description = "回访方式")
    private Integer followUpMethod;

    @Schema(description = "回访状态")
    private Integer followUpStatus;

    @Schema(description = "回访结果")
    private String followUpResult;

    @Schema(description = "回访人员")
    private String followUpUser;

    @Schema(description = "优先级")
    private Integer priorityLevel;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "计划回访开始时间")
    private LocalDateTime plannedStartTime;

    @Schema(description = "计划回访结束时间")
    private LocalDateTime plannedEndTime;

    @Schema(description = "创建人ID")
    private Long createUserId;

    @Schema(description = "更新人ID")
    private Long updateUserId;

    @Schema(description = "关联记录ID")
    private Long relatedRecordId;

    @Schema(description = "关联记录类型")
    private String relatedRecordType;
}

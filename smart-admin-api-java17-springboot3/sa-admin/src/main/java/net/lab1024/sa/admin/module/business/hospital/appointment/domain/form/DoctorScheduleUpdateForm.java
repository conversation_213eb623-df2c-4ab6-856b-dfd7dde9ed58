package net.lab1024.sa.admin.module.business.hospital.appointment.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 医生排班更新表单
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class DoctorScheduleUpdateForm extends DoctorScheduleAddForm {

    @Schema(description = "排班ID")
    @NotNull(message = "排班ID不能为空")
    private Long scheduleId;
}

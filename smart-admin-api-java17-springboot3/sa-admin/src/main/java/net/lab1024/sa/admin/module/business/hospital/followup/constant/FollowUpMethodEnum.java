package net.lab1024.sa.admin.module.business.hospital.followup.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 回访方式枚举
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Getter
@AllArgsConstructor
public enum FollowUpMethodEnum implements BaseEnum {

    /**
     * 电话
     */
    PHONE(1, "电话"),

    /**
     * 微信
     */
    WECHAT(2, "微信"),

    /**
     * 短信
     */
    SMS(3, "短信"),

    /**
     * 邮件
     */
    EMAIL(4, "邮件"),

    /**
     * 上门
     */
    VISIT(5, "上门");

    private final Integer value;

    private final String desc;
}

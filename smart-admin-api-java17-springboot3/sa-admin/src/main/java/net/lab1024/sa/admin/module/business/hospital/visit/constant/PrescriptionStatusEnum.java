package net.lab1024.sa.admin.module.business.hospital.visit.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 开单状态枚举
 *
 * <AUTHOR>
 * @Date 2025-07-29 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Getter
@AllArgsConstructor
public enum PrescriptionStatusEnum implements BaseEnum {

    /**
     * 待审核
     */
    PENDING_AUDIT(1, "待审核"),

    /**
     * 已审核
     */
    AUDITED(2, "已审核"),

    /**
     * 已执行
     */
    EXECUTED(3, "已执行"),

    /**
     * 已取消
     */
    CANCELLED(4, "已取消");

    private final Integer value;

    private final String desc;
}

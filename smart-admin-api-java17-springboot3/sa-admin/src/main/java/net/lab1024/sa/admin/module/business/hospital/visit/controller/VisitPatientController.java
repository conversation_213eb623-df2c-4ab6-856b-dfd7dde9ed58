package net.lab1024.sa.admin.module.business.hospital.visit.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;

import net.lab1024.sa.admin.module.business.hospital.visit.domain.form.VisitPatientAddForm;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.form.VisitPatientQueryForm;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.form.VisitPatientUpdateForm;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.VisitPatientVO;
import net.lab1024.sa.admin.module.business.hospital.visit.service.VisitPatientService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.module.support.operatelog.annotation.OperateLog;
import net.lab1024.sa.admin.util.AdminRequestUtil;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 到诊患者管理Controller
 *
 * <AUTHOR>
 * @Date 2025-07-29 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Tag(name = "到诊患者管理")
@RestController
@RequestMapping("/api/visit/patient")
public class VisitPatientController {

    @Resource
    private VisitPatientService visitPatientService;

    @Operation(summary = "分页查询患者")
    @PostMapping("/query")
    @SaCheckPermission("visit:patient:query")
    public ResponseDTO<PageResult<VisitPatientVO>> queryPage(@RequestBody @Valid VisitPatientQueryForm queryForm) {
        return visitPatientService.queryPage(queryForm);
    }

    @Operation(summary = "添加患者")
    @PostMapping("/add")
    @SaCheckPermission("visit:patient:add")
    @OperateLog
    public ResponseDTO<String> add(@RequestBody @Valid VisitPatientAddForm addForm) {
        return visitPatientService.add(addForm);
    }

    @Operation(summary = "更新患者")
    @PostMapping("/update")
    @SaCheckPermission("visit:patient:update")
    @OperateLog
    public ResponseDTO<String> update(@RequestBody @Valid VisitPatientUpdateForm updateForm) {
        return visitPatientService.update(updateForm);
    }

    @Operation(summary = "删除患者")
    @GetMapping("/delete/{patientId}")
    @SaCheckPermission("visit:patient:delete")
    @OperateLog
    public ResponseDTO<String> delete(@PathVariable Long patientId) {
        return visitPatientService.delete(patientId);
    }

    @Operation(summary = "批量删除患者")
    @PostMapping("/batchDelete")
    @SaCheckPermission("visit:patient:delete")
    @OperateLog
    public ResponseDTO<String> batchDelete(@RequestBody List<Long> patientIds) {
        return visitPatientService.batchDelete(patientIds);
    }

    @Operation(summary = "获取患者详情")
    @GetMapping("/detail/{patientId}")
    @SaCheckPermission("visit:patient:query")
    public ResponseDTO<VisitPatientVO> getDetail(@PathVariable Long patientId) {
        return visitPatientService.getDetail(patientId);
    }

    @Operation(summary = "更新患者到诊状态")
    @PostMapping("/updateStatus/{patientId}/{visitStatus}")
    @SaCheckPermission("visit:patient:status")
    @OperateLog
    public ResponseDTO<String> updateVisitStatus(@PathVariable Long patientId, @PathVariable Integer visitStatus) {
        return visitPatientService.updateVisitStatus(patientId, visitStatus);
    }

    @Operation(summary = "分配医生")
    @PostMapping("/assignDoctor/{patientId}/{doctorId}")
    @SaCheckPermission("visit:patient:assign")
    @OperateLog
    public ResponseDTO<String> assignDoctor(@PathVariable Long patientId, @PathVariable Long doctorId) {
        return visitPatientService.assignDoctor(patientId, doctorId);
    }

    @Operation(summary = "分配治疗助理")
    @PostMapping("/assignAssistant/{patientId}/{assistantId}")
    @SaCheckPermission("visit:patient:assign")
    @OperateLog
    public ResponseDTO<String> assignAssistant(@PathVariable Long patientId, @PathVariable Long assistantId) {
        return visitPatientService.assignAssistant(patientId, assistantId);
    }

    @Operation(summary = "获取患者统计信息")
    @GetMapping("/statistics")
    @SaCheckPermission("visit:patient:query")
    public ResponseDTO<Object> getStatistics() {
        return visitPatientService.getStatistics();
    }

    @Operation(summary = "更新患者诊断状态并创建跟进计划")
    @PostMapping("/updateDiagnosisStatus/{patientId}/{diagnosisStatus}")
    @SaCheckPermission("visit:patient:update")
    @OperateLog
    public ResponseDTO<String> updateDiagnosisStatusWithFollowUp(@PathVariable Long patientId, @PathVariable Integer diagnosisStatus) {
        Long operateUserId = AdminRequestUtil.getRequestUserId();
        String operateUserName = AdminRequestUtil.getRequestUser().getUserName();
        return visitPatientService.updateDiagnosisStatusWithFollowUp(patientId, diagnosisStatus, operateUserId, operateUserName);
    }

    @Operation(summary = "手动创建患者跟进计划")
    @PostMapping("/createFollowUpPlan/{patientId}")
    @SaCheckPermission("visit:patient:follow")
    @OperateLog
    public ResponseDTO<String> createPatientFollowUpPlan(@PathVariable Long patientId) {
        Long operateUserId = AdminRequestUtil.getRequestUserId();
        String operateUserName = AdminRequestUtil.getRequestUser().getUserName();
        return visitPatientService.createPatientFollowUpPlan(patientId, operateUserId, operateUserName);
    }
}

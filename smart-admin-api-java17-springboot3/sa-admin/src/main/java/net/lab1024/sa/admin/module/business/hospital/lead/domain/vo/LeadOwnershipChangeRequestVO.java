package net.lab1024.sa.admin.module.business.hospital.lead.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.swagger.SchemaEnum;
import net.lab1024.sa.admin.module.business.hospital.lead.constant.LeadOwnershipChangeStatusEnum;

import java.time.LocalDateTime;

/**
 * 线索归属变更申请VO
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class LeadOwnershipChangeRequestVO {

    @Schema(description = "申请ID")
    private Long requestId;

    @Schema(description = "线索ID")
    private Long leadId;

    @Schema(description = "客户手机号")
    private String customerPhone;

    @Schema(description = "客户姓名")
    private String customerName;

    @Schema(description = "原归属人ID")
    private Long originalOwnerId;

    @Schema(description = "原归属人姓名")
    private String originalOwnerName;

    @Schema(description = "新归属人ID")
    private Long newOwnerId;

    @Schema(description = "新归属人姓名")
    private String newOwnerName;

    @Schema(description = "申请人所在部门ID")
    private Long departmentId;

    @Schema(description = "申请人所在部门名称")
    private String departmentName;

    @Schema(description = "部门负责人ID")
    private Long departmentManagerId;

    @Schema(description = "部门负责人姓名")
    private String departmentManagerName;

    @Schema(description = "申请原因")
    private String requestReason;

    @SchemaEnum(LeadOwnershipChangeStatusEnum.class)
    private Integer requestStatus;

    @Schema(description = "申请状态描述")
    private String requestStatusDesc;

    @Schema(description = "审批时间")
    private LocalDateTime approveTime;

    @Schema(description = "审批意见")
    private String approveReason;

    @Schema(description = "审批人ID")
    private Long approveUserId;

    @Schema(description = "审批人姓名")
    private String approveUserName;

    @Schema(description = "原线索最后跟进时间")
    private LocalDateTime lastFollowTime;

    @Schema(description = "距离最后跟进天数")
    private Integer daysSinceLastFollow;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人ID")
    private Long createUserId;

    @Schema(description = "更新人ID")
    private Long updateUserId;
}

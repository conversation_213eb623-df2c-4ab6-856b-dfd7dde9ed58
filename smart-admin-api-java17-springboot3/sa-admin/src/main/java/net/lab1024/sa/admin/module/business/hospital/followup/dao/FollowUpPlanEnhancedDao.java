package net.lab1024.sa.admin.module.business.hospital.followup.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.entity.FollowUpPlanEnhancedEntity;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.form.FollowUpPlanEnhancedQueryForm;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.vo.FollowUpPlanEnhancedVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 回访计划增强版DAO
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Mapper
@Component
public interface FollowUpPlanEnhancedDao extends BaseMapper<FollowUpPlanEnhancedEntity> {

    /**
     * 分页查询回访计划
     *
     * @param page 分页参数
     * @param queryForm 查询条件
     * @return 回访计划列表
     */
    List<FollowUpPlanEnhancedVO> queryPage(Page<?> page, @Param("query") FollowUpPlanEnhancedQueryForm queryForm);

    /**
     * 根据计划编号查询计划（用于查重）
     *
     * @param planNo 计划编号
     * @param excludePlanId 排除的计划ID（用于更新时排除自己）
     * @return 计划实体
     */
    FollowUpPlanEnhancedEntity selectByPlanNo(@Param("planNo") String planNo, @Param("excludePlanId") Long excludePlanId);

    /**
     * 获取可执行的计划列表
     *
     * @param currentTime 当前时间
     * @return 可执行的计划列表
     */
    List<FollowUpPlanEnhancedEntity> getExecutablePlans(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 根据负责人ID查询计划列表
     *
     * @param responsibleUserId 负责人ID
     * @param planStatus 计划状态（可选）
     * @return 计划列表
     */
    List<FollowUpPlanEnhancedVO> selectByResponsibleUser(@Param("responsibleUserId") Long responsibleUserId, 
                                                         @Param("planStatus") Integer planStatus);

    /**
     * 根据部门ID查询计划列表
     *
     * @param departmentId 部门ID
     * @param planStatus 计划状态（可选）
     * @return 计划列表
     */
    List<FollowUpPlanEnhancedVO> selectByDepartment(@Param("departmentId") Long departmentId, 
                                                    @Param("planStatus") Integer planStatus);

    /**
     * 更新计划状态
     *
     * @param planId 计划ID
     * @param planStatus 计划状态
     * @param updateUserId 更新人ID
     * @param updateTime 更新时间
     * @return 更新行数
     */
    int updatePlanStatus(@Param("planId") Long planId, @Param("planStatus") Integer planStatus, 
                        @Param("updateUserId") Long updateUserId, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新计划执行信息
     *
     * @param planId 计划ID
     * @param actualPatientCount 实际患者数量
     * @param lastExecutionTime 最后执行时间
     * @param nextExecutionTime 下次执行时间
     * @param totalExecutions 总执行次数
     * @param updateUserId 更新人ID
     * @param updateTime 更新时间
     * @return 更新行数
     */
    int updateExecutionInfo(@Param("planId") Long planId, 
                           @Param("actualPatientCount") Integer actualPatientCount,
                           @Param("lastExecutionTime") LocalDateTime lastExecutionTime,
                           @Param("nextExecutionTime") LocalDateTime nextExecutionTime,
                           @Param("totalExecutions") Integer totalExecutions,
                           @Param("updateUserId") Long updateUserId, 
                           @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新计划统计信息
     *
     * @param planId 计划ID
     * @param completedCount 已完成数量
     * @param successRate 成功率
     * @param updateUserId 更新人ID
     * @param updateTime 更新时间
     * @return 更新行数
     */
    int updateStatistics(@Param("planId") Long planId, 
                        @Param("completedCount") Integer completedCount,
                        @Param("successRate") java.math.BigDecimal successRate,
                        @Param("updateUserId") Long updateUserId, 
                        @Param("updateTime") LocalDateTime updateTime);

    /**
     * 获取计划统计数据
     *
     * @param queryForm 查询条件
     * @return 统计数据
     */
    List<FollowUpPlanEnhancedVO> getPlanStatistics(@Param("query") FollowUpPlanEnhancedQueryForm queryForm);

    /**
     * 根据状态统计计划数量
     *
     * @param planStatus 计划状态
     * @param departmentId 部门ID（可选）
     * @param responsibleUserId 负责人ID（可选）
     * @return 计划数量
     */
    Integer getPlanCountByStatus(@Param("planStatus") Integer planStatus, 
                                @Param("departmentId") Long departmentId, 
                                @Param("responsibleUserId") Long responsibleUserId);

    /**
     * 获取今日待执行计划
     *
     * @param responsibleUserId 负责人ID（可选）
     * @param departmentId 部门ID（可选）
     * @return 今日待执行计划列表
     */
    List<FollowUpPlanEnhancedVO> getTodayPendingPlans(@Param("responsibleUserId") Long responsibleUserId, 
                                                      @Param("departmentId") Long departmentId);

    /**
     * 获取逾期计划
     *
     * @param responsibleUserId 负责人ID（可选）
     * @param departmentId 部门ID（可选）
     * @return 逾期计划列表
     */
    List<FollowUpPlanEnhancedVO> getOverduePlans(@Param("responsibleUserId") Long responsibleUserId, 
                                                 @Param("departmentId") Long departmentId);

    /**
     * 获取最近创建的计划
     *
     * @param limit 限制数量
     * @param departmentId 部门ID（可选）
     * @param responsibleUserId 负责人ID（可选）
     * @return 计划列表
     */
    List<FollowUpPlanEnhancedVO> getRecentPlans(@Param("limit") Integer limit, 
                                               @Param("departmentId") Long departmentId, 
                                               @Param("responsibleUserId") Long responsibleUserId);
}

package net.lab1024.sa.admin.module.business.hospital.visit.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 跟进记录实体类
 *
 * <AUTHOR>
 * @Date 2025-07-29 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@TableName("t_visit_follow")
public class VisitFollowEntity {

    /**
     * 跟进记录ID
     */
    @TableId(type = IdType.AUTO)
    private Long followId;

    /**
     * 患者ID
     */
    private Long patientId;

    /**
     * 患者姓名
     */
    private String patientName;

    /**
     * 患者电话
     */
    private String patientPhone;

    /**
     * 关联诊断记录ID
     */
    private Long diagnosisId;

    /**
     * 关联开单记录ID
     */
    private Long prescriptionId;

    /**
     * 跟进类型：1-术后跟进，2-用药跟进，3-复诊提醒，4-康复指导，5-满意度调查
     */
    private Integer followType;

    /**
     * 跟进方式：1-电话，2-微信，3-短信，4-邮件，5-上门
     */
    private Integer followMethod;

    /**
     * 跟进内容
     */
    private String followContent;

    /**
     * 跟进结果
     */
    private String followResult;

    /**
     * 患者反馈
     */
    private String patientFeedback;

    /**
     * 满意度评分（1-5分）
     */
    private Integer satisfactionScore;

    /**
     * 分配跟进人ID
     */
    private Long assignedUserId;

    /**
     * 分配跟进人姓名
     */
    private String assignedUserName;

    /**
     * 分配人ID
     */
    private Long assignedByUserId;

    /**
     * 分配人姓名
     */
    private String assignedByUserName;

    /**
     * 分配时间
     */
    private LocalDateTime assignedTime;

    /**
     * 计划跟进时间
     */
    private LocalDateTime plannedFollowTime;

    /**
     * 实际跟进时间
     */
    private LocalDateTime actualFollowTime;

    /**
     * 下次跟进时间
     */
    private LocalDateTime nextFollowTime;

    /**
     * 跟进状态：1-待跟进，2-跟进中，3-已完成，4-已取消，5-跟进失败
     */
    private Integer followStatus;

    /**
     * 优先级：1-高，2-中，3-低
     */
    private Integer priorityLevel;

    /**
     * 是否提醒：0-否，1-是
     */
    private Boolean reminderFlag;

    /**
     * 提醒时间
     */
    private LocalDateTime reminderTime;

    /**
     * 部门ID
     */
    private Long departmentId;

    /**
     * 部门名称
     */
    private String departmentName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志：0-未删除，1-已删除
     */
    private Boolean deletedFlag;

    /**
     * 创建人ID
     */
    private Long createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    private Long updateUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}

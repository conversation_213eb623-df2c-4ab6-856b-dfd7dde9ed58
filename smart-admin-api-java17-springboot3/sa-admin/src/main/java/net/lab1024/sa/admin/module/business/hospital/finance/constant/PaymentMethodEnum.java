package net.lab1024.sa.admin.module.business.hospital.finance.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 支付方式枚举
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Getter
@AllArgsConstructor
public enum PaymentMethodEnum implements BaseEnum {

    /**
     * 现金
     */
    CASH(1, "现金"),

    /**
     * 微信
     */
    WECHAT(2, "微信"),

    /**
     * 支付宝
     */
    ALIPAY(3, "支付宝"),

    /**
     * 银行卡
     */
    BANK_CARD(4, "银行卡"),

    /**
     * 组合支付
     */
    COMBINATION(5, "组合支付");

    private final Integer value;

    private final String desc;

    /**
     * 根据值获取枚举
     */
    public static PaymentMethodEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (PaymentMethodEnum methodEnum : values()) {
            if (methodEnum.getValue().equals(value)) {
                return methodEnum;
            }
        }
        return null;
    }
}

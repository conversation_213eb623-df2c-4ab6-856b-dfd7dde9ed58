package net.lab1024.sa.admin.module.business.hospital.visit.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.hospital.visit.dao.DiagnosisDao;
import net.lab1024.sa.admin.module.business.hospital.visit.dao.PrescriptionDao;
import net.lab1024.sa.admin.module.business.hospital.visit.dao.PrescriptionItemDao;
import net.lab1024.sa.admin.module.business.hospital.visit.dao.VisitPatientDao;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.entity.DiagnosisEntity;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.entity.PrescriptionEntity;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.entity.PrescriptionItemEntity;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.entity.VisitPatientEntity;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.form.PrescriptionAddForm;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.form.PrescriptionItemForm;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.form.PrescriptionQueryForm;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.PrescriptionVO;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 开单记录服务
 *
 * <AUTHOR>
 * @Date 2025-07-29
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Slf4j
@Service
public class PrescriptionService {

    @Autowired
    private PrescriptionDao prescriptionDao;

    @Autowired
    private PrescriptionItemDao prescriptionItemDao;

    @Autowired
    private DiagnosisDao diagnosisDao;

    @Autowired
    private VisitPatientDao visitPatientDao;

    /**
     * 分页查询开单记录
     */
    public PageResult<PrescriptionVO> queryPage(PrescriptionQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<PrescriptionVO> list = prescriptionDao.queryPage(page, queryForm);
        PageResult<PrescriptionVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return pageResult;
    }

    /**
     * 根据患者ID查询开单记录
     */
    public ResponseDTO<List<PrescriptionVO>> getByPatientId(Long patientId) {
        List<PrescriptionVO> list = prescriptionDao.selectByPatientId(patientId);
        return ResponseDTO.ok(list);
    }

    /**
     * 根据诊断ID查询开单记录
     */
    public ResponseDTO<PrescriptionVO> getByDiagnosisId(Long diagnosisId) {
        PrescriptionVO prescriptionVO = prescriptionDao.selectByDiagnosisId(diagnosisId);
        return ResponseDTO.ok(prescriptionVO);
    }

    /**
     * 根据ID查询开单记录详情
     */
    public ResponseDTO<PrescriptionVO> getById(Long prescriptionId) {
        PrescriptionVO prescriptionVO = prescriptionDao.selectDetailById(prescriptionId);
        if (prescriptionVO == null) {
            return ResponseDTO.userErrorParam("开单记录不存在");
        }
        return ResponseDTO.ok(prescriptionVO);
    }

    /**
     * 新增开单记录
     */
    @Transactional(rollbackFor = Exception.class)

    public ResponseDTO<Long> add(PrescriptionAddForm addForm, Long operateUserId, String operateUserName) {
        // 1. 验证诊断记录是否存在
        DiagnosisEntity diagnosis = diagnosisDao.selectById(addForm.getDiagnosisId());
        if (diagnosis == null) {
            return ResponseDTO.userErrorParam("诊断记录不存在");
        }

        // 2. 验证患者是否存在
        VisitPatientEntity patient = visitPatientDao.selectById(addForm.getPatientId());
        if (patient == null) {
            return ResponseDTO.userErrorParam("患者不存在");
        }

        // 3. 计算总金额
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (PrescriptionItemForm item : addForm.getTreatmentItems()) {
            BigDecimal subtotal = item.getUnitPrice().multiply(new BigDecimal(item.getQuantity()));
            totalAmount = totalAmount.add(subtotal);
        }

        // 4. 生成开单编号
        String prescriptionNo = generatePrescriptionNo();

        // 5. 创建开单记录
        PrescriptionEntity prescriptionEntity = new PrescriptionEntity();
        prescriptionEntity.setDiagnosisId(addForm.getDiagnosisId());
        prescriptionEntity.setPatientId(addForm.getPatientId());
        prescriptionEntity.setPatientNo(patient.getPatientNo());
        prescriptionEntity.setPatientName(patient.getPatientName());
        prescriptionEntity.setPrescriptionNo(prescriptionNo);
        prescriptionEntity.setTotalAmount(totalAmount);
        prescriptionEntity.setPrescriptionNote(addForm.getPrescriptionNote());
        prescriptionEntity.setPrescriptionStatus(1); // 已开单
        prescriptionEntity.setCreateUserId(operateUserId);
        prescriptionEntity.setCreateUserName(operateUserName);
        prescriptionEntity.setCreateTime(LocalDateTime.now());
        prescriptionEntity.setDeletedFlag(0);

        prescriptionDao.insert(prescriptionEntity);

        // 6. 创建开单项目明细
        List<PrescriptionItemEntity> itemList = new ArrayList<>();
        for (PrescriptionItemForm itemForm : addForm.getTreatmentItems()) {
            PrescriptionItemEntity itemEntity = SmartBeanUtil.copy(itemForm, PrescriptionItemEntity.class);
            itemEntity.setPrescriptionId(prescriptionEntity.getPrescriptionId());
            itemEntity.setSubtotal(itemForm.getUnitPrice().multiply(new BigDecimal(itemForm.getQuantity())));
            itemEntity.setCreateTime(LocalDateTime.now());
            itemEntity.setUpdateTime(LocalDateTime.now());
            itemList.add(itemEntity);
        }
        prescriptionItemDao.batchInsert(itemList);

        // 7. 更新诊断状态
        diagnosisDao.updateDiagnosisStatus(addForm.getDiagnosisId(), 2, operateUserId, operateUserName);

        // 8. 更新患者诊断状态
        visitPatientDao.updateDiagnosisStatus(addForm.getPatientId(), 3, operateUserId, operateUserName);

        return ResponseDTO.ok(prescriptionEntity.getPrescriptionId());
    }

    /**
     * 生成开单编号
     */
    private String generatePrescriptionNo() {
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String timeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HHmmss"));
        return "P" + dateStr + timeStr + String.format("%03d", (int) (Math.random() * 1000));
    }

    /**
     * 更新开单状态
     */
    @Transactional(rollbackFor = Exception.class)

    public ResponseDTO<String> updateStatus(Long prescriptionId, Integer prescriptionStatus, Long operateUserId, String operateUserName) {
        int result = prescriptionDao.updatePrescriptionStatus(prescriptionId, prescriptionStatus, operateUserId, operateUserName);
        if (result == 0) {
            return ResponseDTO.userErrorParam("开单记录不存在");
        }
        return ResponseDTO.ok();
    }

    /**
     * 删除开单记录
     */
    @Transactional(rollbackFor = Exception.class)

    public ResponseDTO<String> delete(Long prescriptionId, Long operateUserId, String operateUserName) {
        PrescriptionEntity entity = prescriptionDao.selectById(prescriptionId);
        if (entity == null) {
            return ResponseDTO.userErrorParam("开单记录不存在");
        }

        entity.setDeletedFlag(1);
        entity.setUpdateUserId(operateUserId);
        entity.setUpdateUserName(operateUserName);
        entity.setUpdateTime(LocalDateTime.now());
        prescriptionDao.updateById(entity);

        return ResponseDTO.ok();
    }
}

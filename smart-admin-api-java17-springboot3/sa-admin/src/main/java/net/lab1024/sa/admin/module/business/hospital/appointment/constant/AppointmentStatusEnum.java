package net.lab1024.sa.admin.module.business.hospital.appointment.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 预约状态枚举
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Getter
@AllArgsConstructor
public enum AppointmentStatusEnum implements BaseEnum {

    /**
     * 未到诊
     */
    NOT_ARRIVED(1, "未到诊"),

    /**
     * 已到诊
     */
    ARRIVED(2, "已到诊"),

    /**
     * 已完成
     */
    COMPLETED(3, "已完成"),

    /**
     * 爽约
     */
    NO_SHOW(4, "爽约"),

    /**
     * 已取消
     */
    CANCELLED(5, "已取消");

    private final Integer value;

    private final String desc;
}

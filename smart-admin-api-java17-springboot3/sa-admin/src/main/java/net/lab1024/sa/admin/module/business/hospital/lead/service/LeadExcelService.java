package net.lab1024.sa.admin.module.business.hospital.lead.service;

import cn.idev.excel.FastExcel;
import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.read.listener.ReadListener;
import cn.idev.excel.util.ListUtils;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.hospital.lead.constant.LeadQualityEnum;
import net.lab1024.sa.admin.module.business.hospital.lead.constant.LeadStatusEnum;
import net.lab1024.sa.admin.module.business.hospital.lead.dao.LeadDao;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.entity.LeadEntity;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.form.LeadImportForm;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.form.LeadQueryForm;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadExcelVO;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartExcelUtil;
import net.lab1024.sa.base.common.util.SmartResponseUtil;
import net.lab1024.sa.base.module.support.datatracer.constant.DataTracerTypeEnum;
import net.lab1024.sa.base.module.support.datatracer.service.DataTracerService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 线索Excel导入导出Service
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Slf4j
@Service
public class LeadExcelService {

    @Resource
    private LeadDao leadDao;

    @Resource
    private DataTracerService dataTracerService;

    /**
     * 导出线索数据
     */
    public void exportExcel(LeadQueryForm queryForm, HttpServletResponse response) throws IOException {
        // 获取导出数据
        queryForm.setDeletedFlag(Boolean.FALSE);
        List<LeadExcelVO> exportData = leadDao.selectExcelExportData(queryForm);

        if (exportData.isEmpty()) {
            SmartResponseUtil.write(response, ResponseDTO.userErrorParam("暂无数据"));
            return;
        }

        // 使用SmartAdmin标准导出方式
        SmartExcelUtil.exportExcel(response, "线索数据.xlsx", "线索数据", LeadExcelVO.class, exportData);
    }

    /**
     * 下载导入模板
     */
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        // 创建模板数据
        List<LeadImportForm> templateData = createTemplateData();

        // 使用SmartAdmin标准导出方式
        SmartExcelUtil.exportExcel(response, "线索导入模板.xlsx", "线索导入模板", LeadImportForm.class, templateData);
    }

    /**
     * 导入线索数据
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> importExcel(MultipartFile file) {
        try {
            List<LeadImportForm> importDataList = new ArrayList<>();
            List<String> errorMessages = new ArrayList<>();

            // 读取Excel数据
            FastExcel.read(file.getInputStream(), LeadImportForm.class, new ReadListener<LeadImportForm>() {
                @Override
                public void invoke(LeadImportForm data, AnalysisContext context) {
                    // 数据校验
                    String errorMsg = validateImportData(data, context.readRowHolder().getRowIndex() + 1);
                    if (StringUtils.isNotBlank(errorMsg)) {
                        errorMessages.add(errorMsg);
                    } else {
                        importDataList.add(data);
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    log.info("Excel读取完成，共读取{}条数据", importDataList.size());
                }
            }).sheet().doRead();

            // 如果有错误，返回错误信息
            if (!errorMessages.isEmpty()) {
                return ResponseDTO.userErrorParam("数据校验失败：" + String.join("; ", errorMessages));
            }

            // 转换并保存数据
            List<LeadEntity> leadEntityList = convertToEntityList(importDataList);
            for (LeadEntity leadEntity : leadEntityList) {
                leadDao.insert(leadEntity);
                // 记录数据变动
                dataTracerService.insert(leadEntity.getLeadId(), DataTracerTypeEnum.LEAD);
            }

            return ResponseDTO.ok("导入成功，共导入" + leadEntityList.size() + "条数据");

        } catch (IOException e) {
            log.error("导入Excel失败", e);
            return ResponseDTO.userErrorParam("文件读取失败");
        } catch (Exception e) {
            log.error("导入数据失败", e);
            return ResponseDTO.userErrorParam("导入失败：" + e.getMessage());
        }
    }

    /**
     * 创建模板数据
     */
    private List<LeadImportForm> createTemplateData() {
        List<LeadImportForm> templateData = ListUtils.newArrayList();

        // 添加示例数据
        LeadImportForm example = new LeadImportForm();
        example.setLeadSource("商务通");
        example.setCustomerName("张三");
        example.setCustomerPhone("13800138000");
        example.setCustomerWechat("zhangsan123");
        example.setGenderName("男");
        example.setAge(28);
        example.setSymptom("抑郁症状");
        example.setLeadQualityName("A级");
        example.setRemark("客户对价格比较敏感");

        templateData.add(example);
        return templateData;
    }

    /**
     * 校验导入数据
     */
    private String validateImportData(LeadImportForm data, int rowIndex) {
        List<String> errors = new ArrayList<>();

        // 必填字段校验
        if (StringUtils.isBlank(data.getLeadSource())) {
            errors.add("线索来源不能为空");
        } else {
            // 线索来源有效性校验
            String leadSourceValue = convertLeadSourceToValue(data.getLeadSource());
            if ("OTHER".equals(leadSourceValue) && !data.getLeadSource().trim().equals("其他")) {
                errors.add("线索来源无效，请使用：商务通、百度、抖音、快手、微信推广、朋友介绍、电话咨询、网络推广、其他");
            }
        }
        if (StringUtils.isBlank(data.getCustomerName())) {
            errors.add("客户姓名不能为空");
        }
        if (StringUtils.isBlank(data.getCustomerPhone())) {
            errors.add("客户电话不能为空");
        }

        // 手机号格式校验
        if (StringUtils.isNotBlank(data.getCustomerPhone()) && 
            !data.getCustomerPhone().matches("^1[3-9]\\d{9}$")) {
            errors.add("客户电话格式不正确");
        }

        // 手机号查重
        if (StringUtils.isNotBlank(data.getCustomerPhone())) {
            LeadEntity existLead = leadDao.selectByPhone(data.getCustomerPhone(), null);
            if (existLead != null) {
                errors.add("客户电话已存在");
            }
        }

        // 年龄校验
        if (data.getAge() != null && (data.getAge() < 1 || data.getAge() > 120)) {
            errors.add("年龄必须在1-120之间");
        }

        // 性别校验
        if (StringUtils.isNotBlank(data.getGenderName()) && 
            !data.getGenderName().matches("^(男|女)$")) {
            errors.add("性别只能是男或女");
        }

        // 线索质量校验
        if (StringUtils.isNotBlank(data.getLeadQualityName()) && 
            !data.getLeadQualityName().matches("^(A级|B级|C级)$")) {
            errors.add("线索质量只能是A级、B级或C级");
        }

        if (!errors.isEmpty()) {
            return "第" + rowIndex + "行：" + String.join(", ", errors);
        }

        return null;
    }

    /**
     * 转换为实体类列表
     */
    private List<LeadEntity> convertToEntityList(List<LeadImportForm> importDataList) {
        List<LeadEntity> entityList = new ArrayList<>();

        for (LeadImportForm importForm : importDataList) {
            LeadEntity entity = new LeadEntity();

            // 线索来源转换（中文名称转换为字典值）
            entity.setLeadSource(convertLeadSourceToValue(importForm.getLeadSource()));
            entity.setCustomerName(importForm.getCustomerName());
            entity.setCustomerPhone(importForm.getCustomerPhone());
            entity.setCustomerWechat(importForm.getCustomerWechat());
            entity.setAge(importForm.getAge());
            entity.setSymptom(importForm.getSymptom());
            entity.setRemark(importForm.getRemark());

            // 性别转换
            if ("男".equals(importForm.getGenderName())) {
                entity.setGender(1);
            } else if ("女".equals(importForm.getGenderName())) {
                entity.setGender(2);
            }

            // 线索质量转换
            if ("A级".equals(importForm.getLeadQualityName())) {
                entity.setLeadQuality(LeadQualityEnum.A_LEVEL.getValue());
            } else if ("B级".equals(importForm.getLeadQualityName())) {
                entity.setLeadQuality(LeadQualityEnum.B_LEVEL.getValue());
            } else if ("C级".equals(importForm.getLeadQualityName())) {
                entity.setLeadQuality(LeadQualityEnum.C_LEVEL.getValue());
            }

            // 设置默认值
            entity.setLeadStatus(LeadStatusEnum.NEW.getValue());
            entity.setDeletedFlag(Boolean.FALSE);
            entity.setCreateTime(LocalDateTime.now());
            entity.setUpdateTime(LocalDateTime.now());

            entityList.add(entity);
        }

        return entityList;
    }

    /**
     * 将线索来源中文名称转换为字典值
     */
    private String convertLeadSourceToValue(String leadSourceName) {
        if (StringUtils.isBlank(leadSourceName)) {
            return null;
        }

        // 根据中文名称映射到字典值
        switch (leadSourceName.trim()) {
            case "百度":
                return "BAIDU";
            case "抖音":
                return "DOUYIN";
            case "快手":
                return "KUAISHOU";
            case "商务通":
                return "SHANGWUTONG";
            case "微信推广":
                return "WECHAT";
            case "朋友介绍":
                return "FRIEND_REFERRAL";
            case "电话咨询":
                return "PHONE_INQUIRY";
            case "网络推广":
                return "ONLINE_PROMOTION";
            case "其他":
                return "OTHER";
            default:
                // 如果是字典值本身，直接返回
                if (leadSourceName.matches("^[A-Z_]+$")) {
                    return leadSourceName;
                }
                // 默认返回其他
                return "OTHER";
        }
    }
}

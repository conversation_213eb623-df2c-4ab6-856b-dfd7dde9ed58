package net.lab1024.sa.admin.module.business.hospital.visit.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 到诊患者跟进记录VO
 *
 * <AUTHOR>
 * @Date 2025-07-30 00:00:00
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@Schema(description = "到诊患者跟进记录VO")
public class VisitFollowVO {

    @Schema(description = "跟进记录ID")
    private Long followId;

    @Schema(description = "患者ID")
    private Long patientId;

    @Schema(description = "患者姓名")
    private String patientName;

    @Schema(description = "患者电话")
    private String patientPhone;

    @Schema(description = "关联诊断记录ID")
    private Long diagnosisId;

    @Schema(description = "关联开单记录ID")
    private Long prescriptionId;

    @Schema(description = "跟进类型：1-治疗回访，2-复诊提醒，3-满意度调查，4-术后跟进，5-用药跟进")
    private Integer followType;

    @Schema(description = "跟进类型名称")
    private String followTypeName;

    @Schema(description = "跟进方式：1-电话，2-微信，3-短信，4-邮件，5-上门")
    private Integer followMethod;

    @Schema(description = "跟进方式名称")
    private String followMethodName;

    @Schema(description = "跟进内容")
    private String followContent;

    @Schema(description = "跟进结果")
    private String followResult;

    @Schema(description = "患者反馈")
    private String patientFeedback;

    @Schema(description = "满意度评分（1-5分）")
    private Integer satisfactionScore;

    @Schema(description = "分配跟进人ID")
    private Long assignedUserId;

    @Schema(description = "分配跟进人姓名")
    private String assignedUserName;

    @Schema(description = "分配人ID")
    private Long assignedByUserId;

    @Schema(description = "分配人姓名")
    private String assignedByUserName;

    @Schema(description = "分配时间")
    private LocalDateTime assignedTime;

    @Schema(description = "计划跟进时间")
    private LocalDateTime plannedFollowTime;

    @Schema(description = "实际跟进时间")
    private LocalDateTime actualFollowTime;

    @Schema(description = "下次跟进时间")
    private LocalDateTime nextFollowTime;

    @Schema(description = "跟进状态：1-待跟进，2-跟进中，3-已完成，4-已取消，5-跟进失败")
    private Integer followStatus;

    @Schema(description = "跟进状态名称")
    private String followStatusName;

    @Schema(description = "优先级：1-高，2-中，3-低")
    private Integer priorityLevel;

    @Schema(description = "优先级名称")
    private String priorityLevelName;

    @Schema(description = "是否提醒：0-否，1-是")
    private Boolean reminderFlag;

    @Schema(description = "提醒时间")
    private LocalDateTime reminderTime;

    @Schema(description = "跟进人员姓名")
    private String followUserName;

    @Schema(description = "部门ID")
    private Long departmentId;

    @Schema(description = "部门名称")
    private String departmentName;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}

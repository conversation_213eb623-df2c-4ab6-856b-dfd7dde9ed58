package net.lab1024.sa.admin.module.business.hospital.care.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 关怀状态枚举
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Getter
@AllArgsConstructor
public enum CareStatusEnum implements BaseEnum {

    /**
     * 待执行
     */
    PENDING(1, "待执行"),

    /**
     * 执行中
     */
    IN_PROGRESS(2, "执行中"),

    /**
     * 已完成
     */
    COMPLETED(3, "已完成"),

    /**
     * 已取消
     */
    CANCELLED(4, "已取消");

    private final Integer value;

    private final String desc;

    /**
     * 根据值获取枚举
     */
    public static CareStatusEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (CareStatusEnum statusEnum : values()) {
            if (statusEnum.getValue().equals(value)) {
                return statusEnum;
            }
        }
        return null;
    }
}

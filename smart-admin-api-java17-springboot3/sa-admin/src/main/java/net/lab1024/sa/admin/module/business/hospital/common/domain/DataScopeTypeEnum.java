package net.lab1024.sa.admin.module.business.hospital.common.domain;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数据权限范围枚举
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Getter
@AllArgsConstructor
public enum DataScopeTypeEnum {

    /**
     * 全部数据权限
     */
    ALL(1, "全部数据权限"),

    /**
     * 本部门数据权限
     */
    DEPT(2, "本部门数据权限"),

    /**
     * 本部门及以下数据权限
     */
    DEPT_AND_SUB(3, "本部门及以下数据权限"),

    /**
     * 仅本人数据权限
     */
    SELF(4, "仅本人数据权限"),

    /**
     * 自定义数据权限
     */
    CUSTOM(5, "自定义数据权限");

    private final Integer value;

    private final String desc;

    public static DataScopeTypeEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (DataScopeTypeEnum item : DataScopeTypeEnum.values()) {
            if (item.getValue().equals(value)) {
                return item;
            }
        }
        return null;
    }
}

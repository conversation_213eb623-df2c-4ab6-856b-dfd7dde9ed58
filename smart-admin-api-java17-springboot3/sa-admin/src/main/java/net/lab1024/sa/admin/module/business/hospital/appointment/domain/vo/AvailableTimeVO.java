package net.lab1024.sa.admin.module.business.hospital.appointment.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalTime;

/**
 * 可预约时间视图对象
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class AvailableTimeVO {

    @Schema(description = "时间段")
    private LocalTime time;

    @Schema(description = "时间段显示文本")
    private String timeText;

    @Schema(description = "是否可预约")
    private Boolean available;

    @Schema(description = "已预约数量")
    private Integer appointedCount;

    @Schema(description = "最大预约数量")
    private Integer maxCount;

    @Schema(description = "排班ID")
    private Long scheduleId;
}

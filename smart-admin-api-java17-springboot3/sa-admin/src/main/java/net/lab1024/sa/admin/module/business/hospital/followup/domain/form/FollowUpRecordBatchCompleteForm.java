package net.lab1024.sa.admin.module.business.hospital.followup.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.domain.ValidateList;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;

/**
 * 批量完成回访记录表单
 *
 * <AUTHOR>
 * @Date 2025-07-30 00:00:00
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@Schema(description = "批量完成回访记录表单")
public class FollowUpRecordBatchCompleteForm {

    @Schema(description = "回访记录ID列表")
    @NotEmpty(message = "请选择要完成的回访记录")
    @Valid
    private ValidateList<Long> recordIdList;

    @Schema(description = "回访内容")
    @Size(max = 2000, message = "回访内容不能超过2000个字符")
    private String followUpContent;

    @Schema(description = "回访结果")
    @Size(max = 2000, message = "回访结果不能超过2000个字符")
    private String followUpResult;

    @Schema(description = "满意度评分（1-5分）")
    @Min(value = 1, message = "满意度评分最低为1分")
    @Max(value = 5, message = "满意度评分最高为5分")
    private Integer satisfactionScore;

    @Schema(description = "备注")
    @Size(max = 500, message = "备注不能超过500个字符")
    private String remark;
}

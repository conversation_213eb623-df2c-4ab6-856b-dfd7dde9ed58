package net.lab1024.sa.admin.module.business.hospital.lead.service;

import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.hospital.lead.dao.LeadFollowReminderDao;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.entity.LeadFollowReminderEntity;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadFollowReminderVO;
import net.lab1024.sa.admin.util.AdminRequestUtil;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 线索跟进提醒服务
 * 
 * <AUTHOR>
 * @Date 2025-07-22
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Slf4j
@Service
public class LeadFollowReminderService {

    @Autowired
    private LeadFollowReminderDao leadFollowReminderDao;

    /**
     * 创建跟进提醒
     * 
     * @param leadId 线索ID
     * @param followTime 跟进时间
     * @param reminderType 提醒方式
     * @param reminderMinutes 提前提醒分钟数
     * @return 创建结果
     */
    public ResponseDTO<String> createFollowReminder(Long leadId, LocalDateTime followTime,
                                                   Integer reminderType, Integer reminderMinutes) {
        try {
            LeadFollowReminderEntity reminderEntity = new LeadFollowReminderEntity();
            reminderEntity.setLeadId(leadId);
            reminderEntity.setReminderType(reminderType != null ? reminderType : 1); // 默认系统通知

            // 计算提醒时间（提前30分钟）
            int minutes = reminderMinutes != null ? reminderMinutes : 30;
            reminderEntity.setReminderTime(followTime.minusMinutes(minutes));
            reminderEntity.setReminderStatus(1); // 待提醒

            // 设置必填字段
            reminderEntity.setReminderTitle("跟进提醒");
            reminderEntity.setReminderContent("请及时跟进线索");

            // 设置分配员工信息（当前用户）
            Long currentUserId = AdminRequestUtil.getRequestUserId();
            reminderEntity.setAssignedEmployeeId(currentUserId);
            reminderEntity.setAssignedEmployeeName(AdminRequestUtil.getRequestUser().getActualName());

            // 设置其他字段
            reminderEntity.setIsSent(false);
            reminderEntity.setCreateUserId(currentUserId);
            reminderEntity.setDeletedFlag(false);

            leadFollowReminderDao.insert(reminderEntity);
            
            log.info("创建跟进提醒成功：leadId={}, followTime={}, reminderTime={}", 
                leadId, followTime, reminderEntity.getReminderTime());
            
            return ResponseDTO.ok("跟进提醒创建成功");
        } catch (Exception e) {
            log.error("创建跟进提醒失败：leadId={}", leadId, e);
            return ResponseDTO.userErrorParam("创建跟进提醒失败：" + e.getMessage());
        }
    }

    /**
     * 查询用户的跟进提醒列表
     * 
     * @param userId 用户ID
     * @return 提醒列表
     */
    public ResponseDTO<List<LeadFollowReminderVO>> getUserReminders(Long userId) {
        try {
            List<LeadFollowReminderVO> reminders = leadFollowReminderDao.selectByUserId(userId);
            return ResponseDTO.ok(reminders);
        } catch (Exception e) {
            log.error("查询用户跟进提醒失败：userId={}", userId, e);
            return ResponseDTO.userErrorParam("查询跟进提醒失败");
        }
    }

    /**
     * 查询待提醒的记录
     * 
     * @return 待提醒记录列表
     */
    public List<LeadFollowReminderVO> getPendingReminders() {
        return leadFollowReminderDao.selectPendingReminders(LocalDateTime.now());
    }

    /**
     * 标记提醒为已发送
     * 
     * @param reminderId 提醒ID
     */
    public void markReminderSent(Long reminderId) {
        try {
            leadFollowReminderDao.updateReminderStatus(reminderId, 2, LocalDateTime.now());
            log.info("标记提醒已发送：reminderId={}", reminderId);
        } catch (Exception e) {
            log.error("标记提醒状态失败：reminderId={}", reminderId, e);
        }
    }

    /**
     * 取消跟进提醒
     * 
     * @param reminderId 提醒ID
     * @return 取消结果
     */
    public ResponseDTO<String> cancelReminder(Long reminderId) {
        try {
            leadFollowReminderDao.updateReminderStatus(reminderId, 3, LocalDateTime.now());
            log.info("取消跟进提醒成功：reminderId={}", reminderId);
            return ResponseDTO.ok("提醒已取消");
        } catch (Exception e) {
            log.error("取消跟进提醒失败：reminderId={}", reminderId, e);
            return ResponseDTO.userErrorParam("取消提醒失败");
        }
    }

    /**
     * 完成跟进后自动取消相关提醒
     * 
     * @param leadId 线索ID
     */
    public void autoCompleteReminders(Long leadId) {
        try {
            leadFollowReminderDao.completeRemindersByLeadId(leadId, LocalDateTime.now());
            log.info("自动完成线索相关提醒：leadId={}", leadId);
        } catch (Exception e) {
            log.error("自动完成提醒失败：leadId={}", leadId, e);
        }
    }

    /**
     * 查询用户今日待跟进的线索
     *
     * @param userId 用户ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 待跟进线索列表
     */
    public List<LeadFollowReminderVO> getTodayFollowReminders(Long userId, LocalDateTime startTime, LocalDateTime endTime) {
        return leadFollowReminderDao.selectTodayFollowReminders(userId, startTime, endTime);
    }

    /**
     * 查询逾期未跟进的线索
     *
     * @param userId 用户ID
     * @param currentTime 当前时间
     * @return 逾期线索列表
     */
    public List<LeadFollowReminderVO> getOverdueReminders(Long userId, LocalDateTime currentTime) {
        return leadFollowReminderDao.selectOverdueReminders(userId, currentTime);
    }
}

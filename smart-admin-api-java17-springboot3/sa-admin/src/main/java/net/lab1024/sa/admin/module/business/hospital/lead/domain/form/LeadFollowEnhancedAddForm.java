package net.lab1024.sa.admin.module.business.hospital.lead.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 增强的线索跟进记录添加表单
 * 
 * <AUTHOR>
 * @Date 2025-07-22
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@Schema(description = "增强的线索跟进记录添加表单")
public class LeadFollowEnhancedAddForm {

    @Schema(description = "线索ID")
    @NotNull(message = "线索ID不能为空")
    private Long leadId;

    @Schema(description = "跟进类型：1电话 2微信 3面谈 4其他")
    @NotNull(message = "跟进类型不能为空")
    private Integer followType;

    @Schema(description = "跟进内容")
    @NotNull(message = "跟进内容不能为空")
    @Length(max = 1000, message = "跟进内容长度不能超过1000字符")
    private String followContent;

    @Schema(description = "跟进结果：1客户有预约意向 2需要再次跟进 3客户无意向/无效线索 4仅记录跟进")
    @NotNull(message = "跟进结果不能为空")
    private Integer followResult;

    // ==================== 后续操作相关字段 ====================

    @Schema(description = "下次跟进时间（当跟进结果为2时必填）")
    private LocalDateTime nextFollowTime;

    @Schema(description = "关闭原因（当跟进结果为3时必填）")
    @Length(max = 500, message = "关闭原因长度不能超过500字符")
    private String closeReason;

    // ==================== 预约相关字段 ====================

    @Schema(description = "是否立即创建预约（当跟进结果为1时使用）")
    private Boolean createAppointmentNow;

    @Schema(description = "预约项目ID（创建预约时必填）")
    private Long projectId;

    @Schema(description = "预约医生ID（创建预约时必填）")
    private Long doctorId;

    @Schema(description = "预约日期（创建预约时必填）")
    private LocalDate appointmentDate;

    @Schema(description = "预约时间（创建预约时必填）")
    private LocalTime appointmentTime;

    @Schema(description = "预约备注")
    @Length(max = 500, message = "预约备注长度不能超过500字符")
    private String appointmentRemark;

    // ==================== 跟进提醒相关字段 ====================

    @Schema(description = "是否设置跟进提醒（当跟进结果为2时使用）")
    private Boolean setFollowReminder;

    @Schema(description = "提醒方式：1系统通知 2短信 3邮件")
    private Integer reminderType;

    @Schema(description = "提前提醒时间（分钟）")
    private Integer reminderMinutes;
}

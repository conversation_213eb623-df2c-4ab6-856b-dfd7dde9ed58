package net.lab1024.sa.admin.module.business.hospital.lead.strategy;

import jakarta.annotation.Resource;
import net.lab1024.sa.admin.module.system.datascope.constant.DataScopeViewTypeEnum;
import net.lab1024.sa.admin.module.system.datascope.domain.DataScopeSqlConfig;
import net.lab1024.sa.admin.module.system.datascope.service.DataScopeViewService;
import net.lab1024.sa.admin.module.system.datascope.strategy.AbstractDataScopeStrategy;
import net.lab1024.sa.admin.module.system.employee.dao.EmployeeDao;
import net.lab1024.sa.admin.module.system.employee.domain.entity.EmployeeEntity;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 线索数据权限策略
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Component
public class LeadDataScopeStrategy extends AbstractDataScopeStrategy {

    @Resource
    private DataScopeViewService dataScopeViewService;

    @Resource
    private EmployeeDao employeeDao;

    @Override
    public String getCondition(DataScopeViewTypeEnum viewTypeEnum, Map<String, Object> paramMap, DataScopeSqlConfig sqlConfigDTO) {
        Long employeeId = (Long) paramMap.get("currentUserId");
        if (employeeId == null) {
            return "";
        }

        // 获取当前用户信息
        EmployeeEntity employeeEntity = employeeDao.selectById(employeeId);
        if (employeeEntity == null) {
            return "";
        }

        // 如果是超级管理员，可以查看所有线索
        if (employeeEntity.getAdministratorFlag()) {
            return "";
        }

        switch (viewTypeEnum) {
            case ME:
                // 仅本人：只能查看自己创建的线索或分配给自己的线索
                return String.format("(l.create_user_id = %d OR l.assigned_employee_id = %d)", employeeId, employeeId);

            case DEPARTMENT:
                // 本部门：可以查看本部门员工创建的线索或分配给本部门员工的线索
                List<Long> departmentEmployeeIds = dataScopeViewService.getCanViewEmployeeId(viewTypeEnum, employeeId);
                if (CollectionUtils.isEmpty(departmentEmployeeIds)) {
                    return String.format("(l.create_user_id = %d OR l.assigned_employee_id = %d)", employeeId, employeeId);
                }
                String departmentEmployeeIdsStr = StringUtils.join(departmentEmployeeIds, ",");
                return String.format("(l.create_user_id IN (%s) OR l.assigned_employee_id IN (%s))", 
                    departmentEmployeeIdsStr, departmentEmployeeIdsStr);

            case DEPARTMENT_AND_SUB:
                // 本部门及下属部门：可以查看本部门及下属部门员工创建的线索或分配给这些员工的线索
                List<Long> departmentAndSubEmployeeIds = dataScopeViewService.getCanViewEmployeeId(viewTypeEnum, employeeId);
                if (CollectionUtils.isEmpty(departmentAndSubEmployeeIds)) {
                    return String.format("(l.create_user_id = %d OR l.assigned_employee_id = %d)", employeeId, employeeId);
                }
                String departmentAndSubEmployeeIdsStr = StringUtils.join(departmentAndSubEmployeeIds, ",");
                return String.format("(l.create_user_id IN (%s) OR l.assigned_employee_id IN (%s))", 
                    departmentAndSubEmployeeIdsStr, departmentAndSubEmployeeIdsStr);

            case ALL:
                // 全部：可以查看所有线索（通常只有超级管理员才有此权限）
                return "";

            default:
                // 默认只能查看自己的线索
                return String.format("(l.create_user_id = %d OR l.assigned_employee_id = %d)", employeeId, employeeId);
        }
    }
}

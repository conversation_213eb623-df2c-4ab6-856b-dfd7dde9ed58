package net.lab1024.sa.admin.module.business.hospital.project.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.admin.module.business.hospital.project.constant.ProjectStatusEnum;
import net.lab1024.sa.base.common.swagger.SchemaEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 项目视图对象
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class ProjectVO {

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "项目分类")
    private String projectCategory;

    @Schema(description = "项目价格")
    private BigDecimal projectPrice;

    @Schema(description = "项目时长（分钟）")
    private Integer duration;

    @Schema(description = "项目状态：1-启用，2-禁用")
    @SchemaEnum(ProjectStatusEnum.class)
    private Integer projectStatus;

    @Schema(description = "项目状态名称")
    private String projectStatusName;

    @Schema(description = "项目描述")
    private String projectDescription;

    @Schema(description = "注意事项")
    private String precautions;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人姓名")
    private String createUserName;
}

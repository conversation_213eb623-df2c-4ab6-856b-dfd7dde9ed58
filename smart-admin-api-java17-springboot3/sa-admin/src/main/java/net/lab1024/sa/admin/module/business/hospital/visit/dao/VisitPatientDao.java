package net.lab1024.sa.admin.module.business.hospital.visit.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.entity.VisitPatientEntity;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.form.VisitPatientQueryForm;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.VisitPatientVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 到诊患者DAO
 *
 * <AUTHOR>
 * @Date 2025-07-29 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Mapper
@Component
public interface VisitPatientDao extends BaseMapper<VisitPatientEntity> {

    /**
     * 分页查询患者
     *
     * @param page 分页参数
     * @param queryForm 查询条件
     * @return 患者列表
     */
    List<VisitPatientVO> queryPage(Page<?> page, @Param("query") VisitPatientQueryForm queryForm);

    /**
     * 根据电话号码查询患者（用于查重）
     *
     * @param patientPhone 患者电话
     * @param excludePatientId 排除的患者ID（用于更新时排除自己）
     * @return 患者实体
     */
    VisitPatientEntity selectByPhone(@Param("patientPhone") String patientPhone, @Param("excludePatientId") Long excludePatientId);

    /**
     * 更新患者诊断状态
     *
     * @param patientId 患者ID
     * @param diagnosisStatus 诊断状态
     * @param updateUserId 更新人ID
     * @param updateUserName 更新人姓名
     * @return 更新行数
     */
    int updateDiagnosisStatus(@Param("patientId") Long patientId,
                              @Param("diagnosisStatus") Integer diagnosisStatus,
                              @Param("updateUserId") Long updateUserId,
                              @Param("updateUserName") String updateUserName);

    /**
     * 根据身份证号查询患者（用于查重）
     *
     * @param idCard 身份证号
     * @param excludePatientId 排除的患者ID（用于更新时排除自己）
     * @return 患者实体
     */
    VisitPatientEntity selectByIdCard(@Param("idCard") String idCard, @Param("excludePatientId") Long excludePatientId);

    /**
     * 根据患者编号查询患者（用于查重）
     *
     * @param patientNo 患者编号
     * @param excludePatientId 排除的患者ID（用于更新时排除自己）
     * @return 患者实体
     */
    VisitPatientEntity selectByPatientNo(@Param("patientNo") String patientNo, @Param("excludePatientId") Long excludePatientId);

    /**
     * 根据医生ID查询分配给该医生的患者列表
     *
     * @param doctorId 医生ID
     * @param visitStatus 到诊状态（可选）
     * @return 患者列表
     */
    List<VisitPatientVO> selectByDoctorId(@Param("doctorId") Long doctorId, @Param("visitStatus") Integer visitStatus);

    /**
     * 根据治疗助理ID查询分配给该助理的患者列表
     *
     * @param assistantId 治疗助理ID
     * @param visitStatus 到诊状态（可选）
     * @return 患者列表
     */
    List<VisitPatientVO> selectByAssistantId(@Param("assistantId") Long assistantId, @Param("visitStatus") Integer visitStatus);

    /**
     * 根据部门ID查询该部门的患者列表
     *
     * @param departmentId 部门ID
     * @param visitStatus 到诊状态（可选）
     * @return 患者列表
     */
    List<VisitPatientVO> selectByDepartmentId(@Param("departmentId") Long departmentId, @Param("visitStatus") Integer visitStatus);

    /**
     * 更新患者到诊状态
     *
     * @param patientId 患者ID
     * @param visitStatus 到诊状态
     * @param updateUserId 更新人ID
     * @param updateTime 更新时间
     * @return 更新行数
     */
    int updateVisitStatus(@Param("patientId") Long patientId, @Param("visitStatus") Integer visitStatus, 
                         @Param("updateUserId") Long updateUserId, @Param("updateTime") LocalDateTime updateTime);

    /**
     * 分配医生
     *
     * @param patientId 患者ID
     * @param doctorId 医生ID
     * @param doctorName 医生姓名
     * @param updateUserId 更新人ID
     * @param updateTime 更新时间
     * @return 更新行数
     */
    int assignDoctor(@Param("patientId") Long patientId, @Param("doctorId") Long doctorId, 
                    @Param("doctorName") String doctorName, @Param("updateUserId") Long updateUserId, 
                    @Param("updateTime") LocalDateTime updateTime);

    /**
     * 分配治疗助理
     *
     * @param patientId 患者ID
     * @param assistantId 治疗助理ID
     * @param assistantName 治疗助理姓名
     * @param updateUserId 更新人ID
     * @param updateTime 更新时间
     * @return 更新行数
     */
    int assignAssistant(@Param("patientId") Long patientId, @Param("assistantId") Long assistantId, 
                       @Param("assistantName") String assistantName, @Param("updateUserId") Long updateUserId, 
                       @Param("updateTime") LocalDateTime updateTime);

    /**
     * 获取患者统计数据
     *
     * @param departmentId 部门ID（可选）
     * @param doctorId 医生ID（可选）
     * @return 患者数量
     */
    Integer getPatientCount(@Param("departmentId") Long departmentId, @Param("doctorId") Long doctorId);

    /**
     * 根据状态统计患者数量
     *
     * @param visitStatus 到诊状态
     * @param departmentId 部门ID（可选）
     * @param doctorId 医生ID（可选）
     * @return 患者数量
     */
    Integer getPatientCountByStatus(@Param("visitStatus") Integer visitStatus, 
                                   @Param("departmentId") Long departmentId, 
                                   @Param("doctorId") Long doctorId);

    /**
     * 获取患者详情（包含统计信息）
     *
     * @param patientId 患者ID
     * @return 患者详情
     */
    VisitPatientVO getPatientDetail(@Param("patientId") Long patientId);

    /**
     * 根据预约ID查询到诊患者记录
     *
     * @param appointmentId 预约ID
     * @return 到诊患者实体
     */
    VisitPatientEntity selectByAppointmentId(@Param("appointmentId") Long appointmentId);

    /**
     * 根据标签查询患者
     *
     * @param patientTags 患者标签
     * @return 患者列表
     */
    List<VisitPatientVO> selectByTags(@Param("patientTags") String patientTags);

    /**
     * 获取最近登记的患者
     *
     * @param limit 限制数量
     * @param departmentId 部门ID（可选）
     * @param doctorId 医生ID（可选）
     * @return 患者列表
     */
    List<VisitPatientVO> getRecentPatients(@Param("limit") Integer limit, 
                                          @Param("departmentId") Long departmentId, 
                                          @Param("doctorId") Long doctorId);

    /**
     * 获取待处理患者列表（需要跟进、诊断等）
     *
     * @param assignedUserId 分配用户ID
     * @param limit 限制数量
     * @return 患者列表
     */
    List<VisitPatientVO> getPendingPatients(@Param("assignedUserId") Long assignedUserId, @Param("limit") Integer limit);

    /**
     * 批量更新患者删除状态
     *
     * @param patientIds 患者ID列表
     * @param deletedFlag 删除标志
     * @param updateUserId 更新人ID
     * @param updateTime 更新时间
     * @return 更新行数
     */
    int batchUpdateDeleted(@Param("patientIds") List<Long> patientIds, @Param("deletedFlag") Boolean deletedFlag,
                          @Param("updateUserId") Long updateUserId, @Param("updateTime") LocalDateTime updateTime);
}

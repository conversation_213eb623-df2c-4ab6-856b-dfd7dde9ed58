package net.lab1024.sa.admin.module.business.hospital.lead.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 线索状态变更历史VO
 * 
 * <AUTHOR>
 * @Date 2025-07-22
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@Schema(description = "线索状态变更历史")
public class LeadStatusHistoryVO {

    @Schema(description = "历史记录ID")
    private Long historyId;

    @Schema(description = "线索ID")
    private Long leadId;

    @Schema(description = "原状态")
    private Integer fromStatus;

    @Schema(description = "原状态名称")
    private String fromStatusName;

    @Schema(description = "新状态")
    private Integer toStatus;

    @Schema(description = "新状态名称")
    private String toStatusName;

    @Schema(description = "触发类型")
    private String triggerType;

    @Schema(description = "触发类型名称")
    private String triggerTypeName;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "状态停留时长（分钟）")
    private Long durationMinutes;

    @Schema(description = "状态停留时长描述")
    private String durationDesc;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人")
    private String createName;
}

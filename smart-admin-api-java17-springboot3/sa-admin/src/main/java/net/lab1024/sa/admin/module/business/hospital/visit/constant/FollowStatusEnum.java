package net.lab1024.sa.admin.module.business.hospital.visit.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 跟进状态枚举
 *
 * <AUTHOR>
 * @Date 2025-07-29 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Getter
@AllArgsConstructor
public enum FollowStatusEnum implements BaseEnum {

    /**
     * 待跟进
     */
    PENDING(1, "待跟进"),

    /**
     * 跟进中
     */
    IN_PROGRESS(2, "跟进中"),

    /**
     * 已完成
     */
    COMPLETED(3, "已完成"),

    /**
     * 已取消
     */
    CANCELLED(4, "已取消"),

    /**
     * 跟进失败
     */
    FAILED(5, "跟进失败");

    private final Integer value;

    private final String desc;
}

package net.lab1024.sa.admin.module.business.hospital.visit.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 开单记录VO
 *
 * <AUTHOR>
 * @Date 2025-07-29
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@Schema(description = "开单记录VO")
public class PrescriptionVO {

    @Schema(description = "开单ID")
    private Long prescriptionId;

    @Schema(description = "诊断ID")
    private Long diagnosisId;

    @Schema(description = "患者ID")
    private Long patientId;

    @Schema(description = "患者编号")
    private String patientNo;

    @Schema(description = "患者姓名")
    private String patientName;

    @Schema(description = "开单编号")
    private String prescriptionNo;

    @Schema(description = "总金额")
    private BigDecimal totalAmount;

    @Schema(description = "开单说明")
    private String prescriptionNote;

    @Schema(description = "开单状态：1-已开单，2-已收费，3-已完成")
    private Integer prescriptionStatus;

    @Schema(description = "开单状态名称")
    private String prescriptionStatusName;

    @Schema(description = "项目明细列表")
    private List<PrescriptionItemVO> itemList;

    @Schema(description = "创建人姓名")
    private String createUserName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人姓名")
    private String updateUserName;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}

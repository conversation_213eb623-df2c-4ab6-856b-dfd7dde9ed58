package net.lab1024.sa.admin.module.business.hospital.lead.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import net.lab1024.sa.admin.module.business.hospital.lead.constant.FollowResultEnum;
import net.lab1024.sa.admin.module.business.hospital.lead.constant.FollowTypeEnum;
import net.lab1024.sa.base.common.swagger.SchemaEnum;
import net.lab1024.sa.base.common.validator.enumeration.CheckEnum;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDateTime;

/**
 * 线索跟进添加表单
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class LeadFollowAddForm {

    @Schema(description = "线索ID")
    @NotNull(message = "线索ID不能为空")
    private Long leadId;

    @Schema(description = "跟进方式：1-电话，2-微信，3-面谈，4-其他")
    @SchemaEnum(FollowTypeEnum.class)
    @CheckEnum(message = "跟进方式错误", value = FollowTypeEnum.class, required = true)
    @NotNull(message = "跟进方式不能为空")
    private Integer followType;

    @Schema(description = "跟进内容")
    @NotBlank(message = "跟进内容不能为空")
    @Length(max = 1000, message = "跟进内容最多1000字符")
    private String followContent;

    @Schema(description = "跟进结果：1-有意向，2-无意向，3-需再次跟进")
    @SchemaEnum(FollowResultEnum.class)
    @CheckEnum(message = "跟进结果错误", value = FollowResultEnum.class, required = false)
    private Integer followResult;

    @Schema(description = "下次跟进时间")
    private LocalDateTime nextFollowTime;
}

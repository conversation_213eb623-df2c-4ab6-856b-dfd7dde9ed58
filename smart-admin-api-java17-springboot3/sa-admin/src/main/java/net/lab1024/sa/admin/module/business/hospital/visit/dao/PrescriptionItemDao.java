package net.lab1024.sa.admin.module.business.hospital.visit.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.entity.PrescriptionItemEntity;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.PrescriptionItemVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 开单项目明细Dao
 *
 * <AUTHOR>
 * @Date 2025-07-29
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Mapper
public interface PrescriptionItemDao extends BaseMapper<PrescriptionItemEntity> {

    /**
     * 根据开单ID查询项目明细
     *
     * @param prescriptionId 开单ID
     * @return 项目明细列表
     */
    List<PrescriptionItemVO> selectByPrescriptionId(@Param("prescriptionId") Long prescriptionId);

    /**
     * 批量插入项目明细
     *
     * @param itemList 项目明细列表
     * @return 插入行数
     */
    int batchInsert(@Param("itemList") List<PrescriptionItemEntity> itemList);

    /**
     * 根据开单ID删除项目明细
     *
     * @param prescriptionId 开单ID
     * @return 删除行数
     */
    int deleteByPrescriptionId(@Param("prescriptionId") Long prescriptionId);
}

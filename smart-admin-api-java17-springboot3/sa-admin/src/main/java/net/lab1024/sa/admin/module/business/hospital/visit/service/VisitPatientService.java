package net.lab1024.sa.admin.module.business.hospital.visit.service;

import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.hospital.visit.dao.VisitPatientDao;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.entity.VisitPatientEntity;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.form.VisitPatientAddForm;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.form.VisitPatientQueryForm;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.form.VisitPatientUpdateForm;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.VisitPatientVO;
import net.lab1024.sa.admin.module.system.employee.dao.EmployeeDao;
import net.lab1024.sa.admin.module.system.employee.domain.entity.EmployeeEntity;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.admin.util.AdminRequestUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

// 添加预约相关的导入
import net.lab1024.sa.admin.module.business.hospital.appointment.dao.AppointmentDao;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.entity.AppointmentEntity;

/**
 * 到诊患者管理服务
 *
 * <AUTHOR>
 * @Date 2025-07-29 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Slf4j
@Service
public class VisitPatientService {

    @Resource
    private VisitPatientDao visitPatientDao;

    @Resource
    private EmployeeDao employeeDao;

    @Resource
    private VisitFollowService visitFollowService;

    @Resource
    private AppointmentDao appointmentDao;

    @Resource
    private VisitPermissionService visitPermissionService;



    /**
     * 分页查询患者
     */
    public ResponseDTO<PageResult<VisitPatientVO>> queryPage(VisitPatientQueryForm queryForm) {
        queryForm.setDeletedFlag(Boolean.FALSE);

        // 应用数据权限过滤
        Long currentUserId = AdminRequestUtil.getRequestUserId();
        visitPermissionService.applyDataScopeFilter(queryForm, currentUserId);

        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<VisitPatientVO> patientList = visitPatientDao.queryPage(page, queryForm);
        PageResult<VisitPatientVO> pageResult = SmartPageUtil.convert2PageResult(page, patientList);
        return ResponseDTO.ok(pageResult);
    }

    /**
     * 添加患者
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> add(VisitPatientAddForm addForm) {
        // 校验患者电话是否重复
        VisitPatientEntity existPatient = visitPatientDao.selectByPhone(addForm.getPatientPhone(), null);
        if (existPatient != null) {
            return ResponseDTO.userErrorParam("患者电话已存在");
        }

        // 校验身份证号是否重复
        if (StringUtils.isNotBlank(addForm.getIdCard())) {
            VisitPatientEntity existIdCardPatient = visitPatientDao.selectByIdCard(addForm.getIdCard(), null);
            if (existIdCardPatient != null) {
                return ResponseDTO.userErrorParam("身份证号已存在");
            }
        }

        // 生成患者编号
        String patientNo = generatePatientNo();
        
        // 校验患者编号是否重复
        VisitPatientEntity existPatientNo = visitPatientDao.selectByPatientNo(patientNo, null);
        if (existPatientNo != null) {
            return ResponseDTO.userErrorParam("患者编号生成失败，请重试");
        }

        // 获取当前用户信息
        Long currentUserId = AdminRequestUtil.getRequestUserId();
        EmployeeEntity currentEmployee = employeeDao.selectById(currentUserId);

        VisitPatientEntity patientEntity = SmartBeanUtil.copy(addForm, VisitPatientEntity.class);
        patientEntity.setPatientNo(patientNo);
        patientEntity.setCreateUserId(currentUserId);
        patientEntity.setCreateTime(LocalDateTime.now());
        patientEntity.setDeletedFlag(Boolean.FALSE);
        
        // 设置默认部门信息
        if (currentEmployee != null) {
            patientEntity.setDepartmentId(currentEmployee.getDepartmentId());
            // 这里需要查询部门名称，简化处理
            patientEntity.setDepartmentName("默认部门");
        }

        // 设置默认到诊状态
        if (patientEntity.getVisitStatus() == null) {
            patientEntity.setVisitStatus(1); // 默认未到诊
        }

        // 设置登记时间
        if (patientEntity.getRegistrationTime() == null) {
            patientEntity.setRegistrationTime(LocalDateTime.now());
        }

        visitPatientDao.insert(patientEntity);

        return ResponseDTO.ok();
    }

    /**
     * 更新患者
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> update(VisitPatientUpdateForm updateForm) {
        VisitPatientEntity existPatient = visitPatientDao.selectById(updateForm.getPatientId());
        if (existPatient == null || existPatient.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("患者不存在");
        }

        // 检查权限
        Long currentUserId = AdminRequestUtil.getRequestUserId();
        if (!visitPermissionService.hasPermissionToViewPatient(updateForm.getPatientId(), currentUserId)) {
            return ResponseDTO.userErrorParam("无权限操作此患者");
        }

        // 校验患者电话是否重复
        VisitPatientEntity duplicatePhonePatient = visitPatientDao.selectByPhone(updateForm.getPatientPhone(), updateForm.getPatientId());
        if (duplicatePhonePatient != null) {
            return ResponseDTO.userErrorParam("患者电话已存在");
        }

        // 校验身份证号是否重复
        if (StringUtils.isNotBlank(updateForm.getIdCard())) {
            VisitPatientEntity duplicateIdCardPatient = visitPatientDao.selectByIdCard(updateForm.getIdCard(), updateForm.getPatientId());
            if (duplicateIdCardPatient != null) {
                return ResponseDTO.userErrorParam("身份证号已存在");
            }
        }

        VisitPatientEntity patientEntity = SmartBeanUtil.copy(updateForm, VisitPatientEntity.class);
        patientEntity.setUpdateUserId(currentUserId);
        patientEntity.setUpdateTime(LocalDateTime.now());

        visitPatientDao.updateById(patientEntity);

        return ResponseDTO.ok();
    }

    /**
     * 删除患者
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> delete(Long patientId) {
        VisitPatientEntity patientEntity = visitPatientDao.selectById(patientId);
        if (patientEntity == null || patientEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("患者不存在");
        }

        // 检查权限
        Long currentUserId = AdminRequestUtil.getRequestUserId();
        if (!visitPermissionService.hasPermissionToViewPatient(patientId, currentUserId)) {
            return ResponseDTO.userErrorParam("无权限操作此患者");
        }

        // 检查是否有关联的诊断记录、开单记录等
        // 如果有关联记录，不允许删除
        try {
            // 这里可以添加具体的关联检查逻辑
            // 例如：检查是否有未完成的诊断记录、开单记录等
            // 暂时允许删除，后续可以根据业务需求完善
            log.info("删除患者前检查关联记录，患者ID: {}", patientId);
        } catch (Exception e) {
            log.warn("检查患者关联记录时出现异常", e);
        }

        patientEntity.setDeletedFlag(Boolean.TRUE);
        patientEntity.setUpdateUserId(currentUserId);
        patientEntity.setUpdateTime(LocalDateTime.now());
        visitPatientDao.updateById(patientEntity);

        return ResponseDTO.ok();
    }

    /**
     * 获取患者详情
     */
    public ResponseDTO<VisitPatientVO> getDetail(Long patientId) {
        // 检查权限
        Long currentUserId = AdminRequestUtil.getRequestUserId();
        if (!visitPermissionService.hasPermissionToViewPatient(patientId, currentUserId)) {
            return ResponseDTO.userErrorParam("无权限查看此患者");
        }

        VisitPatientVO patientVO = visitPatientDao.getPatientDetail(patientId);
        if (patientVO == null) {
            return ResponseDTO.userErrorParam("患者不存在");
        }

        return ResponseDTO.ok(patientVO);
    }

    /**
     * 更新患者到诊状态
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> updateVisitStatus(Long patientId, Integer visitStatus) {
        VisitPatientEntity patientEntity = visitPatientDao.selectById(patientId);
        if (patientEntity == null || patientEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("患者不存在");
        }

        // 检查权限
        Long currentUserId = AdminRequestUtil.getRequestUserId();
        if (!visitPermissionService.hasPermissionToViewPatient(patientId, currentUserId)) {
            return ResponseDTO.userErrorParam("无权限操作此患者");
        }

        // 更新状态
        int result = visitPatientDao.updateVisitStatus(patientId, visitStatus, currentUserId, LocalDateTime.now());
        if (result > 0) {
            return ResponseDTO.ok();
        } else {
            return ResponseDTO.userErrorParam("更新状态失败");
        }
    }

    /**
     * 分配医生
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> assignDoctor(Long patientId, Long doctorId) {
        VisitPatientEntity patientEntity = visitPatientDao.selectById(patientId);
        if (patientEntity == null || patientEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("患者不存在");
        }

        // 检查权限
        Long currentUserId = AdminRequestUtil.getRequestUserId();
        if (!visitPermissionService.hasPermissionToAssignPatient(doctorId, currentUserId)) {
            return ResponseDTO.userErrorParam("无权限分配患者给此医生");
        }

        // 获取医生信息
        EmployeeEntity doctor = employeeDao.selectById(doctorId);
        if (doctor == null || doctor.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("医生不存在");
        }

        // 分配医生
        int result = visitPatientDao.assignDoctor(patientId, doctorId, doctor.getActualName(), currentUserId, LocalDateTime.now());
        if (result > 0) {
            return ResponseDTO.ok();
        } else {
            return ResponseDTO.userErrorParam("分配医生失败");
        }
    }

    /**
     * 分配治疗助理
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> assignAssistant(Long patientId, Long assistantId) {
        VisitPatientEntity patientEntity = visitPatientDao.selectById(patientId);
        if (patientEntity == null || patientEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("患者不存在");
        }

        // 检查权限
        Long currentUserId = AdminRequestUtil.getRequestUserId();
        if (!visitPermissionService.hasPermissionToAssignPatient(assistantId, currentUserId)) {
            return ResponseDTO.userErrorParam("无权限分配患者给此助理");
        }

        // 获取助理信息
        EmployeeEntity assistant = employeeDao.selectById(assistantId);
        if (assistant == null || assistant.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("治疗助理不存在");
        }

        // 分配助理
        int result = visitPatientDao.assignAssistant(patientId, assistantId, assistant.getActualName(), currentUserId, LocalDateTime.now());
        if (result > 0) {
            log.info("成功为患者{}分配助理{}", patientId, assistantId);
            return ResponseDTO.ok();
        } else {
            return ResponseDTO.userErrorParam("分配治疗助理失败");
        }
    }

    /**
     * 生成患者编号
     */
    private String generatePatientNo() {
        // 简化实现：使用时间戳 + 随机数
        // 实际项目中可能需要更复杂的编号规则
        long timestamp = System.currentTimeMillis();
        int random = (int) (Math.random() * 1000);
        return "P" + timestamp + String.format("%03d", random);
    }

    /**
     * 批量删除患者
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> batchDelete(List<Long> patientIds) {
        if (patientIds == null || patientIds.isEmpty()) {
            return ResponseDTO.userErrorParam("请选择要删除的患者");
        }

        Long currentUserId = AdminRequestUtil.getRequestUserId();

        // 检查权限（简化处理，实际需要逐个检查）
        for (Long patientId : patientIds) {
            if (!visitPermissionService.hasPermissionToViewPatient(patientId, currentUserId)) {
                return ResponseDTO.userErrorParam("无权限删除部分患者");
            }
        }

        // 批量更新删除状态
        int result = visitPatientDao.batchUpdateDeleted(patientIds, Boolean.TRUE, currentUserId, LocalDateTime.now());
        if (result > 0) {
            return ResponseDTO.ok();
        } else {
            return ResponseDTO.userErrorParam("批量删除失败");
        }
    }

    /**
     * 获取患者统计信息
     */
    public ResponseDTO<Object> getStatistics() {
        Long currentUserId = AdminRequestUtil.getRequestUserId();

        try {
            // 根据用户权限获取统计数据
            VisitPatientQueryForm queryForm = new VisitPatientQueryForm();
            queryForm.setDeletedFlag(Boolean.FALSE);

            // 应用数据权限过滤
            visitPermissionService.applyDataScopeFilter(queryForm, currentUserId);

            // 获取基础统计数据
            Integer totalPatients = visitPatientDao.getPatientCount(null, null);
            Integer todayPatients = visitPatientDao.getPatientCountByStatus(2, null, null); // 今日到诊
            Integer inTreatment = visitPatientDao.getPatientCountByStatus(3, null, null); // 诊疗中
            Integer completed = visitPatientDao.getPatientCountByStatus(4, null, null); // 已完成

            // 构建统计结果
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalPatients", totalPatients != null ? totalPatients : 0);
            statistics.put("todayPatients", todayPatients != null ? todayPatients : 0);
            statistics.put("inTreatment", inTreatment != null ? inTreatment : 0);
            statistics.put("completed", completed != null ? completed : 0);

            return ResponseDTO.ok(statistics);
        } catch (Exception e) {
            log.error("获取患者统计信息失败", e);
            return ResponseDTO.userErrorParam("获取统计信息失败");
        }
    }

    /**
     * 从预约创建到诊患者记录
     * 当预约确认到诊时自动调用此方法
     *
     * @param appointmentId 预约ID
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> createFromAppointment(Long appointmentId) {
        try {
            // 检查是否已存在该预约的到诊记录
            VisitPatientEntity existingPatient = visitPatientDao.selectByAppointmentId(appointmentId);
            if (existingPatient != null) {
                log.info("预约ID {}已存在对应的到诊患者记录，跳过创建", appointmentId);
                return ResponseDTO.ok("患者记录已存在");
            }

            // 获取预约信息
            AppointmentEntity appointment = appointmentDao.selectById(appointmentId);
            if (appointment == null || appointment.getDeletedFlag()) {
                return ResponseDTO.userErrorParam("预约记录不存在");
            }

            // 创建到诊患者实体
            VisitPatientEntity visitPatient = new VisitPatientEntity();

            // 基本信息映射
            visitPatient.setPatientName(appointment.getCustomerName());
            visitPatient.setPatientPhone(appointment.getCustomerPhone());
            visitPatient.setGender(appointment.getGender());
            visitPatient.setAge(appointment.getAge());
            visitPatient.setPatientWechat(appointment.getWechat());

            // 到诊相关信息
            visitPatient.setVisitStatus(2); // 已到诊
            visitPatient.setVisitDate(appointment.getAppointmentDate());
            visitPatient.setRegistrationTime(LocalDateTime.now()); // 使用当前时间作为登记时间

            // 分配信息 - 优先使用预约中的医生信息，如果医生ID为0或null则表示未指定医生
            if (appointment.getDoctorId() != null && appointment.getDoctorId() > 0L) {
                visitPatient.setAssignedDoctorId(appointment.getDoctorId());
                visitPatient.setAssignedDoctorName(appointment.getDoctorName());
            } else {
                visitPatient.setAssignedDoctorId(0L);
                visitPatient.setAssignedDoctorName("未指定医生");
            }
            // 注意：VisitPatientEntity中没有assignedEmployeeId字段，使用assignedAssistantId
            visitPatient.setAssignedAssistantId(appointment.getAssignedEmployeeId());

            // 关联预约ID
            visitPatient.setAppointmentId(appointmentId);

            // 生成患者编号
            String patientNo = generatePatientNo();
            visitPatient.setPatientNo(patientNo);

            // 设置默认值
            visitPatient.setPatientLevel(1); // 1-普通患者
            visitPatient.setPatientSource("预约转入");

            // 设置创建信息
            visitPatient.setCreateUserId(AdminRequestUtil.getRequestUserId());
            visitPatient.setCreateTime(LocalDateTime.now());
            visitPatient.setDeletedFlag(false);

            // 保存到数据库
            visitPatientDao.insert(visitPatient);

            log.info("成功从预约ID {}创建到诊患者记录，患者编号：{}", appointmentId, patientNo);
            return ResponseDTO.ok("成功创建到诊患者记录");

        } catch (Exception e) {
            log.error("从预约创建到诊患者记录失败，预约ID：{}", appointmentId, e);
            return ResponseDTO.userErrorParam("创建到诊患者记录失败：" + e.getMessage());
        }
    }

    /**
     * 更新患者诊断状态并自动创建跟进计划
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> updateDiagnosisStatusWithFollowUp(Long patientId, Integer diagnosisStatus,
                                                                Long operateUserId, String operateUserName) {
        try {
            // 1. 更新患者诊断状态
            VisitPatientEntity patient = visitPatientDao.selectById(patientId);
            if (patient == null || patient.getDeletedFlag()) {
                return ResponseDTO.userErrorParam("患者不存在");
            }

            patient.setDiagnosisStatus(diagnosisStatus);
            patient.setUpdateUserId(operateUserId);
            patient.setUpdateTime(LocalDateTime.now());

            // 如果诊断状态为已完成(7)，设置完成时间
            if (diagnosisStatus == 7) {
                patient.setCompletionTime(LocalDateTime.now());
                patient.setVisitStatus(4); // 设置到诊状态为已完成
            }

            visitPatientDao.updateById(patient);

            // 2. 如果诊断状态为已完成，自动创建跟进计划
            if (diagnosisStatus == 7) {
                visitFollowService.createFollowUpPlan(patientId, operateUserId, operateUserName);
                log.info("患者 {} 诊断完成，已自动创建跟进计划", patient.getPatientName());
            }

            return ResponseDTO.ok("患者状态更新成功");
        } catch (Exception e) {
            log.error("更新患者诊断状态失败，患者ID：{}", patientId, e);
            return ResponseDTO.userErrorParam("更新患者状态失败");
        }
    }

    /**
     * 手动创建患者跟进计划
     */
    public ResponseDTO<String> createPatientFollowUpPlan(Long patientId, Long operateUserId, String operateUserName) {
        return visitFollowService.createFollowUpPlan(patientId, operateUserId, operateUserName);
    }

    /**
     * 根据预约ID更新患者记录中的医生信息
     * 当预约分配医生后，同步更新对应的患者记录
     *
     * @param appointmentId 预约ID
     * @param doctorId 医生ID
     * @param doctorName 医生姓名
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> updateDoctorByAppointmentId(Long appointmentId, Long doctorId, String doctorName) {
        try {
            // 查找对应的患者记录
            VisitPatientEntity patient = visitPatientDao.selectByAppointmentId(appointmentId);
            if (patient == null) {
                log.warn("未找到预约ID {}对应的患者记录", appointmentId);
                return ResponseDTO.userErrorParam("未找到对应的患者记录");
            }

            // 更新医生信息
            patient.setAssignedDoctorId(doctorId);
            patient.setAssignedDoctorName(doctorName);
            patient.setUpdateTime(LocalDateTime.now());
            patient.setUpdateUserId(AdminRequestUtil.getRequestUserId());

            int result = visitPatientDao.updateById(patient);
            if (result > 0) {
                log.info("成功更新患者记录中的医生信息: patientId={}, doctorId={}, doctorName={}",
                        patient.getPatientId(), doctorId, doctorName);
                return ResponseDTO.ok("医生信息更新成功");
            } else {
                return ResponseDTO.userErrorParam("医生信息更新失败");
            }
        } catch (Exception e) {
            log.error("更新患者记录中的医生信息异常: appointmentId={}, doctorId={}, doctorName={}",
                    appointmentId, doctorId, doctorName, e);
            return ResponseDTO.userErrorParam("医生信息更新失败");
        }
    }
}

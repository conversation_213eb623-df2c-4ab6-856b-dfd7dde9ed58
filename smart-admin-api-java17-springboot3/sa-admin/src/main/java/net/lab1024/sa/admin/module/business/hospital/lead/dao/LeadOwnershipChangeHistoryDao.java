package net.lab1024.sa.admin.module.business.hospital.lead.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.entity.LeadOwnershipChangeHistoryEntity;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadOwnershipChangeHistoryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 线索归属变更历史DAO
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Mapper
@Component
public interface LeadOwnershipChangeHistoryDao extends BaseMapper<LeadOwnershipChangeHistoryEntity> {

    /**
     * 根据线索ID查询变更历史
     *
     * @param leadId 线索ID
     * @return 变更历史列表
     */
    List<LeadOwnershipChangeHistoryVO> selectByLeadId(@Param("leadId") Long leadId);

    /**
     * 分页查询变更历史
     *
     * @param page 分页参数
     * @param leadId 线索ID
     * @return 变更历史列表
     */
    List<LeadOwnershipChangeHistoryVO> queryPage(Page page, @Param("leadId") Long leadId);

    /**
     * 统计某个线索的变更次数
     *
     * @param leadId 线索ID
     * @return 变更次数
     */
    Integer countByLeadId(@Param("leadId") Long leadId);
}

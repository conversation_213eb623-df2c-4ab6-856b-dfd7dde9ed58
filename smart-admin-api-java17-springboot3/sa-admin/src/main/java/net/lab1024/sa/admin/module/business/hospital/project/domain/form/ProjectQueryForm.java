package net.lab1024.sa.admin.module.business.hospital.project.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.admin.module.business.hospital.project.constant.ProjectStatusEnum;
import net.lab1024.sa.base.common.domain.PageParam;
import net.lab1024.sa.base.common.swagger.SchemaEnum;
import net.lab1024.sa.base.common.validator.enumeration.CheckEnum;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDateTime;

/**
 * 项目查询表单
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class ProjectQueryForm extends PageParam {

    @Schema(description = "项目名称")
    @Length(max = 100, message = "项目名称最多100字符")
    private String projectName;

    @Schema(description = "项目分类")
    @Length(max = 50, message = "项目分类最多50字符")
    private String projectCategory;

    @Schema(description = "项目状态：1-启用，2-禁用")
    @SchemaEnum(ProjectStatusEnum.class)
    @CheckEnum(message = "项目状态错误", value = ProjectStatusEnum.class, required = false)
    private Integer projectStatus;

    @Schema(description = "创建时间-开始")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间-结束")
    private LocalDateTime createTimeEnd;

    @Schema(description = "搜索关键词")
    @Length(max = 50, message = "搜索关键词最多50字符")
    private String searchWord;

    @Schema(hidden = true)
    private Boolean deletedFlag;
}

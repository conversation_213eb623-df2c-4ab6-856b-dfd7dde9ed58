package net.lab1024.sa.admin.module.business.hospital.visit.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * 到诊患者跟进记录新增表单
 *
 * <AUTHOR>
 * @Date 2025-07-30 00:00:00
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@Schema(description = "到诊患者跟进记录新增表单")
public class VisitFollowAddForm {

    @Schema(description = "患者ID")
    @NotNull(message = "患者ID不能为空")
    private Long patientId;

    @Schema(description = "跟进类型：1-治疗回访，2-复诊提醒，3-满意度调查，4-术后跟进，5-用药跟进")
    @NotNull(message = "跟进类型不能为空")
    private Integer followType;

    @Schema(description = "跟进方式：1-电话，2-微信，3-短信，4-邮件，5-上门")
    @NotNull(message = "跟进方式不能为空")
    private Integer followMethod;

    @Schema(description = "计划跟进时间")
    @NotNull(message = "计划跟进时间不能为空")
    private LocalDateTime plannedFollowTime;

    @Schema(description = "跟进内容")
    @NotNull(message = "跟进内容不能为空")
    private String followContent;

    @Schema(description = "跟进结果")
    private String followResult;

    @Schema(description = "患者反馈")
    private String patientFeedback;

    @Schema(description = "满意度评分（1-5分）")
    private Integer satisfactionScore;

    @Schema(description = "下次跟进时间")
    private LocalDateTime nextFollowTime;

    @Schema(description = "跟进状态：1-待跟进，2-跟进中，3-已完成，4-已取消，5-跟进失败")
    private Integer followStatus = 1;

    @Schema(description = "优先级：1-高，2-中，3-低")
    private Integer priorityLevel = 2;

    @Schema(description = "是否提醒：0-否，1-是")
    private Boolean reminderFlag = false;

    @Schema(description = "提醒时间")
    private LocalDateTime reminderTime;

    @Schema(description = "备注")
    private String remark;
}

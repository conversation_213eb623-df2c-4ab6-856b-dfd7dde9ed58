package net.lab1024.sa.admin.module.business.hospital.visit.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.form.DiagnosisFlowForm;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.form.DiagnosisFlowCompleteForm;
import net.lab1024.sa.admin.module.business.hospital.visit.service.DiagnosisFlowService;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.RequestUser;
import net.lab1024.sa.base.common.util.SmartRequestUtil;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 诊断流程控制器
 *
 * <AUTHOR>
 * @Date 2025-07-29
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Tag(name = "诊断流程管理")
@RestController
@RequestMapping("/visit/diagnosisFlow")
public class DiagnosisFlowController {

    @Autowired
    private DiagnosisFlowService diagnosisFlowService;

    @Operation(summary = "执行完整的诊断流程")
    @PostMapping("/execute")
    public ResponseDTO<String> executeFlow(@RequestBody @Valid DiagnosisFlowForm flowForm) {
        RequestUser requestUser = SmartRequestUtil.getRequestUser();
        return diagnosisFlowService.executeFlow(flowForm, requestUser.getUserId(), requestUser.getUserName());
    }

    @Operation(summary = "完成诊断流程")
    @PostMapping("/complete")
    public ResponseDTO<String> completeFlow(@RequestBody @Valid DiagnosisFlowCompleteForm completeForm) {
        RequestUser requestUser = SmartRequestUtil.getRequestUser();
        return diagnosisFlowService.completeFlow(completeForm, requestUser.getUserId(), requestUser.getUserName());
    }

    @Operation(summary = "分配治疗助理")
    @PostMapping("/assignAssistant")
    public ResponseDTO<String> assignAssistant(@RequestParam Long patientId,
                                               @RequestParam Long assistantId,
                                               @RequestParam(required = false) String assignNote) {
        RequestUser requestUser = SmartRequestUtil.getRequestUser();
        return diagnosisFlowService.assignAssistant(patientId, assistantId, assignNote,
                requestUser.getUserId(), requestUser.getUserName());
    }
}

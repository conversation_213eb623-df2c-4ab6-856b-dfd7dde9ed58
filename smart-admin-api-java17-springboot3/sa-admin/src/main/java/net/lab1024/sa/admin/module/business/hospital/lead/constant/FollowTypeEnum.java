package net.lab1024.sa.admin.module.business.hospital.lead.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 跟进方式枚举
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Getter
@AllArgsConstructor
public enum FollowTypeEnum implements BaseEnum {

    /**
     * 电话跟进
     */
    PHONE(1, "电话"),

    /**
     * 微信跟进
     */
    WECHAT(2, "微信"),

    /**
     * 面谈
     */
    FACE_TO_FACE(3, "面谈"),

    /**
     * 其他方式
     */
    OTHER(4, "其他");

    private final Integer value;

    private final String desc;
}

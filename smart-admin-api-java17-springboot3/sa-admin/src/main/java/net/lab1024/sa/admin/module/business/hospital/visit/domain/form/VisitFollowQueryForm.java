package net.lab1024.sa.admin.module.business.hospital.visit.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.domain.PageParam;

import java.time.LocalDateTime;

/**
 * 到诊患者跟进记录查询表单
 *
 * <AUTHOR>
 * @Date 2025-07-30 00:00:00
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@Schema(description = "到诊患者跟进记录查询表单")
public class VisitFollowQueryForm extends PageParam {

    @Schema(description = "患者ID")
    private Long patientId;

    @Schema(description = "患者姓名")
    private String patientName;

    @Schema(description = "患者电话")
    private String patientPhone;

    @Schema(description = "跟进类型：1-治疗回访，2-复诊提醒，3-满意度调查，4-术后跟进，5-用药跟进")
    private Integer followType;

    @Schema(description = "跟进方式：1-电话，2-微信，3-短信，4-邮件，5-上门")
    private Integer followMethod;

    @Schema(description = "跟进状态：1-待跟进，2-跟进中，3-已完成，4-已取消，5-跟进失败")
    private Integer followStatus;

    @Schema(description = "优先级：1-高，2-中，3-低")
    private Integer priorityLevel;

    @Schema(description = "跟进人员ID")
    private Long followUserId;

    @Schema(description = "计划跟进开始时间")
    private LocalDateTime plannedStartTime;

    @Schema(description = "计划跟进结束时间")
    private LocalDateTime plannedEndTime;

    @Schema(description = "实际跟进开始时间")
    private LocalDateTime actualStartTime;

    @Schema(description = "实际跟进结束时间")
    private LocalDateTime actualEndTime;

    @Schema(description = "创建开始时间")
    private LocalDateTime createStartTime;

    @Schema(description = "创建结束时间")
    private LocalDateTime createEndTime;
}

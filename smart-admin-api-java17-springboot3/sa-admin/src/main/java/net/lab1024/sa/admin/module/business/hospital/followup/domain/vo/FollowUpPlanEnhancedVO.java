package net.lab1024.sa.admin.module.business.hospital.followup.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.swagger.SchemaEnum;
import net.lab1024.sa.admin.module.business.hospital.followup.constant.FollowUpPlanTypeEnum;
import net.lab1024.sa.admin.module.business.hospital.followup.constant.TargetPatientTypeEnum;
import net.lab1024.sa.admin.module.business.hospital.followup.constant.FollowUpMethodEnum;
import net.lab1024.sa.admin.module.business.hospital.followup.constant.ExecutionFrequencyEnum;
import net.lab1024.sa.admin.module.business.hospital.followup.constant.FollowUpPlanStatusEnum;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * 回访计划增强版VO
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@Schema(description = "回访计划增强版VO")
public class FollowUpPlanEnhancedVO {

    @Schema(description = "计划ID")
    private Long planId;

    @Schema(description = "计划编号")
    private String planNo;

    @Schema(description = "计划名称")
    private String planName;

    @Schema(description = "计划描述")
    private String planDescription;

    @Schema(description = "计划类型：1-定期回访，2-节日回访，3-特殊回访，4-术后回访，5-用药跟进")
    @SchemaEnum(FollowUpPlanTypeEnum.class)
    private Integer planType;

    @Schema(description = "计划类型名称")
    private String planTypeName;

    @Schema(description = "目标患者类型：1-所有已分配医生患者，2-指定医生患者，3-指定科室患者，4-指定标签患者")
    @SchemaEnum(TargetPatientTypeEnum.class)
    private Integer targetPatientType;

    @Schema(description = "目标患者类型名称")
    private String targetPatientTypeName;

    @Schema(description = "筛选条件(JSON格式)")
    private String filterConditions;

    @Schema(description = "目标医生ID列表")
    private List<Long> targetDoctorIds;

    @Schema(description = "目标医生名称列表")
    private List<String> targetDoctorNames;

    @Schema(description = "目标科室ID列表")
    private List<Long> targetDepartmentIds;

    @Schema(description = "目标科室名称列表")
    private List<String> targetDepartmentNames;

    @Schema(description = "目标患者标签")
    private String targetPatientTags;

    @Schema(description = "到诊日期范围开始")
    private LocalDate visitDateRangeStart;

    @Schema(description = "到诊日期范围结束")
    private LocalDate visitDateRangeEnd;

    @Schema(description = "回访方式：1-电话，2-微信，3-短信，4-邮件，5-上门")
    @SchemaEnum(FollowUpMethodEnum.class)
    private Integer followUpMethod;

    @Schema(description = "回访方式名称")
    private String followUpMethodName;

    @Schema(description = "回访内容模板")
    private String followUpContentTemplate;

    @Schema(description = "回访问题列表")
    private List<FollowUpQuestionVO> followUpQuestions;

    @Schema(description = "计划开始日期")
    private LocalDate planStartDate;

    @Schema(description = "计划结束日期")
    private LocalDate planEndDate;

    @Schema(description = "执行频率：1-每日，2-每周，3-每月，4-每季度，5-每年，6-一次性")
    @SchemaEnum(ExecutionFrequencyEnum.class)
    private Integer executionFrequency;

    @Schema(description = "执行频率名称")
    private String executionFrequencyName;

    @Schema(description = "执行时间")
    private LocalTime executionTime;

    @Schema(description = "执行日期(周几或月几号)")
    private String executionDays;

    @Schema(description = "到诊后延迟天数")
    private Integer delayDaysAfterVisit;

    @Schema(description = "优先级：1-高，2-中，3-低")
    private Integer priorityLevel;

    @Schema(description = "优先级名称")
    private String priorityLevelName;

    @Schema(description = "计划状态：1-草稿，2-执行中，3-已暂停，4-已完成，5-已取消")
    @SchemaEnum(FollowUpPlanStatusEnum.class)
    private Integer planStatus;

    @Schema(description = "计划状态名称")
    private String planStatusName;

    @Schema(description = "是否自动生成回访记录")
    private Boolean autoGenerateRecords;

    @Schema(description = "负责人ID")
    private Long responsibleUserId;

    @Schema(description = "负责人姓名")
    private String responsibleUserName;

    @Schema(description = "所属部门ID")
    private Long departmentId;

    @Schema(description = "所属部门名称")
    private String departmentName;

    @Schema(description = "预期患者数量")
    private Integer expectedPatientCount;

    @Schema(description = "实际患者数量")
    private Integer actualPatientCount;

    @Schema(description = "已完成数量")
    private Integer completedCount;

    @Schema(description = "成功率(%)")
    private BigDecimal successRate;

    @Schema(description = "最后执行时间")
    private LocalDateTime lastExecutionTime;

    @Schema(description = "下次执行时间")
    private LocalDateTime nextExecutionTime;

    @Schema(description = "总执行次数")
    private Integer totalExecutions;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 回访问题VO
     */
    @Data
    @Schema(description = "回访问题VO")
    public static class FollowUpQuestionVO {

        @Schema(description = "问题内容")
        private String question;

        @Schema(description = "问题类型：1-单选，2-多选，3-文本")
        private Integer questionType;

        @Schema(description = "问题类型名称")
        private String questionTypeName;

        @Schema(description = "选项列表")
        private List<String> options;

        @Schema(description = "是否必填")
        private Boolean required;

        @Schema(description = "排序")
        private Integer sortOrder;
    }
}

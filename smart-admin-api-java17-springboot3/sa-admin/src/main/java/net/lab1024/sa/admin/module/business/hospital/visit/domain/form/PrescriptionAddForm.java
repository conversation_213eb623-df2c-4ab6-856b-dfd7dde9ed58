package net.lab1024.sa.admin.module.business.hospital.visit.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 开单记录添加表单
 *
 * <AUTHOR>
 * @Date 2025-07-29
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@Schema(description = "开单记录添加表单")
public class PrescriptionAddForm {

    @Schema(description = "诊断ID")
    @NotNull(message = "诊断ID不能为空")
    private Long diagnosisId;

    @Schema(description = "患者ID")
    @NotNull(message = "患者ID不能为空")
    private Long patientId;

    @Schema(description = "治疗项目列表")
    @NotEmpty(message = "治疗项目不能为空")
    @Valid
    private List<PrescriptionItemForm> treatmentItems;

    @Schema(description = "开单说明")
    private String prescriptionNote;
}

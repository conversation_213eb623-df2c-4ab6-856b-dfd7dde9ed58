package net.lab1024.sa.admin.module.business.hospital.visit.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 到诊状态枚举
 *
 * <AUTHOR>
 * @Date 2025-07-29 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Getter
@AllArgsConstructor
public enum VisitStatusEnum implements BaseEnum {

    /**
     * 未到诊
     */
    NOT_ARRIVED(1, "未到诊"),

    /**
     * 已到诊
     */
    ARRIVED(2, "已到诊"),

    /**
     * 诊疗中
     */
    IN_TREATMENT(3, "诊疗中"),

    /**
     * 已完成
     */
    COMPLETED(4, "已完成"),

    /**
     * 已离院
     */
    DISCHARGED(5, "已离院");

    private final Integer value;

    private final String desc;
}

package net.lab1024.sa.admin.module.business.hospital.lead.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 线索实体类
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@TableName("t_lead")
public class LeadEntity {

    /**
     * 线索ID
     */
    @TableId(type = IdType.AUTO)
    private Long leadId;

    /**
     * 线索来源
     */
    private String leadSource;

    /**
     * 客户姓名
     */
    private String customerName;

    /**
     * 客户电话
     */
    private String customerPhone;

    /**
     * 工作手机设备标识
     */
    private String workPhone;

    /**
     * 微信号
     */
    private String customerWechat;

    /**
     * 性别：使用GENDER字典
     */
    private Integer gender;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 症状
     */
    private String symptom;

    /**
     * 地区
     */
    private String region;

    /**
     * 线索状态：1-新线索，2-跟进中，3-已预约，4-已转化，5-已关闭
     */
    private Integer leadStatus;

    /**
     * 线索质量：1-A级，2-B级，3-C级
     */
    private Integer leadQuality;

    /**
     * 分配员工ID
     */
    private Long assignedEmployeeId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 聊天记录
     */
    private String chatRecord;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标志：0-未删除，1-已删除
     */
    private Boolean deletedFlag;

    /**
     * 创建人ID
     */
    private Long createUserId;

    /**
     * 更新人ID
     */
    private Long updateUserId;
}

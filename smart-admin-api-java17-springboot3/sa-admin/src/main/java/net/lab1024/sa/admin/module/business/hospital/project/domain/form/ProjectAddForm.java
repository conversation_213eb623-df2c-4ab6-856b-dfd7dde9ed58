package net.lab1024.sa.admin.module.business.hospital.project.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import net.lab1024.sa.admin.module.business.hospital.project.constant.ProjectStatusEnum;
import net.lab1024.sa.base.common.swagger.SchemaEnum;
import net.lab1024.sa.base.common.validator.enumeration.CheckEnum;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import java.math.BigDecimal;

/**
 * 项目添加表单
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class ProjectAddForm {

    @Schema(description = "项目名称")
    @NotBlank(message = "项目名称不能为空")
    @Length(max = 100, message = "项目名称最多100字符")
    private String projectName;

    @Schema(description = "项目分类")
    @Length(max = 50, message = "项目分类最多50字符")
    private String projectCategory;

    @Schema(description = "项目价格")
    @NotNull(message = "项目价格不能为空")
    private BigDecimal standardPrice;

    @Schema(description = "项目时长（分钟）")
    @Range(min = 1, max = 1440, message = "项目时长必须在1-1440分钟之间")
    private Integer durationMinutes;

    @Schema(description = "项目状态：1-启用，2-禁用")
    @SchemaEnum(ProjectStatusEnum.class)
    @CheckEnum(message = "项目状态错误", value = ProjectStatusEnum.class, required = false)
    private Integer projectStatus;

    @Schema(description = "项目描述")
    @Length(max = 500, message = "项目描述最多500字符")
    private String projectDescription;

    @Schema(description = "备注")
    @Length(max = 500, message = "备注最多500字符")
    private String remark;

    @Schema(description = "排序")
    private Integer sort;
}

package net.lab1024.sa.admin.module.business.hospital.dashboard.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 仪表板统计数据 VO
 *
 * <AUTHOR>
 * @Date 2024-12-15 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright <a href="https://1024lab.net">1024创新实验室</a>
 */
@Data
@Schema(description = "仪表板统计数据")
public class DashboardStatisticsVO {

    @Schema(description = "总线索数")
    private Long leadCount;

    @Schema(description = "总客户数")
    private Long customerCount;

    @Schema(description = "总预约数")
    private Long appointmentCount;

    @Schema(description = "总收入(万元)")
    private BigDecimal totalRevenue;

    @Schema(description = "今日新增线索数")
    private Long todayLeadCount;

    @Schema(description = "今日新增客户数")
    private Long todayCustomerCount;

    @Schema(description = "今日预约数")
    private Long todayAppointmentCount;

    @Schema(description = "今日收入(万元)")
    private BigDecimal todayRevenue;

    @Schema(description = "待处理线索数")
    private Long pendingLeadCount;

    @Schema(description = "待确认预约数")
    private Long pendingAppointmentCount;

    @Schema(description = "今日待办数")
    private Long todayTodoCount;
}

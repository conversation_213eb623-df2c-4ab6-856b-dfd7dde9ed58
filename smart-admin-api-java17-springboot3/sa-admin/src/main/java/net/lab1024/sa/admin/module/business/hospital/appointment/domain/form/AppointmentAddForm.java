package net.lab1024.sa.admin.module.business.hospital.appointment.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import net.lab1024.sa.admin.module.business.hospital.appointment.constant.AppointmentStatusEnum;
import net.lab1024.sa.base.common.swagger.SchemaEnum;
import net.lab1024.sa.base.common.validator.enumeration.CheckEnum;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 预约添加表单
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class AppointmentAddForm {

    @Schema(description = "关联线索ID")
    private Long leadId;

    @Schema(description = "关联客户ID")
    private Long customerId;

    @Schema(description = "客户姓名")
    @NotBlank(message = "客户姓名不能为空")
    @Length(max = 100, message = "客户姓名最多100字符")
    private String customerName;

    @Schema(description = "客户电话")
    @NotBlank(message = "客户电话不能为空")
    @Length(max = 20, message = "客户电话最多20字符")
    private String customerPhone;

    @Schema(description = "微信号")
    @Length(max = 50, message = "微信号最多50字符")
    private String wechat;

    @Schema(description = "性别：1-男，2-女")
    private Integer gender;

    @Schema(description = "年龄")
    private Integer age;

    @Schema(description = "症状描述")
    @Length(max = 200, message = "症状描述最多200字符")
    private String symptom;

    @Schema(description = "地区")
    @Length(max = 50, message = "地区最多50字符")
    private String region;

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "项目名称")
    @Length(max = 100, message = "项目名称最多100字符")
    private String projectName;

    @Schema(description = "医生ID")
    private Long doctorId;

    @Schema(description = "医生姓名")
    @Length(max = 50, message = "医生姓名最多50字符")
    private String doctorName;

    @Schema(description = "分配员工ID")
    private Long assignedEmployeeId;

    @Schema(description = "预约日期")
    @NotNull(message = "预约日期不能为空")
    private LocalDate appointmentDate;

    @Schema(description = "预约时间")
    @NotNull(message = "预约时间不能为空")
    private LocalTime appointmentTime;

    @Schema(description = "预约状态：1-未到诊，2-已到诊")
    @SchemaEnum(AppointmentStatusEnum.class)
    @CheckEnum(message = "预约状态错误", value = AppointmentStatusEnum.class, required = true)
    @NotNull(message = "预约状态不能为空")
    private Integer appointmentStatus;

    @Schema(description = "备注")
    @Length(max = 500, message = "备注最多500字符")
    private String remark;
}

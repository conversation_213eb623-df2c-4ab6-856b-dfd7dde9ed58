package net.lab1024.sa.admin.module.business.hospital.dashboard.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 仪表盘概览数据VO
 *
 * <AUTHOR>
 * @Date 2024-12-15 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@Schema(description = "仪表盘概览数据")
public class DashboardOverviewVO {

    @Schema(description = "总线索数（活跃线索）")
    private Integer totalLeads;

    @Schema(description = "今日新增线索数")
    private Integer todayLeads;

    @Schema(description = "本月线索数")
    private Integer monthLeads;

    @Schema(description = "总预约数")
    private Integer totalAppointments;

    @Schema(description = "今日预约数")
    private Integer todayAppointments;

    @Schema(description = "本月预约数")
    private Integer monthAppointments;

    @Schema(description = "总到院数")
    private Integer totalArrived;

    @Schema(description = "今日到院数")
    private Integer todayArrived;

    @Schema(description = "本月到院数")
    private Integer monthArrived;
}

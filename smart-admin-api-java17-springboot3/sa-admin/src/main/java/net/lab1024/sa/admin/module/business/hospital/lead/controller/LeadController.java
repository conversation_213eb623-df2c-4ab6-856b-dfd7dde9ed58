package net.lab1024.sa.admin.module.business.hospital.lead.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.constant.AdminSwaggerTagConst;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.form.*;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadExcelVO;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadVO;
import net.lab1024.sa.admin.module.business.hospital.lead.service.LeadExcelService;
import net.lab1024.sa.admin.module.business.hospital.lead.service.LeadService;
import net.lab1024.sa.admin.module.system.employee.domain.vo.EmployeeVO;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.ValidateList;
import net.lab1024.sa.base.module.support.operatelog.annotation.OperateLog;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 线索管理Controller
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Tag(name = AdminSwaggerTagConst.Business.HOSPITAL_LEAD)
@RestController
@RequestMapping("/api/lead")
public class LeadController {

    @Resource
    private LeadService leadService;

    @Resource
    private LeadExcelService leadExcelService;

    @Operation(summary = "分页查询线索")
    @PostMapping("/query")
    @SaCheckPermission("hospital:lead:query")
    public ResponseDTO<PageResult<LeadVO>> queryPage(@RequestBody @Valid LeadQueryForm queryForm) {
        return leadService.queryPage(queryForm);
    }

    @Operation(summary = "添加线索")
    @PostMapping("/add")
    @SaCheckPermission("hospital:lead:add")
    @OperateLog
    public ResponseDTO<String> add(@RequestBody @Valid LeadAddForm addForm) {
        return leadService.add(addForm);
    }

    @Operation(summary = "更新线索")
    @PostMapping("/update")
    @SaCheckPermission("hospital:lead:update")
    @OperateLog
    public ResponseDTO<String> update(@RequestBody @Valid LeadUpdateForm updateForm) {
        return leadService.update(updateForm);
    }

    @Operation(summary = "删除线索")
    @GetMapping("/delete/{leadId}")
    @SaCheckPermission("hospital:lead:delete")
    @OperateLog
    public ResponseDTO<String> delete(@PathVariable Long leadId) {
        return leadService.delete(leadId);
    }

    @Operation(summary = "批量删除线索")
    @PostMapping("/batchDelete")
    @SaCheckPermission("hospital:lead:delete")
    @OperateLog
    public ResponseDTO<String> batchDelete(@RequestBody ValidateList<Long> leadIdList) {
        return leadService.batchDelete(leadIdList);
    }

    @Operation(summary = "分配线索")
    @PostMapping("/assign")
    @SaCheckPermission("hospital:lead:assign")
    @OperateLog
    public ResponseDTO<String> assign(@RequestBody @Valid LeadAssignForm assignForm) {
        return leadService.assign(assignForm);
    }

    @Operation(summary = "线索转客户")
    @PostMapping("/convertToCustomer/{leadId}")
    @SaCheckPermission("hospital:lead:convert")
    @OperateLog
    public ResponseDTO<String> convertToCustomer(@PathVariable Long leadId) {
        return leadService.convertToCustomer(leadId);
    }

    @Operation(summary = "导出线索")
    @PostMapping("/exportExcel")
    @SaCheckPermission("hospital:lead:export")
    @OperateLog
    public void exportExcel(@RequestBody @Valid LeadQueryForm queryForm, HttpServletResponse response) throws IOException {
        leadExcelService.exportExcel(queryForm, response);
    }

    @Operation(summary = "导入线索")
    @PostMapping("/importExcel")
    @SaCheckPermission("hospital:lead:import")
    @OperateLog
    public ResponseDTO<String> importExcel(@RequestParam("file") MultipartFile file) {
        return leadExcelService.importExcel(file);
    }

    @Operation(summary = "下载导入模板")
    @GetMapping("/downloadTemplate")
    @SaCheckPermission("hospital:lead:import")
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        leadExcelService.downloadTemplate(response);
    }

    @Operation(summary = "获取我的线索统计")
    @GetMapping("/getMyLeadCount")
    @SaCheckPermission("hospital:lead:query")
    public ResponseDTO<Integer> getMyLeadCount() {
        return leadService.getMyLeadCount();
    }

    @Operation(summary = "获取待跟进线索")
    @GetMapping("/getPendingFollowUp")
    @SaCheckPermission("hospital:lead:query")
    public ResponseDTO<List<LeadVO>> getPendingFollowUp() {
        return leadService.getPendingFollowUp();
    }

    @Operation(summary = "获取线索详情")
    @GetMapping("/detail/{leadId}")
    @SaCheckPermission("hospital:lead:query")
    public ResponseDTO<LeadVO> getDetail(@PathVariable Long leadId) {
        return leadService.getDetail(leadId);
    }

    @Operation(summary = "获取可分配的员工列表")
    @GetMapping("/assignable-employees")
    @SaCheckPermission("hospital:lead:assign")
    public ResponseDTO<List<EmployeeVO>> getAssignableEmployees() {
        return leadService.getAssignableEmployees();
    }
}

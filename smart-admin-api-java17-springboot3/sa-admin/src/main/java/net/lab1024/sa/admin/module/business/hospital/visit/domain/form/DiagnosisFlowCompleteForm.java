package net.lab1024.sa.admin.module.business.hospital.visit.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotNull;

/**
 * 诊断流程完成表单
 *
 * <AUTHOR>
 * @Date 2025-07-29
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@Schema(description = "诊断流程完成表单")
public class DiagnosisFlowCompleteForm {

    @Schema(description = "患者ID")
    @NotNull(message = "患者ID不能为空")
    private Long patientId;

    @Schema(description = "诊断ID")
    @NotNull(message = "诊断ID不能为空")
    private Long diagnosisId;

    @Schema(description = "开单ID")
    @NotNull(message = "开单ID不能为空")
    private Long prescriptionId;

    @Schema(description = "分配助理ID")
    @NotNull(message = "分配助理不能为空")
    private Long assignedAssistantId;

    @Schema(description = "分配说明")
    private String assignNote;
}

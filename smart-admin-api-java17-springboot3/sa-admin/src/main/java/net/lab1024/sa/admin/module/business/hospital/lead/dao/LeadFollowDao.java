package net.lab1024.sa.admin.module.business.hospital.lead.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.entity.LeadFollowEntity;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadFollowVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 线索跟进DAO
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Mapper
@Component
public interface LeadFollowDao extends BaseMapper<LeadFollowEntity> {

    /**
     * 根据线索ID查询跟进记录
     *
     * @param leadId 线索ID
     * @return 跟进记录列表
     */
    List<LeadFollowVO> selectByLeadId(@Param("leadId") Long leadId);

    /**
     * 分页查询跟进记录
     *
     * @param page 分页参数
     * @param leadId 线索ID
     * @param employeeId 员工ID
     * @return 跟进记录列表
     */
    List<LeadFollowVO> queryPage(Page page, @Param("leadId") Long leadId, @Param("employeeId") Long employeeId);

    /**
     * 获取线索的最后跟进时间
     *
     * @param leadId 线索ID
     * @return 最后跟进时间
     */
    LocalDateTime getLastFollowTime(@Param("leadId") Long leadId);

    /**
     * 获取线索的跟进次数
     *
     * @param leadId 线索ID
     * @return 跟进次数
     */
    Integer getFollowCount(@Param("leadId") Long leadId);

    /**
     * 根据员工ID查询跟进记录数量
     *
     * @param employeeId 员工ID
     * @return 跟进记录数量
     */
    Integer countByEmployeeId(@Param("employeeId") Long employeeId);

    /**
     * 查询需要跟进提醒的记录
     *
     * @param currentTime 当前时间
     * @return 需要提醒的跟进记录
     */
    List<LeadFollowVO> selectNeedReminder(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 批量删除跟进记录
     *
     * @param leadIdList 线索ID列表
     */
    void batchDeleteByLeadIds(@Param("leadIdList") List<Long> leadIdList);
}

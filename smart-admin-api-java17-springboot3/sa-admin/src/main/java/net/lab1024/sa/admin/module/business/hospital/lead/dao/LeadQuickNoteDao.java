package net.lab1024.sa.admin.module.business.hospital.lead.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.entity.LeadQuickNoteEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

/**
 * 线索快速笔记DAO
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Mapper
@Component
public interface LeadQuickNoteDao extends BaseMapper<LeadQuickNoteEntity> {

    /**
     * 根据线索ID查询最新的快速笔记
     *
     * @param leadId 线索ID
     * @return 快速笔记
     */
    LeadQuickNoteEntity selectLatestByLeadId(@Param("leadId") Long leadId);

    /**
     * 根据线索ID删除所有笔记
     *
     * @param leadId 线索ID
     */
    void deleteByLeadId(@Param("leadId") Long leadId);
}

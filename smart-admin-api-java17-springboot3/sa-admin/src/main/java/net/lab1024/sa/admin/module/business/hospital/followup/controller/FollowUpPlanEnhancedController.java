package net.lab1024.sa.admin.module.business.hospital.followup.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.constant.AdminSwaggerTagConst;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.form.FollowUpPlanEnhancedAddForm;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.form.FollowUpPlanEnhancedQueryForm;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.form.FollowUpPlanEnhancedUpdateForm;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.vo.FollowUpPlanEnhancedVO;
import net.lab1024.sa.admin.module.business.hospital.followup.service.FollowUpPlanEnhancedService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.ValidateList;
import net.lab1024.sa.base.module.support.operatelog.annotation.OperateLog;
import net.lab1024.sa.base.module.support.repeatsubmit.annoation.RepeatSubmit;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.Map;

/**
 * 回访计划增强版Controller
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Slf4j
@Tag(name = AdminSwaggerTagConst.Business.HOSPITAL_FOLLOW_UP_ENHANCED)
@RestController
@RequestMapping("/api/followup/plan/enhanced")
public class FollowUpPlanEnhancedController {

    @Resource
    private FollowUpPlanEnhancedService followUpPlanEnhancedService;

    @Operation(summary = "分页查询回访计划")
    @PostMapping("/queryPage")
    @SaCheckPermission("hospital:followup:plan:query")
    public ResponseDTO<PageResult<FollowUpPlanEnhancedVO>> queryPage(@RequestBody @Valid FollowUpPlanEnhancedQueryForm queryForm) {
        return followUpPlanEnhancedService.queryPage(queryForm);
    }

    @Operation(summary = "添加回访计划")
    @PostMapping("/add")
    @SaCheckPermission("hospital:followup:plan:add")
    @RepeatSubmit
    @OperateLog
    public ResponseDTO<String> add(@RequestBody @Valid FollowUpPlanEnhancedAddForm addForm) {
        return followUpPlanEnhancedService.add(addForm);
    }

    @Operation(summary = "更新回访计划")
    @PostMapping("/update")
    @SaCheckPermission("hospital:followup:plan:update")
    @RepeatSubmit
    @OperateLog
    public ResponseDTO<String> update(@RequestBody @Valid FollowUpPlanEnhancedUpdateForm updateForm) {
        return followUpPlanEnhancedService.update(updateForm);
    }

    @Operation(summary = "删除回访计划")
    @GetMapping("/delete/{planId}")
    @SaCheckPermission("hospital:followup:plan:delete")
    @OperateLog
    public ResponseDTO<String> delete(@PathVariable Long planId) {
        return followUpPlanEnhancedService.delete(planId);
    }

    @Operation(summary = "批量删除回访计划")
    @PostMapping("/batchDelete")
    @SaCheckPermission("hospital:followup:plan:delete")
    @RepeatSubmit
    @OperateLog
    public ResponseDTO<String> batchDelete(@RequestBody @Valid ValidateList<Long> planIdList) {
        for (Long planId : planIdList) {
            ResponseDTO<String> result = followUpPlanEnhancedService.delete(planId);
            if (!result.getOk()) {
                return result;
            }
        }
        return ResponseDTO.ok();
    }

    @Operation(summary = "获取回访计划详情")
    @GetMapping("/detail/{planId}")
    @SaCheckPermission("hospital:followup:plan:detail")
    public ResponseDTO<FollowUpPlanEnhancedVO> detail(@PathVariable Long planId) {
        return followUpPlanEnhancedService.detail(planId);
    }

    @Operation(summary = "启动回访计划")
    @PostMapping("/start/{planId}")
    @SaCheckPermission("hospital:followup:plan:start")
    @RepeatSubmit
    @OperateLog
    public ResponseDTO<String> start(@PathVariable Long planId) {
        return followUpPlanEnhancedService.startPlan(planId);
    }

    @Operation(summary = "暂停回访计划")
    @PostMapping("/pause/{planId}")
    @SaCheckPermission("hospital:followup:plan:pause")
    @RepeatSubmit
    @OperateLog
    public ResponseDTO<String> pause(@PathVariable Long planId) {
        return followUpPlanEnhancedService.pausePlan(planId);
    }

    @Operation(summary = "执行回访计划")
    @PostMapping("/execute/{planId}")
    @SaCheckPermission("hospital:followup:plan:execute")
    @RepeatSubmit
    @OperateLog
    public ResponseDTO<String> execute(@PathVariable Long planId) {
        return followUpPlanEnhancedService.executePlan(planId);
    }

    @Operation(summary = "批量执行回访计划")
    @PostMapping("/batchExecute")
    @SaCheckPermission("hospital:followup:plan:execute")
    @RepeatSubmit
    @OperateLog
    public ResponseDTO<String> batchExecute(@RequestBody @Valid ValidateList<Long> planIdList) {
        int successCount = 0;
        int failCount = 0;
        StringBuilder errorMessages = new StringBuilder();
        
        for (Long planId : planIdList) {
            ResponseDTO<String> result = followUpPlanEnhancedService.executePlan(planId);
            if (result.getOk()) {
                successCount++;
            } else {
                failCount++;
                errorMessages.append("计划ID ").append(planId).append(": ").append(result.getMsg()).append("; ");
            }
        }
        
        if (failCount == 0) {
            return ResponseDTO.ok("批量执行成功，共执行" + successCount + "个计划");
        } else {
            return ResponseDTO.userErrorParam("批量执行完成，成功" + successCount + "个，失败" + failCount + "个。失败原因：" + errorMessages.toString());
        }
    }

    @Operation(summary = "导出回访计划")
    @PostMapping("/export")
    @SaCheckPermission("hospital:followup:plan:export")
    @OperateLog
    public ResponseDTO<String> export(@RequestBody @Valid FollowUpPlanEnhancedQueryForm queryForm) {
        return followUpPlanEnhancedService.export(queryForm);
    }

    @Operation(summary = "获取回访计划统计数据")
    @PostMapping("/statistics")
    // @SaCheckPermission("hospital:followup:plan:statistics") // 临时去掉权限验证用于测试
    public ResponseDTO<Map<String, Object>> getStatistics(@RequestBody @Valid FollowUpPlanEnhancedQueryForm queryForm) {
        return followUpPlanEnhancedService.getStatistics(queryForm);
    }

    @Operation(summary = "获取今日待执行计划")
    @GetMapping("/today/pending")
    @SaCheckPermission("hospital:followup:plan:query")
    public ResponseDTO<PageResult<FollowUpPlanEnhancedVO>> getTodayPendingPlans() {
        // 这里可以实现获取今日待执行计划的逻辑
        FollowUpPlanEnhancedQueryForm queryForm = new FollowUpPlanEnhancedQueryForm();
        queryForm.setPlanStatus(2); // 执行中状态
        return followUpPlanEnhancedService.queryPage(queryForm);
    }

    @Operation(summary = "获取逾期计划")
    @GetMapping("/overdue")
    @SaCheckPermission("hospital:followup:plan:query")
    public ResponseDTO<PageResult<FollowUpPlanEnhancedVO>> getOverduePlans() {
        // 这里可以实现获取逾期计划的逻辑
        FollowUpPlanEnhancedQueryForm queryForm = new FollowUpPlanEnhancedQueryForm();
        queryForm.setPlanStatus(2); // 执行中状态
        return followUpPlanEnhancedService.queryPage(queryForm);
    }

    @Operation(summary = "复制回访计划")
    @PostMapping("/copy/{planId}")
    @SaCheckPermission("hospital:followup:plan:add")
    @RepeatSubmit
    @OperateLog
    public ResponseDTO<String> copyPlan(@PathVariable Long planId) {
        // 获取原计划详情
        ResponseDTO<FollowUpPlanEnhancedVO> detailResult = followUpPlanEnhancedService.detail(planId);
        if (!detailResult.getOk()) {
            return ResponseDTO.userErrorParam("原计划不存在");
        }
        
        FollowUpPlanEnhancedVO originalPlan = detailResult.getData();
        
        // 创建新的添加表单
        FollowUpPlanEnhancedAddForm addForm = new FollowUpPlanEnhancedAddForm();
        addForm.setPlanName(originalPlan.getPlanName() + "_副本");
        addForm.setPlanDescription(originalPlan.getPlanDescription());
        addForm.setPlanType(originalPlan.getPlanType());
        addForm.setTargetPatientType(originalPlan.getTargetPatientType());
        addForm.setTargetDoctorIds(originalPlan.getTargetDoctorIds() != null ? 
            new ValidateList<>(originalPlan.getTargetDoctorIds()) : null);
        addForm.setTargetDepartmentIds(originalPlan.getTargetDepartmentIds() != null ? 
            new ValidateList<>(originalPlan.getTargetDepartmentIds()) : null);
        addForm.setTargetPatientTags(originalPlan.getTargetPatientTags());
        addForm.setVisitDateRangeStart(originalPlan.getVisitDateRangeStart());
        addForm.setVisitDateRangeEnd(originalPlan.getVisitDateRangeEnd());
        addForm.setFollowUpMethod(originalPlan.getFollowUpMethod());
        addForm.setFollowUpContentTemplate(originalPlan.getFollowUpContentTemplate());
        // 注意：这里需要类型转换，实际项目中应该创建转换方法
        // addForm.setFollowUpQuestions(originalPlan.getFollowUpQuestions());
        addForm.setPlanStartDate(originalPlan.getPlanStartDate());
        addForm.setPlanEndDate(originalPlan.getPlanEndDate());
        addForm.setExecutionFrequency(originalPlan.getExecutionFrequency());
        addForm.setExecutionTime(originalPlan.getExecutionTime());
        addForm.setExecutionDays(originalPlan.getExecutionDays());
        addForm.setDelayDaysAfterVisit(originalPlan.getDelayDaysAfterVisit());
        addForm.setPriorityLevel(originalPlan.getPriorityLevel());
        addForm.setAutoGenerateRecords(originalPlan.getAutoGenerateRecords());
        addForm.setResponsibleUserId(originalPlan.getResponsibleUserId());
        addForm.setExpectedPatientCount(originalPlan.getExpectedPatientCount());
        addForm.setRemark("复制自计划：" + originalPlan.getPlanName());
        
        return followUpPlanEnhancedService.add(addForm);
    }

    @Operation(summary = "测试接口")
    @GetMapping("/test")
    public ResponseDTO<String> test() {
        return ResponseDTO.ok("回访计划增强版API测试成功！");
    }
}

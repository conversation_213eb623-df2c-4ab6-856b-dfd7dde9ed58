package net.lab1024.sa.admin.module.business.hospital.visit.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 开单项目明细VO
 *
 * <AUTHOR>
 * @Date 2025-07-29
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@Schema(description = "开单项目明细VO")
public class PrescriptionItemVO {

    @Schema(description = "项目ID")
    private Long itemId;

    @Schema(description = "开单ID")
    private Long prescriptionId;

    @Schema(description = "项目名称")
    private String itemName;

    @Schema(description = "项目类型：treatment-治疗，medicine-药品，examination-检查，material-材料")
    private String itemType;

    @Schema(description = "项目类型名称")
    private String itemTypeName;

    @Schema(description = "单价")
    private BigDecimal unitPrice;

    @Schema(description = "数量")
    private Integer quantity;

    @Schema(description = "小计")
    private BigDecimal subtotal;

    @Schema(description = "项目说明")
    private String itemDescription;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}

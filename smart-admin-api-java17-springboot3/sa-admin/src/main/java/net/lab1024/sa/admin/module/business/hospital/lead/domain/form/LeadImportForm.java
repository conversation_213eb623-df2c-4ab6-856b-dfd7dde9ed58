package net.lab1024.sa.admin.module.business.hospital.lead.domain.form;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 线索导入表单
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class LeadImportForm {

    @ExcelProperty("线索来源")
    private String leadSource;

    @ExcelProperty("客户姓名")
    private String customerName;

    @ExcelProperty("客户电话")
    private String customerPhone;

    @ExcelProperty("微信号")
    private String customerWechat;

    @ExcelProperty("性别")
    private String genderName;

    @ExcelProperty("年龄")
    private Integer age;

    @ExcelProperty("症状")
    private String symptom;

    @ExcelProperty("线索质量")
    private String leadQualityName;

    @ExcelProperty("备注")
    private String remark;
}

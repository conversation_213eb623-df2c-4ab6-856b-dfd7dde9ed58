package net.lab1024.sa.admin.module.business.hospital.customer.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 客户等级枚举
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Getter
@AllArgsConstructor
public enum CustomerLevelEnum implements BaseEnum {

    /**
     * 普通客户
     */
    NORMAL(1, "普通"),

    /**
     * VIP客户
     */
    VIP(2, "VIP"),

    /**
     * SVIP客户
     */
    SVIP(3, "SVIP");

    private final Integer value;

    private final String desc;
}

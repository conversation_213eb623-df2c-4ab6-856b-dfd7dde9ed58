package net.lab1024.sa.admin.module.business.hospital.lead.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 线索归属变更类型枚举
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Getter
@AllArgsConstructor
public enum LeadOwnershipChangeTypeEnum implements BaseEnum {

    /**
     * 申请变更
     */
    REQUEST_CHANGE(1, "申请变更"),

    /**
     * 管理员直接变更
     */
    ADMIN_CHANGE(2, "管理员直接变更"),

    /**
     * 系统自动分配
     */
    SYSTEM_ASSIGN(3, "系统自动分配");

    private final Integer value;

    private final String desc;

    public static LeadOwnershipChangeTypeEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (LeadOwnershipChangeTypeEnum typeEnum : values()) {
            if (typeEnum.getValue().equals(value)) {
                return typeEnum;
            }
        }
        return null;
    }
}

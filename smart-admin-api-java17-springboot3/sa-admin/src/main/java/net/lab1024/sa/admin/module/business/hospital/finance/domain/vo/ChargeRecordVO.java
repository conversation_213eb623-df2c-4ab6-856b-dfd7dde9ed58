package net.lab1024.sa.admin.module.business.hospital.finance.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 收费记录VO
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class ChargeRecordVO {

    @Schema(description = "收费记录ID")
    private Long chargeId;

    @Schema(description = "收费单号")
    private String chargeNo;

    @Schema(description = "客户ID")
    private Long customerId;

    @Schema(description = "客户姓名")
    private String customerName;

    @Schema(description = "客户电话")
    private String customerPhone;

    @Schema(description = "预约ID")
    private Long appointmentId;

    @Schema(description = "病历ID")
    private Long recordId;

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "项目分类")
    private String projectCategory;

    @Schema(description = "标准价格")
    private BigDecimal standardPrice;

    @Schema(description = "实际收费金额")
    private BigDecimal chargeAmount;

    @Schema(description = "优惠金额")
    private BigDecimal discountAmount;

    @Schema(description = "优惠原因")
    private String discountReason;

    @Schema(description = "收费日期")
    private LocalDate chargeDate;

    @Schema(description = "收费状态：1-待收费，2-已收费，3-部分收费，4-已退费")
    private Integer chargeStatus;

    @Schema(description = "收费状态名称")
    private String chargeStatusName;

    @Schema(description = "支付方式：1-现金，2-微信，3-支付宝，4-银行卡，5-组合支付")
    private Integer paymentMethod;

    @Schema(description = "支付方式名称")
    private String paymentMethodName;

    @Schema(description = "支付详情")
    private String paymentDetails;

    @Schema(description = "收费员工ID")
    private Long chargeEmployeeId;

    @Schema(description = "收费员工姓名")
    private String chargeEmployeeName;

    @Schema(description = "收费科室ID")
    private Long chargeDepartmentId;

    @Schema(description = "收费科室名称")
    private String chargeDepartmentName;

    @Schema(description = "发票号码")
    private String invoiceNo;

    @Schema(description = "是否开具发票")
    private Boolean invoiceFlag;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "创建人姓名")
    private String createUserName;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}

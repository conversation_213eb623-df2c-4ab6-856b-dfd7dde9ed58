package net.lab1024.sa.admin.module.business.hospital.customer.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.constant.AdminSwaggerTagConst;
import net.lab1024.sa.admin.module.business.hospital.customer.domain.form.MedicalRecordAddForm;
import net.lab1024.sa.admin.module.business.hospital.customer.domain.form.MedicalRecordQueryForm;
import net.lab1024.sa.admin.module.business.hospital.customer.domain.form.MedicalRecordUpdateForm;
import net.lab1024.sa.admin.module.business.hospital.customer.domain.vo.MedicalRecordVO;
import net.lab1024.sa.admin.module.business.hospital.customer.service.MedicalRecordService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.ValidateList;
import net.lab1024.sa.base.module.support.operatelog.annotation.OperateLog;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

/**
 * 病历管理Controller
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Tag(name = AdminSwaggerTagConst.Business.HOSPITAL_MEDICAL_RECORD)
@RestController
@RequestMapping("/api/medicalRecord")
public class MedicalRecordController {

    @Resource
    private MedicalRecordService medicalRecordService;

    @Operation(summary = "分页查询病历")
    @PostMapping("/query")
    @SaCheckPermission("hospital:record:query")
    public ResponseDTO<PageResult<MedicalRecordVO>> queryPage(@RequestBody @Valid MedicalRecordQueryForm queryForm) {
        return medicalRecordService.queryPage(queryForm);
    }

    @Operation(summary = "添加病历")
    @PostMapping("/add")
    @SaCheckPermission("hospital:record:add")
    @OperateLog
    public ResponseDTO<String> add(@RequestBody @Valid MedicalRecordAddForm addForm) {
        return medicalRecordService.add(addForm);
    }

    @Operation(summary = "更新病历")
    @PostMapping("/update")
    @SaCheckPermission("hospital:record:update")
    @OperateLog
    public ResponseDTO<String> update(@RequestBody @Valid MedicalRecordUpdateForm updateForm) {
        return medicalRecordService.update(updateForm);
    }

    @Operation(summary = "删除病历")
    @GetMapping("/delete/{recordId}")
    @SaCheckPermission("hospital:record:delete")
    @OperateLog
    public ResponseDTO<String> delete(@PathVariable Long recordId) {
        return medicalRecordService.delete(recordId);
    }

    @Operation(summary = "批量删除病历")
    @PostMapping("/batchDelete")
    @SaCheckPermission("hospital:record:delete")
    @OperateLog
    public ResponseDTO<String> batchDelete(@RequestBody ValidateList<Long> recordIdList) {
        return medicalRecordService.batchDelete(recordIdList);
    }

    @Operation(summary = "根据客户ID查询病历")
    @GetMapping("/customer/{customerId}")
    @SaCheckPermission("hospital:record:query")
    public ResponseDTO<List<MedicalRecordVO>> getByCustomerId(@PathVariable Long customerId) {
        return medicalRecordService.getByCustomerId(customerId);
    }

    @Operation(summary = "获取客户最近的病历")
    @GetMapping("/recent/{customerId}/{limit}")
    @SaCheckPermission("hospital:record:query")
    public ResponseDTO<List<MedicalRecordVO>> getRecentRecords(@PathVariable Long customerId, @PathVariable Integer limit) {
        return medicalRecordService.getRecentRecords(customerId, limit);
    }

    @Operation(summary = "根据医生ID查询病历")
    @GetMapping("/doctor/{doctorId}")
    @SaCheckPermission("hospital:record:query")
    public ResponseDTO<List<MedicalRecordVO>> getByDoctorId(
            @PathVariable Long doctorId,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        return medicalRecordService.getByDoctorId(doctorId, startDate, endDate);
    }

    @Operation(summary = "获取需要复诊的病历")
    @GetMapping("/followUp")
    @SaCheckPermission("hospital:record:query")
    public ResponseDTO<List<MedicalRecordVO>> getFollowUpRecords(
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate date) {
        return medicalRecordService.getFollowUpRecords(date);
    }

    @Operation(summary = "获取病历详情")
    @GetMapping("/detail/{recordId}")
    @SaCheckPermission("hospital:record:query")
    public ResponseDTO<MedicalRecordVO> getDetail(@PathVariable Long recordId) {
        return medicalRecordService.getDetail(recordId);
    }

    @Operation(summary = "病历出院")
    @PostMapping("/discharge/{recordId}")
    @SaCheckPermission("hospital:record:update")
    @OperateLog
    public ResponseDTO<String> discharge(@PathVariable Long recordId) {
        return medicalRecordService.discharge(recordId);
    }

    @Operation(summary = "更新病历状态")
    @PostMapping("/update-status/{recordId}")
    @SaCheckPermission("hospital:record:update")
    @OperateLog
    public ResponseDTO<String> updateStatus(@PathVariable Long recordId, @RequestParam Integer recordStatus) {
        return medicalRecordService.updateStatus(recordId, recordStatus);
    }
}

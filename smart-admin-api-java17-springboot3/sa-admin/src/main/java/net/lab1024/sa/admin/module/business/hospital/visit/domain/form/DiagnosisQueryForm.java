package net.lab1024.sa.admin.module.business.hospital.visit.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.domain.PageParam;

import java.time.LocalDateTime;

/**
 * 诊断记录查询表单
 *
 * <AUTHOR>
 * @Date 2025-07-29
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@Schema(description = "诊断记录查询表单")
public class DiagnosisQueryForm extends PageParam {

    @Schema(description = "患者ID")
    private Long patientId;

    @Schema(description = "患者编号")
    private String patientNo;

    @Schema(description = "患者姓名")
    private String patientName;

    @Schema(description = "诊断类型：1-初诊，2-复诊，3-会诊")
    private Integer diagnosisType;

    @Schema(description = "诊断医生ID")
    private Long diagnosisDoctorId;

    @Schema(description = "诊断医生姓名")
    private String diagnosisDoctorName;

    @Schema(description = "诊断状态：1-已诊断，2-已开单，3-已收费，4-已完成")
    private Integer diagnosisStatus;

    @Schema(description = "诊断开始时间")
    private LocalDateTime diagnosisTimeStart;

    @Schema(description = "诊断结束时间")
    private LocalDateTime diagnosisTimeEnd;

    @Schema(description = "创建开始时间")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建结束时间")
    private LocalDateTime createTimeEnd;
}

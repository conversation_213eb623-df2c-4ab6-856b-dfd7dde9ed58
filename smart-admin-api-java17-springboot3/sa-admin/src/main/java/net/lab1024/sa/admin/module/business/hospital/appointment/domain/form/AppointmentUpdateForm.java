package net.lab1024.sa.admin.module.business.hospital.appointment.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 预约更新表单
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class AppointmentUpdateForm extends AppointmentAddForm {

    @Schema(description = "预约ID")
    @NotNull(message = "预约ID不能为空")
    private Long appointmentId;
}

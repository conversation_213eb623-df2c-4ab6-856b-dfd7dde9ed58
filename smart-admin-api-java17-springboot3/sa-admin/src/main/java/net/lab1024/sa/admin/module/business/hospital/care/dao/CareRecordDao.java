package net.lab1024.sa.admin.module.business.hospital.care.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.hospital.care.domain.entity.CareRecordEntity;
import net.lab1024.sa.admin.module.business.hospital.care.domain.form.CareRecordQueryForm;
import net.lab1024.sa.admin.module.business.hospital.care.domain.vo.CareRecordVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 关怀记录DAO
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Mapper
@Component
public interface CareRecordDao extends BaseMapper<CareRecordEntity> {

    /**
     * 分页查询关怀记录
     *
     * @param page 分页参数
     * @param queryForm 查询条件
     * @return 关怀记录列表
     */
    List<CareRecordVO> queryPage(Page page, @Param("query") CareRecordQueryForm queryForm);

    /**
     * 根据客户ID查询关怀记录
     *
     * @param customerId 客户ID
     * @return 关怀记录列表
     */
    List<CareRecordVO> selectByCustomerId(@Param("customerId") Long customerId);

    /**
     * 根据病历ID查询关怀记录
     *
     * @param recordId 病历ID
     * @return 关怀记录列表
     */
    List<CareRecordEntity> selectByRecordId(@Param("recordId") Long recordId);

    /**
     * 根据关怀计划ID查询关怀记录
     *
     * @param carePlanId 关怀计划ID
     * @return 关怀记录列表
     */
    List<CareRecordEntity> selectByCarePlanId(@Param("carePlanId") Long carePlanId);

    /**
     * 更新关怀状态
     *
     * @param careId 关怀记录ID
     * @param careStatus 关怀状态
     * @param actualTime 实际执行时间
     */
    void updateCareStatus(@Param("careId") Long careId, 
                         @Param("careStatus") Integer careStatus,
                         @Param("actualTime") LocalDateTime actualTime);

    /**
     * 更新执行结果
     *
     * @param careId 关怀记录ID
     * @param executionResult 执行结果
     * @param customerFeedback 客户反馈
     * @param satisfactionScore 满意度评分
     */
    void updateExecutionResult(@Param("careId") Long careId,
                              @Param("executionResult") String executionResult,
                              @Param("customerFeedback") String customerFeedback,
                              @Param("satisfactionScore") Integer satisfactionScore);

    /**
     * 获取待执行的关怀记录
     *
     * @param currentTime 当前时间
     * @return 待执行的关怀记录列表
     */
    List<CareRecordEntity> getPendingCareRecords(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 获取需要跟进的关怀记录
     *
     * @param currentTime 当前时间
     * @return 需要跟进的关怀记录列表
     */
    List<CareRecordEntity> getFollowUpCareRecords(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 获取关怀统计信息
     *
     * @param customerId 客户ID
     * @return 统计信息
     */
    CareRecordVO getCareStatistics(@Param("customerId") Long customerId);

    /**
     * 根据出院病历自动创建关怀记录
     *
     * @param recordId 病历ID
     * @return 创建的关怀记录数量
     */
    int createCareFromDischarge(@Param("recordId") Long recordId);
}

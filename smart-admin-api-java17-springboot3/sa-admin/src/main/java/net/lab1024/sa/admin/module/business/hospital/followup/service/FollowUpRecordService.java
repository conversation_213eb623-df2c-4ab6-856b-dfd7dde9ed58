package net.lab1024.sa.admin.module.business.hospital.followup.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.hospital.followup.dao.FollowUpRecordDao;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.entity.FollowUpRecordEntity;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.form.FollowUpRecordAddForm;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.form.FollowUpRecordBatchCompleteForm;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.form.FollowUpRecordCompleteForm;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.form.FollowUpRecordQueryForm;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.form.FollowUpRecordUpdateForm;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.vo.FollowUpRecordVO;
import net.lab1024.sa.admin.util.AdminRequestUtil;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.ValidateList;
import net.lab1024.sa.base.common.exception.BusinessException;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 回访记录Service
 *
 * <AUTHOR>
 * @Date 2025-07-30 00:00:00
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Slf4j
@Service
public class FollowUpRecordService {

    @Resource
    private FollowUpRecordDao followUpRecordDao;

    /**
     * 分页查询回访记录
     */
    public ResponseDTO<PageResult<FollowUpRecordVO>> queryPage(FollowUpRecordQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<FollowUpRecordVO> list = followUpRecordDao.queryPage(page, queryForm);
        PageResult<FollowUpRecordVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return ResponseDTO.ok(pageResult);
    }

    /**
     * 添加回访记录
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> add(FollowUpRecordAddForm addForm) {
        FollowUpRecordEntity entity = SmartBeanUtil.copy(addForm, FollowUpRecordEntity.class);
        entity.setCreateUserId(AdminRequestUtil.getRequestUserId());
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateUserId(AdminRequestUtil.getRequestUserId());
        entity.setUpdateTime(LocalDateTime.now());

        // 设置实际回访时间为当前时间（如果是已完成的回访）
        if (entity.getFollowUpStatus() != null && entity.getFollowUpStatus() == 2) {
            entity.setActualTime(LocalDateTime.now());
        }

        // 设置删除标志为未删除
        entity.setDeletedFlag(0);

        // 设置回访人员信息
        if (entity.getFollowUpUserId() == null) {
            entity.setFollowUpUserId(AdminRequestUtil.getRequestUserId());
        }
        if (entity.getFollowUpUserName() == null || entity.getFollowUpUserName().isEmpty()) {
            entity.setFollowUpUserName(AdminRequestUtil.getRequestUser().getUserName());
        }

        followUpRecordDao.insert(entity);
        return ResponseDTO.ok();
    }

    /**
     * 更新回访记录
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> update(FollowUpRecordUpdateForm updateForm) {
        FollowUpRecordEntity entity = followUpRecordDao.selectById(updateForm.getFollowUpId());
        if (entity == null) {
            return ResponseDTO.userErrorParam("回访记录不存在");
        }

        SmartBeanUtil.copyProperties(updateForm, entity);
        entity.setUpdateUserId(AdminRequestUtil.getRequestUserId());
        entity.setUpdateTime(LocalDateTime.now());
        
        followUpRecordDao.updateById(entity);
        return ResponseDTO.ok();
    }

    /**
     * 删除回访记录
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> delete(Long recordId) {
        FollowUpRecordEntity entity = followUpRecordDao.selectById(recordId);
        if (entity == null) {
            return ResponseDTO.userErrorParam("回访记录不存在");
        }

        followUpRecordDao.deleteById(recordId);
        return ResponseDTO.ok();
    }

    /**
     * 批量删除回访记录
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> batchDelete(ValidateList<Long> recordIdList) {
        if (CollectionUtils.isEmpty(recordIdList)) {
            return ResponseDTO.userErrorParam("请选择要删除的记录");
        }

        followUpRecordDao.deleteBatchIds(recordIdList);
        return ResponseDTO.ok();
    }

    /**
     * 获取回访记录详情
     */
    public ResponseDTO<FollowUpRecordVO> detail(Long recordId) {
        FollowUpRecordEntity entity = followUpRecordDao.selectById(recordId);
        if (entity == null) {
            return ResponseDTO.userErrorParam("回访记录不存在");
        }

        FollowUpRecordVO vo = SmartBeanUtil.copy(entity, FollowUpRecordVO.class);
        return ResponseDTO.ok(vo);
    }

    /**
     * 导出回访记录
     */
    public ResponseDTO<String> export(FollowUpRecordQueryForm queryForm) {
        try {
            // 查询所有符合条件的记录
            List<FollowUpRecordVO> list = followUpRecordDao.queryPage(null, queryForm);

            if (CollectionUtils.isEmpty(list)) {
                return ResponseDTO.userErrorParam("没有可导出的数据");
            }

            // 注意：实际的导出功能需要在Controller层实现，这里只是验证数据
            // 因为导出需要HttpServletResponse对象来设置响应头和输出流
            return ResponseDTO.ok("数据准备完成，共" + list.size() + "条记录");

        } catch (Exception e) {
            log.error("导出回访记录失败", e);
            return ResponseDTO.userErrorParam("导出失败：" + e.getMessage());
        }
    }

    /**
     * 获取回访统计数据
     */
    public ResponseDTO<Object> getStatistics(FollowUpRecordQueryForm queryForm) {
        try {
            Map<String, Object> statistics = new HashMap<>();

            // 总回访记录数
            Long totalCount = followUpRecordDao.selectCount(
                new LambdaQueryWrapper<FollowUpRecordEntity>()
                    .eq(FollowUpRecordEntity::getDeletedFlag, 0)
            );
            statistics.put("totalCount", totalCount);

            // 按状态统计
            Map<String, Long> statusStats = new HashMap<>();
            for (int status = 1; status <= 4; status++) {
                Long count = followUpRecordDao.selectCount(
                    new LambdaQueryWrapper<FollowUpRecordEntity>()
                        .eq(FollowUpRecordEntity::getDeletedFlag, 0)
                        .eq(FollowUpRecordEntity::getFollowUpStatus, status)
                );
                statusStats.put(getFollowUpStatusDesc(status), count);
            }
            statistics.put("statusStats", statusStats);

            // 按类型统计
            Map<String, Long> typeStats = new HashMap<>();
            for (int type = 1; type <= 6; type++) {
                Long count = followUpRecordDao.selectCount(
                    new LambdaQueryWrapper<FollowUpRecordEntity>()
                        .eq(FollowUpRecordEntity::getDeletedFlag, 0)
                        .eq(FollowUpRecordEntity::getFollowUpType, type)
                );
                typeStats.put(getFollowUpTypeDesc(type), count);
            }
            statistics.put("typeStats", typeStats);

            // 满意度统计
            Double avgSatisfaction = followUpRecordDao.selectObjs(
                new LambdaQueryWrapper<FollowUpRecordEntity>()
                    .select(FollowUpRecordEntity::getSatisfactionScore)
                    .eq(FollowUpRecordEntity::getDeletedFlag, 0)
                    .isNotNull(FollowUpRecordEntity::getSatisfactionScore)
            ).stream()
                .mapToDouble(obj -> Double.parseDouble(obj.toString()))
                .average()
                .orElse(0.0);
            statistics.put("avgSatisfaction", Math.round(avgSatisfaction * 100.0) / 100.0);

            return ResponseDTO.ok(statistics);
        } catch (Exception e) {
            log.error("获取回访统计数据失败", e);
            return ResponseDTO.userErrorParam("获取统计数据失败");
        }
    }

    /**
     * 获取客户回访历史
     */
    public ResponseDTO<List<FollowUpRecordVO>> getCustomerFollowUpHistory(Long customerId) {
        List<FollowUpRecordVO> list = followUpRecordDao.getCustomerFollowUpHistory(customerId);
        return ResponseDTO.ok(list);
    }

    /**
     * 完成回访记录
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> complete(Long recordId, FollowUpRecordCompleteForm completeForm) {
        FollowUpRecordEntity entity = followUpRecordDao.selectById(recordId);
        if (entity == null) {
            return ResponseDTO.userErrorParam("回访记录不存在");
        }

        // 更新回访状态为已完成
        entity.setFollowUpStatus(2);
        entity.setActualTime(LocalDateTime.now());
        entity.setUpdateUserId(AdminRequestUtil.getRequestUserId());
        entity.setUpdateTime(LocalDateTime.now());

        // 如果提供了完成表单数据，则更新相关字段
        if (completeForm != null) {
            if (completeForm.getFollowUpContent() != null) {
                entity.setFollowUpContent(completeForm.getFollowUpContent());
            }
            if (completeForm.getFollowUpResult() != null) {
                entity.setFollowUpResult(completeForm.getFollowUpResult());
            }
            if (completeForm.getSatisfactionScore() != null) {
                entity.setSatisfactionScore(completeForm.getSatisfactionScore());
            }
            if (completeForm.getRemark() != null) {
                entity.setRemark(completeForm.getRemark());
            }
        }

        followUpRecordDao.updateById(entity);
        return ResponseDTO.ok();
    }

    /**
     * 批量完成回访记录
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> batchComplete(FollowUpRecordBatchCompleteForm batchCompleteForm) {
        if (CollectionUtils.isEmpty(batchCompleteForm.getRecordIdList())) {
            return ResponseDTO.userErrorParam("请选择要完成的回访记录");
        }

        for (Long recordId : batchCompleteForm.getRecordIdList()) {
            FollowUpRecordEntity entity = followUpRecordDao.selectById(recordId);
            if (entity == null) {
                continue; // 跳过不存在的记录
            }

            // 更新回访状态为已完成
            entity.setFollowUpStatus(2);
            entity.setActualTime(LocalDateTime.now());
            entity.setUpdateUserId(AdminRequestUtil.getRequestUserId());
            entity.setUpdateTime(LocalDateTime.now());

            // 更新批量完成的数据
            if (batchCompleteForm.getFollowUpContent() != null) {
                entity.setFollowUpContent(batchCompleteForm.getFollowUpContent());
            }
            if (batchCompleteForm.getFollowUpResult() != null) {
                entity.setFollowUpResult(batchCompleteForm.getFollowUpResult());
            }
            if (batchCompleteForm.getSatisfactionScore() != null) {
                entity.setSatisfactionScore(batchCompleteForm.getSatisfactionScore());
            }
            if (batchCompleteForm.getRemark() != null) {
                entity.setRemark(batchCompleteForm.getRemark());
            }

            followUpRecordDao.updateById(entity);
        }

        return ResponseDTO.ok();
    }

    /**
     * 获取回访类型描述
     */
    private String getFollowUpTypeDesc(Integer followUpType) {
        if (followUpType == null) {
            return "未知";
        }
        return switch (followUpType) {
            case 1 -> "用药跟进";
            case 2 -> "住院跟进";
            case 3 -> "疗程跟进";
            case 4 -> "次卡核销跟进";
            case 5 -> "复诊提醒跟进";
            case 6 -> "康复指导跟进";
            default -> "未知";
        };
    }

    /**
     * 获取回访方式描述
     */
    private String getFollowUpMethodDesc(Integer followUpMethod) {
        if (followUpMethod == null) {
            return "未知";
        }
        return switch (followUpMethod) {
            case 1 -> "电话";
            case 2 -> "微信";
            case 3 -> "短信";
            case 4 -> "邮件";
            case 5 -> "上门";
            default -> "未知";
        };
    }

    /**
     * 获取回访状态描述
     */
    private String getFollowUpStatusDesc(Integer followUpStatus) {
        if (followUpStatus == null) {
            return "未知";
        }
        return switch (followUpStatus) {
            case 1 -> "待回访";
            case 2 -> "已回访";
            case 3 -> "无法联系";
            case 4 -> "已取消";
            default -> "未知";
        };
    }
}

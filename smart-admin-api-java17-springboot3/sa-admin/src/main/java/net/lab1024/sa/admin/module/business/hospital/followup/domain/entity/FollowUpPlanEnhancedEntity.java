package net.lab1024.sa.admin.module.business.hospital.followup.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 回访计划增强版实体类
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@TableName("t_follow_up_plan_enhanced")
public class FollowUpPlanEnhancedEntity {

    /**
     * 计划ID
     */
    @TableId(type = IdType.AUTO)
    private Long planId;

    /**
     * 计划编号
     */
    private String planNo;

    /**
     * 计划名称
     */
    private String planName;

    /**
     * 计划描述
     */
    private String planDescription;

    /**
     * 计划类型：1-定期回访，2-节日回访，3-特殊回访，4-术后回访，5-用药跟进
     */
    private Integer planType;

    /**
     * 目标患者类型：1-所有已分配医生患者，2-指定医生患者，3-指定科室患者，4-指定标签患者
     */
    private Integer targetPatientType;

    /**
     * 筛选条件(JSON格式)：医生ID列表、科室ID列表、患者标签、时间范围等
     */
    private String filterConditions;

    /**
     * 目标医生ID列表(逗号分隔)
     */
    private String targetDoctorIds;

    /**
     * 目标科室ID列表(逗号分隔)
     */
    private String targetDepartmentIds;

    /**
     * 目标患者标签
     */
    private String targetPatientTags;

    /**
     * 到诊日期范围开始
     */
    private LocalDate visitDateRangeStart;

    /**
     * 到诊日期范围结束
     */
    private LocalDate visitDateRangeEnd;

    /**
     * 回访方式：1-电话，2-微信，3-短信，4-邮件，5-上门
     */
    private Integer followUpMethod;

    /**
     * 回访内容模板
     */
    private String followUpContentTemplate;

    /**
     * 回访问题列表(JSON格式)
     */
    private String followUpQuestions;

    /**
     * 计划开始日期
     */
    private LocalDate planStartDate;

    /**
     * 计划结束日期
     */
    private LocalDate planEndDate;

    /**
     * 执行频率：1-每日，2-每周，3-每月，4-每季度，5-每年，6-一次性
     */
    private Integer executionFrequency;

    /**
     * 执行时间
     */
    private LocalTime executionTime;

    /**
     * 执行日期(周几或月几号)
     */
    private String executionDays;

    /**
     * 到诊后延迟天数
     */
    private Integer delayDaysAfterVisit;

    /**
     * 优先级：1-高，2-中，3-低
     */
    private Integer priorityLevel;

    /**
     * 计划状态：1-草稿，2-执行中，3-已暂停，4-已完成，5-已取消
     */
    private Integer planStatus;

    /**
     * 是否自动生成回访记录：0-否，1-是
     */
    private Boolean autoGenerateRecords;

    /**
     * 负责人ID
     */
    private Long responsibleUserId;

    /**
     * 负责人姓名
     */
    private String responsibleUserName;

    /**
     * 所属部门ID
     */
    private Long departmentId;

    /**
     * 所属部门名称
     */
    private String departmentName;

    /**
     * 预期患者数量
     */
    private Integer expectedPatientCount;

    /**
     * 实际患者数量
     */
    private Integer actualPatientCount;

    /**
     * 已完成数量
     */
    private Integer completedCount;

    /**
     * 成功率(%)
     */
    private BigDecimal successRate;

    /**
     * 最后执行时间
     */
    private LocalDateTime lastExecutionTime;

    /**
     * 下次执行时间
     */
    private LocalDateTime nextExecutionTime;

    /**
     * 总执行次数
     */
    private Integer totalExecutions;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标志：0-未删除，1-已删除
     */
    private Boolean deletedFlag;

    /**
     * 创建人ID
     */
    private Long createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    private Long updateUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}

package net.lab1024.sa.admin.module.business.hospital.care.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.domain.PageParam;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDateTime;

/**
 * 关怀记录查询表单
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class CareRecordQueryForm extends PageParam {

    @Schema(description = "关怀计划ID")
    private Long carePlanId;

    @Schema(description = "客户ID")
    private Long customerId;

    @Schema(description = "客户姓名")
    @Length(max = 50, message = "客户姓名最多50字符")
    private String customerName;

    @Schema(description = "客户电话")
    @Length(max = 20, message = "客户电话最多20字符")
    private String customerPhone;

    @Schema(description = "病历ID")
    private Long recordId;

    @Schema(description = "关怀类型：1-出院关怀，2-复诊提醒，3-生日关怀，4-节日关怀，5-满意度调查")
    private Integer careType;

    @Schema(description = "关怀方式：1-电话，2-短信，3-微信，4-邮件，5-上门")
    private Integer careMethod;

    @Schema(description = "关怀模板ID")
    private Long templateId;

    @Schema(description = "关怀状态：1-待执行，2-执行中，3-已完成，4-已取消")
    private Integer careStatus;

    @Schema(description = "执行人ID")
    private Long executorId;

    @Schema(description = "执行人姓名")
    @Length(max = 50, message = "执行人姓名最多50字符")
    private String executorName;

    @Schema(description = "开始计划执行时间")
    private LocalDateTime startPlannedTime;

    @Schema(description = "结束计划执行时间")
    private LocalDateTime endPlannedTime;

    @Schema(description = "开始实际执行时间")
    private LocalDateTime startActualTime;

    @Schema(description = "结束实际执行时间")
    private LocalDateTime endActualTime;

    @Schema(description = "满意度评分：1-5分")
    private Integer satisfactionScore;

    @Schema(description = "是否需要跟进")
    private Boolean needFollowUp;

    @Schema(description = "关键字（客户姓名、电话、关怀内容）")
    @Length(max = 100, message = "关键字最多100字符")
    private String keywords;

    @Schema(description = "删除标识")
    private Boolean deletedFlag;
}

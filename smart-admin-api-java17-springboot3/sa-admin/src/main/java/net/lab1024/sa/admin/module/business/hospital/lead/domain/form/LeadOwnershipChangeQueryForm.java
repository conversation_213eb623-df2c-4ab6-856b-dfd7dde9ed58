package net.lab1024.sa.admin.module.business.hospital.lead.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.domain.PageParam;
import net.lab1024.sa.base.common.swagger.SchemaEnum;
import net.lab1024.sa.admin.module.business.hospital.lead.constant.LeadOwnershipChangeStatusEnum;

import java.time.LocalDateTime;

/**
 * 线索归属变更申请查询表单
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class LeadOwnershipChangeQueryForm extends PageParam {

    @Schema(description = "客户手机号")
    private String customerPhone;

    @Schema(description = "客户姓名")
    private String customerName;

    @Schema(description = "原归属人姓名")
    private String originalOwnerName;

    @Schema(description = "新归属人姓名")
    private String newOwnerName;

    @SchemaEnum(LeadOwnershipChangeStatusEnum.class)
    private Integer requestStatus;

    @Schema(description = "申请开始时间")
    private LocalDateTime createTimeStart;

    @Schema(description = "申请结束时间")
    private LocalDateTime createTimeEnd;

    @Schema(description = "审批开始时间")
    private LocalDateTime approveTimeStart;

    @Schema(description = "审批结束时间")
    private LocalDateTime approveTimeEnd;

    @Schema(description = "部门ID")
    private Long departmentId;

    @Schema(description = "是否只查看我的申请")
    private Boolean onlyMyRequests;

    @Schema(description = "是否只查看待我审批的")
    private Boolean onlyPendingApproval;

    @Schema(description = "请求用户ID（内部使用）", hidden = true)
    private Long requestUserId;
}

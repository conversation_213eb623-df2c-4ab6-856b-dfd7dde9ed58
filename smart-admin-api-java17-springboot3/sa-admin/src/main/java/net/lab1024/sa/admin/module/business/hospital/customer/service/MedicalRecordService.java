package net.lab1024.sa.admin.module.business.hospital.customer.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.hospital.customer.dao.CustomerDao;
import net.lab1024.sa.admin.module.business.hospital.customer.dao.MedicalRecordDao;
import net.lab1024.sa.admin.module.business.hospital.customer.domain.entity.CustomerEntity;
import net.lab1024.sa.admin.module.business.hospital.customer.domain.entity.MedicalRecordEntity;
import net.lab1024.sa.admin.module.business.hospital.customer.domain.form.MedicalRecordAddForm;
import net.lab1024.sa.admin.module.business.hospital.customer.domain.form.MedicalRecordQueryForm;
import net.lab1024.sa.admin.module.business.hospital.customer.domain.form.MedicalRecordUpdateForm;
import net.lab1024.sa.admin.module.business.hospital.customer.domain.vo.MedicalRecordVO;
import net.lab1024.sa.admin.util.AdminRequestUtil;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.module.support.datatracer.constant.DataTracerTypeEnum;
import net.lab1024.sa.base.module.support.datatracer.service.DataTracerService;
import net.lab1024.sa.admin.module.business.hospital.care.service.CareRecordService;
import net.lab1024.sa.admin.module.business.hospital.customer.constant.MedicalRecordStatusEnum;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 病历Service
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Slf4j
@Service
public class MedicalRecordService {

    @Resource
    private MedicalRecordDao medicalRecordDao;

    @Resource
    private CustomerDao customerDao;

    @Resource
    private DataTracerService dataTracerService;

    @Resource
    private CareRecordService careRecordService;

    /**
     * 分页查询病历
     */
    public ResponseDTO<PageResult<MedicalRecordVO>> queryPage(MedicalRecordQueryForm queryForm) {
        queryForm.setDeletedFlag(Boolean.FALSE);
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<MedicalRecordVO> recordList = medicalRecordDao.queryPage(page, queryForm);
        PageResult<MedicalRecordVO> pageResult = SmartPageUtil.convert2PageResult(page, recordList);
        return ResponseDTO.ok(pageResult);
    }

    /**
     * 添加病历
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> add(MedicalRecordAddForm addForm) {
        // 校验客户是否存在
        CustomerEntity customerEntity = customerDao.selectById(addForm.getCustomerId());
        if (customerEntity == null || customerEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("客户不存在");
        }

        // 检查该预约是否已有病历记录
        if (addForm.getAppointmentId() != null) {
            MedicalRecordEntity existRecord = medicalRecordDao.selectByAppointmentId(addForm.getAppointmentId());
            if (existRecord != null) {
                return ResponseDTO.userErrorParam("该预约已有病历记录");
            }
        }

        MedicalRecordEntity recordEntity = SmartBeanUtil.copy(addForm, MedicalRecordEntity.class);
        recordEntity.setDeletedFlag(Boolean.FALSE);
        recordEntity.setCreateTime(LocalDateTime.now());
        recordEntity.setUpdateTime(LocalDateTime.now());
        recordEntity.setCreateUserId(AdminRequestUtil.getRequestUserId());

        medicalRecordDao.insert(recordEntity);

        // 记录数据变动
        dataTracerService.insert(recordEntity.getRecordId(), DataTracerTypeEnum.MEDICAL_RECORD);

        return ResponseDTO.ok();
    }

    /**
     * 更新病历
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> update(MedicalRecordUpdateForm updateForm) {
        // 校验病历是否存在
        MedicalRecordEntity existRecord = medicalRecordDao.selectById(updateForm.getRecordId());
        if (existRecord == null || existRecord.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("病历不存在");
        }

        // 校验客户是否存在
        CustomerEntity customerEntity = customerDao.selectById(updateForm.getCustomerId());
        if (customerEntity == null || customerEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("客户不存在");
        }

        // 检查该客户是否已有重复的病历记录（根据就诊日期）
        // 这里可以根据业务需求添加重复检查逻辑

        // 获取原实体用于数据变动记录
        MedicalRecordEntity originEntity = existRecord;

        MedicalRecordEntity recordEntity = SmartBeanUtil.copy(updateForm, MedicalRecordEntity.class);
        recordEntity.setUpdateTime(LocalDateTime.now());
        medicalRecordDao.updateById(recordEntity);

        // 记录数据变动
        dataTracerService.update(recordEntity.getRecordId(), DataTracerTypeEnum.MEDICAL_RECORD, originEntity, recordEntity);

        return ResponseDTO.ok();
    }

    /**
     * 删除病历
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> delete(Long recordId) {
        MedicalRecordEntity recordEntity = medicalRecordDao.selectById(recordId);
        if (recordEntity == null || recordEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("病历不存在");
        }

        recordEntity.setDeletedFlag(Boolean.TRUE);
        recordEntity.setUpdateTime(LocalDateTime.now());
        medicalRecordDao.updateById(recordEntity);

        // 记录数据变动
        dataTracerService.delete(recordId, DataTracerTypeEnum.MEDICAL_RECORD);

        return ResponseDTO.ok();
    }

    /**
     * 批量删除病历
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> batchDelete(List<Long> recordIdList) {
        if (recordIdList == null || recordIdList.isEmpty()) {
            return ResponseDTO.userErrorParam("病历ID列表不能为空");
        }

        medicalRecordDao.batchUpdateDeleted(recordIdList, Boolean.TRUE);

        // 记录数据变动
        for (Long recordId : recordIdList) {
            dataTracerService.delete(recordId, DataTracerTypeEnum.MEDICAL_RECORD);
        }

        return ResponseDTO.ok();
    }

    /**
     * 根据客户ID查询病历
     */
    public ResponseDTO<List<MedicalRecordVO>> getByCustomerId(Long customerId) {
        // 校验客户是否存在
        CustomerEntity customerEntity = customerDao.selectById(customerId);
        if (customerEntity == null || customerEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("客户不存在");
        }

        List<MedicalRecordVO> recordList = medicalRecordDao.selectByCustomerId(customerId);
        return ResponseDTO.ok(recordList);
    }

    /**
     * 获取客户最近的病历
     */
    public ResponseDTO<List<MedicalRecordVO>> getRecentRecords(Long customerId, Integer limit) {
        // 校验客户是否存在
        CustomerEntity customerEntity = customerDao.selectById(customerId);
        if (customerEntity == null || customerEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("客户不存在");
        }

        if (limit == null || limit <= 0) {
            limit = 5;
        }

        List<MedicalRecordVO> recordList = medicalRecordDao.getRecentRecords(customerId, limit);
        return ResponseDTO.ok(recordList);
    }

    /**
     * 根据医生ID查询病历
     */
    public ResponseDTO<List<MedicalRecordVO>> getByDoctorId(Long doctorId, LocalDate startDate, LocalDate endDate) {
        List<MedicalRecordVO> recordList = medicalRecordDao.selectByDoctorId(doctorId, startDate, endDate);
        return ResponseDTO.ok(recordList);
    }

    /**
     * 获取需要复诊的病历
     */
    public ResponseDTO<List<MedicalRecordVO>> getFollowUpRecords(LocalDate date) {
        if (date == null) {
            date = LocalDate.now();
        }
        List<MedicalRecordVO> recordList = medicalRecordDao.getFollowUpRecords(date);
        return ResponseDTO.ok(recordList);
    }

    /**
     * 获取病历详情
     */
    public ResponseDTO<MedicalRecordVO> getDetail(Long recordId) {
        MedicalRecordEntity recordEntity = medicalRecordDao.selectById(recordId);
        if (recordEntity == null || recordEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("病历不存在");
        }

        // 这里可以通过查询获取更详细的信息，暂时简单转换
        MedicalRecordVO recordVO = SmartBeanUtil.copy(recordEntity, MedicalRecordVO.class);
        return ResponseDTO.ok(recordVO);
    }

    /**
     * 病历出院（更新状态为已出院，并自动创建关怀记录）
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> discharge(Long recordId) {
        MedicalRecordEntity recordEntity = medicalRecordDao.selectById(recordId);
        if (recordEntity == null || recordEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("病历不存在");
        }

        if (!MedicalRecordStatusEnum.COMPLETED.getValue().equals(recordEntity.getRecordStatus())) {
            return ResponseDTO.userErrorParam("只有已完成的病历才能出院");
        }

        // 获取原实体用于数据变动记录
        MedicalRecordEntity originEntity = SmartBeanUtil.copy(recordEntity, MedicalRecordEntity.class);

        // 更新病历状态为已出院
        recordEntity.setRecordStatus(MedicalRecordStatusEnum.DISCHARGED.getValue());
        recordEntity.setUpdateTime(LocalDateTime.now());
        medicalRecordDao.updateById(recordEntity);

        // 自动创建出院关怀记录
        ResponseDTO<String> careResult = careRecordService.createCareFromDischarge(recordId);
        if (!careResult.getOk()) {
            // 如果创建关怀记录失败，记录日志但不影响出院操作
            log.warn("病历出院后创建关怀记录失败: recordId={}, error={}", recordId, careResult.getMsg());
        }

        // 记录数据变动
        dataTracerService.update(recordId, DataTracerTypeEnum.MEDICAL_RECORD, originEntity, recordEntity);

        return ResponseDTO.ok();
    }

    /**
     * 更新病历状态
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> updateStatus(Long recordId, Integer recordStatus) {
        MedicalRecordEntity recordEntity = medicalRecordDao.selectById(recordId);
        if (recordEntity == null || recordEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("病历不存在");
        }

        // 获取原实体用于数据变动记录
        MedicalRecordEntity originEntity = SmartBeanUtil.copy(recordEntity, MedicalRecordEntity.class);

        recordEntity.setRecordStatus(recordStatus);
        recordEntity.setUpdateTime(LocalDateTime.now());
        medicalRecordDao.updateById(recordEntity);

        // 如果状态更新为已出院，自动创建关怀记录
        if (MedicalRecordStatusEnum.DISCHARGED.getValue().equals(recordStatus)) {
            ResponseDTO<String> careResult = careRecordService.createCareFromDischarge(recordId);
            if (!careResult.getOk()) {
                log.warn("病历状态更新为出院后创建关怀记录失败: recordId={}, error={}", recordId, careResult.getMsg());
            }
        }

        // 记录数据变动
        dataTracerService.update(recordId, DataTracerTypeEnum.MEDICAL_RECORD, originEntity, recordEntity);

        return ResponseDTO.ok();
    }
}

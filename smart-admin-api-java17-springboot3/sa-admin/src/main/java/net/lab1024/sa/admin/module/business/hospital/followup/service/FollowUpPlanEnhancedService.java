package net.lab1024.sa.admin.module.business.hospital.followup.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.hospital.followup.dao.FollowUpPlanEnhancedDao;
import net.lab1024.sa.admin.module.business.hospital.followup.dao.FollowUpPlanPatientDao;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.entity.FollowUpPlanEnhancedEntity;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.entity.FollowUpPlanPatientEntity;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.form.FollowUpPlanEnhancedAddForm;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.form.FollowUpPlanEnhancedQueryForm;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.form.FollowUpPlanEnhancedUpdateForm;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.vo.FollowUpPlanEnhancedVO;
import net.lab1024.sa.admin.module.business.hospital.followup.constant.FollowUpPlanStatusEnum;
import net.lab1024.sa.admin.module.business.hospital.followup.constant.ExecutionFrequencyEnum;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.VisitPatientVO;
import net.lab1024.sa.admin.module.system.employee.dao.EmployeeDao;
import net.lab1024.sa.admin.module.system.employee.domain.entity.EmployeeEntity;
import net.lab1024.sa.admin.module.system.department.dao.DepartmentDao;
import net.lab1024.sa.admin.module.system.department.domain.entity.DepartmentEntity;
import net.lab1024.sa.admin.util.AdminRequestUtil;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 回访计划增强版Service
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Slf4j
@Service
public class FollowUpPlanEnhancedService {

    @Resource
    private FollowUpPlanEnhancedDao followUpPlanEnhancedDao;

    @Resource
    private FollowUpPlanPatientDao followUpPlanPatientDao;

    @Resource
    private EmployeeDao employeeDao;

    @Resource
    private DepartmentDao departmentDao;

    @Resource
    private ObjectMapper objectMapper;

    /**
     * 分页查询回访计划
     */
    public ResponseDTO<PageResult<FollowUpPlanEnhancedVO>> queryPage(FollowUpPlanEnhancedQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<FollowUpPlanEnhancedVO> list = followUpPlanEnhancedDao.queryPage(page, queryForm);
        
        // 填充扩展信息
        fillExtendedInfo(list);
        
        PageResult<FollowUpPlanEnhancedVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return ResponseDTO.ok(pageResult);
    }

    /**
     * 添加回访计划
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> add(FollowUpPlanEnhancedAddForm addForm) {
        try {
            // 数据验证
            ResponseDTO<String> validateResult = validatePlanForm(addForm, null);
            if (!validateResult.getOk()) {
                return validateResult;
            }

            // 创建实体
            FollowUpPlanEnhancedEntity entity = SmartBeanUtil.copy(addForm, FollowUpPlanEnhancedEntity.class);
            
            // 生成计划编号
            String planNo = generatePlanNo();
            entity.setPlanNo(planNo);
            
            // 设置基本信息
            Long currentUserId = AdminRequestUtil.getRequestUserId();
            entity.setCreateUserId(currentUserId);
            entity.setCreateTime(LocalDateTime.now());
            entity.setDeletedFlag(false);
            entity.setPlanStatus(FollowUpPlanStatusEnum.DRAFT.getValue());
            
            // 处理目标医生和科室ID列表
            if (CollectionUtils.isNotEmpty(addForm.getTargetDoctorIds())) {
                entity.setTargetDoctorIds(addForm.getTargetDoctorIds().stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(",")));
            }
            
            if (CollectionUtils.isNotEmpty(addForm.getTargetDepartmentIds())) {
                entity.setTargetDepartmentIds(addForm.getTargetDepartmentIds().stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(",")));
            }
            
            // 处理回访问题列表
            if (CollectionUtils.isNotEmpty(addForm.getFollowUpQuestions())) {
                String questionsJson = objectMapper.writeValueAsString(addForm.getFollowUpQuestions());
                entity.setFollowUpQuestions(questionsJson);
            }
            
            // 设置负责人和部门信息
            setResponsibleUserInfo(entity, addForm.getResponsibleUserId());
            
            // 计算下次执行时间
            entity.setNextExecutionTime(calculateNextExecutionTime(entity));
            
            // 保存计划
            followUpPlanEnhancedDao.insert(entity);
            
            return ResponseDTO.ok();
        } catch (Exception e) {
            log.error("添加回访计划失败", e);
            return ResponseDTO.userErrorParam("添加失败：" + e.getMessage());
        }
    }

    /**
     * 更新回访计划
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> update(FollowUpPlanEnhancedUpdateForm updateForm) {
        try {
            // 检查计划是否存在
            FollowUpPlanEnhancedEntity existingEntity = followUpPlanEnhancedDao.selectById(updateForm.getPlanId());
            if (existingEntity == null || existingEntity.getDeletedFlag()) {
                return ResponseDTO.userErrorParam("回访计划不存在");
            }
            
            // 检查计划状态是否允许修改
            if (existingEntity.getPlanStatus().equals(FollowUpPlanStatusEnum.EXECUTING.getValue())) {
                return ResponseDTO.userErrorParam("执行中的计划不允许修改，请先暂停计划");
            }
            
            // 数据验证
            ResponseDTO<String> validateResult = validatePlanForm(updateForm, updateForm.getPlanId());
            if (!validateResult.getOk()) {
                return validateResult;
            }

            // 更新实体
            FollowUpPlanEnhancedEntity entity = SmartBeanUtil.copy(updateForm, FollowUpPlanEnhancedEntity.class);
            entity.setPlanId(updateForm.getPlanId());
            entity.setUpdateUserId(AdminRequestUtil.getRequestUserId());
            entity.setUpdateTime(LocalDateTime.now());
            
            // 处理目标医生和科室ID列表
            if (CollectionUtils.isNotEmpty(updateForm.getTargetDoctorIds())) {
                entity.setTargetDoctorIds(updateForm.getTargetDoctorIds().stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(",")));
            }
            
            if (CollectionUtils.isNotEmpty(updateForm.getTargetDepartmentIds())) {
                entity.setTargetDepartmentIds(updateForm.getTargetDepartmentIds().stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(",")));
            }
            
            // 处理回访问题列表
            if (CollectionUtils.isNotEmpty(updateForm.getFollowUpQuestions())) {
                String questionsJson = objectMapper.writeValueAsString(updateForm.getFollowUpQuestions());
                entity.setFollowUpQuestions(questionsJson);
            }
            
            // 设置负责人和部门信息
            setResponsibleUserInfo(entity, updateForm.getResponsibleUserId());
            
            // 重新计算下次执行时间
            entity.setNextExecutionTime(calculateNextExecutionTime(entity));
            
            // 更新计划
            followUpPlanEnhancedDao.updateById(entity);
            
            return ResponseDTO.ok();
        } catch (Exception e) {
            log.error("更新回访计划失败", e);
            return ResponseDTO.userErrorParam("更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除回访计划
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> delete(Long planId) {
        FollowUpPlanEnhancedEntity entity = followUpPlanEnhancedDao.selectById(planId);
        if (entity == null || entity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("回访计划不存在");
        }
        
        // 检查计划状态
        if (entity.getPlanStatus().equals(FollowUpPlanStatusEnum.EXECUTING.getValue())) {
            return ResponseDTO.userErrorParam("执行中的计划不允许删除，请先暂停计划");
        }
        
        // 软删除计划
        entity.setDeletedFlag(true);
        entity.setUpdateUserId(AdminRequestUtil.getRequestUserId());
        entity.setUpdateTime(LocalDateTime.now());
        followUpPlanEnhancedDao.updateById(entity);
        
        // 删除关联的患者记录
        followUpPlanPatientDao.deleteByPlanId(planId, AdminRequestUtil.getRequestUserId(), LocalDateTime.now());
        
        return ResponseDTO.ok();
    }

    /**
     * 获取回访计划详情
     */
    public ResponseDTO<FollowUpPlanEnhancedVO> detail(Long planId) {
        FollowUpPlanEnhancedEntity entity = followUpPlanEnhancedDao.selectById(planId);
        if (entity == null || entity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("回访计划不存在");
        }
        
        FollowUpPlanEnhancedVO vo = SmartBeanUtil.copy(entity, FollowUpPlanEnhancedVO.class);
        
        // 填充扩展信息
        fillExtendedInfo(List.of(vo));
        
        return ResponseDTO.ok(vo);
    }

    /**
     * 生成计划编号
     */
    private String generatePlanNo() {
        // 简化实现：使用时间戳 + 随机数
        // 实际项目中可能需要更复杂的编号规则
        long timestamp = System.currentTimeMillis();
        int random = (int) (Math.random() * 1000);
        return "FP" + timestamp + String.format("%03d", random);
    }

    /**
     * 数据验证
     */
    private ResponseDTO<String> validatePlanForm(FollowUpPlanEnhancedAddForm form, Long excludePlanId) {
        // 验证负责人是否存在
        EmployeeEntity employee = employeeDao.selectById(form.getResponsibleUserId());
        if (employee == null || employee.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("负责人不存在");
        }
        
        // 验证日期范围
        if (form.getPlanEndDate() != null && form.getPlanStartDate().isAfter(form.getPlanEndDate())) {
            return ResponseDTO.userErrorParam("计划开始日期不能晚于结束日期");
        }
        
        if (form.getVisitDateRangeEnd() != null && form.getVisitDateRangeStart() != null 
            && form.getVisitDateRangeStart().isAfter(form.getVisitDateRangeEnd())) {
            return ResponseDTO.userErrorParam("到诊日期范围开始不能晚于结束");
        }
        
        return ResponseDTO.ok();
    }

    /**
     * 设置负责人和部门信息
     */
    private void setResponsibleUserInfo(FollowUpPlanEnhancedEntity entity, Long responsibleUserId) {
        EmployeeEntity employee = employeeDao.selectById(responsibleUserId);
        if (employee != null) {
            entity.setResponsibleUserName(employee.getActualName());
            entity.setDepartmentId(employee.getDepartmentId());
            
            // 获取部门信息
            DepartmentEntity department = departmentDao.selectById(employee.getDepartmentId());
            if (department != null) {
                entity.setDepartmentName(department.getDepartmentName());
            }
        }
    }

    /**
     * 计算下次执行时间
     */
    private LocalDateTime calculateNextExecutionTime(FollowUpPlanEnhancedEntity entity) {
        LocalDateTime now = LocalDateTime.now();
        Integer frequency = entity.getExecutionFrequency();
        
        if (frequency == null) {
            return now.plusDays(1); // 默认明天
        }
        
        return switch (frequency) {
            case 1 -> now.plusDays(1);    // 每日
            case 2 -> now.plusWeeks(1);   // 每周
            case 3 -> now.plusMonths(1);  // 每月
            case 4 -> now.plusMonths(3);  // 每季度
            case 5 -> now.plusYears(1);   // 每年
            case 6 -> entity.getPlanStartDate().atStartOfDay(); // 一次性
            default -> now.plusDays(1);   // 默认每日
        };
    }

    /**
     * 填充扩展信息
     */
    private void fillExtendedInfo(List<FollowUpPlanEnhancedVO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        
        for (FollowUpPlanEnhancedVO vo : list) {
            // 填充枚举描述
            fillEnumDescriptions(vo);
            
            // 解析目标医生和科室ID列表
            parseTargetIds(vo);
            
            // 解析回访问题列表
            parseFollowUpQuestions(vo);
        }
    }

    /**
     * 填充枚举描述
     */
    private void fillEnumDescriptions(FollowUpPlanEnhancedVO vo) {
        // 这里可以根据枚举值填充对应的描述
        // 为了简化，这里只是示例
        vo.setPlanTypeName("计划类型" + vo.getPlanType());
        vo.setTargetPatientTypeName("目标患者类型" + vo.getTargetPatientType());
        vo.setFollowUpMethodName("回访方式" + vo.getFollowUpMethod());
        vo.setExecutionFrequencyName("执行频率" + vo.getExecutionFrequency());
        vo.setPlanStatusName("计划状态" + vo.getPlanStatus());
        vo.setPriorityLevelName("优先级" + vo.getPriorityLevel());
    }

    /**
     * 解析目标ID列表
     */
    private void parseTargetIds(FollowUpPlanEnhancedVO vo) {
        // 解析目标医生ID列表
        if (vo.getTargetDoctorIds() != null && !vo.getTargetDoctorIds().isEmpty()) {
            // 这里可以查询医生姓名列表
            vo.setTargetDoctorNames(new ArrayList<>());
        }
        
        // 解析目标科室ID列表
        if (vo.getTargetDepartmentIds() != null && !vo.getTargetDepartmentIds().isEmpty()) {
            // 这里可以查询科室名称列表
            vo.setTargetDepartmentNames(new ArrayList<>());
        }
    }

    /**
     * 解析回访问题列表
     */
    private void parseFollowUpQuestions(FollowUpPlanEnhancedVO vo) {
        try {
            if (vo.getFollowUpQuestions() != null && !vo.getFollowUpQuestions().isEmpty()) {
                // 这里可以解析JSON格式的问题列表
                vo.setFollowUpQuestions(new ArrayList<>());
            }
        } catch (Exception e) {
            log.warn("解析回访问题列表失败", e);
        }
    }

    /**
     * 启动回访计划
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> startPlan(Long planId) {
        FollowUpPlanEnhancedEntity entity = followUpPlanEnhancedDao.selectById(planId);
        if (entity == null || entity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("回访计划不存在");
        }

        if (!entity.getPlanStatus().equals(FollowUpPlanStatusEnum.DRAFT.getValue())
            && !entity.getPlanStatus().equals(FollowUpPlanStatusEnum.PAUSED.getValue())) {
            return ResponseDTO.userErrorParam("只有草稿或暂停状态的计划才能启动");
        }

        // 更新计划状态为执行中
        followUpPlanEnhancedDao.updatePlanStatus(planId, FollowUpPlanStatusEnum.EXECUTING.getValue(),
            AdminRequestUtil.getRequestUserId(), LocalDateTime.now());

        return ResponseDTO.ok();
    }

    /**
     * 暂停回访计划
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> pausePlan(Long planId) {
        FollowUpPlanEnhancedEntity entity = followUpPlanEnhancedDao.selectById(planId);
        if (entity == null || entity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("回访计划不存在");
        }

        if (!entity.getPlanStatus().equals(FollowUpPlanStatusEnum.EXECUTING.getValue())) {
            return ResponseDTO.userErrorParam("只有执行中的计划才能暂停");
        }

        // 更新计划状态为已暂停
        followUpPlanEnhancedDao.updatePlanStatus(planId, FollowUpPlanStatusEnum.PAUSED.getValue(),
            AdminRequestUtil.getRequestUserId(), LocalDateTime.now());

        return ResponseDTO.ok();
    }

    /**
     * 执行回访计划
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> executePlan(Long planId) {
        try {
            FollowUpPlanEnhancedEntity entity = followUpPlanEnhancedDao.selectById(planId);
            if (entity == null || entity.getDeletedFlag()) {
                return ResponseDTO.userErrorParam("回访计划不存在");
            }

            if (!entity.getPlanStatus().equals(FollowUpPlanStatusEnum.EXECUTING.getValue())) {
                return ResponseDTO.userErrorParam("只有执行中的计划才能执行");
            }

            // 根据筛选条件获取目标患者
            List<VisitPatientVO> targetPatients = getTargetPatients(entity);

            if (CollectionUtils.isEmpty(targetPatients)) {
                return ResponseDTO.userErrorParam("没有找到符合条件的患者");
            }

            // 生成患者关联记录
            int generatedCount = generatePatientRelations(entity, targetPatients);

            // 更新计划执行信息
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime nextExecutionTime = calculateNextExecutionTime(entity);
            int totalExecutions = entity.getTotalExecutions() != null ? entity.getTotalExecutions() + 1 : 1;

            followUpPlanEnhancedDao.updateExecutionInfo(planId, generatedCount, now, nextExecutionTime,
                totalExecutions, AdminRequestUtil.getRequestUserId(), now);

            return ResponseDTO.ok("计划执行成功，生成" + generatedCount + "条患者关联记录");
        } catch (Exception e) {
            log.error("执行回访计划失败", e);
            return ResponseDTO.userErrorParam("执行失败：" + e.getMessage());
        }
    }

    /**
     * 获取目标患者
     */
    private List<VisitPatientVO> getTargetPatients(FollowUpPlanEnhancedEntity entity) {
        // 解析目标医生ID列表
        List<Long> targetDoctorIds = null;
        if (entity.getTargetDoctorIds() != null && !entity.getTargetDoctorIds().isEmpty()) {
            targetDoctorIds = List.of(entity.getTargetDoctorIds().split(","))
                .stream()
                .map(Long::valueOf)
                .collect(Collectors.toList());
        }

        // 解析目标科室ID列表
        List<Long> targetDepartmentIds = null;
        if (entity.getTargetDepartmentIds() != null && !entity.getTargetDepartmentIds().isEmpty()) {
            targetDepartmentIds = List.of(entity.getTargetDepartmentIds().split(","))
                .stream()
                .map(Long::valueOf)
                .collect(Collectors.toList());
        }

        // 查询符合条件的患者
        return followUpPlanPatientDao.selectTargetPatients(
            entity.getTargetPatientType(),
            targetDoctorIds,
            targetDepartmentIds,
            entity.getTargetPatientTags(),
            entity.getVisitDateRangeStart(),
            entity.getVisitDateRangeEnd()
        );
    }

    /**
     * 生成患者关联记录
     */
    private int generatePatientRelations(FollowUpPlanEnhancedEntity entity, List<VisitPatientVO> targetPatients) {
        int generatedCount = 0;
        Long currentUserId = AdminRequestUtil.getRequestUserId();
        LocalDateTime now = LocalDateTime.now();

        for (VisitPatientVO patient : targetPatients) {
            // 检查患者是否已在计划中
            FollowUpPlanPatientEntity existingRelation = followUpPlanPatientDao.selectByPlanAndPatient(
                entity.getPlanId(), patient.getPatientId());

            if (existingRelation != null) {
                continue; // 跳过已存在的患者
            }

            // 创建患者关联记录
            FollowUpPlanPatientEntity relation = new FollowUpPlanPatientEntity();
            relation.setPlanId(entity.getPlanId());
            relation.setPatientId(patient.getPatientId());
            relation.setPatientName(patient.getPatientName());
            relation.setPatientPhone(patient.getPatientPhone());
            relation.setAssignedDoctorId(patient.getAssignedDoctorId());
            relation.setAssignedDoctorName(patient.getAssignedDoctorName());
            relation.setDepartmentId(patient.getDepartmentId());
            relation.setDepartmentName(patient.getDepartmentName());
            relation.setVisitDate(patient.getVisitDate());
            // 注意：VisitPatientVO中没有diagnosisStatus字段，这里设置默认值
            relation.setDiagnosisStatus(1); // 默认为待诊断状态
            relation.setExecutionStatus(1); // 待执行
            relation.setPriorityLevel(entity.getPriorityLevel());
            relation.setMaxRetryCount(3);
            relation.setRetryCount(0);
            relation.setDeletedFlag(false);
            relation.setCreateUserId(currentUserId);
            relation.setCreateTime(now);

            // 计算计划执行时间
            LocalDateTime scheduledTime = calculatePatientScheduledTime(entity, patient);
            relation.setScheduledTime(scheduledTime);

            followUpPlanPatientDao.insert(relation);
            generatedCount++;
        }

        return generatedCount;
    }

    /**
     * 计算患者的计划执行时间
     */
    private LocalDateTime calculatePatientScheduledTime(FollowUpPlanEnhancedEntity entity, VisitPatientVO patient) {
        LocalDateTime baseTime = LocalDateTime.now();

        // 如果设置了到诊后延迟天数
        if (entity.getDelayDaysAfterVisit() != null && entity.getDelayDaysAfterVisit() > 0
            && patient.getVisitDate() != null) {
            baseTime = patient.getVisitDate().atStartOfDay().plusDays(entity.getDelayDaysAfterVisit());
        }

        // 如果设置了执行时间
        if (entity.getExecutionTime() != null) {
            baseTime = baseTime.toLocalDate().atTime(entity.getExecutionTime());
        }

        return baseTime;
    }

    /**
     * 获取计划统计数据
     */
    public ResponseDTO<Map<String, Object>> getStatistics(FollowUpPlanEnhancedQueryForm queryForm) {
        try {
            Map<String, Object> statistics = new HashMap<>();

            // 总计划数
            Long totalCount = (long) followUpPlanEnhancedDao.selectCount(null);
            statistics.put("totalCount", totalCount);

            // 按状态统计
            Map<String, Object> statusStats = new HashMap<>();
            for (FollowUpPlanStatusEnum status : FollowUpPlanStatusEnum.values()) {
                Integer count = followUpPlanEnhancedDao.getPlanCountByStatus(status.getValue(), null, null);
                statusStats.put(status.getDesc(), count);
            }
            statistics.put("statusStats", statusStats);

            // 今日待执行计划
            List<FollowUpPlanEnhancedVO> todayPlans = followUpPlanEnhancedDao.getTodayPendingPlans(null, null);
            statistics.put("todayPendingCount", todayPlans.size());

            // 逾期计划
            List<FollowUpPlanEnhancedVO> overduePlans = followUpPlanEnhancedDao.getOverduePlans(null, null);
            statistics.put("overdueCount", overduePlans.size());

            return ResponseDTO.ok(statistics);
        } catch (Exception e) {
            log.error("获取计划统计数据失败", e);
            return ResponseDTO.userErrorParam("获取统计数据失败");
        }
    }

    /**
     * 导出回访计划
     */
    public ResponseDTO<String> export(FollowUpPlanEnhancedQueryForm queryForm) {
        try {
            List<FollowUpPlanEnhancedVO> list = followUpPlanEnhancedDao.queryPage(null, queryForm);

            if (CollectionUtils.isEmpty(list)) {
                return ResponseDTO.userErrorParam("没有可导出的数据");
            }

            // 填充扩展信息
            fillExtendedInfo(list);

            // 注意：实际的导出功能需要在Controller层实现
            return ResponseDTO.ok("数据准备完成，共" + list.size() + "条记录");

        } catch (Exception e) {
            log.error("导出回访计划失败", e);
            return ResponseDTO.userErrorParam("导出失败：" + e.getMessage());
        }
    }
}

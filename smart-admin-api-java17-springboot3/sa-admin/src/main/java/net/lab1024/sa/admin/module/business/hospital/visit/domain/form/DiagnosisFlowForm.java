package net.lab1024.sa.admin.module.business.hospital.visit.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

/**
 * 诊断流程完整表单
 *
 * <AUTHOR>
 * @Date 2025-07-29
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@Schema(description = "诊断流程完整表单")
public class DiagnosisFlowForm {

    @Schema(description = "患者ID")
    @NotNull(message = "患者ID不能为空")
    private Long patientId;

    @Schema(description = "诊断信息")
    @Valid
    @NotNull(message = "诊断信息不能为空")
    private DiagnosisAddForm diagnosisForm;

    @Schema(description = "开单信息")
    @Valid
    @NotNull(message = "开单信息不能为空")
    private PrescriptionAddForm prescriptionForm;

    @Schema(description = "收费信息")
    @Valid
    @NotNull(message = "收费信息不能为空")
    private ChargeAddForm chargeForm;

    @Schema(description = "分配助理ID")
    @NotNull(message = "分配助理不能为空")
    private Long assignedAssistantId;

    @Schema(description = "分配说明")
    private String assignNote;
}

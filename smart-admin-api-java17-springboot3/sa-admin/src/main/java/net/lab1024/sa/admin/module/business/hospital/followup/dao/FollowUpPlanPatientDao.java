package net.lab1024.sa.admin.module.business.hospital.followup.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.lab1024.sa.admin.module.business.hospital.followup.domain.entity.FollowUpPlanPatientEntity;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.VisitPatientVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 回访计划患者关联DAO
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Mapper
@Component
public interface FollowUpPlanPatientDao extends BaseMapper<FollowUpPlanPatientEntity> {

    /**
     * 根据计划ID查询关联的患者列表
     *
     * @param planId 计划ID
     * @param executionStatus 执行状态（可选）
     * @return 患者关联列表
     */
    List<FollowUpPlanPatientEntity> selectByPlanId(@Param("planId") Long planId, 
                                                   @Param("executionStatus") Integer executionStatus);

    /**
     * 根据患者ID查询关联的计划列表
     *
     * @param patientId 患者ID
     * @param executionStatus 执行状态（可选）
     * @return 计划关联列表
     */
    List<FollowUpPlanPatientEntity> selectByPatientId(@Param("patientId") Long patientId, 
                                                      @Param("executionStatus") Integer executionStatus);

    /**
     * 根据医生ID查询关联的患者列表
     *
     * @param doctorId 医生ID
     * @param planId 计划ID（可选）
     * @param executionStatus 执行状态（可选）
     * @return 患者关联列表
     */
    List<FollowUpPlanPatientEntity> selectByDoctorId(@Param("doctorId") Long doctorId, 
                                                     @Param("planId") Long planId,
                                                     @Param("executionStatus") Integer executionStatus);

    /**
     * 根据部门ID查询关联的患者列表
     *
     * @param departmentId 部门ID
     * @param planId 计划ID（可选）
     * @param executionStatus 执行状态（可选）
     * @return 患者关联列表
     */
    List<FollowUpPlanPatientEntity> selectByDepartmentId(@Param("departmentId") Long departmentId, 
                                                         @Param("planId") Long planId,
                                                         @Param("executionStatus") Integer executionStatus);

    /**
     * 更新执行状态
     *
     * @param relationId 关联ID
     * @param executionStatus 执行状态
     * @param actualExecutionTime 实际执行时间
     * @param followUpRecordId 回访记录ID
     * @param updateUserId 更新人ID
     * @param updateTime 更新时间
     * @return 更新行数
     */
    int updateExecutionStatus(@Param("relationId") Long relationId, 
                             @Param("executionStatus") Integer executionStatus,
                             @Param("actualExecutionTime") LocalDateTime actualExecutionTime,
                             @Param("followUpRecordId") Long followUpRecordId,
                             @Param("updateUserId") Long updateUserId, 
                             @Param("updateTime") LocalDateTime updateTime);

    /**
     * 批量更新执行状态
     *
     * @param relationIds 关联ID列表
     * @param executionStatus 执行状态
     * @param updateUserId 更新人ID
     * @param updateTime 更新时间
     * @return 更新行数
     */
    int batchUpdateExecutionStatus(@Param("relationIds") List<Long> relationIds, 
                                  @Param("executionStatus") Integer executionStatus,
                                  @Param("updateUserId") Long updateUserId, 
                                  @Param("updateTime") LocalDateTime updateTime);

    /**
     * 根据筛选条件查询符合条件的到诊患者
     *
     * @param targetPatientType 目标患者类型
     * @param targetDoctorIds 目标医生ID列表
     * @param targetDepartmentIds 目标科室ID列表
     * @param targetPatientTags 目标患者标签
     * @param visitDateRangeStart 到诊日期范围开始
     * @param visitDateRangeEnd 到诊日期范围结束
     * @return 符合条件的患者列表
     */
    List<VisitPatientVO> selectTargetPatients(@Param("targetPatientType") Integer targetPatientType,
                                             @Param("targetDoctorIds") List<Long> targetDoctorIds,
                                             @Param("targetDepartmentIds") List<Long> targetDepartmentIds,
                                             @Param("targetPatientTags") String targetPatientTags,
                                             @Param("visitDateRangeStart") LocalDate visitDateRangeStart,
                                             @Param("visitDateRangeEnd") LocalDate visitDateRangeEnd);

    /**
     * 检查患者是否已在计划中
     *
     * @param planId 计划ID
     * @param patientId 患者ID
     * @return 关联记录
     */
    FollowUpPlanPatientEntity selectByPlanAndPatient(@Param("planId") Long planId, @Param("patientId") Long patientId);

    /**
     * 获取待执行的患者关联列表
     *
     * @param scheduledTimeBefore 计划执行时间之前
     * @param limit 限制数量
     * @return 待执行的患者关联列表
     */
    List<FollowUpPlanPatientEntity> getPendingExecutions(@Param("scheduledTimeBefore") LocalDateTime scheduledTimeBefore, 
                                                         @Param("limit") Integer limit);

    /**
     * 统计计划的执行情况
     *
     * @param planId 计划ID
     * @return 执行统计信息
     */
    FollowUpPlanPatientEntity getExecutionStatistics(@Param("planId") Long planId);

    /**
     * 删除计划的所有患者关联
     *
     * @param planId 计划ID
     * @param updateUserId 更新人ID
     * @param updateTime 更新时间
     * @return 删除行数
     */
    int deleteByPlanId(@Param("planId") Long planId, 
                      @Param("updateUserId") Long updateUserId, 
                      @Param("updateTime") LocalDateTime updateTime);
}

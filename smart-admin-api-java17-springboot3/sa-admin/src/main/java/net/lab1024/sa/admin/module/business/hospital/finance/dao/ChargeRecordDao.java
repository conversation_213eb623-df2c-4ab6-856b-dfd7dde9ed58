package net.lab1024.sa.admin.module.business.hospital.finance.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.hospital.finance.domain.entity.ChargeRecordEntity;
import net.lab1024.sa.admin.module.business.hospital.finance.domain.form.ChargeRecordQueryForm;
import net.lab1024.sa.admin.module.business.hospital.finance.domain.vo.ChargeRecordVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 收费记录DAO
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Mapper
@Component
public interface ChargeRecordDao extends BaseMapper<ChargeRecordEntity> {

    /**
     * 分页查询收费记录
     *
     * @param page 分页参数
     * @param queryForm 查询条件
     * @return 收费记录列表
     */
    List<ChargeRecordVO> queryPage(Page page, @Param("query") ChargeRecordQueryForm queryForm);

    /**
     * 根据收费单号查询收费记录
     *
     * @param chargeNo 收费单号
     * @return 收费记录实体
     */
    ChargeRecordEntity selectByChargeNo(@Param("chargeNo") String chargeNo);

    /**
     * 根据预约ID查询收费记录
     *
     * @param appointmentId 预约ID
     * @return 收费记录列表
     */
    List<ChargeRecordEntity> selectByAppointmentId(@Param("appointmentId") Long appointmentId);

    /**
     * 根据客户ID查询收费记录
     *
     * @param customerId 客户ID
     * @return 收费记录列表
     */
    List<ChargeRecordVO> selectByCustomerId(@Param("customerId") Long customerId);

    /**
     * 根据病历ID查询收费记录
     *
     * @param recordId 病历ID
     * @return 收费记录列表
     */
    List<ChargeRecordEntity> selectByRecordId(@Param("recordId") Long recordId);

    /**
     * 更新收费状态
     *
     * @param chargeId 收费记录ID
     * @param chargeStatus 收费状态
     * @param paymentMethod 支付方式
     * @param paymentDetails 支付详情
     */
    void updateChargeStatus(@Param("chargeId") Long chargeId, 
                           @Param("chargeStatus") Integer chargeStatus,
                           @Param("paymentMethod") Integer paymentMethod,
                           @Param("paymentDetails") String paymentDetails);

    /**
     * 更新发票信息
     *
     * @param chargeId 收费记录ID
     * @param invoiceNo 发票号码
     * @param invoiceFlag 是否开具发票
     */
    void updateInvoiceInfo(@Param("chargeId") Long chargeId,
                          @Param("invoiceNo") String invoiceNo,
                          @Param("invoiceFlag") Boolean invoiceFlag);

    /**
     * 获取收费统计信息
     *
     * @param customerId 客户ID
     * @return 统计信息
     */
    ChargeRecordVO getChargeStatistics(@Param("customerId") Long customerId);
}

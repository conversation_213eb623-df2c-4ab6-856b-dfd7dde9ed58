package net.lab1024.sa.admin.module.business.hospital.project.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.hospital.project.constant.ProjectStatusEnum;
import net.lab1024.sa.admin.module.business.hospital.project.dao.ProjectDao;
import net.lab1024.sa.admin.module.business.hospital.project.domain.entity.ProjectEntity;
import net.lab1024.sa.admin.module.business.hospital.project.domain.form.ProjectAddForm;
import net.lab1024.sa.admin.module.business.hospital.project.domain.form.ProjectQueryForm;
import net.lab1024.sa.admin.module.business.hospital.project.domain.form.ProjectUpdateForm;
import net.lab1024.sa.admin.module.business.hospital.project.domain.vo.ProjectVO;
import net.lab1024.sa.admin.util.AdminRequestUtil;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.module.support.datatracer.constant.DataTracerTypeEnum;
import net.lab1024.sa.base.module.support.datatracer.service.DataTracerService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 项目Service
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Slf4j
@Service
public class ProjectService {

    @Resource
    private ProjectDao projectDao;

    @Resource
    private DataTracerService dataTracerService;

    /**
     * 分页查询项目
     */
    public ResponseDTO<PageResult<ProjectVO>> queryPage(ProjectQueryForm queryForm) {
        queryForm.setDeletedFlag(Boolean.FALSE);
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<ProjectVO> projectList = projectDao.queryPage(page, queryForm);
        PageResult<ProjectVO> pageResult = SmartPageUtil.convert2PageResult(page, projectList);
        return ResponseDTO.ok(pageResult);
    }

    /**
     * 添加项目
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> add(ProjectAddForm addForm) {
        // 校验项目名称是否重复
        ProjectEntity existProject = projectDao.selectByName(addForm.getProjectName(), null);
        if (existProject != null) {
            return ResponseDTO.userErrorParam("项目名称已存在");
        }

        ProjectEntity projectEntity = SmartBeanUtil.copy(addForm, ProjectEntity.class);
        projectEntity.setDeletedFlag(Boolean.FALSE);
        projectEntity.setCreateTime(LocalDateTime.now());
        projectEntity.setUpdateTime(LocalDateTime.now());
        projectEntity.setCreateUserId(AdminRequestUtil.getRequestUserId());

        // 如果没有设置状态，默认为启用
        if (projectEntity.getProjectStatus() == null) {
            projectEntity.setProjectStatus(ProjectStatusEnum.ENABLED.getValue());
        }

        // 如果没有设置排序，默认为0
        if (projectEntity.getSort() == null) {
            projectEntity.setSort(0);
        }

        projectDao.insert(projectEntity);

        // 记录数据变动
        dataTracerService.insert(projectEntity.getProjectId(), DataTracerTypeEnum.PROJECT);

        return ResponseDTO.ok();
    }

    /**
     * 更新项目
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> update(ProjectUpdateForm updateForm) {
        // 校验项目是否存在
        ProjectEntity existProject = projectDao.selectById(updateForm.getProjectId());
        if (existProject == null || existProject.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("项目不存在");
        }

        // 校验项目名称是否重复
        ProjectEntity duplicateProject = projectDao.selectByName(updateForm.getProjectName(), updateForm.getProjectId());
        if (duplicateProject != null) {
            return ResponseDTO.userErrorParam("项目名称已存在");
        }

        ProjectEntity projectEntity = SmartBeanUtil.copy(updateForm, ProjectEntity.class);
        projectEntity.setUpdateTime(LocalDateTime.now());
        // 获取原实体用于数据变动记录
        ProjectEntity originEntity = projectDao.selectById(projectEntity.getProjectId());

        projectDao.updateById(projectEntity);

        // 记录数据变动
        dataTracerService.update(projectEntity.getProjectId(), DataTracerTypeEnum.PROJECT, originEntity, projectEntity);

        return ResponseDTO.ok();
    }

    /**
     * 删除项目
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> delete(Long projectId) {
        ProjectEntity projectEntity = projectDao.selectById(projectId);
        if (projectEntity == null || projectEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("项目不存在");
        }

        // TODO: 检查是否有关联的预约记录
        // 如果有关联的预约记录，不允许删除

        projectEntity.setDeletedFlag(Boolean.TRUE);
        projectEntity.setUpdateTime(LocalDateTime.now());
        projectDao.updateById(projectEntity);

        // 记录数据变动
        dataTracerService.delete(projectId, DataTracerTypeEnum.PROJECT);

        return ResponseDTO.ok();
    }

    /**
     * 批量删除项目
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> batchDelete(List<Long> projectIdList) {
        if (projectIdList == null || projectIdList.isEmpty()) {
            return ResponseDTO.userErrorParam("项目ID列表不能为空");
        }

        // TODO: 检查是否有关联的预约记录

        projectDao.batchUpdateDeleted(projectIdList, Boolean.TRUE);

        // 记录数据变动
        for (Long projectId : projectIdList) {
            dataTracerService.delete(projectId, DataTracerTypeEnum.PROJECT);
        }

        return ResponseDTO.ok();
    }

    /**
     * 启用/禁用项目
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> updateStatus(Long projectId, Integer projectStatus) {
        ProjectEntity projectEntity = projectDao.selectById(projectId);
        if (projectEntity == null || projectEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("项目不存在");
        }

        if (!ProjectStatusEnum.ENABLED.getValue().equals(projectStatus) && 
            !ProjectStatusEnum.DISABLED.getValue().equals(projectStatus)) {
            return ResponseDTO.userErrorParam("项目状态错误");
        }

        // 获取原实体用于数据变动记录
        ProjectEntity originEntity = projectDao.selectById(projectId);

        projectDao.updateStatus(projectId, projectStatus);

        // 获取更新后的实体
        ProjectEntity updatedEntity = projectDao.selectById(projectId);

        // 记录数据变动
        dataTracerService.update(projectId, DataTracerTypeEnum.PROJECT, originEntity, updatedEntity);

        return ResponseDTO.ok();
    }

    /**
     * 查询所有启用的项目
     */
    public ResponseDTO<List<ProjectVO>> queryAllEnabled() {
        List<ProjectVO> projectList = projectDao.selectAllEnabled();
        return ResponseDTO.ok(projectList);
    }

    /**
     * 根据分类查询项目
     */
    public ResponseDTO<List<ProjectVO>> queryByCategory(String projectCategory) {
        List<ProjectVO> projectList = projectDao.selectByCategory(projectCategory);
        return ResponseDTO.ok(projectList);
    }
}

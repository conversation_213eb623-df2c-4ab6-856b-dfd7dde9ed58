package net.lab1024.sa.admin.module.business.hospital.lead.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.swagger.SchemaEnum;
import net.lab1024.sa.admin.module.business.hospital.lead.constant.LeadOwnershipChangeTypeEnum;

import java.time.LocalDateTime;

/**
 * 线索归属变更历史VO
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class LeadOwnershipChangeHistoryVO {

    @Schema(description = "历史记录ID")
    private Long historyId;

    @Schema(description = "线索ID")
    private Long leadId;

    @Schema(description = "关联的申请ID")
    private Long requestId;

    @SchemaEnum(LeadOwnershipChangeTypeEnum.class)
    private Integer changeType;

    @Schema(description = "变更类型描述")
    private String changeTypeDesc;

    @Schema(description = "原归属人ID")
    private Long originalOwnerId;

    @Schema(description = "原归属人姓名")
    private String originalOwnerName;

    @Schema(description = "新归属人ID")
    private Long newOwnerId;

    @Schema(description = "新归属人姓名")
    private String newOwnerName;

    @Schema(description = "变更原因")
    private String changeReason;

    @Schema(description = "操作人ID")
    private Long operateUserId;

    @Schema(description = "操作人姓名")
    private String operateUserName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}

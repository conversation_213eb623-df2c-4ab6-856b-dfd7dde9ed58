package net.lab1024.sa.admin.module.business.hospital.care.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 关怀计划实体类
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@TableName("t_care_plan")
public class CarePlanEntity {

    /**
     * 关怀计划ID
     */
    @TableId(type = IdType.AUTO)
    private Long planId;

    /**
     * 计划名称
     */
    private String planName;

    /**
     * 计划描述
     */
    private String planDescription;

    /**
     * 关怀类型：1-出院关怀，2-复诊提醒，3-生日关怀，4-节日关怀，5-满意度调查
     */
    private Integer careType;

    /**
     * 触发条件（JSON格式）
     */
    private String triggerConditions;

    /**
     * 执行时机：1-立即执行，2-延迟执行，3-定时执行
     */
    private Integer executeTiming;

    /**
     * 延迟时间（分钟）
     */
    private Integer delayMinutes;

    /**
     * 定时执行时间
     */
    private String scheduleTime;

    /**
     * 关怀方式：1-电话，2-短信，3-微信，4-邮件，5-上门
     */
    private Integer careMethod;

    /**
     * 关怀模板ID
     */
    private Long templateId;

    /**
     * 关怀模板名称
     */
    private String templateName;

    /**
     * 目标客户群体（JSON格式）
     */
    private String targetCustomers;

    /**
     * 执行部门ID
     */
    private Long executeDepartmentId;

    /**
     * 执行部门名称
     */
    private String executeDepartmentName;

    /**
     * 默认执行人ID
     */
    private Long defaultExecutorId;

    /**
     * 默认执行人姓名
     */
    private String defaultExecutorName;

    /**
     * 是否自动执行
     */
    private Boolean autoExecuteFlag;

    /**
     * 最大重试次数
     */
    private Integer maxRetryCount;

    /**
     * 重试间隔（分钟）
     */
    private Integer retryInterval;

    /**
     * 计划状态：1-启用，0-禁用
     */
    private Integer planStatus;

    /**
     * 优先级：1-低，2-中，3-高
     */
    private Integer priority;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标识
     */
    private Boolean deletedFlag;

    /**
     * 创建人ID
     */
    private Long createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    private Long updateUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}

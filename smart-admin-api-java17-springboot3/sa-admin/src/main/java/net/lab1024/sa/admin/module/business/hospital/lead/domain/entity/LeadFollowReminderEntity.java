package net.lab1024.sa.admin.module.business.hospital.lead.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 线索跟进提醒实体
 * 
 * <AUTHOR>
 * @Date 2025-07-22
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@TableName("t_lead_follow_reminder")
public class LeadFollowReminderEntity {

    /**
     * 提醒ID
     */
    @TableId(type = IdType.AUTO)
    private Long reminderId;

    /**
     * 线索ID
     */
    private Long leadId;

    /**
     * 跟进记录ID
     */
    private Long followId;

    /**
     * 提醒时间
     */
    private LocalDateTime reminderTime;

    /**
     * 提醒方式：1系统通知 2短信 3邮件
     */
    private Integer reminderType;

    /**
     * 提醒状态：1待提醒 2已发送 3已取消 4已完成
     */
    private Integer reminderStatus;

    /**
     * 处理时间
     */
    private LocalDateTime processedTime;

    /**
     * 处理结果
     */
    private String processedResult;

    /**
     * 是否已发送
     */
    private Boolean isSent;

    /**
     * 实际发送时间
     */
    private LocalDateTime sentTime;

    /**
     * 发送方式
     */
    private String sendMethod;

    /**
     * 备注
     */
    private String remark;

    /**
     * 提醒标题
     */
    private String reminderTitle;

    /**
     * 提醒内容
     */
    private String reminderContent;

    /**
     * 分配员工ID
     */
    private Long assignedEmployeeId;

    /**
     * 分配员工姓名
     */
    private String assignedEmployeeName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    private Long createUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人ID
     */
    private Long updateUserId;

    /**
     * 删除标志
     */
    private Boolean deletedFlag;
}

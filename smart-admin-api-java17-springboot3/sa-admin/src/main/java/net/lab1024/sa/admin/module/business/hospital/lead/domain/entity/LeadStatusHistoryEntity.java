package net.lab1024.sa.admin.module.business.hospital.lead.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 线索状态变更历史实体
 * 
 * <AUTHOR>
 * @Date 2025-07-22
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@TableName("t_lead_status_history")
public class LeadStatusHistoryEntity {

    /**
     * 历史记录ID
     */
    @TableId(type = IdType.AUTO)
    private Long historyId;

    /**
     * 线索ID
     */
    private Long leadId;

    /**
     * 原状态
     */
    private Integer fromStatus;

    /**
     * 新状态
     */
    private Integer toStatus;

    /**
     * 触发类型
     * FOLLOW_ADD: 添加跟进记录
     * APPOINTMENT_CREATE: 创建预约
     * ARRIVAL_CONFIRM: 确认到院
     * CONVERSION_COMPLETE: 完成转化
     * MANUAL_CLOSE: 手动关闭
     * MANUAL: 手动操作
     */
    private String triggerType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String createName;
}

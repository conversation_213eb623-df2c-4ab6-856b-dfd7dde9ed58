package net.lab1024.sa.admin.module.business.hospital.lead.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 线索状态枚举
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Getter
@AllArgsConstructor
public enum LeadStatusEnum implements BaseEnum {

    /**
     * 新线索
     */
    NEW(1, "新线索"),

    /**
     * 跟进中
     */
    FOLLOWING(2, "跟进中"),

    /**
     * 已预约
     */
    APPOINTED(3, "已预约"),

    /**
     * 已到院
     */
    ARRIVED(4, "已到院"),

    /**
     * 已转化
     */
    CONVERTED(5, "已转化"),

    /**
     * 爽约
     */
    NO_SHOW(6, "爽约"),

    /**
     * 已关闭
     */
    CLOSED(7, "已关闭");

    private final Integer value;

    private final String desc;

    /**
     * 根据值获取枚举
     */
    public static LeadStatusEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (LeadStatusEnum enumItem : values()) {
            if (enumItem.getValue().equals(value)) {
                return enumItem;
            }
        }
        return null;
    }
}

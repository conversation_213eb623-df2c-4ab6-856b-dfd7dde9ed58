package net.lab1024.sa.admin.module.business.hospital.customer.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 付费状态枚举
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Getter
@AllArgsConstructor
public enum PaymentStatusEnum implements BaseEnum {

    /**
     * 未付费
     */
    UNPAID(1, "未付费"),

    /**
     * 部分付费
     */
    PARTIAL_PAID(2, "部分付费"),

    /**
     * 已付费
     */
    PAID(3, "已付费");

    private final Integer value;

    private final String desc;
}

package net.lab1024.sa.admin.module.business.hospital.followup.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.base.common.domain.PageParam;
import net.lab1024.sa.base.common.swagger.SchemaEnum;
import net.lab1024.sa.base.common.validator.enumeration.CheckEnum;
import net.lab1024.sa.admin.module.business.hospital.followup.constant.FollowUpPlanTypeEnum;
import net.lab1024.sa.admin.module.business.hospital.followup.constant.TargetPatientTypeEnum;
import net.lab1024.sa.admin.module.business.hospital.followup.constant.FollowUpMethodEnum;
import net.lab1024.sa.admin.module.business.hospital.followup.constant.ExecutionFrequencyEnum;
import net.lab1024.sa.admin.module.business.hospital.followup.constant.FollowUpPlanStatusEnum;

import java.time.LocalDate;

/**
 * 回访计划增强版查询表单
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@Schema(description = "回访计划增强版查询表单")
public class FollowUpPlanEnhancedQueryForm extends PageParam {

    @Schema(description = "计划名称")
    private String planName;

    @Schema(description = "计划类型：1-定期回访，2-节日回访，3-特殊回访，4-术后回访，5-用药跟进")
    @SchemaEnum(FollowUpPlanTypeEnum.class)
    @CheckEnum(value = FollowUpPlanTypeEnum.class, message = "计划类型错误", required = false)
    private Integer planType;

    @Schema(description = "目标患者类型：1-所有已分配医生患者，2-指定医生患者，3-指定科室患者，4-指定标签患者")
    @SchemaEnum(TargetPatientTypeEnum.class)
    private Integer targetPatientType;

    @Schema(description = "回访方式：1-电话，2-微信，3-短信，4-邮件，5-上门")
    @SchemaEnum(FollowUpMethodEnum.class)
    private Integer followUpMethod;

    @Schema(description = "执行频率：1-每日，2-每周，3-每月，4-每季度，5-每年，6-一次性")
    @SchemaEnum(ExecutionFrequencyEnum.class)
    private Integer executionFrequency;

    @Schema(description = "计划状态：1-草稿，2-执行中，3-已暂停，4-已完成，5-已取消")
    @SchemaEnum(FollowUpPlanStatusEnum.class)
    private Integer planStatus;

    @Schema(description = "优先级：1-高，2-中，3-低")
    private Integer priorityLevel;

    @Schema(description = "负责人姓名")
    private String responsibleUserName;

    @Schema(description = "部门ID")
    private Long departmentId;

    @Schema(description = "计划开始日期-开始")
    private LocalDate planStartDateBegin;

    @Schema(description = "计划开始日期-结束")
    private LocalDate planStartDateEnd;

    @Schema(description = "计划结束日期-开始")
    private LocalDate planEndDateBegin;

    @Schema(description = "计划结束日期-结束")
    private LocalDate planEndDateEnd;

    @Schema(description = "下次执行时间-开始")
    private LocalDate nextExecutionTimeBegin;

    @Schema(description = "下次执行时间-结束")
    private LocalDate nextExecutionTimeEnd;

    @Schema(description = "创建时间-开始")
    private LocalDate createTimeBegin;

    @Schema(description = "创建时间-结束")
    private LocalDate createTimeEnd;
}

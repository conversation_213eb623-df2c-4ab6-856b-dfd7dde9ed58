package net.lab1024.sa.admin.module.business.hospital.lead.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.hospital.lead.constant.FollowResultEnum;
import net.lab1024.sa.admin.module.business.hospital.lead.constant.LeadStatusEnum;
import net.lab1024.sa.admin.module.business.hospital.lead.dao.LeadDao;
import net.lab1024.sa.admin.module.business.hospital.lead.dao.LeadFollowDao;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.entity.LeadEntity;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.entity.LeadFollowEntity;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.form.LeadFollowAddForm;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.form.LeadFollowEnhancedAddForm;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.form.LeadFollowUpdateForm;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadFollowVO;
import net.lab1024.sa.admin.module.system.employee.dao.EmployeeDao;
import net.lab1024.sa.admin.module.system.employee.domain.entity.EmployeeEntity;
import net.lab1024.sa.admin.util.AdminRequestUtil;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartEnumUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.module.support.datatracer.constant.DataTracerTypeEnum;
import net.lab1024.sa.base.module.support.datatracer.service.DataTracerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 线索跟进Service
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Slf4j
@Service
public class LeadFollowService {

    @Resource
    private LeadFollowDao leadFollowDao;

    @Resource
    private LeadDao leadDao;

    @Resource
    private DataTracerService dataTracerService;

    @Resource
    private EmployeeDao employeeDao;

    @Autowired
    private LeadStatusFlowService leadStatusFlowService;

    @Autowired
    private LeadFollowResultProcessor followResultProcessor;

    @Autowired
    private LeadService leadService;

    /**
     * 根据线索ID查询跟进记录
     */
    public ResponseDTO<List<LeadFollowVO>> queryByLeadId(Long leadId) {
        // 检查用户是否有权限访问该线索
        Long currentUserId = AdminRequestUtil.getRequestUserId();
        LeadEntity leadEntity = leadDao.selectById(leadId);
        if (!hasLeadPermission(leadEntity, currentUserId)) {
            return ResponseDTO.userErrorParam("无权限查看该线索的跟进记录");
        }

        List<LeadFollowVO> followList = leadFollowDao.selectByLeadId(leadId);
        return ResponseDTO.ok(followList);
    }

    /**
     * 分页查询跟进记录
     */
    public ResponseDTO<PageResult<LeadFollowVO>> queryPage(Long leadId, Long employeeId, Integer pageNum, Integer pageSize) {
        Page<LeadFollowVO> page = new Page<>(pageNum, pageSize);
        List<LeadFollowVO> followList = leadFollowDao.queryPage(page, leadId, employeeId);
        PageResult<LeadFollowVO> pageResult = SmartPageUtil.convert2PageResult(page, followList);
        return ResponseDTO.ok(pageResult);
    }

    /**
     * 添加跟进记录
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> add(LeadFollowAddForm addForm) {
        // 校验线索是否存在
        LeadEntity leadEntity = leadDao.selectById(addForm.getLeadId());
        if (leadEntity == null || leadEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("线索不存在");
        }

        // 检查用户是否有权限访问该线索
        Long currentUserId = AdminRequestUtil.getRequestUserId();
        if (!hasLeadPermission(leadEntity, currentUserId)) {
            return ResponseDTO.userErrorParam("无权限对该线索进行跟进");
        }

        // 校验线索是否已转化或关闭
        if (LeadStatusEnum.CONVERTED.getValue().equals(leadEntity.getLeadStatus()) ||
            LeadStatusEnum.CLOSED.getValue().equals(leadEntity.getLeadStatus())) {
            return ResponseDTO.userErrorParam("线索已转化或关闭，无法添加跟进记录");
        }

        LeadFollowEntity followEntity = SmartBeanUtil.copy(addForm, LeadFollowEntity.class);

        // 转换跟进结果：将Integer类型转换为对应的中文字符串
        if (addForm.getFollowResult() != null) {
            FollowResultEnum resultEnum = SmartEnumUtil.getEnumByValue(addForm.getFollowResult(), FollowResultEnum.class);
            if (resultEnum != null) {
                followEntity.setFollowResult(resultEnum.getDesc());
            }
        }

        // 设置用户信息
        if (currentUserId != null) {
            followEntity.setFollowUserId(currentUserId);
            followEntity.setFollowUserName(AdminRequestUtil.getRequestUser().getActualName());
            followEntity.setCreateUserId(currentUserId);
            followEntity.setUpdateUserId(currentUserId);
        } else {
            // 如果没有登录用户，设置为默认值（测试环境）
            followEntity.setFollowUserId(1L);
            followEntity.setFollowUserName("系统用户");
            followEntity.setCreateUserId(1L);
            followEntity.setUpdateUserId(1L);
        }

        followEntity.setDeletedFlag(Boolean.FALSE);
        followEntity.setCreateTime(LocalDateTime.now());
        followEntity.setUpdateTime(LocalDateTime.now());

        leadFollowDao.insert(followEntity);

        // 自动流转线索状态为跟进中
        if (LeadStatusEnum.NEW.getValue().equals(leadEntity.getLeadStatus())) {
            leadStatusFlowService.onFollowAdded(addForm.getLeadId());
        }

        // 记录数据变动
        dataTracerService.insert(followEntity.getFollowId(), DataTracerTypeEnum.LEAD_FOLLOW);

        return ResponseDTO.ok();
    }

    /**
     * 增强的跟进记录添加（支持后续操作）
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> addEnhanced(LeadFollowEnhancedAddForm enhancedForm) {
        // 1. 验证表单数据
        ResponseDTO<String> validationResult = followResultProcessor.validateFollowForm(enhancedForm);
        if (!validationResult.getOk()) {
            return validationResult;
        }

        // 2. 校验线索是否存在
        LeadEntity leadEntity = leadDao.selectById(enhancedForm.getLeadId());
        if (leadEntity == null || leadEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("线索不存在");
        }

        // 2.5. 检查用户是否有权限访问该线索
        Long currentUserId = AdminRequestUtil.getRequestUserId();
        if (!hasLeadPermission(leadEntity, currentUserId)) {
            return ResponseDTO.userErrorParam("无权限对该线索进行跟进");
        }

        // 3. 校验线索是否已转化或关闭
        if (LeadStatusEnum.CONVERTED.getValue().equals(leadEntity.getLeadStatus()) ||
            LeadStatusEnum.CLOSED.getValue().equals(leadEntity.getLeadStatus())) {
            return ResponseDTO.userErrorParam("线索已转化或关闭，无法添加跟进记录");
        }

        // 4. 创建跟进记录
        LeadFollowEntity followEntity = new LeadFollowEntity();
        followEntity.setLeadId(enhancedForm.getLeadId());
        followEntity.setFollowUserId(AdminRequestUtil.getRequestUserId());
        followEntity.setFollowUserName(AdminRequestUtil.getRequestUser().getActualName());
        followEntity.setFollowType(enhancedForm.getFollowType());
        followEntity.setFollowContent(enhancedForm.getFollowContent());

        // 转换跟进结果
        FollowResultEnum resultEnum = FollowResultEnum.getByValue(enhancedForm.getFollowResult());
        if (resultEnum != null) {
            followEntity.setFollowResult(resultEnum.getDesc());
            followEntity.setFollowResultType(resultEnum.getValue());
        }

        // 设置下次跟进时间
        followEntity.setNextFollowTime(enhancedForm.getNextFollowTime());
        followEntity.setCreateTime(LocalDateTime.now());
        followEntity.setUpdateTime(LocalDateTime.now());
        followEntity.setDeletedFlag(Boolean.FALSE);

        // 设置创建人和更新人信息
        if (currentUserId != null) {
            followEntity.setCreateUserId(currentUserId);
            followEntity.setUpdateUserId(currentUserId);
        } else {
            // 如果没有登录用户，设置为默认值（测试环境）
            followEntity.setCreateUserId(1L);
            followEntity.setUpdateUserId(1L);
        }

        // 5. 保存跟进记录
        leadFollowDao.insert(followEntity);

        // 6. 处理跟进结果的后续操作
        ResponseDTO<String> processResult = followResultProcessor.processFollowResult(enhancedForm);
        if (!processResult.getOk()) {
            // 如果后续操作失败，回滚跟进记录
            throw new RuntimeException("跟进后续操作失败：" + processResult.getMsg());
        }

        // 7. 记录数据变动
        dataTracerService.insert(followEntity.getFollowId(), DataTracerTypeEnum.LEAD_FOLLOW);

        log.info("增强跟进记录添加成功：leadId={}, followResult={}, message={}",
            enhancedForm.getLeadId(), enhancedForm.getFollowResult(), processResult.getData());

        return processResult;
    }

    /**
     * 更新跟进记录
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> update(LeadFollowUpdateForm updateForm) {
        // 校验跟进记录是否存在
        LeadFollowEntity existFollow = leadFollowDao.selectById(updateForm.getFollowId());
        if (existFollow == null || existFollow.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("跟进记录不存在");
        }

        // 校验是否是本人的跟进记录
        Long currentUserId = AdminRequestUtil.getRequestUserId();
        if (!currentUserId.equals(existFollow.getFollowUserId())) {
            return ResponseDTO.userErrorParam("只能修改自己的跟进记录");
        }

        // 获取原实体用于数据变动记录
        LeadFollowEntity originEntity = existFollow;

        LeadFollowEntity followEntity = SmartBeanUtil.copy(updateForm, LeadFollowEntity.class);

        // 转换跟进结果：将Integer类型转换为对应的中文字符串
        if (updateForm.getFollowResult() != null) {
            FollowResultEnum resultEnum = SmartEnumUtil.getEnumByValue(updateForm.getFollowResult(), FollowResultEnum.class);
            if (resultEnum != null) {
                followEntity.setFollowResult(resultEnum.getDesc());
            }
        }

        followEntity.setUpdateTime(LocalDateTime.now());
        followEntity.setUpdateUserId(AdminRequestUtil.getRequestUserId());
        leadFollowDao.updateById(followEntity);

        // 记录数据变动
        dataTracerService.update(followEntity.getFollowId(), DataTracerTypeEnum.LEAD_FOLLOW, originEntity, followEntity);

        return ResponseDTO.ok();
    }

    /**
     * 删除跟进记录
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> delete(Long followId) {
        LeadFollowEntity followEntity = leadFollowDao.selectById(followId);
        if (followEntity == null || followEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("跟进记录不存在");
        }

        // 校验是否是本人的跟进记录
        Long currentUserId = AdminRequestUtil.getRequestUserId();
        if (!currentUserId.equals(followEntity.getFollowUserId())) {
            return ResponseDTO.userErrorParam("只能删除自己的跟进记录");
        }

        followEntity.setDeletedFlag(Boolean.TRUE);
        followEntity.setUpdateTime(LocalDateTime.now());
        followEntity.setUpdateUserId(AdminRequestUtil.getRequestUserId());
        leadFollowDao.updateById(followEntity);

        // 记录数据变动
        dataTracerService.delete(followId, DataTracerTypeEnum.LEAD_FOLLOW);

        return ResponseDTO.ok();
    }

    /**
     * 批量删除跟进记录
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> batchDelete(List<Long> followIds) {
        if (followIds == null || followIds.isEmpty()) {
            return ResponseDTO.userErrorParam("请选择要删除的跟进记录");
        }

        Long currentUserId = AdminRequestUtil.getRequestUserId();

        for (Long followId : followIds) {
            // 校验跟进记录是否存在
            LeadFollowEntity followEntity = leadFollowDao.selectById(followId);
            if (followEntity == null || followEntity.getDeletedFlag()) {
                continue; // 跳过不存在的记录
            }

            // 校验是否是本人的跟进记录
            if (!currentUserId.equals(followEntity.getFollowUserId())) {
                return ResponseDTO.userErrorParam("只能删除自己的跟进记录");
            }

            // 软删除
            followEntity.setDeletedFlag(Boolean.TRUE);
            followEntity.setUpdateTime(LocalDateTime.now());
            followEntity.setUpdateUserId(currentUserId);
            leadFollowDao.updateById(followEntity);

            // 记录数据变动
            dataTracerService.delete(followId, DataTracerTypeEnum.LEAD_FOLLOW);
        }

        return ResponseDTO.ok();
    }

    /**
     * 获取我的跟进统计
     */
    public ResponseDTO<Integer> getMyFollowCount() {
        Long employeeId = AdminRequestUtil.getRequestUserId();
        Integer count = leadFollowDao.countByEmployeeId(employeeId);
        return ResponseDTO.ok(count);
    }

    /**
     * 获取需要跟进提醒的记录
     */
    public ResponseDTO<List<LeadFollowVO>> getNeedReminder() {
        LocalDateTime currentTime = LocalDateTime.now();
        List<LeadFollowVO> reminderList = leadFollowDao.selectNeedReminder(currentTime);
        return ResponseDTO.ok(reminderList);
    }

    /**
     * 检查用户是否有权限访问指定线索
     */
    private boolean hasLeadPermission(LeadEntity leadEntity, Long currentUserId) {
        if (leadEntity == null || currentUserId == null) {
            return false;
        }
        // 使用LeadService中更完整的权限检查逻辑
        return leadService.hasLeadPermission(leadEntity.getLeadId(), currentUserId);
    }
}

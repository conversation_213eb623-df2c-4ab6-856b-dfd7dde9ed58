package net.lab1024.sa.admin.module.business.hospital.visit.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 诊断记录VO
 *
 * <AUTHOR>
 * @Date 2025-07-29
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@Schema(description = "诊断记录VO")
public class DiagnosisVO {

    @Schema(description = "诊断ID")
    private Long diagnosisId;

    @Schema(description = "患者ID")
    private Long patientId;

    @Schema(description = "患者编号")
    private String patientNo;

    @Schema(description = "患者姓名")
    private String patientName;

    @Schema(description = "诊断结果")
    private String diagnosisResult;

    @Schema(description = "诊断类型：1-初诊，2-复诊，3-会诊")
    private Integer diagnosisType;

    @Schema(description = "诊断类型名称")
    private String diagnosisTypeName;

    @Schema(description = "诊断说明")
    private String diagnosisDescription;

    @Schema(description = "诊断医生ID")
    private Long diagnosisDoctorId;

    @Schema(description = "诊断医生姓名")
    private String diagnosisDoctorName;

    @Schema(description = "诊断时间")
    private LocalDateTime diagnosisTime;

    @Schema(description = "诊断状态：1-已诊断，2-已开单，3-已收费，4-已完成")
    private Integer diagnosisStatus;

    @Schema(description = "诊断状态名称")
    private String diagnosisStatusName;

    @Schema(description = "创建人姓名")
    private String createUserName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人姓名")
    private String updateUserName;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}

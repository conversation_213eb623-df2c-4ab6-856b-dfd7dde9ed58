package net.lab1024.sa.admin.module.business.hospital.lead.service;

import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.hospital.lead.dao.LeadStatusHistoryDao;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.entity.LeadStatusHistoryEntity;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadStatusHistoryVO;
import net.lab1024.sa.admin.module.business.hospital.lead.constant.LeadStatusEnum;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.code.UserErrorCode;
import net.lab1024.sa.admin.util.AdminRequestUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 线索状态变更历史服务
 * 
 * <AUTHOR>
 * @Date 2025-07-22
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Slf4j
@Service
public class LeadStatusHistoryService {

    @Autowired
    private LeadStatusHistoryDao leadStatusHistoryDao;

    /**
     * 记录状态变更历史
     * 
     * @param leadId 线索ID
     * @param fromStatus 原状态
     * @param toStatus 新状态
     * @param triggerType 触发类型
     * @param remark 备注
     */
    public void recordStatusChange(Long leadId, Integer fromStatus, Integer toStatus, String triggerType, String remark) {
        try {
            LeadStatusHistoryEntity historyEntity = new LeadStatusHistoryEntity();
            historyEntity.setLeadId(leadId);
            historyEntity.setFromStatus(fromStatus);
            historyEntity.setToStatus(toStatus);
            historyEntity.setTriggerType(triggerType);
            historyEntity.setRemark(remark);
            historyEntity.setCreateTime(LocalDateTime.now());
            historyEntity.setCreateName(AdminRequestUtil.getRequestUser().getActualName());
            
            leadStatusHistoryDao.insert(historyEntity);
            
            log.info("记录线索状态变更历史成功：leadId={}, {}({}) -> {}({})", 
                leadId, 
                LeadStatusEnum.getByValue(fromStatus).getDesc(), fromStatus,
                LeadStatusEnum.getByValue(toStatus).getDesc(), toStatus);
                
        } catch (Exception e) {
            log.error("记录线索状态变更历史失败：leadId={}", leadId, e);
        }
    }

    /**
     * 查询线索状态变更历史
     * 
     * @param leadId 线索ID
     * @return 状态变更历史列表
     */
    public ResponseDTO<List<LeadStatusHistoryVO>> getStatusHistory(Long leadId) {
        try {
            List<LeadStatusHistoryVO> historyList = leadStatusHistoryDao.selectByLeadId(leadId);
            return ResponseDTO.ok(historyList);
        } catch (Exception e) {
            log.error("查询线索状态变更历史失败：leadId={}", leadId, e);
            return ResponseDTO.error(UserErrorCode.DATA_NOT_EXIST, "查询失败");
        }
    }

    /**
     * 获取线索当前所处的业务阶段
     * 
     * @param leadStatus 线索状态
     * @return 业务阶段描述
     */
    public String getBusinessStage(Integer leadStatus) {
        if (leadStatus == null) {
            return "未知阶段";
        }
        
        switch (leadStatus) {
            case 1: // 新线索
                return "线索获取阶段";
            case 2: // 跟进中
                return "线索跟进阶段";
            case 3: // 已预约
                return "预约管理阶段";
            case 4: // 已到院
                return "到院服务阶段";
            case 5: // 已转化
                return "客户管理阶段";
            case 6: // 已关闭
                return "线索关闭阶段";
            default:
                return "未知阶段";
        }
    }

    /**
     * 统计线索在各个状态的停留时间
     * 
     * @param leadId 线索ID
     * @return 状态停留时间统计
     */
    public ResponseDTO<List<LeadStatusHistoryVO>> getStatusDurationStats(Long leadId) {
        try {
            List<LeadStatusHistoryVO> historyList = leadStatusHistoryDao.selectByLeadIdWithDuration(leadId);
            return ResponseDTO.ok(historyList);
        } catch (Exception e) {
            log.error("统计线索状态停留时间失败：leadId={}", leadId, e);
            return ResponseDTO.error(UserErrorCode.DATA_NOT_EXIST, "统计失败");
        }
    }
}

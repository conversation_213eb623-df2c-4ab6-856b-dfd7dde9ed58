package net.lab1024.sa.admin.module.business.hospital.customer.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.constant.AdminSwaggerTagConst;
import net.lab1024.sa.admin.module.business.hospital.customer.domain.form.CustomerAddForm;
import net.lab1024.sa.admin.module.business.hospital.customer.domain.form.CustomerQueryForm;
import net.lab1024.sa.admin.module.business.hospital.customer.domain.form.CustomerUpdateForm;
import net.lab1024.sa.admin.module.business.hospital.customer.domain.form.LeadToCustomerForm;
import net.lab1024.sa.admin.module.business.hospital.customer.domain.vo.CustomerVO;
import net.lab1024.sa.admin.module.business.hospital.customer.domain.vo.Customer360VO;
import net.lab1024.sa.admin.module.business.hospital.customer.service.CustomerService;
import net.lab1024.sa.admin.module.system.employee.domain.vo.EmployeeVO;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.ValidateList;
import net.lab1024.sa.base.module.support.operatelog.annotation.OperateLog;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 客户管理Controller
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Tag(name = AdminSwaggerTagConst.Business.HOSPITAL_CUSTOMER)
@RestController
@RequestMapping("/api/customer")
public class CustomerController {

    @Resource
    private CustomerService customerService;

    @Operation(summary = "分页查询客户")
    @PostMapping("/query")
    @SaCheckPermission("hospital:customer:query")
    public ResponseDTO<PageResult<CustomerVO>> queryPage(@RequestBody @Valid CustomerQueryForm queryForm) {
        return customerService.queryPage(queryForm);
    }

    @Operation(summary = "添加客户")
    @PostMapping("/add")
    @SaCheckPermission("hospital:customer:add")
    @OperateLog
    public ResponseDTO<String> add(@RequestBody @Valid CustomerAddForm addForm) {
        return customerService.add(addForm);
    }

    @Operation(summary = "更新客户")
    @PostMapping("/update")
    @SaCheckPermission("hospital:customer:update")
    @OperateLog
    public ResponseDTO<String> update(@RequestBody @Valid CustomerUpdateForm updateForm) {
        return customerService.update(updateForm);
    }

    @Operation(summary = "删除客户")
    @GetMapping("/delete/{customerId}")
    @SaCheckPermission("hospital:customer:delete")
    @OperateLog
    public ResponseDTO<String> delete(@PathVariable Long customerId) {
        return customerService.delete(customerId);
    }

    @Operation(summary = "批量删除客户")
    @PostMapping("/batchDelete")
    @SaCheckPermission("hospital:customer:delete")
    @OperateLog
    public ResponseDTO<String> batchDelete(@RequestBody ValidateList<Long> customerIdList) {
        return customerService.batchDelete(customerIdList);
    }

    @Operation(summary = "更新客户状态")
    @PostMapping("/updateStatus/{customerId}/{customerStatus}")
    @SaCheckPermission("hospital:customer:update")
    @OperateLog
    public ResponseDTO<String> updateStatus(@PathVariable Long customerId, @PathVariable Integer customerStatus) {
        return customerService.updateStatus(customerId, customerStatus);
    }

    @Operation(summary = "获取客户详情")
    @GetMapping("/detail/{customerId}")
    @SaCheckPermission("hospital:customer:query")
    public ResponseDTO<CustomerVO> getDetail(@PathVariable Long customerId) {
        return customerService.getDetail(customerId);
    }

    @Operation(summary = "线索转客户")
    @PostMapping("/leadToCustomer")
    @SaCheckPermission("hospital:customer:convert")
    @OperateLog
    public ResponseDTO<String> leadToCustomer(@RequestBody @Valid LeadToCustomerForm leadToCustomerForm) {
        return customerService.leadToCustomer(leadToCustomerForm);
    }

    @Operation(summary = "根据标签查询客户")
    @GetMapping("/queryByTags/{customerTags}")
    @SaCheckPermission("hospital:customer:query")
    public ResponseDTO<List<CustomerVO>> queryByTags(@PathVariable String customerTags) {
        return customerService.queryByTags(customerTags);
    }

    @Operation(summary = "获取最近创建的客户")
    @GetMapping("/recent/{limit}")
    @SaCheckPermission("hospital:customer:query")
    public ResponseDTO<List<CustomerVO>> getRecentCustomers(@PathVariable Integer limit) {
        return customerService.getRecentCustomers(limit);
    }

    @Operation(summary = "获取客户360度视图")
    @GetMapping("/360view/{customerId}")
    @SaCheckPermission("hospital:customer:query")
    public ResponseDTO<Customer360VO> getCustomer360View(@PathVariable Long customerId) {
        return customerService.getCustomer360View(customerId);
    }

    @Operation(summary = "分配客户")
    @PostMapping("/assign/{customerId}/{assigneeId}")
    @SaCheckPermission("hospital:customer:assign")
    @OperateLog
    public ResponseDTO<String> assignCustomer(@PathVariable Long customerId, @PathVariable Long assigneeId) {
        return customerService.assignCustomer(customerId, assigneeId);
    }

    @Operation(summary = "获取可分配的员工列表")
    @GetMapping("/assignable-employees")
    @SaCheckPermission("hospital:customer:assign")
    public ResponseDTO<List<EmployeeVO>> getAssignableEmployees() {
        return customerService.getAssignableEmployees();
    }
}

package net.lab1024.sa.admin.module.business.hospital.appointment.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 医生排班视图对象
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class DoctorScheduleVO {

    @Schema(description = "排班ID")
    private Long scheduleId;

    @Schema(description = "医生ID")
    private Long doctorId;

    @Schema(description = "医生姓名")
    private String doctorName;

    @Schema(description = "医生科室")
    private String doctorDepartment;

    @Schema(description = "排班日期")
    private LocalDate scheduleDate;

    @Schema(description = "开始时间")
    private LocalTime startTime;

    @Schema(description = "结束时间")
    private LocalTime endTime;

    @Schema(description = "最大预约数")
    private Integer maxAppointments;

    @Schema(description = "当前预约数")
    private Integer currentAppointments;

    @Schema(description = "剩余预约数")
    private Integer remainingAppointments;

    @Schema(description = "状态：1-正常，2-暂停")
    private Integer status;

    @Schema(description = "状态名称")
    private String statusName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}

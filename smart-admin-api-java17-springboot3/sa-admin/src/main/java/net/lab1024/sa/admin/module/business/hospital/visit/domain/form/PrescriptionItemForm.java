package net.lab1024.sa.admin.module.business.hospital.visit.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Min;
import java.math.BigDecimal;

/**
 * 开单项目表单
 *
 * <AUTHOR>
 * @Date 2025-07-29
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@Schema(description = "开单项目表单")
public class PrescriptionItemForm {

    @Schema(description = "项目名称")
    @NotBlank(message = "项目名称不能为空")
    private String itemName;

    @Schema(description = "项目类型：treatment-治疗，medicine-药品，examination-检查，material-材料")
    @NotBlank(message = "项目类型不能为空")
    private String itemType;

    @Schema(description = "单价")
    @NotNull(message = "单价不能为空")
    @DecimalMin(value = "0.01", message = "单价必须大于0")
    private BigDecimal unitPrice;

    @Schema(description = "数量")
    @NotNull(message = "数量不能为空")
    @Min(value = 1, message = "数量必须大于0")
    private Integer quantity;

    @Schema(description = "项目说明")
    private String itemDescription;
}

package net.lab1024.sa.admin.module.business.hospital.project.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import net.lab1024.sa.admin.constant.AdminSwaggerTagConst;
import net.lab1024.sa.admin.module.business.hospital.project.domain.form.ProjectAddForm;
import net.lab1024.sa.admin.module.business.hospital.project.domain.form.ProjectQueryForm;
import net.lab1024.sa.admin.module.business.hospital.project.domain.form.ProjectUpdateForm;
import net.lab1024.sa.admin.module.business.hospital.project.domain.vo.ProjectVO;
import net.lab1024.sa.admin.module.business.hospital.project.service.ProjectService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.ValidateList;
import net.lab1024.sa.base.module.support.operatelog.annotation.OperateLog;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目管理Controller
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Tag(name = AdminSwaggerTagConst.Business.HOSPITAL_PROJECT)
@RestController
@RequestMapping("/api/project")
public class ProjectController {

    @Resource
    private ProjectService projectService;

    @Operation(summary = "分页查询项目")
    @PostMapping("/query")
    @SaCheckPermission("hospital:project:query")
    public ResponseDTO<PageResult<ProjectVO>> queryPage(@RequestBody @Valid ProjectQueryForm queryForm) {
        return projectService.queryPage(queryForm);
    }

    @Operation(summary = "添加项目")
    @PostMapping("/add")
    @SaCheckPermission("hospital:project:add")
    @OperateLog
    public ResponseDTO<String> add(@RequestBody @Valid ProjectAddForm addForm) {
        return projectService.add(addForm);
    }

    @Operation(summary = "更新项目")
    @PostMapping("/update")
    @SaCheckPermission("hospital:project:update")
    @OperateLog
    public ResponseDTO<String> update(@RequestBody @Valid ProjectUpdateForm updateForm) {
        return projectService.update(updateForm);
    }

    @Operation(summary = "删除项目")
    @GetMapping("/delete/{projectId}")
    @SaCheckPermission("hospital:project:delete")
    @OperateLog
    public ResponseDTO<String> delete(@PathVariable Long projectId) {
        return projectService.delete(projectId);
    }

    @Operation(summary = "批量删除项目")
    @PostMapping("/batchDelete")
    @SaCheckPermission("hospital:project:delete")
    @OperateLog
    public ResponseDTO<String> batchDelete(@RequestBody ValidateList<Long> projectIdList) {
        return projectService.batchDelete(projectIdList);
    }

    @Operation(summary = "启用/禁用项目")
    @PostMapping("/updateStatus/{projectId}/{projectStatus}")
    @SaCheckPermission("hospital:project:update")
    @OperateLog
    public ResponseDTO<String> updateStatus(@PathVariable Long projectId, @PathVariable Integer projectStatus) {
        return projectService.updateStatus(projectId, projectStatus);
    }

    @Operation(summary = "查询所有启用的项目")
    @GetMapping("/queryAllEnabled")
    @SaCheckPermission("hospital:project:query")
    public ResponseDTO<List<ProjectVO>> queryAllEnabled() {
        return projectService.queryAllEnabled();
    }

    @Operation(summary = "根据分类查询项目")
    @GetMapping("/queryByCategory/{projectCategory}")
    @SaCheckPermission("hospital:project:query")
    public ResponseDTO<List<ProjectVO>> queryByCategory(@PathVariable String projectCategory) {
        return projectService.queryByCategory(projectCategory);
    }
}

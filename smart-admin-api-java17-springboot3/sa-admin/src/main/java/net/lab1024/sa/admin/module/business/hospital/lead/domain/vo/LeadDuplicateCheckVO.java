package net.lab1024.sa.admin.module.business.hospital.lead.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 线索重复检查结果VO
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class LeadDuplicateCheckVO {

    @Schema(description = "是否重复")
    private Boolean isDuplicate;

    @Schema(description = "重复线索ID")
    private Long leadId;

    @Schema(description = "客户姓名")
    private String customerName;

    @Schema(description = "客户手机号")
    private String customerPhone;

    @Schema(description = "归属人ID")
    private Long ownerId;

    @Schema(description = "归属人姓名")
    private String ownerName;

    @Schema(description = "最后跟进时间")
    private LocalDateTime lastFollowTime;

    @Schema(description = "距今天数")
    private Integer daysSinceLastFollow;

    @Schema(description = "线索状态")
    private Integer leadStatus;

    @Schema(description = "线索状态描述")
    private String leadStatusDesc;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "是否可以申请变更归属")
    private Boolean canApplyChange;

    @Schema(description = "不能申请变更的原因")
    private String cannotApplyReason;
}

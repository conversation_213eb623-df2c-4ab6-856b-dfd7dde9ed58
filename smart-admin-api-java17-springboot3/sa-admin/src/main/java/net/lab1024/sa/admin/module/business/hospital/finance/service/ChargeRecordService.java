package net.lab1024.sa.admin.module.business.hospital.finance.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.hospital.appointment.dao.AppointmentDao;
import net.lab1024.sa.admin.module.business.hospital.appointment.domain.entity.AppointmentEntity;
import net.lab1024.sa.admin.module.business.hospital.customer.dao.CustomerDao;
import net.lab1024.sa.admin.module.business.hospital.customer.domain.entity.CustomerEntity;
import net.lab1024.sa.admin.module.business.hospital.finance.constant.ChargeStatusEnum;
import net.lab1024.sa.admin.module.business.hospital.finance.constant.PaymentMethodEnum;
import net.lab1024.sa.admin.module.business.hospital.finance.dao.ChargeRecordDao;
import net.lab1024.sa.admin.module.business.hospital.finance.domain.entity.ChargeRecordEntity;
import net.lab1024.sa.admin.module.business.hospital.finance.domain.form.ChargeRecordAddForm;
import net.lab1024.sa.admin.module.business.hospital.finance.domain.form.ChargeRecordQueryForm;
import net.lab1024.sa.admin.module.business.hospital.finance.domain.form.ChargeRecordUpdateForm;
import net.lab1024.sa.admin.module.business.hospital.finance.domain.vo.ChargeRecordVO;
import net.lab1024.sa.admin.module.business.hospital.project.dao.ProjectDao;
import net.lab1024.sa.admin.module.business.hospital.project.domain.entity.ProjectEntity;
import net.lab1024.sa.admin.module.system.employee.dao.EmployeeDao;
import net.lab1024.sa.admin.module.system.employee.domain.entity.EmployeeEntity;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.util.SmartBeanUtil;
import net.lab1024.sa.base.common.util.SmartPageUtil;
import net.lab1024.sa.base.module.support.datatracer.constant.DataTracerTypeEnum;
import net.lab1024.sa.base.module.support.datatracer.service.DataTracerService;
import net.lab1024.sa.base.module.support.operatelog.annotation.OperateLog;
import net.lab1024.sa.base.module.support.serialnumber.constant.SerialNumberIdEnum;
import net.lab1024.sa.base.module.support.serialnumber.service.SerialNumberService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 收费记录Service
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Slf4j
@Service
public class ChargeRecordService {

    @Resource
    private ChargeRecordDao chargeRecordDao;

    @Resource
    private AppointmentDao appointmentDao;

    @Resource
    private CustomerDao customerDao;

    @Resource
    private ProjectDao projectDao;

    @Resource
    private EmployeeDao employeeDao;

    @Resource
    private SerialNumberService serialNumberService;

    @Resource
    private DataTracerService dataTracerService;

    /**
     * 分页查询收费记录
     */
    public ResponseDTO<PageResult<ChargeRecordVO>> queryPage(ChargeRecordQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<ChargeRecordVO> list = chargeRecordDao.queryPage(page, queryForm);
        
        // 设置枚举显示名称
        list.forEach(this::setEnumDisplayNames);
        
        PageResult<ChargeRecordVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return ResponseDTO.ok(pageResult);
    }

    /**
     * 添加收费记录
     */
    @Transactional(rollbackFor = Exception.class)
    @OperateLog
    public ResponseDTO<String> add(ChargeRecordAddForm addForm) {
        // 验证项目是否存在
        if (addForm.getProjectId() != null) {
            ProjectEntity project = projectDao.selectById(addForm.getProjectId());
            if (project == null) {
                return ResponseDTO.userErrorParam("项目不存在");
            }
        }

        // 验证客户是否存在
        if (addForm.getCustomerId() != null) {
            CustomerEntity customer = customerDao.selectById(addForm.getCustomerId());
            if (customer == null) {
                return ResponseDTO.userErrorParam("客户不存在");
            }
        }

        // 验证预约是否存在
        if (addForm.getAppointmentId() != null) {
            AppointmentEntity appointment = appointmentDao.selectById(addForm.getAppointmentId());
            if (appointment == null) {
                return ResponseDTO.userErrorParam("预约不存在");
            }
        }

        ChargeRecordEntity chargeRecordEntity = SmartBeanUtil.copy(addForm, ChargeRecordEntity.class);
        
        // 生成收费单号
        String chargeNo = serialNumberService.generate(SerialNumberIdEnum.CHARGE_RECORD);
        chargeRecordEntity.setChargeNo(chargeNo);
        
        // 设置默认值
        if (chargeRecordEntity.getChargeStatus() == null) {
            chargeRecordEntity.setChargeStatus(ChargeStatusEnum.PENDING.getValue());
        }
        if (chargeRecordEntity.getChargeDate() == null) {
            chargeRecordEntity.setChargeDate(LocalDate.now());
        }
        if (chargeRecordEntity.getInvoiceFlag() == null) {
            chargeRecordEntity.setInvoiceFlag(false);
        }
        if (chargeRecordEntity.getDeletedFlag() == null) {
            chargeRecordEntity.setDeletedFlag(false);
        }

        chargeRecordDao.insert(chargeRecordEntity);

        // 记录数据变动
        dataTracerService.insert(chargeRecordEntity.getChargeId(), DataTracerTypeEnum.CHARGE_RECORD);

        return ResponseDTO.ok();
    }

    /**
     * 更新收费记录
     */
    @Transactional(rollbackFor = Exception.class)
    @OperateLog
    public ResponseDTO<String> update(ChargeRecordUpdateForm updateForm) {
        ChargeRecordEntity oldEntity = chargeRecordDao.selectById(updateForm.getChargeId());
        if (oldEntity == null) {
            return ResponseDTO.userErrorParam("收费记录不存在");
        }

        ChargeRecordEntity chargeRecordEntity = SmartBeanUtil.copy(updateForm, ChargeRecordEntity.class);
        chargeRecordDao.updateById(chargeRecordEntity);

        // 记录数据变动
        dataTracerService.update(chargeRecordEntity.getChargeId(), DataTracerTypeEnum.CHARGE_RECORD, oldEntity, chargeRecordEntity);

        return ResponseDTO.ok();
    }

    /**
     * 删除收费记录
     */
    @Transactional(rollbackFor = Exception.class)
    @OperateLog
    public ResponseDTO<String> delete(Long chargeId) {
        ChargeRecordEntity chargeRecordEntity = chargeRecordDao.selectById(chargeId);
        if (chargeRecordEntity == null) {
            return ResponseDTO.userErrorParam("收费记录不存在");
        }

        chargeRecordEntity.setDeletedFlag(true);
        chargeRecordDao.updateById(chargeRecordEntity);

        // 记录数据变动
        dataTracerService.delete(chargeId, DataTracerTypeEnum.CHARGE_RECORD);

        return ResponseDTO.ok();
    }

    /**
     * 根据预约创建收费记录
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> createFromAppointment(Long appointmentId) {
        AppointmentEntity appointment = appointmentDao.selectById(appointmentId);
        if (appointment == null) {
            return ResponseDTO.userErrorParam("预约不存在");
        }

        // 检查是否已经创建过收费记录
        List<ChargeRecordEntity> existingRecords = chargeRecordDao.selectByAppointmentId(appointmentId);
        if (!existingRecords.isEmpty()) {
            return ResponseDTO.userErrorParam("该预约已经创建过收费记录");
        }

        ChargeRecordEntity chargeRecord = new ChargeRecordEntity();
        
        // 从预约信息填充收费记录
        chargeRecord.setCustomerId(appointment.getCustomerId());
        chargeRecord.setCustomerName(appointment.getCustomerName());
        chargeRecord.setCustomerPhone(appointment.getCustomerPhone());
        chargeRecord.setAppointmentId(appointmentId);
        // 预约实体中没有项目字段，需要从其他地方获取或设置默认值
        // chargeRecord.setProjectId(appointment.getProjectId());
        // chargeRecord.setProjectName(appointment.getProjectName());

        // 获取项目标准价格 - 暂时注释掉，因为预约中没有项目信息
        // ProjectEntity project = projectDao.selectById(appointment.getProjectId());
        // if (project != null) {
        //     chargeRecord.setStandardPrice(project.getStandardPrice());
        //     chargeRecord.setChargeAmount(project.getStandardPrice());
        //     chargeRecord.setProjectCategory(project.getProjectCategory());
        // }

        // 生成收费单号
        String chargeNo = serialNumberService.generate(SerialNumberIdEnum.CHARGE_RECORD);
        chargeRecord.setChargeNo(chargeNo);
        
        // 设置默认值
        chargeRecord.setChargeStatus(ChargeStatusEnum.PENDING.getValue());
        chargeRecord.setChargeDate(LocalDate.now());
        chargeRecord.setInvoiceFlag(false);
        chargeRecord.setDeletedFlag(false);

        chargeRecordDao.insert(chargeRecord);

        // 记录数据变动
        dataTracerService.insert(chargeRecord.getChargeId(), DataTracerTypeEnum.CHARGE_RECORD);

        return ResponseDTO.ok();
    }

    /**
     * 根据客户ID查询收费记录
     */
    public ResponseDTO<List<ChargeRecordVO>> getByCustomerId(Long customerId) {
        List<ChargeRecordVO> list = chargeRecordDao.selectByCustomerId(customerId);
        list.forEach(this::setEnumDisplayNames);
        return ResponseDTO.ok(list);
    }

    /**
     * 根据预约ID查询收费记录
     */
    public ResponseDTO<List<ChargeRecordVO>> getByAppointmentId(Long appointmentId) {
        List<ChargeRecordEntity> entities = chargeRecordDao.selectByAppointmentId(appointmentId);
        List<ChargeRecordVO> list = SmartBeanUtil.copyList(entities, ChargeRecordVO.class);
        list.forEach(this::setEnumDisplayNames);
        return ResponseDTO.ok(list);
    }

    /**
     * 更新收费状态
     */
    @Transactional(rollbackFor = Exception.class)
    @OperateLog
    public ResponseDTO<String> updateChargeStatus(Long chargeId, Integer chargeStatus, Integer paymentMethod, String paymentDetails) {
        ChargeRecordEntity chargeRecord = chargeRecordDao.selectById(chargeId);
        if (chargeRecord == null) {
            return ResponseDTO.userErrorParam("收费记录不存在");
        }

        chargeRecordDao.updateChargeStatus(chargeId, chargeStatus, paymentMethod, paymentDetails);

        // 记录数据变动
        ChargeRecordEntity updatedEntity = chargeRecordDao.selectById(chargeId);
        dataTracerService.update(chargeId, DataTracerTypeEnum.CHARGE_RECORD, chargeRecord, updatedEntity);

        return ResponseDTO.ok();
    }

    /**
     * 更新发票信息
     */
    @Transactional(rollbackFor = Exception.class)
    @OperateLog
    public ResponseDTO<String> updateInvoiceInfo(Long chargeId, String invoiceNo, Boolean invoiceFlag) {
        ChargeRecordEntity chargeRecord = chargeRecordDao.selectById(chargeId);
        if (chargeRecord == null) {
            return ResponseDTO.userErrorParam("收费记录不存在");
        }

        chargeRecordDao.updateInvoiceInfo(chargeId, invoiceNo, invoiceFlag);

        // 记录数据变动
        ChargeRecordEntity updatedEntity = chargeRecordDao.selectById(chargeId);
        dataTracerService.update(chargeId, DataTracerTypeEnum.CHARGE_RECORD, chargeRecord, updatedEntity);

        return ResponseDTO.ok();
    }

    /**
     * 设置枚举显示名称
     */
    private void setEnumDisplayNames(ChargeRecordVO vo) {
        if (vo.getChargeStatus() != null) {
            ChargeStatusEnum chargeStatus = ChargeStatusEnum.getByValue(vo.getChargeStatus());
            if (chargeStatus != null) {
                vo.setChargeStatusName(chargeStatus.getDesc());
            }
        }
        
        if (vo.getPaymentMethod() != null) {
            PaymentMethodEnum paymentMethod = PaymentMethodEnum.getByValue(vo.getPaymentMethod());
            if (paymentMethod != null) {
                vo.setPaymentMethodName(paymentMethod.getDesc());
            }
        }
    }
}

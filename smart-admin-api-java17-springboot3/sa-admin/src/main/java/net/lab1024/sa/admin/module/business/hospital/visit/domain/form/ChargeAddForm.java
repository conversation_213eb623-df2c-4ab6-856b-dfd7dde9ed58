package net.lab1024.sa.admin.module.business.hospital.visit.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMin;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 收费记录添加表单
 *
 * <AUTHOR>
 * @Date 2025-07-29
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@Schema(description = "收费记录添加表单")
public class ChargeAddForm {

    @Schema(description = "开单ID")
    @NotNull(message = "开单ID不能为空")
    private Long prescriptionId;

    @Schema(description = "诊断ID")
    @NotNull(message = "诊断ID不能为空")
    private Long diagnosisId;

    @Schema(description = "患者ID")
    @NotNull(message = "患者ID不能为空")
    private Long patientId;

    @Schema(description = "应收金额")
    @NotNull(message = "应收金额不能为空")
    @DecimalMin(value = "0.01", message = "应收金额必须大于0")
    private BigDecimal totalAmount;

    @Schema(description = "实收金额")
    @NotNull(message = "实收金额不能为空")
    @DecimalMin(value = "0.00", message = "实收金额不能为负数")
    private BigDecimal actualAmount;

    @Schema(description = "优惠金额")
    private BigDecimal discountAmount;

    @Schema(description = "收款账户：cash-现金，alipay-支付宝，wechat-微信支付，bank-银行卡")
    @NotBlank(message = "收款账户不能为空")
    private String paymentAccount;

    @Schema(description = "收费状态：1-已收费，2-部分收费，3-未收费")
    @NotNull(message = "收费状态不能为空")
    private Integer chargeStatus;

    @Schema(description = "收费时间")
    @NotNull(message = "收费时间不能为空")
    private LocalDateTime chargeTime;

    @Schema(description = "收费备注")
    private String chargeNote;
}

package net.lab1024.sa.admin.module.business.hospital.lead.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 线索跟进记录实体类
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@TableName("t_lead_follow")
public class LeadFollowEntity {

    /**
     * 跟进记录ID
     */
    @TableId(type = IdType.AUTO)
    private Long followId;

    /**
     * 线索ID
     */
    private Long leadId;

    /**
     * 跟进方式：1-电话，2-微信，3-短信，4-邮件，5-上门
     */
    private Integer followType;

    /**
     * 跟进内容
     */
    private String followContent;

    /**
     * 跟进结果
     */
    private String followResult;

    /**
     * 跟进结果类型：1-客户有预约意向，2-需要再次跟进，3-客户无意向/无效线索，4-仅记录跟进
     */
    private Integer followResultType;

    /**
     * 跟进人ID
     */
    private Long followUserId;

    /**
     * 跟进人姓名
     */
    private String followUserName;

    /**
     * 下次跟进时间
     */
    private LocalDateTime nextFollowTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标志：0-未删除，1-已删除
     */
    private Boolean deletedFlag;

    /**
     * 创建人ID
     */
    private Long createUserId;

    /**
     * 更新人ID
     */
    private Long updateUserId;
}

package net.lab1024.sa.admin.module.business.hospital.customer.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import net.lab1024.sa.admin.module.business.hospital.customer.constant.CustomerStatusEnum;
import net.lab1024.sa.base.common.domain.PageParam;
import net.lab1024.sa.base.common.swagger.SchemaEnum;
import net.lab1024.sa.base.common.validator.enumeration.CheckEnum;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户查询表单
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class CustomerQueryForm extends PageParam {

    @Schema(description = "线索ID")
    private Long leadId;

    @Schema(description = "客户姓名")
    @Length(max = 100, message = "客户姓名最多100字符")
    private String customerName;

    @Schema(description = "客户电话")
    @Length(max = 20, message = "客户电话最多20字符")
    private String customerPhone;

    @Schema(description = "性别：1-男，2-女")
    private Integer gender;

    @Schema(description = "客户来源")
    @Length(max = 50, message = "客户来源最多50字符")
    private String customerSource;

    @Schema(description = "客户状态：1-潜在客户，2-意向客户，3-成交客户，4-流失客户")
    @SchemaEnum(CustomerStatusEnum.class)
    @CheckEnum(message = "客户状态错误", value = CustomerStatusEnum.class, required = false)
    private Integer customerStatus;

    @Schema(description = "客户标签")
    @Length(max = 200, message = "客户标签最多200字符")
    private String customerTags;

    @Schema(description = "创建时间-开始")
    private LocalDateTime createTimeStart;

    @Schema(description = "创建时间-结束")
    private LocalDateTime createTimeEnd;

    @Schema(description = "搜索关键词")
    @Length(max = 50, message = "搜索关键词最多50字符")
    private String searchWord;

    @Schema(hidden = true)
    private Boolean deletedFlag;

    /**
     * 数据权限过滤 - 允许查看的员工ID列表
     */
    @Schema(hidden = true)
    private List<Long> dataScopeEmployeeIds;
}

package net.lab1024.sa.admin.module.business.hospital.lead.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.entity.LeadEntity;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.form.LeadQueryForm;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadExcelVO;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadVO;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadDuplicateCheckVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 线索DAO
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Mapper
@Component
public interface LeadDao extends BaseMapper<LeadEntity> {

    /**
     * 分页查询线索
     *
     * @param page 分页参数
     * @param queryForm 查询条件
     * @return 线索列表
     */
    List<LeadVO> queryPage(Page page, @Param("query") LeadQueryForm queryForm);

    /**
     * 根据手机号查询线索（查重）
     *
     * @param customerPhone 客户手机号
     * @param excludeLeadId 排除的线索ID（用于更新时排除自己）
     * @return 线索实体
     */
    LeadEntity selectByPhone(@Param("customerPhone") String customerPhone, @Param("excludeLeadId") Long excludeLeadId);

    /**
     * 批量更新删除状态
     *
     * @param leadIdList 线索ID列表
     * @param deletedFlag 删除标志
     */
    void batchUpdateDeleted(@Param("leadIdList") List<Long> leadIdList, @Param("deletedFlag") Boolean deletedFlag);

    /**
     * 批量分配线索
     *
     * @param leadIdList 线索ID列表
     * @param assignedEmployeeId 分配员工ID
     */
    void batchAssign(@Param("leadIdList") List<Long> leadIdList, @Param("assignedEmployeeId") Long assignedEmployeeId);

    /**
     * 更新线索状态
     *
     * @param leadId 线索ID
     * @param leadStatus 线索状态
     */
    void updateStatus(@Param("leadId") Long leadId, @Param("leadStatus") Integer leadStatus);

    /**
     * 获取导出数据
     *
     * @param queryForm 查询条件
     * @return 导出数据列表
     */
    List<LeadExcelVO> selectExcelExportData(@Param("query") LeadQueryForm queryForm);

    /**
     * 根据员工ID查询线索数量
     *
     * @param employeeId 员工ID
     * @return 线索数量
     */
    Integer countByEmployeeId(@Param("employeeId") Long employeeId);

    /**
     * 查询待跟进的线索
     *
     * @param employeeId 员工ID
     * @return 待跟进线索列表
     */
    List<LeadVO> selectPendingFollowUp(@Param("employeeId") Long employeeId);

    /**
     * 根据线索ID列表查询线索信息
     *
     * @param leadIdList 线索ID列表
     * @return 线索列表
     */
    List<LeadVO> selectByIdList(@Param("leadIdList") List<Long> leadIdList);

    /**
     * 统计总线索数
     *
     * @return 总线索数
     */
    Integer countTotal();

    /**
     * 统计活跃线索数（排除已到院和已转化）
     *
     * @return 活跃线索数
     */
    Integer countActiveLeads();

    /**
     * 统计指定时间范围内的线索数
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 线索数
     */
    Integer countByDateRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 统计已转化的线索数
     *
     * @return 已转化线索数
     */
    Integer countConverted();

    /**
     * 根据手机号查询线索详细信息（用于重复检查）
     *
     * @param customerPhone 客户手机号
     * @param excludeLeadId 排除的线索ID（用于更新时排除自己）
     * @return 线索重复检查结果
     */
    LeadDuplicateCheckVO selectDuplicateCheckByPhone(@Param("customerPhone") String customerPhone, @Param("excludeLeadId") Long excludeLeadId);

    // ==================== 仪表盘统计方法 ====================

    /**
     * 统计总线索数（按用户）
     */
    Integer countTotalLeads(@Param("userId") Long userId);

    /**
     * 统计今日新增线索
     */
    Integer countTodayNewLeads(@Param("userId") Long userId);

    /**
     * 按状态统计线索数量
     */
    Integer countLeadsByStatus(@Param("userId") Long userId, @Param("status") Integer status);

    /**
     * 统计今日待跟进线索
     */
    Integer countTodayPendingLeads(@Param("userId") Long userId);

    /**
     * 统计本月转化线索
     */
    Integer countMonthlyConverted(@Param("userId") Long userId);

    /**
     * 统计本月预约数量
     */
    Integer countMonthlyAppointments(@Param("userId") Long userId);

    /**
     * 统计本月到院数量
     */
    Integer countMonthlyArrivals(@Param("userId") Long userId);

    /**
     * 统计本月总线索数
     */
    Integer countMonthlyTotal(@Param("userId") Long userId);

    /**
     * 获取线索来源统计
     */
    List<Map<String, Object>> getLeadSourceStats(@Param("userId") Long userId);

    /**
     * 获取线索状态统计
     */
    List<Map<String, Object>> getLeadStatusStats(@Param("userId") Long userId);

    /**
     * 获取线索质量统计
     */
    List<Map<String, Object>> getLeadQualityStats(@Param("userId") Long userId);

    /**
     * 获取症状统计
     */
    List<Map<String, Object>> getSymptomStats(@Param("userId") Long userId);

    /**
     * 获取地区统计
     */
    List<Map<String, Object>> getRegionStats(@Param("userId") Long userId);

    /**
     * 获取年龄分布统计
     */
    List<Map<String, Object>> getAgeStats(@Param("userId") Long userId);

    /**
     * 获取性别分布统计
     */
    List<Map<String, Object>> getGenderStats(@Param("userId") Long userId);

    /**
     * 获取线索趋势统计
     */
    List<Map<String, Object>> getLeadTrendStats(@Param("userId") Long userId, @Param("startDate") String startDate, @Param("endDate") String endDate);
}

package net.lab1024.sa.admin.module.business.hospital.lead.service;

import lombok.extern.slf4j.Slf4j;
import net.lab1024.sa.admin.module.business.hospital.lead.constant.FollowResultEnum;
import net.lab1024.sa.admin.module.business.hospital.lead.constant.LeadStatusEnum;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.form.LeadFollowEnhancedAddForm;
import net.lab1024.sa.admin.module.business.hospital.appointment.service.AppointmentService;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 跟进结果处理服务
 * 根据跟进结果执行相应的后续操作
 * 
 * <AUTHOR>
 * @Date 2025-07-22
 * @Wechat zhuda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Slf4j
@Service
public class LeadFollowResultProcessor {

    @Autowired
    private LeadStatusFlowService leadStatusFlowService;

    @Autowired
    private AppointmentService appointmentService;

    @Autowired
    private LeadFollowReminderService leadFollowReminderService;

    /**
     * 处理跟进结果的后续操作
     * 
     * @param followForm 跟进表单
     * @return 处理结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> processFollowResult(LeadFollowEnhancedAddForm followForm) {
        try {
            FollowResultEnum followResult = FollowResultEnum.getByValue(followForm.getFollowResult());
            if (followResult == null) {
                return ResponseDTO.userErrorParam("无效的跟进结果");
            }

            switch (followResult) {
                case APPOINTMENT_INTERESTED:
                    return handleAppointmentInterested(followForm);
                case NEED_FOLLOW_UP:
                    return handleNeedFollowUp(followForm);
                case NO_INTEREST_INVALID:
                    return handleNoInterestInvalid(followForm);
                case RECORD_ONLY:
                    return handleRecordOnly(followForm);
                default:
                    return ResponseDTO.userErrorParam("未知的跟进结果类型");
            }
        } catch (Exception e) {
            log.error("处理跟进结果失败：leadId={}, followResult={}", 
                followForm.getLeadId(), followForm.getFollowResult(), e);
            return ResponseDTO.userErrorParam("处理跟进结果失败：" + e.getMessage());
        }
    }

    /**
     * 处理客户有预约意向的情况
     */
    private ResponseDTO<String> handleAppointmentInterested(LeadFollowEnhancedAddForm followForm) {
        // 如果选择立即创建预约
        if (Boolean.TRUE.equals(followForm.getCreateAppointmentNow())) {
            // 验证预约必填字段（项目和医生现在是可选的）
            if (followForm.getAppointmentDate() == null || followForm.getAppointmentTime() == null) {
                return ResponseDTO.userErrorParam("创建预约时，日期、时间为必填项");
            }

            // 使用AppointmentService的addFromLead方法创建预约
            ResponseDTO<String> appointmentResult = appointmentService.addFromLead(
                followForm.getLeadId(),
                followForm.getAppointmentDate(),
                followForm.getAppointmentTime(),
                followForm.getAppointmentRemark()
            );
            if (!appointmentResult.getOk()) {
                return appointmentResult;
            }

            log.info("客户有预约意向，已自动创建预约：leadId={}", followForm.getLeadId());
            return ResponseDTO.ok("跟进记录已添加，预约已创建，线索状态已更新为已预约");
        } else {
            // 仅更新线索状态为跟进中，等待后续手动创建预约
            leadStatusFlowService.autoFlowStatus(followForm.getLeadId(), 
                LeadStatusEnum.FOLLOWING.getValue(), "FOLLOW_ADD", 
                "客户有预约意向，等待创建预约");
            
            return ResponseDTO.ok("跟进记录已添加，客户有预约意向，请及时安排预约");
        }
    }

    /**
     * 处理需要再次跟进的情况
     */
    private ResponseDTO<String> handleNeedFollowUp(LeadFollowEnhancedAddForm followForm) {
        // 确保线索状态为跟进中
        leadStatusFlowService.autoFlowStatus(followForm.getLeadId(), 
            LeadStatusEnum.FOLLOWING.getValue(), "FOLLOW_ADD", 
            "需要再次跟进，保持跟进中状态");

        // 如果设置了下次跟进时间，创建跟进提醒
        if (followForm.getNextFollowTime() != null) {
            if (Boolean.TRUE.equals(followForm.getSetFollowReminder())) {
                leadFollowReminderService.createFollowReminder(
                    followForm.getLeadId(),
                    followForm.getNextFollowTime(),
                    followForm.getReminderType(),
                    followForm.getReminderMinutes()
                );
                log.info("已设置跟进提醒：leadId={}, nextFollowTime={}", 
                    followForm.getLeadId(), followForm.getNextFollowTime());
            }
        }

        return ResponseDTO.ok("跟进记录已添加，线索保持跟进中状态" + 
            (followForm.getNextFollowTime() != null ? "，已设置下次跟进时间" : ""));
    }

    /**
     * 处理客户无意向/无效线索的情况
     */
    private ResponseDTO<String> handleNoInterestInvalid(LeadFollowEnhancedAddForm followForm) {
        if (followForm.getCloseReason() == null || followForm.getCloseReason().trim().isEmpty()) {
            return ResponseDTO.userErrorParam("关闭线索时必须填写关闭原因");
        }

        // 自动关闭线索
        ResponseDTO<Boolean> closeResult = leadStatusFlowService.closeLeadManually(
            followForm.getLeadId(), followForm.getCloseReason());
        
        if (!closeResult.getOk()) {
            return ResponseDTO.userErrorParam("关闭线索失败：" + closeResult.getMsg());
        }

        log.info("客户无意向，已自动关闭线索：leadId={}, reason={}", 
            followForm.getLeadId(), followForm.getCloseReason());
        
        return ResponseDTO.ok("跟进记录已添加，线索已关闭");
    }

    /**
     * 处理仅记录跟进的情况
     */
    private ResponseDTO<String> handleRecordOnly(LeadFollowEnhancedAddForm followForm) {
        // 仅确保线索状态为跟进中，不执行其他操作
        leadStatusFlowService.autoFlowStatus(followForm.getLeadId(), 
            LeadStatusEnum.FOLLOWING.getValue(), "FOLLOW_ADD", 
            "仅记录跟进内容");

        return ResponseDTO.ok("跟进记录已添加");
    }

    /**
     * 验证跟进结果的必填字段
     */
    public ResponseDTO<String> validateFollowForm(LeadFollowEnhancedAddForm followForm) {
        FollowResultEnum followResult = FollowResultEnum.getByValue(followForm.getFollowResult());
        if (followResult == null) {
            return ResponseDTO.userErrorParam("无效的跟进结果");
        }

        switch (followResult) {
            case APPOINTMENT_INTERESTED:
                if (Boolean.TRUE.equals(followForm.getCreateAppointmentNow())) {
                    // 项目和医生现在是可选的，只验证日期和时间
                    if (followForm.getAppointmentDate() == null) {
                        return ResponseDTO.userErrorParam("创建预约时日期不能为空");
                    }
                    if (followForm.getAppointmentTime() == null) {
                        return ResponseDTO.userErrorParam("创建预约时时间不能为空");
                    }
                }
                break;
            case NEED_FOLLOW_UP:
                if (followForm.getNextFollowTime() == null) {
                    return ResponseDTO.userErrorParam("选择再次跟进时，下次跟进时间不能为空");
                }
                break;
            case NO_INTEREST_INVALID:
                if (followForm.getCloseReason() == null || followForm.getCloseReason().trim().isEmpty()) {
                    return ResponseDTO.userErrorParam("选择无意向时，关闭原因不能为空");
                }
                break;
            case RECORD_ONLY:
                // 仅记录跟进，无需额外验证
                break;
        }

        return ResponseDTO.ok();
    }
}

package net.lab1024.sa.admin.module.business.hospital.visit.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.form.ChargeAddForm;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.form.ChargeQueryForm;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.ChargeVO;
import net.lab1024.sa.admin.module.business.hospital.visit.service.ChargeService;
import net.lab1024.sa.base.common.domain.PageResult;
import net.lab1024.sa.base.common.domain.ResponseDTO;
import net.lab1024.sa.base.common.domain.RequestUser;
import net.lab1024.sa.base.common.util.SmartRequestUtil;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 收费记录控制器
 *
 * <AUTHOR>
 * @Date 2025-07-29
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Tag(name = "收费记录管理")
@RestController
@RequestMapping("/visit/charge")
public class ChargeController {

    @Autowired
    private ChargeService chargeService;

    @Operation(summary = "分页查询收费记录")
    @PostMapping("/queryPage")
    public ResponseDTO<PageResult<ChargeVO>> queryPage(@RequestBody @Valid ChargeQueryForm queryForm) {
        return ResponseDTO.ok(chargeService.queryPage(queryForm));
    }

    @Operation(summary = "根据患者ID查询收费记录")
    @GetMapping("/patient/{patientId}")
    public ResponseDTO<List<ChargeVO>> getByPatientId(@PathVariable Long patientId) {
        return chargeService.getByPatientId(patientId);
    }

    @Operation(summary = "根据开单ID查询收费记录")
    @GetMapping("/prescription/{prescriptionId}")
    public ResponseDTO<ChargeVO> getByPrescriptionId(@PathVariable Long prescriptionId) {
        return chargeService.getByPrescriptionId(prescriptionId);
    }

    @Operation(summary = "根据诊断ID查询收费记录")
    @GetMapping("/diagnosis/{diagnosisId}")
    public ResponseDTO<ChargeVO> getByDiagnosisId(@PathVariable Long diagnosisId) {
        return chargeService.getByDiagnosisId(diagnosisId);
    }

    @Operation(summary = "根据ID查询收费记录详情")
    @GetMapping("/get/{chargeId}")
    public ResponseDTO<ChargeVO> getById(@PathVariable Long chargeId) {
        return chargeService.getById(chargeId);
    }

    @Operation(summary = "新增收费记录")
    @PostMapping("/add")
    public ResponseDTO<String> add(@RequestBody @Valid ChargeAddForm addForm) {
        RequestUser requestUser = SmartRequestUtil.getRequestUser();
        return chargeService.add(addForm, requestUser.getUserId(), requestUser.getUserName());
    }

    @Operation(summary = "更新收费状态")
    @PostMapping("/updateStatus")
    public ResponseDTO<String> updateStatus(@RequestParam Long chargeId, @RequestParam Integer chargeStatus) {
        RequestUser requestUser = SmartRequestUtil.getRequestUser();
        return chargeService.updateStatus(chargeId, chargeStatus, requestUser.getUserId(), requestUser.getUserName());
    }

    @Operation(summary = "删除收费记录")
    @PostMapping("/delete/{chargeId}")
    public ResponseDTO<String> delete(@PathVariable Long chargeId) {
        RequestUser requestUser = SmartRequestUtil.getRequestUser();
        return chargeService.delete(chargeId, requestUser.getUserId(), requestUser.getUserName());
    }
}

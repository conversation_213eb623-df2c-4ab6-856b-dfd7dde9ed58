package net.lab1024.sa.admin.module.business.hospital.project.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import net.lab1024.sa.admin.module.business.hospital.project.domain.entity.ProjectEntity;
import net.lab1024.sa.admin.module.business.hospital.project.domain.form.ProjectQueryForm;
import net.lab1024.sa.admin.module.business.hospital.project.domain.vo.ProjectVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 项目DAO
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Mapper
@Component
public interface ProjectDao extends BaseMapper<ProjectEntity> {

    /**
     * 分页查询项目
     *
     * @param page 分页参数
     * @param queryForm 查询条件
     * @return 项目列表
     */
    List<ProjectVO> queryPage(Page page, @Param("query") ProjectQueryForm queryForm);

    /**
     * 根据项目名称查询项目（用于查重）
     *
     * @param projectName 项目名称
     * @param excludeProjectId 排除的项目ID（用于更新时排除自己）
     * @return 项目实体
     */
    ProjectEntity selectByName(@Param("projectName") String projectName, @Param("excludeProjectId") Long excludeProjectId);

    /**
     * 更新项目状态
     *
     * @param projectId 项目ID
     * @param projectStatus 项目状态
     */
    void updateStatus(@Param("projectId") Long projectId, @Param("projectStatus") Integer projectStatus);

    /**
     * 批量更新删除状态
     *
     * @param projectIdList 项目ID列表
     * @param deletedFlag 删除标志
     */
    void batchUpdateDeleted(@Param("projectIdList") List<Long> projectIdList, @Param("deletedFlag") Boolean deletedFlag);

    /**
     * 查询所有启用的项目
     *
     * @return 项目列表
     */
    List<ProjectVO> selectAllEnabled();

    /**
     * 根据分类查询项目
     *
     * @param projectCategory 项目分类
     * @return 项目列表
     */
    List<ProjectVO> selectByCategory(@Param("projectCategory") String projectCategory);

    /**
     * 获取项目统计数据
     *
     * @return 项目统计数据
     */
    Integer getProjectCount();

    /**
     * 获取启用项目数量
     *
     * @return 启用项目数量
     */
    Integer getEnabledProjectCount();
}

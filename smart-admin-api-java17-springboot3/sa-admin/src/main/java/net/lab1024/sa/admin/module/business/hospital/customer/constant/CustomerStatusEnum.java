package net.lab1024.sa.admin.module.business.hospital.customer.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 客户状态枚举
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Getter
@AllArgsConstructor
public enum CustomerStatusEnum implements BaseEnum {

    /**
     * 潜在客户
     */
    POTENTIAL(1, "潜在客户"),

    /**
     * 意向客户
     */
    INTERESTED(2, "意向客户"),

    /**
     * 成交客户
     */
    DEAL(3, "成交客户"),

    /**
     * 流失客户
     */
    LOST(4, "流失客户"),

    ;

    private final Integer value;

    private final String desc;
}

package net.lab1024.sa.admin.module.business.hospital.lead.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 线索分配表单
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
public class LeadAssignForm {

    @Schema(description = "线索ID列表")
    @NotEmpty(message = "线索ID列表不能为空")
    private List<Long> leadIdList;

    @Schema(description = "分配员工ID")
    @NotNull(message = "分配员工ID不能为空")
    private Long assignedEmployeeId;

    @Schema(description = "分配说明")
    private String assignRemark;
}

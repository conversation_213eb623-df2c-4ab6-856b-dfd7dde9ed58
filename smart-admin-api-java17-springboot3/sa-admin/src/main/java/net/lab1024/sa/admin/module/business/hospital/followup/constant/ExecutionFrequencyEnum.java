package net.lab1024.sa.admin.module.business.hospital.followup.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import net.lab1024.sa.base.common.enumeration.BaseEnum;

/**
 * 执行频率枚举
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Getter
@AllArgsConstructor
public enum ExecutionFrequencyEnum implements BaseEnum {

    /**
     * 每日
     */
    DAILY(1, "每日"),

    /**
     * 每周
     */
    WEEKLY(2, "每周"),

    /**
     * 每月
     */
    MONTHLY(3, "每月"),

    /**
     * 每季度
     */
    QUARTERLY(4, "每季度"),

    /**
     * 每年
     */
    YEARLY(5, "每年"),

    /**
     * 一次性
     */
    ONCE(6, "一次性");

    private final Integer value;

    private final String desc;
}

package net.lab1024.sa.admin.module.business.hospital.visit.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.lab1024.sa.admin.module.business.hospital.visit.domain.entity.DoctorAssistantRelationEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 医生助理关系DAO
 *
 * <AUTHOR>
 * @Date 2025-07-29 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Mapper
@Component
public interface DoctorAssistantRelationDao extends BaseMapper<DoctorAssistantRelationEntity> {

    /**
     * 根据医生ID查询助理关系
     *
     * @param doctorId 医生ID
     * @return 助理关系列表
     */
    List<DoctorAssistantRelationEntity> selectByDoctorId(@Param("doctorId") Long doctorId);

    /**
     * 根据助理ID查询医生关系
     *
     * @param assistantId 助理ID
     * @return 医生关系列表
     */
    List<DoctorAssistantRelationEntity> selectByAssistantId(@Param("assistantId") Long assistantId);

    /**
     * 根据部门ID查询关系
     *
     * @param departmentId 部门ID
     * @return 关系列表
     */
    List<DoctorAssistantRelationEntity> selectByDepartmentId(@Param("departmentId") Long departmentId);

    /**
     * 检查医生助理关系是否存在
     *
     * @param doctorId 医生ID
     * @param assistantId 助理ID
     * @return 关系实体
     */
    DoctorAssistantRelationEntity selectByDoctorAndAssistant(@Param("doctorId") Long doctorId, @Param("assistantId") Long assistantId);
}

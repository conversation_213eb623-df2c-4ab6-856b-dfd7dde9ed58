package net.lab1024.sa.admin.module.business.hospital.common.util;

import org.apache.commons.lang3.StringUtils;

/**
 * 数据脱敏工具类
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
public class DataMaskingUtil {

    /**
     * 手机号脱敏
     * @param phone 手机号
     * @return 脱敏后的手机号
     */
    public static String maskPhone(String phone) {
        if (StringUtils.isBlank(phone)) {
            return phone;
        }
        if (phone.length() != 11) {
            return phone;
        }
        return phone.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
    }

    /**
     * 身份证号脱敏
     * @param idCard 身份证号
     * @return 脱敏后的身份证号
     */
    public static String maskIdCard(String idCard) {
        if (StringUtils.isBlank(idCard)) {
            return idCard;
        }
        if (idCard.length() == 15) {
            return idCard.replaceAll("(\\d{6})\\d{6}(\\d{3})", "$1******$2");
        } else if (idCard.length() == 18) {
            return idCard.replaceAll("(\\d{6})\\d{8}(\\d{4})", "$1********$2");
        }
        return idCard;
    }

    /**
     * 邮箱脱敏
     * @param email 邮箱
     * @return 脱敏后的邮箱
     */
    public static String maskEmail(String email) {
        if (StringUtils.isBlank(email)) {
            return email;
        }
        int atIndex = email.indexOf('@');
        if (atIndex <= 0) {
            return email;
        }
        
        String username = email.substring(0, atIndex);
        String domain = email.substring(atIndex);
        
        if (username.length() <= 2) {
            return username + domain;
        } else if (username.length() <= 4) {
            return username.substring(0, 1) + "*".repeat(username.length() - 1) + domain;
        } else {
            return username.substring(0, 2) + "*".repeat(username.length() - 4) + username.substring(username.length() - 2) + domain;
        }
    }

    /**
     * 银行卡号脱敏
     * @param bankCard 银行卡号
     * @return 脱敏后的银行卡号
     */
    public static String maskBankCard(String bankCard) {
        if (StringUtils.isBlank(bankCard)) {
            return bankCard;
        }
        if (bankCard.length() < 8) {
            return bankCard;
        }
        return bankCard.replaceAll("(\\d{4})\\d+(\\d{4})", "$1****$2");
    }

    /**
     * 姓名脱敏
     * @param name 姓名
     * @return 脱敏后的姓名
     */
    public static String maskName(String name) {
        if (StringUtils.isBlank(name)) {
            return name;
        }
        if (name.length() == 1) {
            return name;
        }
        if (name.length() == 2) {
            return name.substring(0, 1) + "*";
        }
        return name.substring(0, 1) + "*".repeat(name.length() - 2) + name.substring(name.length() - 1);
    }

    /**
     * 地址脱敏
     * @param address 地址
     * @return 脱敏后的地址
     */
    public static String maskAddress(String address) {
        if (StringUtils.isBlank(address)) {
            return address;
        }
        if (address.length() <= 6) {
            return address;
        }
        return address.substring(0, 3) + "*".repeat(address.length() - 6) + address.substring(address.length() - 3);
    }

    /**
     * 通用脱敏方法
     * @param str 原始字符串
     * @param start 保留开始位数
     * @param end 保留结束位数
     * @return 脱敏后的字符串
     */
    public static String mask(String str, int start, int end) {
        if (StringUtils.isBlank(str)) {
            return str;
        }
        if (str.length() <= start + end) {
            return str;
        }
        return str.substring(0, start) + "*".repeat(str.length() - start - end) + str.substring(str.length() - end);
    }

    /**
     * 根据权限决定是否脱敏
     * @param data 原始数据
     * @param hasPermission 是否有查看权限
     * @param maskFunction 脱敏函数
     * @return 处理后的数据
     */
    public static String maskByPermission(String data, boolean hasPermission, java.util.function.Function<String, String> maskFunction) {
        if (hasPermission) {
            return data;
        }
        return maskFunction.apply(data);
    }
}

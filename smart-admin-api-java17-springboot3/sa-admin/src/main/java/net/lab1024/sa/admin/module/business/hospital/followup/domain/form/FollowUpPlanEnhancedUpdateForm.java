package net.lab1024.sa.admin.module.business.hospital.followup.domain.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotNull;

/**
 * 回访计划增强版更新表单
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "回访计划增强版更新表单")
public class FollowUpPlanEnhancedUpdateForm extends FollowUpPlanEnhancedAddForm {

    @Schema(description = "计划ID")
    @NotNull(message = "计划ID不能为空")
    private Long planId;
}

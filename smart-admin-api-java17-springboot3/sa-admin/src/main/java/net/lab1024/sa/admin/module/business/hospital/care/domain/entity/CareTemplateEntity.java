package net.lab1024.sa.admin.module.business.hospital.care.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 关怀模板实体类
 *
 * <AUTHOR>
 * @Date 2024-01-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Data
@TableName("t_care_template")
public class CareTemplateEntity {

    /**
     * 模板ID
     */
    @TableId(type = IdType.AUTO)
    private Long templateId;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 模板描述
     */
    private String templateDescription;

    /**
     * 关怀类型：1-出院关怀，2-复诊提醒，3-生日关怀，4-节日关怀，5-满意度调查
     */
    private Integer careType;

    /**
     * 关怀方式：1-电话，2-短信，3-微信，4-邮件，5-上门
     */
    private Integer careMethod;

    /**
     * 模板内容
     */
    private String templateContent;

    /**
     * 模板变量（JSON格式，定义可替换的变量）
     */
    private String templateVariables;

    /**
     * 使用场景
     */
    private String useScene;

    /**
     * 适用项目（JSON格式）
     */
    private String applicableProjects;

    /**
     * 适用客户群体（JSON格式）
     */
    private String applicableCustomers;

    /**
     * 模板分类
     */
    private String templateCategory;

    /**
     * 模板标签
     */
    private String templateTags;

    /**
     * 使用次数
     */
    private Integer usageCount;

    /**
     * 平均满意度
     */
    private java.math.BigDecimal avgSatisfaction;

    /**
     * 模板状态：1-启用，0-禁用
     */
    private Integer templateStatus;

    /**
     * 是否默认模板
     */
    private Boolean defaultFlag;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除标识
     */
    private Boolean deletedFlag;

    /**
     * 创建人ID
     */
    private Long createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    private Long updateUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}

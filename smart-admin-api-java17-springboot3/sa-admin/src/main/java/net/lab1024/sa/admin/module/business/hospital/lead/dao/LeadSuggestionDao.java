package net.lab1024.sa.admin.module.business.hospital.lead.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import net.lab1024.sa.admin.module.business.hospital.lead.domain.entity.LeadSuggestionEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 线索建议DAO
 *
 * <AUTHOR>
 * @Date 2025-07-30 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */
@Mapper
@Component
public interface LeadSuggestionDao extends BaseMapper<LeadSuggestionEntity> {

    /**
     * 根据线索ID查询建议列表
     *
     * @param leadId 线索ID
     * @return 建议列表
     */
    List<LeadSuggestionEntity> selectByLeadId(@Param("leadId") Long leadId);

    /**
     * 根据线索ID和状态查询建议列表
     *
     * @param leadId 线索ID
     * @param status 状态
     * @return 建议列表
     */
    List<LeadSuggestionEntity> selectByLeadIdAndStatus(@Param("leadId") Long leadId, @Param("status") Integer status);

    /**
     * 更新建议状态
     *
     * @param suggestionId 建议ID
     * @param status 状态
     */
    void updateStatus(@Param("suggestionId") Long suggestionId, @Param("status") Integer status);
}

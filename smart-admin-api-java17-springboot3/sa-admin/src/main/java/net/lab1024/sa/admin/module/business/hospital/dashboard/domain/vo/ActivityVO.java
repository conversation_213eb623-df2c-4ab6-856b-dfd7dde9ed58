package net.lab1024.sa.admin.module.business.hospital.dashboard.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 活动记录 VO
 *
 * <AUTHOR>
 * @Date 2024-12-15 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright <a href="https://1024lab.net">1024创新实验室</a>
 */
@Data
@Schema(description = "活动记录")
public class ActivityVO {

    @Schema(description = "活动ID")
    private Long id;

    @Schema(description = "活动类型")
    private String type;

    @Schema(description = "活动标题")
    private String title;

    @Schema(description = "活动内容")
    private String content;

    @Schema(description = "业务ID")
    private Long businessId;

    @Schema(description = "业务类型")
    private String businessType;

    @Schema(description = "操作员工ID")
    private Long employeeId;

    @Schema(description = "操作员工姓名")
    private String employeeName;

    @Schema(description = "活动时间")
    private LocalDateTime time;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
} 
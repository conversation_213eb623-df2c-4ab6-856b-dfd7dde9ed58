<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.hospital.customer.dao.CustomerDao">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="net.lab1024.sa.admin.module.business.hospital.customer.domain.entity.CustomerEntity">
        <id column="customer_id" property="customerId" />
        <result column="customer_no" property="customerNo" />
        <result column="customer_name" property="customerName" />
        <result column="customer_phone" property="customerPhone" />
        <result column="customer_wechat" property="customerWechat" />
        <result column="customer_email" property="customerEmail" />
        <result column="gender" property="gender" />
        <result column="age" property="age" />
        <result column="birthday" property="birthday" />
        <result column="id_card" property="idCard" />
        <result column="address" property="address" />
        <result column="customer_source" property="customerSource" />
        <result column="customer_status" property="customerStatus" />
        <result column="customer_level" property="customerLevel" />
        <result column="customer_tags" property="customerTags" />
        <result column="responsible_employee_id" property="responsibleEmployeeId" />
        <result column="responsible_employee_name" property="responsibleEmployeeName" />
        <result column="first_visit_date" property="firstVisitDate" />
        <result column="last_visit_date" property="lastVisitDate" />
        <result column="total_consumption" property="totalConsumption" />
        <result column="remark" property="remark" />
        <result column="deleted_flag" property="deletedFlag" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_time" property="createTime" />
        <result column="update_user_id" property="updateUserId" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        customer_id, customer_no, customer_name, customer_phone, customer_wechat, customer_email,
        gender, age, birthday, id_card, address, customer_source, customer_status, customer_level,
        customer_tags, responsible_employee_id, responsible_employee_name, first_visit_date,
        last_visit_date, total_consumption, remark, deleted_flag, create_user_id, create_time,
        update_user_id, update_time
    </sql>

    <!-- 查询条件 -->
    <sql id="Query_Where_Clause">
        <where>
            deleted_flag = 0
            <if test="customerName != null and customerName != ''">
                AND customer_name LIKE CONCAT('%', #{customerName}, '%')
            </if>
            <if test="customerPhone != null and customerPhone != ''">
                AND customer_phone LIKE CONCAT('%', #{customerPhone}, '%')
            </if>
            <if test="customerStatus != null">
                AND customer_status = #{customerStatus}
            </if>
            <if test="customerLevel != null">
                AND customer_level = #{customerLevel}
            </if>
            <if test="responsibleEmployeeId != null">
                AND responsible_employee_id = #{responsibleEmployeeId}
            </if>
            <if test="createTimeStart != null">
                AND create_time >= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null">
                AND create_time &lt;= #{createTimeEnd}
            </if>
        </where>
    </sql>

    <!-- 分页查询客户 -->
    <select id="queryPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_customer
        <include refid="Query_Where_Clause" />
        ORDER BY create_time DESC
    </select>

    <!-- 根据ID查询客户 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_customer
        WHERE customer_id = #{customerId} AND deleted_flag = 0
    </select>

    <!-- 根据手机号查询客户 -->
    <select id="selectByPhone" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_customer
        WHERE customer_phone = #{customerPhone} AND deleted_flag = 0
        LIMIT 1
    </select>

    <!-- 根据客户编号查询客户 -->
    <select id="selectByCustomerNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_customer
        WHERE customer_no = #{customerNo} AND deleted_flag = 0
        LIMIT 1
    </select>

    <!-- 插入客户 -->
    <insert id="insert" parameterType="net.lab1024.sa.admin.module.business.hospital.customer.domain.entity.CustomerEntity" useGeneratedKeys="true" keyProperty="customerId">
        INSERT INTO t_customer (
            customer_no, customer_name, customer_phone, customer_wechat, customer_email,
            gender, age, birthday, id_card, address, customer_source, customer_status,
            customer_level, customer_tags, responsible_employee_id, responsible_employee_name,
            first_visit_date, last_visit_date, total_consumption, remark, deleted_flag,
            create_user_id, create_time, update_user_id, update_time
        ) VALUES (
            #{customerNo}, #{customerName}, #{customerPhone}, #{customerWechat}, #{customerEmail},
            #{gender}, #{age}, #{birthday}, #{idCard}, #{address}, #{customerSource}, #{customerStatus},
            #{customerLevel}, #{customerTags}, #{responsibleEmployeeId}, #{responsibleEmployeeName},
            #{firstVisitDate}, #{lastVisitDate}, #{totalConsumption}, #{remark}, #{deletedFlag},
            #{createUserId}, #{createTime}, #{updateUserId}, #{updateTime}
        )
    </insert>

    <!-- 更新客户 -->
    <update id="updateById" parameterType="net.lab1024.sa.admin.module.business.hospital.customer.domain.entity.CustomerEntity">
        UPDATE t_customer
        <set>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="customerPhone != null">customer_phone = #{customerPhone},</if>
            <if test="customerWechat != null">customer_wechat = #{customerWechat},</if>
            <if test="customerEmail != null">customer_email = #{customerEmail},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="age != null">age = #{age},</if>
            <if test="birthday != null">birthday = #{birthday},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="address != null">address = #{address},</if>
            <if test="customerSource != null">customer_source = #{customerSource},</if>
            <if test="customerStatus != null">customer_status = #{customerStatus},</if>
            <if test="customerLevel != null">customer_level = #{customerLevel},</if>
            <if test="customerTags != null">customer_tags = #{customerTags},</if>
            <if test="responsibleEmployeeId != null">responsible_employee_id = #{responsibleEmployeeId},</if>
            <if test="responsibleEmployeeName != null">responsible_employee_name = #{responsibleEmployeeName},</if>
            <if test="firstVisitDate != null">first_visit_date = #{firstVisitDate},</if>
            <if test="lastVisitDate != null">last_visit_date = #{lastVisitDate},</if>
            <if test="totalConsumption != null">total_consumption = #{totalConsumption},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateUserId != null">update_user_id = #{updateUserId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </set>
        WHERE customer_id = #{customerId} AND deleted_flag = 0
    </update>

    <!-- 逻辑删除客户 -->
    <update id="deleteById">
        UPDATE t_customer
        SET deleted_flag = 1, update_user_id = #{updateUserId}, update_time = #{updateTime}
        WHERE customer_id = #{customerId} AND deleted_flag = 0
    </update>

    <!-- 更新客户状态 -->
    <update id="updateStatus">
        UPDATE t_customer
        SET customer_status = #{customerStatus},
            update_user_id = #{updateUserId},
            update_time = #{updateTime}
        WHERE customer_id = #{customerId} AND deleted_flag = 0
    </update>

    <!-- 更新客户等级 -->
    <update id="updateLevel">
        UPDATE t_customer
        SET customer_level = #{customerLevel},
            update_user_id = #{updateUserId},
            update_time = #{updateTime}
        WHERE customer_id = #{customerId} AND deleted_flag = 0
    </update>

    <!-- 更新客户标签 -->
    <update id="updateTags">
        UPDATE t_customer
        SET customer_tags = #{customerTags},
            update_user_id = #{updateUserId},
            update_time = #{updateTime}
        WHERE customer_id = #{customerId} AND deleted_flag = 0
    </update>

    <!-- 更新客户消费金额 -->
    <update id="updateTotalConsumption">
        UPDATE t_customer
        SET total_consumption = total_consumption + #{amount},
            last_visit_date = #{visitDate},
            update_user_id = #{updateUserId},
            update_time = #{updateTime}
        WHERE customer_id = #{customerId} AND deleted_flag = 0
    </update>

    <!-- 统计客户数量按状态 -->
    <select id="countByStatus" resultType="java.util.Map">
        SELECT
            customer_status as status,
            COUNT(1) as count
        FROM t_customer
        WHERE deleted_flag = 0
        <if test="responsibleEmployeeId != null">
            AND responsible_employee_id = #{responsibleEmployeeId}
        </if>
        GROUP BY customer_status
    </select>

    <!-- 统计客户数量按等级 -->
    <select id="countByLevel" resultType="java.util.Map">
        SELECT
            customer_level as level,
            COUNT(1) as count
        FROM t_customer
        WHERE deleted_flag = 0
        <if test="responsibleEmployeeId != null">
            AND responsible_employee_id = #{responsibleEmployeeId}
        </if>
        GROUP BY customer_level
    </select>

    <!-- 查询最近创建的客户 -->
    <select id="selectRecentCustomers" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM t_customer
        WHERE deleted_flag = 0
        ORDER BY create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 查询客户趋势数据 -->
    <select id="selectTrendData" resultType="java.util.Map">
        SELECT
            DATE(create_time) as date,
            COUNT(1) as count
        FROM t_customer
        WHERE deleted_flag = 0
          AND create_time >= #{startDate}
          AND create_time &lt;= #{endDate}
        <if test="responsibleEmployeeId != null">
            AND responsible_employee_id = #{responsibleEmployeeId}
        </if>
        GROUP BY DATE(create_time)
        ORDER BY date ASC
    </select>

    <!-- 生成客户编号 -->
    <select id="generateCustomerNo" resultType="java.lang.String">
        SELECT CONCAT('C', DATE_FORMAT(NOW(), '%Y%m%d'), LPAD(IFNULL(MAX(SUBSTRING(customer_no, 10)), 0) + 1, 4, '0'))
        FROM t_customer
        WHERE customer_no LIKE CONCAT('C', DATE_FORMAT(NOW(), '%Y%m%d'), '%')
          AND deleted_flag = 0
    </select>

    <!-- 统计总客户数 -->
    <select id="countTotal" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM t_customer
        WHERE deleted_flag = 0
    </select>

    <!-- 统计指定时间范围内的客户数 -->
    <select id="countByDateRange" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM t_customer
        WHERE deleted_flag = 0
          AND create_time >= #{startTime}
          AND create_time &lt;= #{endTime}
    </select>

</mapper>

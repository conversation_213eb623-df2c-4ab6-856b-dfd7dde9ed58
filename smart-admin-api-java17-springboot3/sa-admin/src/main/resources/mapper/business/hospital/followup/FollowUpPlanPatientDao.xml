<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.hospital.followup.dao.FollowUpPlanPatientDao">

    <select id="selectByPlanId" resultType="net.lab1024.sa.admin.module.business.hospital.followup.domain.entity.FollowUpPlanPatientEntity">
        SELECT * FROM t_follow_up_plan_patient
        WHERE deleted_flag = 0
          AND plan_id = #{planId}
        <if test="executionStatus != null">
            AND execution_status = #{executionStatus}
        </if>
        ORDER BY scheduled_time ASC
    </select>

    <select id="selectByPatientId" resultType="net.lab1024.sa.admin.module.business.hospital.followup.domain.entity.FollowUpPlanPatientEntity">
        SELECT * FROM t_follow_up_plan_patient
        WHERE deleted_flag = 0
          AND patient_id = #{patientId}
        <if test="executionStatus != null">
            AND execution_status = #{executionStatus}
        </if>
        ORDER BY scheduled_time DESC
    </select>

    <select id="selectByDoctorId" resultType="net.lab1024.sa.admin.module.business.hospital.followup.domain.entity.FollowUpPlanPatientEntity">
        SELECT * FROM t_follow_up_plan_patient
        WHERE deleted_flag = 0
          AND assigned_doctor_id = #{doctorId}
        <if test="planId != null">
            AND plan_id = #{planId}
        </if>
        <if test="executionStatus != null">
            AND execution_status = #{executionStatus}
        </if>
        ORDER BY scheduled_time ASC
    </select>

    <select id="selectByDepartmentId" resultType="net.lab1024.sa.admin.module.business.hospital.followup.domain.entity.FollowUpPlanPatientEntity">
        SELECT * FROM t_follow_up_plan_patient
        WHERE deleted_flag = 0
          AND department_id = #{departmentId}
        <if test="planId != null">
            AND plan_id = #{planId}
        </if>
        <if test="executionStatus != null">
            AND execution_status = #{executionStatus}
        </if>
        ORDER BY scheduled_time ASC
    </select>

    <update id="updateExecutionStatus">
        UPDATE t_follow_up_plan_patient
        SET execution_status = #{executionStatus},
            actual_execution_time = #{actualExecutionTime},
            follow_up_record_id = #{followUpRecordId},
            update_user_id = #{updateUserId},
            update_time = #{updateTime}
        WHERE relation_id = #{relationId}
          AND deleted_flag = 0
    </update>

    <update id="batchUpdateExecutionStatus">
        UPDATE t_follow_up_plan_patient
        SET execution_status = #{executionStatus},
            update_user_id = #{updateUserId},
            update_time = #{updateTime}
        WHERE relation_id IN
        <foreach collection="relationIds" item="relationId" open="(" separator="," close=")">
            #{relationId}
        </foreach>
        AND deleted_flag = 0
    </update>

    <select id="selectTargetPatients" resultType="net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.VisitPatientVO">
        SELECT
            vp.patient_id,
            vp.patient_name,
            vp.patient_phone,
            vp.assigned_doctor_id,
            vp.assigned_doctor_name,
            vp.department_id,
            vp.department_name,
            vp.visit_date,
            vp.patient_tags
        FROM t_visit_patient vp
        WHERE vp.deleted_flag = 0
          AND vp.assigned_doctor_id IS NOT NULL
        <choose>
            <when test="targetPatientType == 1">
                <!-- 所有已分配医生患者 -->
            </when>
            <when test="targetPatientType == 2">
                <!-- 指定医生患者 -->
                <if test="targetDoctorIds != null and targetDoctorIds.size() > 0">
                    AND vp.assigned_doctor_id IN
                    <foreach collection="targetDoctorIds" item="doctorId" open="(" separator="," close=")">
                        #{doctorId}
                    </foreach>
                </if>
            </when>
            <when test="targetPatientType == 3">
                <!-- 指定科室患者 -->
                <if test="targetDepartmentIds != null and targetDepartmentIds.size() > 0">
                    AND vp.department_id IN
                    <foreach collection="targetDepartmentIds" item="departmentId" open="(" separator="," close=")">
                        #{departmentId}
                    </foreach>
                </if>
            </when>
            <when test="targetPatientType == 4">
                <!-- 指定标签患者 -->
                <if test="targetPatientTags != null and targetPatientTags != ''">
                    AND vp.patient_tags LIKE CONCAT('%', #{targetPatientTags}, '%')
                </if>
            </when>
        </choose>
        <if test="visitDateRangeStart != null">
            AND vp.visit_date >= #{visitDateRangeStart}
        </if>
        <if test="visitDateRangeEnd != null">
            AND vp.visit_date &lt;= #{visitDateRangeEnd}
        </if>
        ORDER BY vp.visit_date DESC, vp.patient_id ASC
    </select>

    <select id="selectByPlanAndPatient" resultType="net.lab1024.sa.admin.module.business.hospital.followup.domain.entity.FollowUpPlanPatientEntity">
        SELECT * FROM t_follow_up_plan_patient
        WHERE deleted_flag = 0
          AND plan_id = #{planId}
          AND patient_id = #{patientId}
        LIMIT 1
    </select>

    <select id="getPendingExecutions" resultType="net.lab1024.sa.admin.module.business.hospital.followup.domain.entity.FollowUpPlanPatientEntity">
        SELECT * FROM t_follow_up_plan_patient
        WHERE deleted_flag = 0
          AND execution_status = 1
          AND scheduled_time &lt;= #{scheduledTimeBefore}
        ORDER BY priority_level DESC, scheduled_time ASC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <select id="getExecutionStatistics" resultType="net.lab1024.sa.admin.module.business.hospital.followup.domain.entity.FollowUpPlanPatientEntity">
        SELECT
            COUNT(*) as total_count,
            SUM(CASE WHEN execution_status = 1 THEN 1 ELSE 0 END) as pending_count,
            SUM(CASE WHEN execution_status = 2 THEN 1 ELSE 0 END) as executing_count,
            SUM(CASE WHEN execution_status = 3 THEN 1 ELSE 0 END) as completed_count,
            SUM(CASE WHEN execution_status = 4 THEN 1 ELSE 0 END) as skipped_count,
            SUM(CASE WHEN execution_status = 5 THEN 1 ELSE 0 END) as failed_count
        FROM t_follow_up_plan_patient
        WHERE deleted_flag = 0
          AND plan_id = #{planId}
    </select>

    <update id="deleteByPlanId">
        UPDATE t_follow_up_plan_patient
        SET deleted_flag = 1,
            update_user_id = #{updateUserId},
            update_time = #{updateTime}
        WHERE plan_id = #{planId}
          AND deleted_flag = 0
    </update>

</mapper>

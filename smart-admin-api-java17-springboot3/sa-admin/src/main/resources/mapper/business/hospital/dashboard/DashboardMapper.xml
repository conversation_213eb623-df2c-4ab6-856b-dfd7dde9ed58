<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.hospital.dashboard.dao.DashboardDao">

    <!-- 获取今日待办事项 -->
    <select id="getTodayTodos" resultType="net.lab1024.sa.admin.module.business.hospital.dashboard.domain.vo.TodoItemVO">
        SELECT 
            todo_id as todoId,
            todo_type as todoType,
            todo_title as todoTitle,
            todo_content as todoContent,
            priority_level as priorityLevel,
            todo_status as todoStatus,
            business_type as businessType,
            business_id as businessId,
            due_time as dueTime,
            remind_time as remindTime,
            start_time as startTime,
            complete_time as completeTime,
            assigned_employee_id as assignedEmployeeId,
            assigned_employee_name as assignedEmployeeName,
            handler_employee_id as handlerEmployeeId,
            handler_employee_name as handlerEmployeeName,
            create_time as createTime
        FROM t_todo_item 
        WHERE deleted_flag = 0 
        AND DATE(due_time) = CURDATE()
        AND todo_status = 1
        ORDER BY priority_level ASC, due_time ASC
        LIMIT 10
    </select>

    <!-- 获取最近活动 -->
    <select id="getRecentActivities" resultType="net.lab1024.sa.admin.module.business.hospital.dashboard.domain.vo.ActivityVO">
        SELECT 
            id,
            type,
            title,
            content,
            business_id as businessId,
            business_type as businessType,
            employee_id as employeeId,
            employee_name as employeeName,
            time,
            create_time as createTime
        FROM (
            -- 线索活动
            SELECT 
                lead_id as id,
                'lead' as type,
                CONCAT('新增线索: ', customer_name) as title,
                CONCAT('客户: ', customer_name, ', 电话: ', customer_phone) as content,
                lead_id as business_id,
                'lead' as business_type,
                create_user_id as employee_id,
                (SELECT employee_name FROM t_employee WHERE employee_id = l.create_user_id) as employee_name,
                create_time as time,
                create_time
            FROM t_lead l
            WHERE deleted_flag = 0
            
            UNION ALL
            
            -- 预约活动
            SELECT 
                appointment_id as id,
                'appointment' as type,
                CONCAT('新增预约: ', customer_name) as title,
                CONCAT('客户: ', customer_name, ', 预约时间: ', appointment_date, ' ', appointment_time) as content,
                appointment_id as business_id,
                'appointment' as business_type,
                create_user_id as employee_id,
                (SELECT employee_name FROM t_employee WHERE employee_id = a.create_user_id) as employee_name,
                create_time as time,
                create_time
            FROM t_appointment a
            WHERE deleted_flag = 0
            
            UNION ALL
            
            -- 客户活动
            SELECT 
                customer_id as id,
                'customer' as type,
                CONCAT('新增客户: ', customer_name) as title,
                CONCAT('客户: ', customer_name, ', 电话: ', customer_phone) as content,
                customer_id as business_id,
                'customer' as business_type,
                create_user_id as employee_id,
                (SELECT employee_name FROM t_employee WHERE employee_id = c.create_user_id) as employee_name,
                create_time as time,
                create_time
            FROM t_customer c
            WHERE deleted_flag = 0
            
            UNION ALL
            
            -- 跟进活动
            SELECT 
                follow_up_id as id,
                'follow' as type,
                CONCAT('跟进记录: ', customer_name) as title,
                CONCAT('客户: ', customer_name, ', 跟进内容: ', LEFT(follow_up_content, 50)) as content,
                follow_up_id as business_id,
                'follow' as business_type,
                follow_up_user_id as employee_id,
                follow_up_user_name as employee_name,
                actual_time as time,
                create_time
            FROM t_follow_up_record
            WHERE deleted_flag = 0 AND actual_time IS NOT NULL
        ) activities
        ORDER BY time DESC
        LIMIT 20
    </select>

    <!-- 获取线索转化趋势 -->
    <select id="getLeadConversionTrend" resultType="net.lab1024.sa.admin.module.business.hospital.dashboard.domain.vo.ChartDataVO">
        SELECT 
            'line' as chartType,
            '线索转化趋势' as title,
            JSON_ARRAY('1月', '2月', '3月', '4月', '5月', '6月') as xAxis,
            JSON_ARRAY(
                JSON_OBJECT('name', '新增线索', 'type', 'line', 'data', JSON_ARRAY(12, 19, 15, 25, 22, 30)),
                JSON_OBJECT('name', '转化客户', 'type', 'line', 'data', JSON_ARRAY(8, 12, 10, 18, 15, 22)),
                JSON_OBJECT('name', '预约数', 'type', 'line', 'data', JSON_ARRAY(6, 9, 8, 15, 12, 18))
            ) as series
    </select>

    <!-- 获取客户来源分布 -->
    <select id="getCustomerSourceDistribution" resultType="net.lab1024.sa.admin.module.business.hospital.dashboard.domain.vo.ChartDataVO">
        SELECT 
            'pie' as chartType,
            '客户来源分布' as title,
            JSON_ARRAY(
                JSON_OBJECT('name', '网络推广', 'value', 35, 'percentage', '35%'),
                JSON_OBJECT('name', '老客户推荐', 'value', 25, 'percentage', '25%'),
                JSON_OBJECT('name', '线下活动', 'value', 20, 'percentage', '20%'),
                JSON_OBJECT('name', '电话营销', 'value', 15, 'percentage', '15%'),
                JSON_OBJECT('name', '其他', 'value', 5, 'percentage', '5%')
            ) as pieData
    </select>

    <!-- 获取预约状态分布 -->
    <select id="getAppointmentStatusDistribution" resultType="net.lab1024.sa.admin.module.business.hospital.dashboard.domain.vo.ChartDataVO">
        SELECT 
            'pie' as chartType,
            '预约状态分布' as title,
            JSON_ARRAY(
                JSON_OBJECT('name', '已确认', 'value', 40, 'percentage', '40%'),
                JSON_OBJECT('name', '待确认', 'value', 25, 'percentage', '25%'),
                JSON_OBJECT('name', '已完成', 'value', 20, 'percentage', '20%'),
                JSON_OBJECT('name', '已取消', 'value', 10, 'percentage', '10%'),
                JSON_OBJECT('name', '已过期', 'value', 5, 'percentage', '5%')
            ) as pieData
    </select>

    <!-- 获取月度收入趋势 -->
    <select id="getRevenueTrend" resultType="net.lab1024.sa.admin.module.business.hospital.dashboard.domain.vo.ChartDataVO">
        SELECT 
            'bar' as chartType,
            '月度收入趋势' as title,
            JSON_ARRAY('1月', '2月', '3月', '4月', '5月', '6月') as xAxis,
            JSON_ARRAY(
                JSON_OBJECT('name', '月度收入', 'type', 'bar', 'data', JSON_ARRAY(120, 180, 150, 250, 220, 300))
            ) as series
    </select>

    <!-- 获取员工绩效统计 -->
    <select id="getEmployeePerformance" resultType="net.lab1024.sa.admin.module.business.hospital.dashboard.domain.vo.ChartDataVO">
        SELECT 
            'bar' as chartType,
            '员工绩效统计' as title,
            JSON_ARRAY('张三', '李四', '王五', '赵六') as xAxis,
            JSON_ARRAY(
                JSON_OBJECT('name', '线索数', 'type', 'bar', 'data', JSON_ARRAY(25, 30, 20, 15)),
                JSON_OBJECT('name', '客户数', 'type', 'bar', 'data', JSON_ARRAY(18, 22, 15, 12)),
                JSON_OBJECT('name', '预约数', 'type', 'bar', 'data', JSON_ARRAY(15, 18, 12, 10))
            ) as series
    </select>

    <!-- 获取部门统计 -->
    <select id="getDepartmentStatistics" resultType="net.lab1024.sa.admin.module.business.hospital.dashboard.domain.vo.ChartDataVO">
        SELECT 
            'pie' as chartType,
            '部门统计' as title,
            JSON_ARRAY(
                JSON_OBJECT('name', '营销部', 'value', 45, 'percentage', '45%'),
                JSON_OBJECT('name', '客服部', 'value', 30, 'percentage', '30%'),
                JSON_OBJECT('name', '医疗部', 'value', 20, 'percentage', '20%'),
                JSON_OBJECT('name', '其他', 'value', 5, 'percentage', '5%')
            ) as pieData
    </select>

</mapper> 
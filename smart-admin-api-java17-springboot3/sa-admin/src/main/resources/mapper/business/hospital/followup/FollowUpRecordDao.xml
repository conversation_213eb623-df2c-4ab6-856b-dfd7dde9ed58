<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.hospital.followup.dao.FollowUpRecordDao">

    <select id="queryPage" resultType="net.lab1024.sa.admin.module.business.hospital.followup.domain.vo.FollowUpRecordVO">
        SELECT
            follow_up_id,
            customer_id,
            customer_name,
            customer_phone,
            follow_up_type,
            follow_up_method,
            scheduled_time,
            actual_time,
            follow_up_content,
            follow_up_result,
            follow_up_user_id,
            follow_up_user_name,
            satisfaction_score,
            remark,
            next_follow_up_time,
            priority_level,
            follow_up_status,
            create_user_id,
            create_time,
            update_user_id,
            update_time
        FROM t_follow_up_record
        <where>
            <if test="queryForm.customerName != null and queryForm.customerName != ''">
                AND customer_name LIKE CONCAT('%', #{queryForm.customerName}, '%')
            </if>
            <if test="queryForm.customerPhone != null and queryForm.customerPhone != ''">
                AND customer_phone LIKE CONCAT('%', #{queryForm.customerPhone}, '%')
            </if>
            <if test="queryForm.followUpType != null">
                AND follow_up_type = #{queryForm.followUpType}
            </if>
            <if test="queryForm.followUpMethod != null">
                AND follow_up_method = #{queryForm.followUpMethod}
            </if>
            <if test="queryForm.followUpStatus != null">
                AND follow_up_status = #{queryForm.followUpStatus}
            </if>
            <if test="queryForm.followUpResult != null and queryForm.followUpResult != ''">
                AND follow_up_result = #{queryForm.followUpResult}
            </if>
            <if test="queryForm.followUpUser != null and queryForm.followUpUser != ''">
                AND follow_up_user_name LIKE CONCAT('%', #{queryForm.followUpUser}, '%')
            </if>
            <if test="queryForm.priorityLevel != null">
                AND priority_level = #{queryForm.priorityLevel}
            </if>
            <if test="queryForm.startTime != null">
                AND scheduled_time >= #{queryForm.startTime}
            </if>
            <if test="queryForm.endTime != null">
                AND scheduled_time &lt;= #{queryForm.endTime}
            </if>
            <if test="queryForm.plannedStartTime != null">
                AND next_follow_up_time >= #{queryForm.plannedStartTime}
            </if>
            <if test="queryForm.plannedEndTime != null">
                AND next_follow_up_time &lt;= #{queryForm.plannedEndTime}
            </if>
            <if test="queryForm.createUserId != null">
                AND create_user_id = #{queryForm.createUserId}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="getCustomerFollowUpHistory" resultType="net.lab1024.sa.admin.module.business.hospital.followup.domain.vo.FollowUpRecordVO">
        SELECT
            follow_up_id,
            customer_id,
            customer_name,
            customer_phone,
            follow_up_type,
            follow_up_method,
            scheduled_time,
            actual_time,
            follow_up_content,
            follow_up_result,
            follow_up_user_id,
            follow_up_user_name,
            satisfaction_score,
            remark,
            next_follow_up_time,
            priority_level,
            follow_up_status,
            create_user_id,
            create_time,
            update_user_id,
            update_time
        FROM t_follow_up_record
        WHERE customer_id = #{customerId}
        ORDER BY scheduled_time DESC
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.hospital.lead.dao.LeadOwnershipChangeHistoryDao">

    <!-- 根据线索ID查询变更历史 -->
    <select id="selectByLeadId" resultType="net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadOwnershipChangeHistoryVO">
        SELECT
            h.history_id,
            h.lead_id,
            h.request_id,
            h.change_type,
            CASE h.change_type
                WHEN 1 THEN '申请变更'
                WHEN 2 THEN '管理员直接变更'
                WHEN 3 THEN '系统自动分配'
                ELSE '未知'
            END AS change_type_desc,
            h.original_owner_id,
            h.original_owner_name,
            h.new_owner_id,
            h.new_owner_name,
            h.change_reason,
            h.operate_user_id,
            h.operate_user_name,
            h.create_time
        FROM t_lead_ownership_change_history h
        WHERE h.lead_id = #{leadId}
        ORDER BY h.create_time DESC
    </select>

    <!-- 分页查询变更历史 -->
    <select id="queryPage" resultType="net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadOwnershipChangeHistoryVO">
        SELECT
            h.history_id,
            h.lead_id,
            h.request_id,
            h.change_type,
            CASE h.change_type
                WHEN 1 THEN '申请变更'
                WHEN 2 THEN '管理员直接变更'
                WHEN 3 THEN '系统自动分配'
                ELSE '未知'
            END AS change_type_desc,
            h.original_owner_id,
            h.original_owner_name,
            h.new_owner_id,
            h.new_owner_name,
            h.change_reason,
            h.operate_user_id,
            h.operate_user_name,
            h.create_time
        FROM t_lead_ownership_change_history h
        <where>
            <if test="leadId != null">
                AND h.lead_id = #{leadId}
            </if>
        </where>
        ORDER BY h.create_time DESC
    </select>

    <!-- 统计某个线索的变更次数 -->
    <select id="countByLeadId" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM t_lead_ownership_change_history
        WHERE lead_id = #{leadId}
    </select>

</mapper>

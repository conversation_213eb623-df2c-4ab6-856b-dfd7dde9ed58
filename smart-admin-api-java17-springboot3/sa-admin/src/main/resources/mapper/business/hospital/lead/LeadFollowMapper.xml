<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.hospital.lead.dao.LeadFollowDao">

    <!-- 线索跟进VO结果映射 -->
    <resultMap id="LeadFollowVOMap" type="net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadFollowVO">
        <id column="follow_id" property="followId"/>
        <result column="lead_id" property="leadId"/>
        <result column="follow_user_id" property="employeeId"/>
        <result column="follow_user_name" property="employeeName"/>
        <result column="follow_type" property="followType"/>
        <result column="follow_type_name" property="followTypeName"/>
        <result column="follow_content" property="followContent"/>
        <result column="follow_result" property="followResult"/>
        <result column="follow_result_name" property="followResultName"/>
        <result column="next_follow_time" property="nextFollowTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="customer_name" property="customerName"/>
        <result column="customer_phone" property="customerPhone"/>
    </resultMap>

    <!-- 根据线索ID查询跟进记录 -->
    <select id="selectByLeadId" resultMap="LeadFollowVOMap">
        SELECT
            lf.follow_id,
            lf.lead_id,
            lf.follow_user_id,
            lf.follow_user_name,
            lf.follow_type,
            CASE lf.follow_type
                WHEN 1 THEN '电话'
                WHEN 2 THEN '微信'
                WHEN 3 THEN '面谈'
                WHEN 4 THEN '其他'
                ELSE '未知'
            END AS follow_type_name,
            lf.follow_content,
            CASE lf.follow_result
                WHEN '有意向' THEN 1
                WHEN '无意向' THEN 2
                WHEN '需再次跟进' THEN 3
                ELSE NULL
            END AS follow_result,
            lf.follow_result AS follow_result_name,
            lf.next_follow_time,
            lf.create_time,
            lf.update_time
        FROM t_lead_follow lf
        WHERE lf.lead_id = #{leadId}
        AND lf.deleted_flag = 0
        ORDER BY lf.create_time DESC
    </select>

    <!-- 分页查询跟进记录 -->
    <select id="queryPage" resultMap="LeadFollowVOMap">
        SELECT
            lf.follow_id,
            lf.lead_id,
            lf.follow_user_id,
            lf.follow_user_name,
            lf.follow_type,
            CASE lf.follow_type
                WHEN 1 THEN '电话'
                WHEN 2 THEN '微信'
                WHEN 3 THEN '面谈'
                WHEN 4 THEN '其他'
                ELSE '未知'
            END AS follow_type_name,
            lf.follow_content,
            CASE lf.follow_result
                WHEN '有意向' THEN 1
                WHEN '无意向' THEN 2
                WHEN '需再次跟进' THEN 3
                ELSE NULL
            END AS follow_result,
            lf.follow_result AS follow_result_name,
            lf.next_follow_time,
            lf.create_time,
            lf.update_time,
            l.customer_name,
            l.customer_phone
        FROM t_lead_follow lf
        LEFT JOIN t_lead l ON lf.lead_id = l.lead_id
        <where>
            lf.deleted_flag = 0
            <if test="leadId != null">
                AND lf.lead_id = #{leadId}
            </if>
            <if test="employeeId != null">
                AND lf.follow_user_id = #{employeeId}
            </if>
        </where>
        ORDER BY lf.create_time DESC
    </select>

    <!-- 获取线索的最后跟进时间 -->
    <select id="getLastFollowTime" resultType="java.time.LocalDateTime">
        SELECT MAX(create_time)
        FROM t_lead_follow
        WHERE lead_id = #{leadId}
        AND deleted_flag = 0
    </select>

    <!-- 获取线索的跟进次数 -->
    <select id="getFollowCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_lead_follow
        WHERE lead_id = #{leadId}
        AND deleted_flag = 0
    </select>

    <!-- 根据员工ID查询跟进记录数量 -->
    <select id="countByEmployeeId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_lead_follow
        WHERE follow_user_id = #{employeeId}
        AND deleted_flag = 0
    </select>

    <!-- 查询需要跟进提醒的记录 -->
    <select id="selectNeedReminder" resultMap="LeadFollowVOMap">
        SELECT
            lf.follow_id,
            lf.lead_id,
            lf.follow_user_id,
            lf.follow_user_name,
            lf.follow_content,
            lf.next_follow_time,
            l.customer_name,
            l.customer_phone
        FROM t_lead_follow lf
        LEFT JOIN t_lead l ON lf.lead_id = l.lead_id
        WHERE lf.next_follow_time IS NOT NULL
        AND lf.next_follow_time &lt;= #{currentTime}
        AND lf.deleted_flag = 0
        AND l.deleted_flag = 0
        AND l.lead_status IN (1, 2)
        ORDER BY lf.next_follow_time ASC
    </select>

    <!-- 批量删除跟进记录 -->
    <update id="batchDeleteByLeadIds">
        UPDATE t_lead_follow
        SET deleted_flag = 1,
            update_time = NOW()
        WHERE lead_id IN
        <foreach collection="leadIdList" item="leadId" open="(" separator="," close=")">
            #{leadId}
        </foreach>
    </update>

</mapper>

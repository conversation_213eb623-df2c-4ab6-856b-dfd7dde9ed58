<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.hospital.lead.dao.LeadStatusHistoryDao">

    <!-- 结果映射 -->
    <resultMap id="LeadStatusHistoryVO" type="net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadStatusHistoryVO">
        <id column="history_id" property="historyId"/>
        <result column="lead_id" property="leadId"/>
        <result column="from_status" property="fromStatus"/>
        <result column="from_status_name" property="fromStatusName"/>
        <result column="to_status" property="toStatus"/>
        <result column="to_status_name" property="toStatusName"/>
        <result column="trigger_type" property="triggerType"/>
        <result column="trigger_type_name" property="triggerTypeName"/>
        <result column="remark" property="remark"/>
        <result column="duration_minutes" property="durationMinutes"/>
        <result column="duration_desc" property="durationDesc"/>
        <result column="create_time" property="createTime"/>
        <result column="create_name" property="createName"/>
    </resultMap>

    <!-- 根据线索ID查询状态变更历史 -->
    <select id="selectByLeadId" resultMap="LeadStatusHistoryVO">
        SELECT 
            h.history_id,
            h.lead_id,
            h.from_status,
            CASE 
                WHEN h.from_status = 1 THEN '新线索'
                WHEN h.from_status = 2 THEN '跟进中'
                WHEN h.from_status = 3 THEN '已预约'
                WHEN h.from_status = 4 THEN '已到院'
                WHEN h.from_status = 5 THEN '已转化'
                WHEN h.from_status = 6 THEN '已关闭'
                ELSE '未知'
            END as from_status_name,
            h.to_status,
            CASE 
                WHEN h.to_status = 1 THEN '新线索'
                WHEN h.to_status = 2 THEN '跟进中'
                WHEN h.to_status = 3 THEN '已预约'
                WHEN h.to_status = 4 THEN '已到院'
                WHEN h.to_status = 5 THEN '已转化'
                WHEN h.to_status = 6 THEN '已关闭'
                ELSE '未知'
            END as to_status_name,
            h.trigger_type,
            CASE 
                WHEN h.trigger_type = 'FOLLOW_ADD' THEN '添加跟进记录'
                WHEN h.trigger_type = 'APPOINTMENT_CREATE' THEN '创建预约'
                WHEN h.trigger_type = 'ARRIVAL_CONFIRM' THEN '确认到院'
                WHEN h.trigger_type = 'CONVERSION_COMPLETE' THEN '完成转化'
                WHEN h.trigger_type = 'MANUAL_CLOSE' THEN '手动关闭'
                WHEN h.trigger_type = 'MANUAL' THEN '手动操作'
                WHEN h.trigger_type = 'AUTO_TRIGGER' THEN '自动触发'
                ELSE '未知'
            END as trigger_type_name,
            h.remark,
            h.create_time,
            h.create_name
        FROM t_lead_status_history h
        WHERE h.lead_id = #{leadId}
        ORDER BY h.create_time DESC
    </select>

    <!-- 根据线索ID查询状态变更历史（包含停留时长） -->
    <select id="selectByLeadIdWithDuration" resultMap="LeadStatusHistoryVO">
        SELECT 
            h.history_id,
            h.lead_id,
            h.from_status,
            CASE 
                WHEN h.from_status = 1 THEN '新线索'
                WHEN h.from_status = 2 THEN '跟进中'
                WHEN h.from_status = 3 THEN '已预约'
                WHEN h.from_status = 4 THEN '已到院'
                WHEN h.from_status = 5 THEN '已转化'
                WHEN h.from_status = 6 THEN '已关闭'
                ELSE '未知'
            END as from_status_name,
            h.to_status,
            CASE 
                WHEN h.to_status = 1 THEN '新线索'
                WHEN h.to_status = 2 THEN '跟进中'
                WHEN h.to_status = 3 THEN '已预约'
                WHEN h.to_status = 4 THEN '已到院'
                WHEN h.to_status = 5 THEN '已转化'
                WHEN h.to_status = 6 THEN '已关闭'
                ELSE '未知'
            END as to_status_name,
            h.trigger_type,
            CASE 
                WHEN h.trigger_type = 'FOLLOW_ADD' THEN '添加跟进记录'
                WHEN h.trigger_type = 'APPOINTMENT_CREATE' THEN '创建预约'
                WHEN h.trigger_type = 'ARRIVAL_CONFIRM' THEN '确认到院'
                WHEN h.trigger_type = 'CONVERSION_COMPLETE' THEN '完成转化'
                WHEN h.trigger_type = 'MANUAL_CLOSE' THEN '手动关闭'
                WHEN h.trigger_type = 'MANUAL' THEN '手动操作'
                WHEN h.trigger_type = 'AUTO_TRIGGER' THEN '自动触发'
                ELSE '未知'
            END as trigger_type_name,
            h.remark,
            h.create_time,
            h.create_name,
            COALESCE(
                TIMESTAMPDIFF(MINUTE, 
                    LAG(h.create_time) OVER (PARTITION BY h.lead_id ORDER BY h.create_time), 
                    h.create_time
                ), 
                0
            ) as duration_minutes,
            CASE 
                WHEN COALESCE(
                    TIMESTAMPDIFF(MINUTE, 
                        LAG(h.create_time) OVER (PARTITION BY h.lead_id ORDER BY h.create_time), 
                        h.create_time
                    ), 
                    0
                ) = 0 THEN '初始状态'
                WHEN COALESCE(
                    TIMESTAMPDIFF(MINUTE, 
                        LAG(h.create_time) OVER (PARTITION BY h.lead_id ORDER BY h.create_time), 
                        h.create_time
                    ), 
                    0
                ) &lt; 60 THEN CONCAT(COALESCE(
                    TIMESTAMPDIFF(MINUTE, 
                        LAG(h.create_time) OVER (PARTITION BY h.lead_id ORDER BY h.create_time), 
                        h.create_time
                    ), 
                    0
                ), '分钟')
                WHEN COALESCE(
                    TIMESTAMPDIFF(MINUTE, 
                        LAG(h.create_time) OVER (PARTITION BY h.lead_id ORDER BY h.create_time), 
                        h.create_time
                    ), 
                    0
                ) &lt; 1440 THEN CONCAT(FLOOR(COALESCE(
                    TIMESTAMPDIFF(MINUTE, 
                        LAG(h.create_time) OVER (PARTITION BY h.lead_id ORDER BY h.create_time), 
                        h.create_time
                    ), 
                    0
                ) / 60), '小时', MOD(COALESCE(
                    TIMESTAMPDIFF(MINUTE, 
                        LAG(h.create_time) OVER (PARTITION BY h.lead_id ORDER BY h.create_time), 
                        h.create_time
                    ), 
                    0
                ), 60), '分钟')
                ELSE CONCAT(FLOOR(COALESCE(
                    TIMESTAMPDIFF(MINUTE, 
                        LAG(h.create_time) OVER (PARTITION BY h.lead_id ORDER BY h.create_time), 
                        h.create_time
                    ), 
                    0
                ) / 1440), '天', FLOOR(MOD(COALESCE(
                    TIMESTAMPDIFF(MINUTE, 
                        LAG(h.create_time) OVER (PARTITION BY h.lead_id ORDER BY h.create_time), 
                        h.create_time
                    ), 
                    0
                ), 1440) / 60), '小时')
            END as duration_desc
        FROM t_lead_status_history h
        WHERE h.lead_id = #{leadId}
        ORDER BY h.create_time ASC
    </select>

    <!-- 统计各状态的线索数量 -->
    <select id="getStatusStatistics" resultMap="LeadStatusHistoryVO">
        SELECT 
            h.to_status,
            CASE 
                WHEN h.to_status = 1 THEN '新线索'
                WHEN h.to_status = 2 THEN '跟进中'
                WHEN h.to_status = 3 THEN '已预约'
                WHEN h.to_status = 4 THEN '已到院'
                WHEN h.to_status = 5 THEN '已转化'
                WHEN h.to_status = 6 THEN '已关闭'
                ELSE '未知'
            END as to_status_name,
            COUNT(DISTINCT h.lead_id) as lead_count
        FROM t_lead_status_history h
        INNER JOIN (
            SELECT lead_id, MAX(create_time) as max_time
            FROM t_lead_status_history
            GROUP BY lead_id
        ) latest ON h.lead_id = latest.lead_id AND h.create_time = latest.max_time
        GROUP BY h.to_status
        ORDER BY h.to_status
    </select>

    <!-- 查询状态流转趋势 -->
    <select id="getStatusFlowTrend" resultMap="LeadStatusHistoryVO">
        SELECT 
            h.from_status,
            CASE 
                WHEN h.from_status = 1 THEN '新线索'
                WHEN h.from_status = 2 THEN '跟进中'
                WHEN h.from_status = 3 THEN '已预约'
                WHEN h.from_status = 4 THEN '已到院'
                WHEN h.from_status = 5 THEN '已转化'
                WHEN h.from_status = 6 THEN '已关闭'
                ELSE '未知'
            END as from_status_name,
            h.to_status,
            CASE 
                WHEN h.to_status = 1 THEN '新线索'
                WHEN h.to_status = 2 THEN '跟进中'
                WHEN h.to_status = 3 THEN '已预约'
                WHEN h.to_status = 4 THEN '已到院'
                WHEN h.to_status = 5 THEN '已转化'
                WHEN h.to_status = 6 THEN '已关闭'
                ELSE '未知'
            END as to_status_name,
            COUNT(*) as flow_count,
            AVG(TIMESTAMPDIFF(MINUTE, 
                LAG(h.create_time) OVER (PARTITION BY h.lead_id ORDER BY h.create_time), 
                h.create_time
            )) as avg_duration_minutes
        FROM t_lead_status_history h
        WHERE h.create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY h.from_status, h.to_status
        ORDER BY h.from_status, h.to_status
    </select>

</mapper>

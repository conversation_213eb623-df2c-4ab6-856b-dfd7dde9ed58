<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.hospital.lead.dao.LeadDao">

    <!-- 线索VO结果映射 -->
    <resultMap id="LeadVOMap" type="net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadVO">
        <id column="lead_id" property="leadId"/>
        <result column="lead_source" property="leadSource"/>
        <result column="customer_name" property="customerName"/>
        <result column="customer_phone" property="customerPhone"/>
        <result column="work_phone" property="workPhone"/>
        <result column="customer_wechat" property="customerWechat"/>
        <result column="region" property="region"/>
        <result column="region_name" property="regionName"/>
        <result column="gender" property="gender"/>
        <result column="gender_name" property="genderName"/>
        <result column="age" property="age"/>
        <result column="symptom" property="symptom"/>
        <result column="lead_status" property="leadStatus"/>
        <result column="lead_status_name" property="leadStatusName"/>
        <result column="lead_quality" property="leadQuality"/>
        <result column="lead_quality_name" property="leadQualityName"/>
        <result column="assigned_employee_id" property="assignedEmployeeId"/>
        <result column="assigned_employee_name" property="assignedEmployeeName"/>
        <result column="remark" property="remark"/>
        <result column="chat_record" property="chatRecord"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="last_follow_time" property="lastFollowTime"/>
        <result column="follow_count" property="followCount"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="responsible_person_name" property="responsiblePersonName"/>
    </resultMap>

    <!-- 分页查询线索 -->
    <select id="queryPage" resultMap="LeadVOMap">
        SELECT
            l.lead_id,
            l.lead_source,
            l.customer_name,
            l.customer_phone,
            l.work_phone,
            l.customer_wechat,
            l.region,
            COALESCE(dd.data_label, l.region) AS region_name,
            l.gender,
            CASE l.gender
                WHEN 1 THEN '男'
                WHEN 2 THEN '女'
                ELSE '未知'
            END AS gender_name,
            l.age,
            l.symptom,
            l.lead_status,
            CASE l.lead_status
                WHEN 1 THEN '新线索'
                WHEN 2 THEN '跟进中'
                WHEN 3 THEN '已预约'
                WHEN 4 THEN '已到院'
                WHEN 5 THEN '已转化'
                WHEN 6 THEN '爽约'
                WHEN 7 THEN '已关闭'
                ELSE '未知'
            END AS lead_status_name,
            l.lead_quality,
            CASE l.lead_quality
                WHEN 1 THEN 'A级'
                WHEN 2 THEN 'B级'
                WHEN 3 THEN 'C级'
                ELSE '未评级'
            END AS lead_quality_name,
            l.assigned_employee_id,
            e.actual_name AS assigned_employee_name,
            l.remark,
            l.chat_record,
            l.create_time,
            l.update_time,
            lf.last_follow_time,
            COALESCE(lf.follow_count, 0) AS follow_count,
            creator.actual_name AS create_user_name,
            CASE
                WHEN l.assigned_employee_id IS NOT NULL THEN e.actual_name
                ELSE creator.actual_name
            END AS responsible_person_name
        FROM t_lead l
        LEFT JOIN t_employee e ON l.assigned_employee_id = e.employee_id
        LEFT JOIN t_employee creator ON l.create_user_id = creator.employee_id
        LEFT JOIN t_dict_data dd ON dd.data_value = l.region AND dd.dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'LEAD_REGION')
        LEFT JOIN (
            SELECT 
                lead_id,
                MAX(create_time) AS last_follow_time,
                COUNT(*) AS follow_count
            FROM t_lead_follow 
            WHERE deleted_flag = 0
            GROUP BY lead_id
        ) lf ON l.lead_id = lf.lead_id
        <where>
            l.deleted_flag = 0
            <if test="query.leadSource != null and query.leadSource != ''">
                AND l.lead_source LIKE CONCAT('%', #{query.leadSource}, '%')
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                AND l.customer_name LIKE CONCAT('%', #{query.customerName}, '%')
            </if>
            <if test="query.customerPhone != null and query.customerPhone != ''">
                AND l.customer_phone LIKE CONCAT('%', #{query.customerPhone}, '%')
            </if>
            <if test="query.region != null and query.region != ''">
                AND l.region = #{query.region}
            </if>

            <if test="query.assignedEmployeeId != null">
                AND l.assigned_employee_id = #{query.assignedEmployeeId}
            </if>
            <if test="query.symptom != null and query.symptom != ''">
                AND l.symptom LIKE CONCAT('%', #{query.symptom}, '%')
            </if>
            <if test="query.workPhone != null and query.workPhone != ''">
                AND l.work_phone LIKE CONCAT('%', #{query.workPhone}, '%')
            </if>
            <!-- 线索状态筛选 -->
            <if test="query.leadStatus != null">
                AND l.lead_status = #{query.leadStatus}
            </if>
            <!-- 排除指定状态的线索 -->
            <if test="query.excludeStatuses != null and query.excludeStatuses.size() > 0">
                AND l.lead_status NOT IN
                <foreach collection="query.excludeStatuses" item="status" open="(" close=")" separator=",">
                    #{status}
                </foreach>
            </if>
            <if test="query.createTimeStart != null">
                AND l.create_time >= #{query.createTimeStart}
            </if>
            <if test="query.createTimeEnd != null">
                AND l.create_time &lt;= #{query.createTimeEnd}
            </if>
            <if test="query.searchWord != null and query.searchWord != ''">
                AND (
                    l.customer_name LIKE CONCAT('%', #{query.searchWord}, '%')
                    OR l.customer_phone LIKE CONCAT('%', #{query.searchWord}, '%')
                    OR l.lead_source LIKE CONCAT('%', #{query.searchWord}, '%')
                    OR l.symptom LIKE CONCAT('%', #{query.searchWord}, '%')
                )
            </if>
            <!-- 筛选条件 - 保留业务筛选，移除权限相关筛选 -->
            <if test="query.filterType != null and query.filterType != ''">
                <choose>
                    <!-- 今日待跟进 -->
                    <when test="query.filterType == 'today'">
                        AND EXISTS (
                            SELECT 1 FROM t_lead_follow lf2
                            WHERE lf2.lead_id = l.lead_id
                            AND lf2.deleted_flag = 0
                            AND DATE(lf2.next_follow_time) = CURDATE()
                        )
                    </when>
                    <!-- 7日未跟进 -->
                    <when test="query.filterType == 'week'">
                        AND (lf.last_follow_time IS NULL OR lf.last_follow_time &lt; DATE_SUB(NOW(), INTERVAL 7 DAY))
                    </when>
                    <!-- 30天未跟进 -->
                    <when test="query.filterType == 'month'">
                        AND (lf.last_follow_time IS NULL OR lf.last_follow_time &lt; DATE_SUB(NOW(), INTERVAL 30 DAY))
                    </when>
                    <!-- 其他筛选条件保持不变，权限控制由数据权限框架处理 -->
                </choose>
            </if>

            <!-- 数据权限过滤 -->
            <if test="query.dataScopeEmployeeIds != null and query.dataScopeEmployeeIds.size() > 0">
                <choose>
                    <!-- 我的线索：只看分配给我的线索 -->
                    <when test="query.filterScope == 'my'">
                        AND l.assigned_employee_id IN
                        <foreach collection="query.dataScopeEmployeeIds" item="employeeId" open="(" close=")" separator=",">
                            #{employeeId}
                        </foreach>
                    </when>
                    <!-- 下属线索：只看分配给下属的线索 -->
                    <when test="query.filterScope == 'subordinate'">
                        AND l.assigned_employee_id IN
                        <foreach collection="query.dataScopeEmployeeIds" item="employeeId" open="(" close=")" separator=",">
                            #{employeeId}
                        </foreach>
                    </when>
                    <!-- 其他情况：创建的或分配的线索 -->
                    <otherwise>
                        AND (l.create_user_id IN
                        <foreach collection="query.dataScopeEmployeeIds" item="employeeId" open="(" close=")" separator=",">
                            #{employeeId}
                        </foreach>
                        OR l.assigned_employee_id IN
                        <foreach collection="query.dataScopeEmployeeIds" item="employeeId" open="(" close=")" separator=",">
                            #{employeeId}
                        </foreach>
                        )
                    </otherwise>
                </choose>
            </if>
        </where>
        ORDER BY l.create_time DESC
    </select>

    <!-- 根据手机号查询线索 -->
    <select id="selectByPhone" resultType="net.lab1024.sa.admin.module.business.hospital.lead.domain.entity.LeadEntity">
        SELECT * FROM t_lead
        WHERE customer_phone = #{customerPhone}
        AND deleted_flag = 0
        <if test="excludeLeadId != null">
            AND lead_id != #{excludeLeadId}
        </if>
        LIMIT 1
    </select>

    <!-- 批量更新删除状态 -->
    <update id="batchUpdateDeleted">
        UPDATE t_lead
        SET deleted_flag = #{deletedFlag},
            update_time = NOW()
        WHERE lead_id IN
        <foreach collection="leadIdList" item="leadId" open="(" separator="," close=")">
            #{leadId}
        </foreach>
    </update>

    <!-- 批量分配线索 -->
    <update id="batchAssign">
        UPDATE t_lead
        SET assigned_employee_id = #{assignedEmployeeId},
            update_time = NOW()
        WHERE lead_id IN
        <foreach collection="leadIdList" item="leadId" open="(" separator="," close=")">
            #{leadId}
        </foreach>
        AND deleted_flag = 0
    </update>

    <!-- 更新线索状态 -->
    <update id="updateStatus">
        UPDATE t_lead
        SET lead_status = #{leadStatus},
            update_time = NOW()
        WHERE lead_id = #{leadId}
        AND deleted_flag = 0
    </update>

    <!-- 获取导出数据 -->
    <select id="selectExcelExportData" resultType="net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadExcelVO">
        SELECT
            CASE l.lead_source
                WHEN 'BAIDU' THEN '百度'
                WHEN 'DOUYIN' THEN '抖音'
                WHEN 'KUAISHOU' THEN '快手'
                WHEN 'SHANGWUTONG' THEN '商务通'
                WHEN 'WECHAT' THEN '微信推广'
                WHEN 'FRIEND_REFERRAL' THEN '朋友介绍'
                WHEN 'PHONE_INQUIRY' THEN '电话咨询'
                WHEN 'ONLINE_PROMOTION' THEN '网络推广'
                WHEN 'OTHER' THEN '其他'
                ELSE l.lead_source
            END AS lead_source,
            l.customer_name,
            l.customer_phone,
            l.customer_wechat,
            CASE l.gender
                WHEN 1 THEN '男'
                WHEN 2 THEN '女'
                ELSE '未知'
            END AS gender_name,
            l.age,
            l.symptom,
            CASE l.lead_status
                WHEN 1 THEN '新线索'
                WHEN 2 THEN '跟进中'
                WHEN 3 THEN '已预约'
                WHEN 4 THEN '已转化'
                WHEN 5 THEN '已关闭'
                ELSE '未知'
            END AS lead_status_name,
            CASE l.lead_quality
                WHEN 1 THEN 'A级'
                WHEN 2 THEN 'B级'
                WHEN 3 THEN 'C级'
                ELSE '未评级'
            END AS lead_quality_name,
            e.actual_name AS assigned_employee_name,
            l.create_time,
            l.remark
        FROM t_lead l
        LEFT JOIN t_employee e ON l.assigned_employee_id = e.employee_id
        <where>
            l.deleted_flag = 0
            <if test="query.leadSource != null and query.leadSource != ''">
                AND l.lead_source LIKE CONCAT('%', #{query.leadSource}, '%')
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                AND l.customer_name LIKE CONCAT('%', #{query.customerName}, '%')
            </if>
            <if test="query.customerPhone != null and query.customerPhone != ''">
                AND l.customer_phone LIKE CONCAT('%', #{query.customerPhone}, '%')
            </if>
            <if test="query.leadStatus != null">
                AND l.lead_status = #{query.leadStatus}
            </if>
            <if test="query.leadQuality != null">
                AND l.lead_quality = #{query.leadQuality}
            </if>
            <if test="query.assignedEmployeeId != null">
                AND l.assigned_employee_id = #{query.assignedEmployeeId}
            </if>
            <if test="query.createTimeStart != null">
                AND l.create_time >= #{query.createTimeStart}
            </if>
            <if test="query.createTimeEnd != null">
                AND l.create_time &lt;= #{query.createTimeEnd}
            </if>
        </where>
        <!-- 默认过滤掉已到院和已转化的线索，除非明确指定查询这些状态 -->
        <if test="query.leadStatus == null">
            AND l.lead_status NOT IN (4, 5)
        </if>
        ORDER BY l.create_time DESC
    </select>

    <!-- 根据员工ID查询线索数量 -->
    <select id="countByEmployeeId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_lead
        WHERE assigned_employee_id = #{employeeId}
        AND deleted_flag = 0
    </select>

    <!-- 查询待跟进的线索 -->
    <select id="selectPendingFollowUp" resultMap="LeadVOMap">
        SELECT
            l.lead_id,
            l.lead_source,
            l.customer_name,
            l.customer_phone,
            l.lead_status,
            CASE l.lead_status
                WHEN 1 THEN '新线索'
                WHEN 2 THEN '跟进中'
                WHEN 3 THEN '已预约'
                WHEN 4 THEN '已转化'
                WHEN 5 THEN '已关闭'
                ELSE '未知'
            END AS lead_status_name,
            l.create_time
        FROM t_lead l
        WHERE l.assigned_employee_id = #{employeeId}
        AND l.deleted_flag = 0
        AND l.lead_status IN (1, 2)
        ORDER BY l.create_time ASC
    </select>

    <!-- 根据线索ID列表查询线索信息 -->
    <select id="selectByIdList" resultMap="LeadVOMap">
        SELECT
            l.lead_id,
            l.lead_source,
            l.customer_name,
            l.customer_phone,
            l.lead_status,
            l.lead_quality,
            l.assigned_employee_id,
            e.actual_name AS assigned_employee_name,
            l.create_time
        FROM t_lead l
        LEFT JOIN t_employee e ON l.assigned_employee_id = e.employee_id
        WHERE l.lead_id IN
        <foreach collection="leadIdList" item="leadId" open="(" separator="," close=")">
            #{leadId}
        </foreach>
        AND l.deleted_flag = 0
    </select>

    <!-- 统计总线索数 -->
    <select id="countTotal" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_lead
        WHERE deleted_flag = 0
    </select>

    <!-- 统计活跃线索数（排除已到院和已转化） -->
    <select id="countActiveLeads" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_lead
        WHERE deleted_flag = 0
        AND lead_status NOT IN (4, 5)
    </select>

    <!-- 统计指定时间范围内的线索数 -->
    <select id="countByDateRange" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_lead
        WHERE deleted_flag = 0
        AND create_time BETWEEN #{startTime} AND #{endTime}
    </select>

    <!-- 统计已转化的线索数 -->
    <select id="countConverted" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_lead
        WHERE deleted_flag = 0
        AND lead_status = 3
    </select>

    <!-- 根据手机号查询线索详细信息（用于重复检查） -->
    <select id="selectDuplicateCheckByPhone" resultType="net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadDuplicateCheckVO">
        SELECT
            TRUE as is_duplicate,
            l.lead_id,
            l.customer_name,
            l.customer_phone,
            l.assigned_employee_id as owner_id,
            e.actual_name as owner_name,
            lf.last_follow_time,
            CASE
                WHEN lf.last_follow_time IS NULL THEN DATEDIFF(NOW(), l.create_time)
                ELSE DATEDIFF(NOW(), lf.last_follow_time)
            END as days_since_last_follow,
            l.lead_status,
            CASE l.lead_status
                WHEN 1 THEN '新线索'
                WHEN 2 THEN '跟进中'
                WHEN 3 THEN '已预约'
                WHEN 4 THEN '已转化'
                WHEN 5 THEN '已关闭'
                WHEN 6 THEN '已到院'
                WHEN 7 THEN '已关闭'
                ELSE '未知'
            END as lead_status_desc,
            l.create_time,
            CASE
                WHEN l.lead_status IN (4, 5, 7) THEN FALSE
                ELSE TRUE
            END as can_apply_change,
            CASE
                WHEN l.lead_status IN (4, 5, 7) THEN '线索已关闭或已转化，不能申请变更'
                ELSE NULL
            END as cannot_apply_reason
        FROM t_lead l
        LEFT JOIN t_employee e ON l.assigned_employee_id = e.employee_id
        LEFT JOIN (
            SELECT
                lead_id,
                MAX(create_time) as last_follow_time
            FROM t_lead_follow
            WHERE deleted_flag = 0
            GROUP BY lead_id
        ) lf ON l.lead_id = lf.lead_id
        WHERE l.customer_phone = #{customerPhone}
        AND l.deleted_flag = 0
        <if test="excludeLeadId != null">
            AND l.lead_id != #{excludeLeadId}
        </if>
        LIMIT 1
    </select>

    <!-- ==================== 仪表盘统计查询 ==================== -->

    <!-- 统计总线索数（按用户） -->
    <select id="countTotalLeads" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_lead
        WHERE deleted_flag = 0
        <if test="userId != null">
            AND (assigned_employee_id = #{userId} OR create_user_id = #{userId})
        </if>
    </select>

    <!-- 统计今日新增线索 -->
    <select id="countTodayNewLeads" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_lead
        WHERE deleted_flag = 0
        AND DATE(create_time) = CURDATE()
        <if test="userId != null">
            AND (assigned_employee_id = #{userId} OR create_user_id = #{userId})
        </if>
    </select>

    <!-- 按状态统计线索数量 -->
    <select id="countLeadsByStatus" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_lead
        WHERE deleted_flag = 0
        AND lead_status = #{status}
        <if test="userId != null">
            AND (assigned_employee_id = #{userId} OR create_user_id = #{userId})
        </if>
    </select>

    <!-- 统计今日待跟进线索 -->
    <select id="countTodayPendingLeads" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_lead
        WHERE deleted_flag = 0
        AND lead_status = 1
        AND DATE(create_time) = CURDATE()
        <if test="userId != null">
            AND (assigned_employee_id = #{userId} OR create_user_id = #{userId})
        </if>
    </select>

    <!-- 统计本月转化线索 -->
    <select id="countMonthlyConverted" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_lead
        WHERE deleted_flag = 0
        AND lead_status = 5
        AND YEAR(create_time) = YEAR(CURDATE())
        AND MONTH(create_time) = MONTH(CURDATE())
        <if test="userId != null">
            AND (assigned_employee_id = #{userId} OR create_user_id = #{userId})
        </if>
    </select>

    <!-- 统计本月预约数量 -->
    <select id="countMonthlyAppointments" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_lead
        WHERE deleted_flag = 0
        AND lead_status = 3
        AND YEAR(create_time) = YEAR(CURDATE())
        AND MONTH(create_time) = MONTH(CURDATE())
        <if test="userId != null">
            AND (assigned_employee_id = #{userId} OR create_user_id = #{userId})
        </if>
    </select>

    <!-- 统计本月到院数量 -->
    <select id="countMonthlyArrivals" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_lead
        WHERE deleted_flag = 0
        AND lead_status = 4
        AND YEAR(create_time) = YEAR(CURDATE())
        AND MONTH(create_time) = MONTH(CURDATE())
        <if test="userId != null">
            AND (assigned_employee_id = #{userId} OR create_user_id = #{userId})
        </if>
    </select>

    <!-- 统计本月总线索数 -->
    <select id="countMonthlyTotal" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_lead
        WHERE deleted_flag = 0
        AND YEAR(create_time) = YEAR(CURDATE())
        AND MONTH(create_time) = MONTH(CURDATE())
        <if test="userId != null">
            AND (assigned_employee_id = #{userId} OR create_user_id = #{userId})
        </if>
    </select>

    <!-- 获取线索来源统计 -->
    <select id="getLeadSourceStats" resultType="java.util.Map">
        SELECT
            lead_source as leadSource,
            COUNT(*) as count
        FROM t_lead
        WHERE deleted_flag = 0
        AND lead_source IS NOT NULL
        AND lead_source != ''
        <if test="userId != null">
            AND (assigned_employee_id = #{userId} OR create_user_id = #{userId})
        </if>
        GROUP BY lead_source
        ORDER BY count DESC
    </select>

    <!-- 获取线索状态统计 -->
    <select id="getLeadStatusStats" resultType="java.util.Map">
        SELECT
            lead_status as leadStatus,
            COUNT(*) as count
        FROM t_lead
        WHERE deleted_flag = 0
        <if test="userId != null">
            AND (assigned_employee_id = #{userId} OR create_user_id = #{userId})
        </if>
        GROUP BY lead_status
        ORDER BY lead_status
    </select>

    <!-- 获取线索质量统计 -->
    <select id="getLeadQualityStats" resultType="java.util.Map">
        SELECT
            lead_quality as leadQuality,
            COUNT(*) as count
        FROM t_lead
        WHERE deleted_flag = 0
        AND lead_quality IS NOT NULL
        <if test="userId != null">
            AND (assigned_employee_id = #{userId} OR create_user_id = #{userId})
        </if>
        GROUP BY lead_quality
        ORDER BY lead_quality
    </select>

    <!-- 获取症状统计 -->
    <select id="getSymptomStats" resultType="java.util.Map">
        SELECT
            symptom,
            COUNT(*) as count
        FROM t_lead
        WHERE deleted_flag = 0
        AND symptom IS NOT NULL
        AND symptom != ''
        <if test="userId != null">
            AND (assigned_employee_id = #{userId} OR create_user_id = #{userId})
        </if>
        GROUP BY symptom
        ORDER BY count DESC
        LIMIT 10
    </select>

    <!-- 获取地区统计 -->
    <select id="getRegionStats" resultType="java.util.Map">
        SELECT
            region,
            COUNT(*) as count
        FROM t_lead
        WHERE deleted_flag = 0
        AND region IS NOT NULL
        AND region != ''
        <if test="userId != null">
            AND (assigned_employee_id = #{userId} OR create_user_id = #{userId})
        </if>
        GROUP BY region
        ORDER BY count DESC
        LIMIT 10
    </select>

    <!-- 获取年龄分布统计 -->
    <select id="getAgeStats" resultType="java.util.Map">
        SELECT
            CASE
                WHEN age IS NULL THEN '未知'
                WHEN age &lt; 18 THEN '18岁以下'
                WHEN age BETWEEN 18 AND 25 THEN '18-25岁'
                WHEN age BETWEEN 26 AND 35 THEN '26-35岁'
                WHEN age BETWEEN 36 AND 45 THEN '36-45岁'
                WHEN age BETWEEN 46 AND 55 THEN '46-55岁'
                WHEN age BETWEEN 56 AND 65 THEN '56-65岁'
                ELSE '65岁以上'
            END as ageGroup,
            COUNT(*) as count
        FROM t_lead
        WHERE deleted_flag = 0
        <if test="userId != null">
            AND (assigned_employee_id = #{userId} OR create_user_id = #{userId})
        </if>
        GROUP BY
            CASE
                WHEN age IS NULL THEN '未知'
                WHEN age &lt; 18 THEN '18岁以下'
                WHEN age BETWEEN 18 AND 25 THEN '18-25岁'
                WHEN age BETWEEN 26 AND 35 THEN '26-35岁'
                WHEN age BETWEEN 36 AND 45 THEN '36-45岁'
                WHEN age BETWEEN 46 AND 55 THEN '46-55岁'
                WHEN age BETWEEN 56 AND 65 THEN '56-65岁'
                ELSE '65岁以上'
            END
        ORDER BY count DESC
    </select>

    <!-- 获取性别分布统计 -->
    <select id="getGenderStats" resultType="java.util.Map">
        SELECT
            gender,
            COUNT(*) as count
        FROM t_lead
        WHERE deleted_flag = 0
        AND gender IS NOT NULL
        <if test="userId != null">
            AND (assigned_employee_id = #{userId} OR create_user_id = #{userId})
        </if>
        GROUP BY gender
        ORDER BY gender
    </select>

    <!-- 获取线索趋势统计 -->
    <select id="getLeadTrendStats" resultType="java.util.Map">
        SELECT
            DATE(create_time) as date,
            COUNT(*) as newLeads,
            SUM(CASE WHEN lead_status = 3 THEN 1 ELSE 0 END) as appointments,
            SUM(CASE WHEN lead_status = 4 THEN 1 ELSE 0 END) as arrivals,
            SUM(CASE WHEN lead_status = 5 THEN 1 ELSE 0 END) as conversions
        FROM t_lead
        WHERE deleted_flag = 0
        <if test="startDate != null and endDate != null">
            AND DATE(create_time) BETWEEN #{startDate} AND #{endDate}
        </if>
        <if test="userId != null">
            AND (assigned_employee_id = #{userId} OR create_user_id = #{userId})
        </if>
        GROUP BY DATE(create_time)
        ORDER BY date DESC
        LIMIT 30
    </select>

</mapper>

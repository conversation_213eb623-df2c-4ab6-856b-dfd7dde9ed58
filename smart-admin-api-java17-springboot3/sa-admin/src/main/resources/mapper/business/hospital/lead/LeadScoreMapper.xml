<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.hospital.lead.dao.LeadScoreDao">

    <!-- 根据线索ID查询评分 -->
    <select id="selectByLeadId" resultType="net.lab1024.sa.admin.module.business.hospital.lead.domain.entity.LeadScoreEntity">
        SELECT 
            score_id,
            lead_id,
            total_score,
            follow_score,
            response_score,
            intention_score,
            conversion_probability,
            last_calculate_time,
            create_time,
            update_time
        FROM t_lead_score
        WHERE lead_id = #{leadId}
    </select>

    <!-- 更新线索评分 -->
    <update id="updateScore">
        UPDATE t_lead_score 
        SET 
            total_score = #{totalScore},
            follow_score = #{followScore},
            response_score = #{responseScore},
            intention_score = #{intentionScore},
            conversion_probability = #{conversionProbability},
            last_calculate_time = NOW(),
            update_time = NOW()
        WHERE lead_id = #{leadId}
    </update>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.hospital.appointment.dao.AppointmentDao">

    <!-- 预约VO结果映射 -->
    <resultMap id="AppointmentVOMap" type="net.lab1024.sa.admin.module.business.hospital.appointment.domain.vo.AppointmentVO">
        <id column="appointment_id" property="appointmentId"/>
        <result column="lead_id" property="leadId"/>
        <result column="customer_id" property="customerId"/>
        <result column="customer_name" property="customerName"/>
        <result column="customer_phone" property="customerPhone"/>
        <result column="gender" property="gender"/>
        <result column="age" property="age"/>
        <result column="wechat" property="wechat"/>
        <result column="symptom" property="symptom"/>
        <result column="region" property="region"/>
        <result column="assigned_employee_id" property="assignedEmployeeId"/>
        <result column="assigned_employee_name" property="assignedEmployeeName"/>
        <result column="appointment_date" property="appointmentDate"/>
        <result column="appointment_time" property="appointmentTime"/>
        <result column="appointment_status" property="appointmentStatus"/>
        <result column="appointment_status_name" property="appointmentStatusName"/>
        <result column="actual_arrival_time" property="actualArrivalTime"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_user_name" property="createUserName"/>
    </resultMap>

    <!-- 分页查询预约 -->
    <select id="queryPage" resultMap="AppointmentVOMap">
        SELECT
            a.appointment_id,
            a.lead_id,
            a.customer_id,
            a.customer_name,
            a.customer_phone,
            COALESCE(a.gender, c.gender, l.gender) AS gender,
            COALESCE(a.age, c.age, l.age) AS age,
            COALESCE(a.wechat, c.customer_wechat, l.customer_wechat) AS wechat,
            COALESCE(a.symptom, l.symptom, '') AS symptom,
            COALESCE(a.region, l.region, '') AS region,
            a.assigned_employee_id,
            ae.actual_name AS assigned_employee_name,
            a.appointment_date,
            a.appointment_time,
            a.appointment_status,
            CASE a.appointment_status
                WHEN 1 THEN '未到诊'
                WHEN 2 THEN '已到诊'
                ELSE '未知'
            END AS appointment_status_name,
            a.actual_arrival_time,
            a.remark,
            a.create_time,
            a.update_time,
            creator.actual_name AS create_user_name
        FROM t_appointment a
        LEFT JOIN t_employee creator ON a.create_user_id = creator.employee_id
        LEFT JOIN t_employee ae ON a.assigned_employee_id = ae.employee_id
        LEFT JOIN t_customer c ON a.customer_id = c.customer_id
        LEFT JOIN t_lead l ON a.lead_id = l.lead_id
        <where>
            a.deleted_flag = 0
            <if test="query.customerName != null and query.customerName != ''">
                AND a.customer_name LIKE CONCAT('%', #{query.customerName}, '%')
            </if>
            <if test="query.customerPhone != null and query.customerPhone != ''">
                AND a.customer_phone LIKE CONCAT('%', #{query.customerPhone}, '%')
            </if>

            <if test="query.appointmentStatus != null">
                AND a.appointment_status = #{query.appointmentStatus}
            </if>
            <if test="query.appointmentDateStart != null">
                AND a.appointment_date >= #{query.appointmentDateStart}
            </if>
            <if test="query.appointmentDateEnd != null">
                AND a.appointment_date &lt;= #{query.appointmentDateEnd}
            </if>
            <if test="query.createTimeStart != null">
                AND a.create_time >= #{query.createTimeStart}
            </if>
            <if test="query.createTimeEnd != null">
                AND a.create_time &lt;= #{query.createTimeEnd}
            </if>
            <if test="query.searchWord != null and query.searchWord != ''">
                AND (
                    a.customer_name LIKE CONCAT('%', #{query.searchWord}, '%')
                    OR a.customer_phone LIKE CONCAT('%', #{query.searchWord}, '%')
                    OR p.project_name LIKE CONCAT('%', #{query.searchWord}, '%')
                    OR e.actual_name LIKE CONCAT('%', #{query.searchWord}, '%')
                )
            </if>
            <!-- 筛选条件 -->
            <if test="query.filterType != null and query.filterType != ''">
                <choose>
                    <!-- 预计今日到院 -->
                    <when test="query.filterType == 'today'">
                        AND a.appointment_date = CURDATE()
                        AND a.appointment_status = 1
                    </when>
                    <!-- 预计3日内到院 -->
                    <when test="query.filterType == 'three_days'">
                        AND a.appointment_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 3 DAY)
                        AND a.appointment_status = 1
                    </when>
                    <!-- 本月已到院 -->
                    <when test="query.filterType == 'month_arrived'">
                        AND a.appointment_status = 2
                        AND (
                            (a.actual_arrival_time IS NOT NULL
                             AND YEAR(a.actual_arrival_time) = YEAR(CURDATE())
                             AND MONTH(a.actual_arrival_time) = MONTH(CURDATE()))
                            OR
                            (a.actual_arrival_time IS NULL
                             AND YEAR(a.update_time) = YEAR(CURDATE())
                             AND MONTH(a.update_time) = MONTH(CURDATE()))
                        )
                    </when>
                </choose>
            </if>
        </where>
        ORDER BY a.appointment_status ASC, a.appointment_date DESC, a.appointment_time DESC
    </select>

    <!-- 检查时间冲突 -->
    <select id="checkTimeConflict" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_appointment
        WHERE assigned_employee_id = #{doctorId}
        AND appointment_date = #{appointmentDate}
        AND appointment_time = #{appointmentTime}
        AND appointment_status IN (1, 2)
        AND deleted_flag = 0
        <if test="excludeAppointmentId != null">
            AND appointment_id != #{excludeAppointmentId}
        </if>
    </select>

    <!-- 更新预约状态 -->
    <update id="updateStatus">
        UPDATE t_appointment
        SET appointment_status = #{appointmentStatus},
            update_time = NOW()
        WHERE appointment_id = #{appointmentId}
        AND deleted_flag = 0
    </update>

    <!-- 更新预约状态和实际到院时间 -->
    <update id="updateStatusAndArrivalTime">
        UPDATE t_appointment
        SET appointment_status = #{appointmentStatus},
            actual_arrival_time = #{actualArrivalTime},
            update_time = NOW()
        WHERE appointment_id = #{appointmentId}
        AND deleted_flag = 0
    </update>

    <!-- 批量更新删除状态 -->
    <update id="batchUpdateDeleted">
        UPDATE t_appointment
        SET deleted_flag = #{deletedFlag},
            update_time = NOW()
        WHERE appointment_id IN
        <foreach collection="appointmentIdList" item="appointmentId" open="(" separator="," close=")">
            #{appointmentId}
        </foreach>
    </update>

    <!-- 获取预约日历数据 -->
    <select id="getCalendarData" resultType="net.lab1024.sa.admin.module.business.hospital.appointment.domain.vo.AppointmentCalendarVO">
        SELECT
            a.appointment_id,
            a.customer_name AS title,
            CONCAT(a.appointment_date, ' ', a.appointment_time) AS start,
            CONCAT(a.appointment_date, ' ', ADDTIME(a.appointment_time, '01:00:00')) AS end,
            CASE a.appointment_status
                WHEN 1 THEN '#faad14'
                WHEN 2 THEN '#52c41a'
                ELSE '#d9d9d9'
            END AS color,
            a.customer_name,
            a.customer_phone,
            '' AS project_name,
            e.actual_name AS doctor_name,
            a.appointment_status,
            CASE a.appointment_status
                WHEN 1 THEN '未到诊'
                WHEN 2 THEN '已到诊'
                ELSE '未知'
            END AS appointment_status_name,
            a.appointment_date,
            a.appointment_time,
            a.remark
        FROM t_appointment a
        LEFT JOIN t_employee e ON a.assigned_employee_id = e.employee_id
        WHERE a.deleted_flag = 0
        AND a.appointment_date BETWEEN #{startDate} AND #{endDate}
        <if test="doctorId != null">
            AND a.assigned_employee_id = #{doctorId}
        </if>
        ORDER BY a.appointment_date, a.appointment_time
    </select>

    <!-- 根据医生和日期时间统计预约数量 -->
    <select id="countByDoctorAndDateTime" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_appointment
        WHERE assigned_employee_id = #{doctorId}
        AND appointment_date = #{appointmentDate}
        AND appointment_time = #{appointmentTime}
        AND appointment_status IN (1, 2, 3)
        AND deleted_flag = 0
    </select>

    <!-- 根据线索ID查询预约 -->
    <select id="selectByLeadId" resultMap="AppointmentVOMap">
        SELECT
            a.appointment_id,
            a.lead_id,
            a.customer_name,
            a.customer_phone,
            '' AS project_name,
            e.actual_name AS doctor_name,
            a.appointment_date,
            a.appointment_time,
            a.appointment_status,
            CASE a.appointment_status
                WHEN 1 THEN '未到诊'
                WHEN 2 THEN '已到诊'
                ELSE '未知'
            END AS appointment_status_name,
            a.create_time
        FROM t_appointment a
        LEFT JOIN t_employee e ON a.assigned_employee_id = e.employee_id
        WHERE a.lead_id = #{leadId}
        AND a.deleted_flag = 0
        ORDER BY a.create_time DESC
    </select>

    <!-- 根据客户ID查询预约 -->
    <select id="selectByCustomerId" resultMap="AppointmentVOMap">
        SELECT
            a.appointment_id,
            a.customer_id,
            a.customer_name,
            a.customer_phone,
            '' AS project_name,
            e.actual_name AS doctor_name,
            a.appointment_date,
            a.appointment_time,
            a.appointment_status,
            CASE a.appointment_status
                WHEN 1 THEN '未到诊'
                WHEN 2 THEN '已到诊'
                ELSE '未知'
            END AS appointment_status_name,
            a.create_time
        FROM t_appointment a
        LEFT JOIN t_employee e ON a.assigned_employee_id = e.employee_id
        WHERE a.customer_id = #{customerId}
        AND a.deleted_flag = 0
        ORDER BY a.create_time DESC
    </select>

    <!-- 获取今日预约统计 -->
    <select id="getTodayAppointmentCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_appointment
        WHERE appointment_date = #{appointmentDate}
        AND appointment_status IN (1, 2)
        AND deleted_flag = 0
    </select>

    <!-- 获取待确认预约数量 -->
    <select id="getPendingConfirmCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_appointment
        WHERE appointment_status = 1
        AND deleted_flag = 0
    </select>

    <!-- 统计总预约数 -->
    <select id="countTotal" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM t_appointment
        WHERE deleted_flag = 0
    </select>

    <!-- 统计指定时间范围内的预约数 -->
    <select id="countByDateRange" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM t_appointment
        WHERE deleted_flag = 0
          AND create_time >= #{startTime}
          AND create_time &lt;= #{endTime}
    </select>

    <!-- 统计已完成的预约数 -->
    <select id="countCompleted" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM t_appointment
        WHERE deleted_flag = 0
          AND appointment_status = 2
    </select>

    <!-- 统计已到院预约数量 -->
    <select id="countArrived" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM t_appointment
        WHERE appointment_status = 2
        AND deleted_flag = 0
    </select>

    <!-- 统计指定时间范围内已到院预约数量 -->
    <select id="countArrivedByDateRange" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM t_appointment
        WHERE appointment_status = 2
        AND deleted_flag = 0
        AND actual_arrival_time BETWEEN #{startTime} AND #{endTime}
    </select>

    <!-- 获取总收入 -->
    <select id="getTotalRevenue" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(cr.charge_amount), 0)
        FROM t_charge_record cr
        WHERE cr.deleted_flag = 0
          AND cr.charge_status = 2
    </select>

    <!-- 获取指定时间范围内的收入 -->
    <select id="getRevenueByDateRange" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(cr.charge_amount), 0)
        FROM t_charge_record cr
        WHERE cr.deleted_flag = 0
          AND cr.charge_status = 2
          AND cr.create_time >= #{startTime}
          AND cr.create_time &lt;= #{endTime}
    </select>

</mapper>

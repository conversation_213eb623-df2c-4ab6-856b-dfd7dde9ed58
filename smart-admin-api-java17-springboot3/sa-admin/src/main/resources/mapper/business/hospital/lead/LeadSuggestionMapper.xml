<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.hospital.lead.dao.LeadSuggestionDao">

    <!-- 根据线索ID查询建议列表 -->
    <select id="selectByLeadId" resultType="net.lab1024.sa.admin.module.business.hospital.lead.domain.entity.LeadSuggestionEntity">
        SELECT 
            suggestion_id,
            lead_id,
            suggestion_type,
            title,
            description,
            priority,
            actionable,
            action_text,
            action_data,
            status,
            create_time,
            update_time
        FROM t_lead_suggestion
        WHERE lead_id = #{leadId}
        ORDER BY priority ASC, create_time DESC
    </select>

    <!-- 根据线索ID和状态查询建议列表 -->
    <select id="selectByLeadIdAndStatus" resultType="net.lab1024.sa.admin.module.business.hospital.lead.domain.entity.LeadSuggestionEntity">
        SELECT 
            suggestion_id,
            lead_id,
            suggestion_type,
            title,
            description,
            priority,
            actionable,
            action_text,
            action_data,
            status,
            create_time,
            update_time
        FROM t_lead_suggestion
        WHERE lead_id = #{leadId} AND status = #{status}
        ORDER BY priority ASC, create_time DESC
    </select>

    <!-- 更新建议状态 -->
    <update id="updateStatus">
        UPDATE t_lead_suggestion 
        SET 
            status = #{status},
            update_time = NOW()
        WHERE suggestion_id = #{suggestionId}
    </update>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.hospital.lead.dao.LeadFollowReminderDao">

    <!-- 结果映射 -->
    <resultMap id="LeadFollowReminderVO" type="net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadFollowReminderVO">
        <id column="reminder_id" property="reminderId"/>
        <result column="lead_id" property="leadId"/>
        <result column="customer_name" property="customerName"/>
        <result column="customer_phone" property="customerPhone"/>
        <result column="lead_source" property="leadSource"/>
        <result column="follow_time" property="followTime"/>
        <result column="reminder_time" property="reminderTime"/>
        <result column="reminder_type" property="reminderType"/>
        <result column="reminder_type_name" property="reminderTypeName"/>
        <result column="reminder_minutes" property="reminderMinutes"/>
        <result column="reminder_status" property="reminderStatus"/>
        <result column="reminder_status_name" property="reminderStatusName"/>
        <result column="sent_time" property="sentTime"/>
        <result column="reminder_content" property="reminderContent"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="overdue" property="overdue"/>
        <result column="minutes_to_follow" property="minutesToFollow"/>
    </resultMap>

    <!-- 根据用户ID查询跟进提醒 -->
    <select id="selectByUserId" resultMap="LeadFollowReminderVO">
        SELECT 
            r.reminder_id,
            r.lead_id,
            r.follow_time,
            r.reminder_time,
            r.reminder_type,
            CASE 
                WHEN r.reminder_type = 1 THEN '系统通知'
                WHEN r.reminder_type = 2 THEN '短信提醒'
                WHEN r.reminder_type = 3 THEN '邮件提醒'
                ELSE '未知'
            END as reminder_type_name,
            r.reminder_minutes,
            r.reminder_status,
            CASE 
                WHEN r.reminder_status = 1 THEN '待提醒'
                WHEN r.reminder_status = 2 THEN '已发送'
                WHEN r.reminder_status = 3 THEN '已取消'
                WHEN r.reminder_status = 4 THEN '已完成'
                ELSE '未知'
            END as reminder_status_name,
            r.sent_time,
            r.reminder_content,
            r.create_time,
            l.customer_name,
            l.customer_phone,
            l.lead_source,
            e.actual_name as create_user_name,
            CASE 
                WHEN r.follow_time &lt; NOW() THEN 1
                ELSE 0
            END as overdue,
            TIMESTAMPDIFF(MINUTE, NOW(), r.follow_time) as minutes_to_follow
        FROM t_lead_follow_reminder r
        INNER JOIN t_lead l ON r.lead_id = l.lead_id
        LEFT JOIN t_employee e ON r.create_user_id = e.employee_id
        WHERE r.create_user_id = #{userId}
          AND r.deleted_flag = 0 
          AND l.deleted_flag = 0
        ORDER BY r.reminder_time ASC
    </select>

    <!-- 查询待提醒的记录 -->
    <select id="selectPendingReminders" resultMap="LeadFollowReminderVO">
        SELECT 
            r.reminder_id,
            r.lead_id,
            r.follow_time,
            r.reminder_time,
            r.reminder_type,
            CASE 
                WHEN r.reminder_type = 1 THEN '系统通知'
                WHEN r.reminder_type = 2 THEN '短信提醒'
                WHEN r.reminder_type = 3 THEN '邮件提醒'
                ELSE '未知'
            END as reminder_type_name,
            r.reminder_minutes,
            r.reminder_status,
            '待提醒' as reminder_status_name,
            r.sent_time,
            r.reminder_content,
            r.create_time,
            l.customer_name,
            l.customer_phone,
            l.lead_source,
            e.actual_name as create_user_name,
            CASE 
                WHEN r.follow_time &lt; NOW() THEN 1
                ELSE 0
            END as overdue,
            TIMESTAMPDIFF(MINUTE, NOW(), r.follow_time) as minutes_to_follow
        FROM t_lead_follow_reminder r
        INNER JOIN t_lead l ON r.lead_id = l.lead_id
        LEFT JOIN t_employee e ON r.create_user_id = e.employee_id
        WHERE r.reminder_status = 1
          AND r.reminder_time &lt;= #{currentTime}
          AND r.deleted_flag = 0 
          AND l.deleted_flag = 0
        ORDER BY r.reminder_time ASC
    </select>

    <!-- 更新提醒状态 -->
    <update id="updateReminderStatus">
        UPDATE t_lead_follow_reminder 
        SET reminder_status = #{status},
            sent_time = #{sentTime},
            update_time = NOW()
        WHERE reminder_id = #{reminderId}
    </update>

    <!-- 完成线索相关的所有提醒 -->
    <update id="completeRemindersByLeadId">
        UPDATE t_lead_follow_reminder 
        SET reminder_status = 4,
            sent_time = #{completeTime},
            update_time = NOW()
        WHERE lead_id = #{leadId}
          AND reminder_status IN (1, 2)
          AND deleted_flag = 0
    </update>

    <!-- 查询用户今日待跟进的线索 -->
    <select id="selectTodayFollowReminders" resultMap="LeadFollowReminderVO">
        SELECT 
            r.reminder_id,
            r.lead_id,
            r.follow_time,
            r.reminder_time,
            r.reminder_type,
            CASE 
                WHEN r.reminder_type = 1 THEN '系统通知'
                WHEN r.reminder_type = 2 THEN '短信提醒'
                WHEN r.reminder_type = 3 THEN '邮件提醒'
                ELSE '未知'
            END as reminder_type_name,
            r.reminder_minutes,
            r.reminder_status,
            CASE 
                WHEN r.reminder_status = 1 THEN '待提醒'
                WHEN r.reminder_status = 2 THEN '已发送'
                WHEN r.reminder_status = 3 THEN '已取消'
                WHEN r.reminder_status = 4 THEN '已完成'
                ELSE '未知'
            END as reminder_status_name,
            r.sent_time,
            r.reminder_content,
            r.create_time,
            l.customer_name,
            l.customer_phone,
            l.lead_source,
            e.actual_name as create_user_name,
            CASE 
                WHEN r.follow_time &lt; NOW() THEN 1
                ELSE 0
            END as overdue,
            TIMESTAMPDIFF(MINUTE, NOW(), r.follow_time) as minutes_to_follow
        FROM t_lead_follow_reminder r
        INNER JOIN t_lead l ON r.lead_id = l.lead_id
        LEFT JOIN t_employee e ON r.create_user_id = e.employee_id
        WHERE r.create_user_id = #{userId}
          AND r.follow_time BETWEEN #{startTime} AND #{endTime}
          AND r.reminder_status IN (1, 2)
          AND r.deleted_flag = 0 
          AND l.deleted_flag = 0
        ORDER BY r.follow_time ASC
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.hospital.visit.dao.PrescriptionDao">

    <!-- 分页查询开单记录 -->
    <select id="queryPage" resultType="net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.PrescriptionVO">
        SELECT
            p.prescription_id,
            p.diagnosis_id,
            p.patient_id,
            p.patient_no,
            p.patient_name,
            p.prescription_no,
            p.total_amount,
            p.prescription_note,
            p.prescription_status,
            CASE p.prescription_status
                WHEN 1 THEN '已开单'
                WHEN 2 THEN '已收费'
                WHEN 3 THEN '已完成'
                ELSE '未知'
            END AS prescription_status_name,
            p.create_user_name,
            p.create_time,
            p.update_user_name,
            p.update_time
        FROM t_prescription p
        WHERE p.deleted_flag = 0
        <if test="queryForm.diagnosisId != null">
            AND p.diagnosis_id = #{queryForm.diagnosisId}
        </if>
        <if test="queryForm.patientId != null">
            AND p.patient_id = #{queryForm.patientId}
        </if>
        <if test="queryForm.patientNo != null and queryForm.patientNo != ''">
            AND p.patient_no LIKE CONCAT('%', #{queryForm.patientNo}, '%')
        </if>
        <if test="queryForm.patientName != null and queryForm.patientName != ''">
            AND p.patient_name LIKE CONCAT('%', #{queryForm.patientName}, '%')
        </if>
        <if test="queryForm.prescriptionNo != null and queryForm.prescriptionNo != ''">
            AND p.prescription_no LIKE CONCAT('%', #{queryForm.prescriptionNo}, '%')
        </if>
        <if test="queryForm.prescriptionStatus != null">
            AND p.prescription_status = #{queryForm.prescriptionStatus}
        </if>
        <if test="queryForm.createTimeStart != null">
            AND p.create_time >= #{queryForm.createTimeStart}
        </if>
        <if test="queryForm.createTimeEnd != null">
            AND p.create_time &lt;= #{queryForm.createTimeEnd}
        </if>
        ORDER BY p.create_time DESC
    </select>

    <!-- 根据患者ID查询开单记录 -->
    <select id="selectByPatientId" resultType="net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.PrescriptionVO">
        SELECT
            p.prescription_id,
            p.diagnosis_id,
            p.patient_id,
            p.patient_no,
            p.patient_name,
            p.prescription_no,
            p.total_amount,
            p.prescription_note,
            p.prescription_status,
            CASE p.prescription_status
                WHEN 1 THEN '已开单'
                WHEN 2 THEN '已收费'
                WHEN 3 THEN '已完成'
                ELSE '未知'
            END AS prescription_status_name,
            p.create_user_name,
            p.create_time,
            p.update_user_name,
            p.update_time
        FROM t_prescription p
        WHERE p.deleted_flag = 0
        AND p.patient_id = #{patientId}
        ORDER BY p.create_time DESC
    </select>

    <!-- 根据诊断ID查询开单记录 -->
    <select id="selectByDiagnosisId" resultType="net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.PrescriptionVO">
        SELECT
            p.prescription_id,
            p.diagnosis_id,
            p.patient_id,
            p.patient_no,
            p.patient_name,
            p.prescription_no,
            p.total_amount,
            p.prescription_note,
            p.prescription_status,
            CASE p.prescription_status
                WHEN 1 THEN '已开单'
                WHEN 2 THEN '已收费'
                WHEN 3 THEN '已完成'
                ELSE '未知'
            END AS prescription_status_name,
            p.create_user_name,
            p.create_time,
            p.update_user_name,
            p.update_time
        FROM t_prescription p
        WHERE p.deleted_flag = 0
        AND p.diagnosis_id = #{diagnosisId}
    </select>

    <!-- 根据开单ID查询详情 -->
    <select id="selectDetailById" resultType="net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.PrescriptionVO">
        SELECT
            p.prescription_id,
            p.diagnosis_id,
            p.patient_id,
            p.patient_no,
            p.patient_name,
            p.prescription_no,
            p.total_amount,
            p.prescription_note,
            p.prescription_status,
            CASE p.prescription_status
                WHEN 1 THEN '已开单'
                WHEN 2 THEN '已收费'
                WHEN 3 THEN '已完成'
                ELSE '未知'
            END AS prescription_status_name,
            p.create_user_name,
            p.create_time,
            p.update_user_name,
            p.update_time
        FROM t_prescription p
        WHERE p.deleted_flag = 0
        AND p.prescription_id = #{prescriptionId}
    </select>

    <!-- 更新开单状态 -->
    <update id="updatePrescriptionStatus">
        UPDATE t_prescription
        SET prescription_status = #{prescriptionStatus},
            update_user_id = #{updateUserId},
            update_user_name = #{updateUserName},
            update_time = NOW()
        WHERE prescription_id = #{prescriptionId}
        AND deleted_flag = 0
    </update>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.hospital.visit.dao.DoctorAssistantRelationDao">

    <!-- 根据医生ID查询助理关系 -->
    <select id="selectByDoctorId" resultType="net.lab1024.sa.admin.module.business.hospital.visit.domain.entity.DoctorAssistantRelationEntity">
        SELECT * FROM t_doctor_assistant_relation
        WHERE doctor_id = #{doctorId}
        AND deleted_flag = 0
        AND relation_status = 1
        ORDER BY create_time DESC
    </select>

    <!-- 根据助理ID查询医生关系 -->
    <select id="selectByAssistantId" resultType="net.lab1024.sa.admin.module.business.hospital.visit.domain.entity.DoctorAssistantRelationEntity">
        SELECT * FROM t_doctor_assistant_relation
        WHERE assistant_id = #{assistantId}
        AND deleted_flag = 0
        AND relation_status = 1
        ORDER BY create_time DESC
    </select>

    <!-- 根据部门ID查询关系 -->
    <select id="selectByDepartmentId" resultType="net.lab1024.sa.admin.module.business.hospital.visit.domain.entity.DoctorAssistantRelationEntity">
        SELECT * FROM t_doctor_assistant_relation
        WHERE department_id = #{departmentId}
        AND deleted_flag = 0
        AND relation_status = 1
        ORDER BY create_time DESC
    </select>

    <!-- 检查医生助理关系是否存在 -->
    <select id="selectByDoctorAndAssistant" resultType="net.lab1024.sa.admin.module.business.hospital.visit.domain.entity.DoctorAssistantRelationEntity">
        SELECT * FROM t_doctor_assistant_relation
        WHERE doctor_id = #{doctorId}
        AND assistant_id = #{assistantId}
        AND deleted_flag = 0
        LIMIT 1
    </select>

</mapper>

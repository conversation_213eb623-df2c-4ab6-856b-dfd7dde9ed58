<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.hospital.visit.dao.DiagnosisDao">

    <!-- 分页查询诊断记录 -->
    <select id="queryPage" resultType="net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.DiagnosisVO">
        SELECT
            d.diagnosis_id,
            d.patient_id,
            d.patient_no,
            d.patient_name,
            d.diagnosis_result,
            d.diagnosis_type,
            CASE d.diagnosis_type
                WHEN 1 THEN '初诊'
                WHEN 2 THEN '复诊'
                WHEN 3 THEN '会诊'
                ELSE '未知'
            END AS diagnosis_type_name,
            d.diagnosis_description,
            d.diagnosis_doctor_id,
            d.diagnosis_doctor_name,
            d.diagnosis_time,
            d.diagnosis_status,
            CASE d.diagnosis_status
                WHEN 1 THEN '已诊断'
                WHEN 2 THEN '已开单'
                WHEN 3 THEN '已收费'
                WHEN 4 THEN '已完成'
                ELSE '未知'
            END AS diagnosis_status_name,
            d.create_user_name,
            d.create_time,
            d.update_user_name,
            d.update_time
        FROM t_diagnosis d
        WHERE d.deleted_flag = 0
        <if test="queryForm.patientId != null">
            AND d.patient_id = #{queryForm.patientId}
        </if>
        <if test="queryForm.patientNo != null and queryForm.patientNo != ''">
            AND d.patient_no LIKE CONCAT('%', #{queryForm.patientNo}, '%')
        </if>
        <if test="queryForm.patientName != null and queryForm.patientName != ''">
            AND d.patient_name LIKE CONCAT('%', #{queryForm.patientName}, '%')
        </if>
        <if test="queryForm.diagnosisType != null">
            AND d.diagnosis_type = #{queryForm.diagnosisType}
        </if>
        <if test="queryForm.diagnosisDoctorId != null">
            AND d.diagnosis_doctor_id = #{queryForm.diagnosisDoctorId}
        </if>
        <if test="queryForm.diagnosisDoctorName != null and queryForm.diagnosisDoctorName != ''">
            AND d.diagnosis_doctor_name LIKE CONCAT('%', #{queryForm.diagnosisDoctorName}, '%')
        </if>
        <if test="queryForm.diagnosisStatus != null">
            AND d.diagnosis_status = #{queryForm.diagnosisStatus}
        </if>
        <if test="queryForm.diagnosisTimeStart != null">
            AND d.diagnosis_time >= #{queryForm.diagnosisTimeStart}
        </if>
        <if test="queryForm.diagnosisTimeEnd != null">
            AND d.diagnosis_time &lt;= #{queryForm.diagnosisTimeEnd}
        </if>
        <if test="queryForm.createTimeStart != null">
            AND d.create_time >= #{queryForm.createTimeStart}
        </if>
        <if test="queryForm.createTimeEnd != null">
            AND d.create_time &lt;= #{queryForm.createTimeEnd}
        </if>
        ORDER BY d.create_time DESC
    </select>

    <!-- 根据患者ID查询诊断记录 -->
    <select id="selectByPatientId" resultType="net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.DiagnosisVO">
        SELECT
            d.diagnosis_id,
            d.patient_id,
            d.patient_no,
            d.patient_name,
            d.diagnosis_result,
            d.diagnosis_type,
            CASE d.diagnosis_type
                WHEN 1 THEN '初诊'
                WHEN 2 THEN '复诊'
                WHEN 3 THEN '会诊'
                ELSE '未知'
            END AS diagnosis_type_name,
            d.diagnosis_description,
            d.diagnosis_doctor_id,
            d.diagnosis_doctor_name,
            d.diagnosis_time,
            d.diagnosis_status,
            CASE d.diagnosis_status
                WHEN 1 THEN '已诊断'
                WHEN 2 THEN '已开单'
                WHEN 3 THEN '已收费'
                WHEN 4 THEN '已完成'
                ELSE '未知'
            END AS diagnosis_status_name,
            d.create_user_name,
            d.create_time,
            d.update_user_name,
            d.update_time
        FROM t_diagnosis d
        WHERE d.deleted_flag = 0
        AND d.patient_id = #{patientId}
        ORDER BY d.create_time DESC
    </select>

    <!-- 根据诊断ID查询详情 -->
    <select id="selectDetailById" resultType="net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.DiagnosisVO">
        SELECT
            d.diagnosis_id,
            d.patient_id,
            d.patient_no,
            d.patient_name,
            d.diagnosis_result,
            d.diagnosis_type,
            CASE d.diagnosis_type
                WHEN 1 THEN '初诊'
                WHEN 2 THEN '复诊'
                WHEN 3 THEN '会诊'
                ELSE '未知'
            END AS diagnosis_type_name,
            d.diagnosis_description,
            d.diagnosis_doctor_id,
            d.diagnosis_doctor_name,
            d.diagnosis_time,
            d.diagnosis_status,
            CASE d.diagnosis_status
                WHEN 1 THEN '已诊断'
                WHEN 2 THEN '已开单'
                WHEN 3 THEN '已收费'
                WHEN 4 THEN '已完成'
                ELSE '未知'
            END AS diagnosis_status_name,
            d.create_user_name,
            d.create_time,
            d.update_user_name,
            d.update_time
        FROM t_diagnosis d
        WHERE d.deleted_flag = 0
        AND d.diagnosis_id = #{diagnosisId}
    </select>

    <!-- 更新诊断状态 -->
    <update id="updateDiagnosisStatus">
        UPDATE t_diagnosis
        SET diagnosis_status = #{diagnosisStatus},
            update_user_id = #{updateUserId},
            update_user_name = #{updateUserName},
            update_time = NOW()
        WHERE diagnosis_id = #{diagnosisId}
        AND deleted_flag = 0
    </update>

</mapper>

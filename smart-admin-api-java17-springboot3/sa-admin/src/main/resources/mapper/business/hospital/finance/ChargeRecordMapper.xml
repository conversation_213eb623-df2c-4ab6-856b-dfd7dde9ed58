<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.hospital.finance.dao.ChargeRecordDao">

    <!-- 分页查询收费记录 -->
    <select id="queryPage" resultType="net.lab1024.sa.admin.module.business.hospital.finance.domain.vo.ChargeRecordVO">
        SELECT
            cr.charge_id,
            cr.charge_no,
            cr.customer_id,
            cr.customer_name,
            cr.customer_phone,
            cr.appointment_id,
            cr.record_id,
            cr.project_id,
            cr.project_name,
            cr.project_category,
            cr.standard_price,
            cr.charge_amount,
            cr.discount_amount,
            cr.discount_reason,
            cr.charge_date,
            cr.charge_status,
            cr.payment_method,
            cr.payment_details,
            cr.charge_employee_id,
            cr.charge_employee_name,
            cr.charge_department_id,
            cr.charge_department_name,
            cr.invoice_no,
            cr.invoice_flag,
            cr.remark,
            cr.create_time,
            cr.update_time,
            e.actual_name as create_user_name
        FROM t_charge_record cr
        LEFT JOIN t_employee e ON cr.create_user_id = e.employee_id
        <where>
            cr.deleted_flag = 0
            <if test="query.chargeNo != null and query.chargeNo != ''">
                AND cr.charge_no LIKE CONCAT('%', #{query.chargeNo}, '%')
            </if>
            <if test="query.customerId != null">
                AND cr.customer_id = #{query.customerId}
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                AND cr.customer_name LIKE CONCAT('%', #{query.customerName}, '%')
            </if>
            <if test="query.customerPhone != null and query.customerPhone != ''">
                AND cr.customer_phone LIKE CONCAT('%', #{query.customerPhone}, '%')
            </if>
            <if test="query.appointmentId != null">
                AND cr.appointment_id = #{query.appointmentId}
            </if>
            <if test="query.recordId != null">
                AND cr.record_id = #{query.recordId}
            </if>
            <if test="query.projectId != null">
                AND cr.project_id = #{query.projectId}
            </if>
            <if test="query.projectName != null and query.projectName != ''">
                AND cr.project_name LIKE CONCAT('%', #{query.projectName}, '%')
            </if>
            <if test="query.projectCategory != null and query.projectCategory != ''">
                AND cr.project_category = #{query.projectCategory}
            </if>
            <if test="query.chargeStatus != null">
                AND cr.charge_status = #{query.chargeStatus}
            </if>
            <if test="query.paymentMethod != null">
                AND cr.payment_method = #{query.paymentMethod}
            </if>
            <if test="query.chargeEmployeeId != null">
                AND cr.charge_employee_id = #{query.chargeEmployeeId}
            </if>
            <if test="query.chargeEmployeeName != null and query.chargeEmployeeName != ''">
                AND cr.charge_employee_name LIKE CONCAT('%', #{query.chargeEmployeeName}, '%')
            </if>
            <if test="query.chargeDepartmentId != null">
                AND cr.charge_department_id = #{query.chargeDepartmentId}
            </if>
            <if test="query.chargeDepartmentName != null and query.chargeDepartmentName != ''">
                AND cr.charge_department_name LIKE CONCAT('%', #{query.chargeDepartmentName}, '%')
            </if>
            <if test="query.startChargeDate != null">
                AND cr.charge_date >= #{query.startChargeDate}
            </if>
            <if test="query.endChargeDate != null">
                AND cr.charge_date &lt;= #{query.endChargeDate}
            </if>
            <if test="query.invoiceFlag != null">
                AND cr.invoice_flag = #{query.invoiceFlag}
            </if>
            <if test="query.keywords != null and query.keywords != ''">
                AND (cr.customer_name LIKE CONCAT('%', #{query.keywords}, '%')
                     OR cr.customer_phone LIKE CONCAT('%', #{query.keywords}, '%')
                     OR cr.project_name LIKE CONCAT('%', #{query.keywords}, '%'))
            </if>
        </where>
        ORDER BY cr.create_time DESC
    </select>

    <!-- 根据收费单号查询收费记录 -->
    <select id="selectByChargeNo" resultType="net.lab1024.sa.admin.module.business.hospital.finance.domain.entity.ChargeRecordEntity">
        SELECT * FROM t_charge_record
        WHERE charge_no = #{chargeNo} AND deleted_flag = 0
    </select>

    <!-- 根据预约ID查询收费记录 -->
    <select id="selectByAppointmentId" resultType="net.lab1024.sa.admin.module.business.hospital.finance.domain.entity.ChargeRecordEntity">
        SELECT * FROM t_charge_record
        WHERE appointment_id = #{appointmentId} AND deleted_flag = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据客户ID查询收费记录 -->
    <select id="selectByCustomerId" resultType="net.lab1024.sa.admin.module.business.hospital.finance.domain.vo.ChargeRecordVO">
        SELECT
            cr.*,
            e.actual_name as create_user_name
        FROM t_charge_record cr
        LEFT JOIN t_employee e ON cr.create_user_id = e.employee_id
        WHERE cr.customer_id = #{customerId} AND cr.deleted_flag = 0
        ORDER BY cr.create_time DESC
    </select>

    <!-- 根据病历ID查询收费记录 -->
    <select id="selectByRecordId" resultType="net.lab1024.sa.admin.module.business.hospital.finance.domain.entity.ChargeRecordEntity">
        SELECT * FROM t_charge_record
        WHERE record_id = #{recordId} AND deleted_flag = 0
        ORDER BY create_time DESC
    </select>

    <!-- 更新收费状态 -->
    <update id="updateChargeStatus">
        UPDATE t_charge_record
        SET charge_status = #{chargeStatus},
            payment_method = #{paymentMethod},
            payment_details = #{paymentDetails},
            update_time = NOW()
        WHERE charge_id = #{chargeId}
    </update>

    <!-- 更新发票信息 -->
    <update id="updateInvoiceInfo">
        UPDATE t_charge_record
        SET invoice_no = #{invoiceNo},
            invoice_flag = #{invoiceFlag},
            update_time = NOW()
        WHERE charge_id = #{chargeId}
    </update>

    <!-- 获取收费统计信息 -->
    <select id="getChargeStatistics" resultType="net.lab1024.sa.admin.module.business.hospital.finance.domain.vo.ChargeRecordVO">
        SELECT
            COUNT(*) as totalCount,
            SUM(charge_amount) as totalAmount,
            SUM(CASE WHEN charge_status = 2 THEN charge_amount ELSE 0 END) as paidAmount,
            SUM(CASE WHEN charge_status = 1 THEN charge_amount ELSE 0 END) as pendingAmount
        FROM t_charge_record
        WHERE customer_id = #{customerId} AND deleted_flag = 0
    </select>

</mapper>

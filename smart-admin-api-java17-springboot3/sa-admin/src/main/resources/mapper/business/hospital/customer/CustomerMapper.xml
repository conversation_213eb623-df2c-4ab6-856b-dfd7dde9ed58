<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.hospital.customer.dao.CustomerDao">

    <!-- 客户VO结果映射 -->
    <resultMap id="CustomerVOMap" type="net.lab1024.sa.admin.module.business.hospital.customer.domain.vo.CustomerVO">
        <id column="customer_id" property="customerId"/>
        <result column="lead_id" property="leadId"/>
        <result column="customer_name" property="customerName"/>
        <result column="customer_phone" property="customerPhone"/>
        <result column="customer_wechat" property="customerWechat"/>
        <result column="customer_email" property="customerEmail"/>
        <result column="gender" property="gender"/>
        <result column="gender_name" property="genderName"/>
        <result column="age" property="age"/>
        <result column="birthday" property="birthday"/>
        <result column="id_card" property="idCard"/>
        <result column="customer_address" property="customerAddress"/>
        <result column="occupation" property="occupation"/>
        <result column="customer_source" property="customerSource"/>
        <result column="customer_status" property="customerStatus"/>
        <result column="customer_status_name" property="customerStatusName"/>
        <result column="customer_tags" property="customerTags"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="appointment_count" property="appointmentCount"/>
        <result column="last_appointment_time" property="lastAppointmentTime"/>
        <result column="total_amount" property="totalAmount"/>
    </resultMap>

    <!-- 分页查询客户 -->
    <select id="queryPage" resultMap="CustomerVOMap">
        SELECT
            c.customer_id,
            c.lead_id,
            c.customer_name,
            c.customer_phone,
            c.customer_wechat,
            c.customer_email,
            c.gender,
            CASE c.gender
                WHEN 1 THEN '男'
                WHEN 2 THEN '女'
                ELSE '未知'
            END AS gender_name,
            c.age,
            c.birthday,
            c.id_card,
            c.customer_address,
            c.occupation,
            c.customer_source,
            c.customer_status,
            CASE c.customer_status
                WHEN 1 THEN '潜在客户'
                WHEN 2 THEN '意向客户'
                WHEN 3 THEN '成交客户'
                WHEN 4 THEN '流失客户'
                ELSE '未知'
            END AS customer_status_name,
            c.customer_tags,
            c.remark,
            c.create_time,
            c.update_time,
            e.actual_name AS create_user_name,
            COALESCE(ac.appointment_count, 0) AS appointment_count,
            ac.last_appointment_time,
            COALESCE(ac.total_amount, 0) AS total_amount
        FROM t_customer c
        LEFT JOIN t_employee e ON c.create_user_id = e.employee_id
        LEFT JOIN (
            SELECT 
                customer_id,
                COUNT(*) AS appointment_count,
                MAX(appointment_date) AS last_appointment_time,
                SUM(COALESCE(p.project_price, 0)) AS total_amount
            FROM t_appointment a
            LEFT JOIN t_project p ON a.project_id = p.project_id
            WHERE a.deleted_flag = 0 AND a.appointment_status IN (3, 4)
            GROUP BY customer_id
        ) ac ON c.customer_id = ac.customer_id
        <where>
            c.deleted_flag = 0
            <if test="query.leadId != null">
                AND c.lead_id = #{query.leadId}
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                AND c.customer_name LIKE CONCAT('%', #{query.customerName}, '%')
            </if>
            <if test="query.customerPhone != null and query.customerPhone != ''">
                AND c.customer_phone LIKE CONCAT('%', #{query.customerPhone}, '%')
            </if>
            <if test="query.gender != null">
                AND c.gender = #{query.gender}
            </if>
            <if test="query.customerSource != null and query.customerSource != ''">
                AND c.customer_source = #{query.customerSource}
            </if>
            <if test="query.customerStatus != null">
                AND c.customer_status = #{query.customerStatus}
            </if>
            <if test="query.customerTags != null and query.customerTags != ''">
                AND c.customer_tags LIKE CONCAT('%', #{query.customerTags}, '%')
            </if>
            <if test="query.createTimeStart != null">
                AND c.create_time >= #{query.createTimeStart}
            </if>
            <if test="query.createTimeEnd != null">
                AND c.create_time &lt;= #{query.createTimeEnd}
            </if>
            <if test="query.searchWord != null and query.searchWord != ''">
                AND (
                    c.customer_name LIKE CONCAT('%', #{query.searchWord}, '%')
                    OR c.customer_phone LIKE CONCAT('%', #{query.searchWord}, '%')
                    OR c.customer_wechat LIKE CONCAT('%', #{query.searchWord}, '%')
                    OR c.customer_source LIKE CONCAT('%', #{query.searchWord}, '%')
                    OR c.customer_tags LIKE CONCAT('%', #{query.searchWord}, '%')
                )
            </if>
            <!-- 数据权限过滤 -->
            <if test="query.dataScopeEmployeeIds != null and query.dataScopeEmployeeIds.size() > 0">
                AND (
                    c.create_user_id IN
                    <foreach collection="query.dataScopeEmployeeIds" item="employeeId" open="(" separator="," close=")">
                        #{employeeId}
                    </foreach>
                    OR c.responsible_employee_id IN
                    <foreach collection="query.dataScopeEmployeeIds" item="employeeId" open="(" separator="," close=")">
                        #{employeeId}
                    </foreach>
                )
            </if>
        </where>
        ORDER BY c.create_time DESC
    </select>

    <!-- 根据电话号码查询客户 -->
    <select id="selectByPhone" resultType="net.lab1024.sa.admin.module.business.hospital.customer.domain.entity.CustomerEntity">
        SELECT *
        FROM t_customer
        WHERE customer_phone = #{customerPhone}
        AND deleted_flag = 0
        <if test="excludeCustomerId != null">
            AND customer_id != #{excludeCustomerId}
        </if>
        LIMIT 1
    </select>

    <!-- 根据身份证号查询客户 -->
    <select id="selectByIdCard" resultType="net.lab1024.sa.admin.module.business.hospital.customer.domain.entity.CustomerEntity">
        SELECT *
        FROM t_customer
        WHERE id_card = #{idCard}
        AND deleted_flag = 0
        <if test="excludeCustomerId != null">
            AND customer_id != #{excludeCustomerId}
        </if>
        LIMIT 1
    </select>

    <!-- 根据线索ID查询客户 -->
    <select id="selectByLeadId" resultType="net.lab1024.sa.admin.module.business.hospital.customer.domain.entity.CustomerEntity">
        SELECT *
        FROM t_customer
        WHERE lead_id = #{leadId}
        AND deleted_flag = 0
        LIMIT 1
    </select>

    <!-- 更新客户状态 -->
    <update id="updateStatus">
        UPDATE t_customer
        SET customer_status = #{customerStatus},
            update_time = NOW()
        WHERE customer_id = #{customerId}
        AND deleted_flag = 0
    </update>

    <!-- 批量更新删除状态 -->
    <update id="batchUpdateDeleted">
        UPDATE t_customer
        SET deleted_flag = #{deletedFlag},
            update_time = NOW()
        WHERE customer_id IN
        <foreach collection="customerIdList" item="customerId" open="(" separator="," close=")">
            #{customerId}
        </foreach>
    </update>

    <!-- 获取客户统计数据 -->
    <select id="getCustomerCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_customer
        WHERE deleted_flag = 0
    </select>

    <!-- 根据状态统计客户数量 -->
    <select id="getCustomerCountByStatus" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_customer
        WHERE customer_status = #{customerStatus}
        AND deleted_flag = 0
    </select>

    <!-- 获取客户详情 -->
    <select id="getCustomerDetail" resultMap="CustomerVOMap">
        SELECT
            c.customer_id,
            c.lead_id,
            c.customer_name,
            c.customer_phone,
            c.customer_wechat,
            c.customer_email,
            c.gender,
            CASE c.gender
                WHEN 1 THEN '男'
                WHEN 2 THEN '女'
                ELSE '未知'
            END AS gender_name,
            c.age,
            c.birthday,
            c.id_card,
            c.customer_address,
            c.occupation,
            c.customer_source,
            c.customer_status,
            CASE c.customer_status
                WHEN 1 THEN '潜在客户'
                WHEN 2 THEN '意向客户'
                WHEN 3 THEN '成交客户'
                WHEN 4 THEN '流失客户'
                ELSE '未知'
            END AS customer_status_name,
            c.customer_tags,
            c.remark,
            c.create_time,
            c.update_time,
            e.actual_name AS create_user_name,
            COALESCE(ac.appointment_count, 0) AS appointment_count,
            ac.last_appointment_time,
            COALESCE(ac.total_amount, 0) AS total_amount
        FROM t_customer c
        LEFT JOIN t_employee e ON c.create_user_id = e.employee_id
        LEFT JOIN (
            SELECT 
                customer_id,
                COUNT(*) AS appointment_count,
                MAX(appointment_date) AS last_appointment_time,
                SUM(COALESCE(p.project_price, 0)) AS total_amount
            FROM t_appointment a
            LEFT JOIN t_project p ON a.project_id = p.project_id
            WHERE a.deleted_flag = 0 AND a.appointment_status IN (3, 4)
            GROUP BY customer_id
        ) ac ON c.customer_id = ac.customer_id
        WHERE c.customer_id = #{customerId}
        AND c.deleted_flag = 0
    </select>

    <!-- 根据标签查询客户 -->
    <select id="selectByTags" resultMap="CustomerVOMap">
        SELECT
            c.customer_id,
            c.customer_name,
            c.customer_phone,
            c.customer_status,
            CASE c.customer_status
                WHEN 1 THEN '潜在客户'
                WHEN 2 THEN '意向客户'
                WHEN 3 THEN '成交客户'
                WHEN 4 THEN '流失客户'
                ELSE '未知'
            END AS customer_status_name,
            c.customer_tags,
            c.create_time
        FROM t_customer c
        WHERE c.customer_tags LIKE CONCAT('%', #{customerTags}, '%')
        AND c.deleted_flag = 0
        ORDER BY c.create_time DESC
    </select>

    <!-- 获取最近创建的客户 -->
    <select id="getRecentCustomers" resultMap="CustomerVOMap">
        SELECT
            c.customer_id,
            c.customer_name,
            c.customer_phone,
            c.customer_status,
            CASE c.customer_status
                WHEN 1 THEN '潜在客户'
                WHEN 2 THEN '意向客户'
                WHEN 3 THEN '成交客户'
                WHEN 4 THEN '流失客户'
                ELSE '未知'
            END AS customer_status_name,
            c.create_time,
            e.actual_name AS create_user_name
        FROM t_customer c
        LEFT JOIN t_employee e ON c.create_user_id = e.employee_id
        WHERE c.deleted_flag = 0
        ORDER BY c.create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 获取客户360度视图信息 -->
    <select id="getCustomer360View" resultType="net.lab1024.sa.admin.module.business.hospital.customer.domain.vo.Customer360VO">
        SELECT
            c.customer_id,
            c.customer_name,
            c.customer_phone,
            c.customer_wechat,
            c.customer_email,
            c.gender,
            CASE c.gender WHEN 1 THEN '男' WHEN 2 THEN '女' ELSE '未知' END as gender_name,
            c.age,
            c.birthday,
            c.id_card,
            c.customer_address,
            c.occupation,
            c.customer_source,
            c.customer_status,
            CASE c.customer_status
                WHEN 1 THEN '潜在客户'
                WHEN 2 THEN '意向客户'
                WHEN 3 THEN '成交客户'
                WHEN 4 THEN '流失客户'
                ELSE '未知'
            END as customer_status_name,
            c.customer_tags,
            c.customer_level,
            CASE c.customer_level
                WHEN 1 THEN '普通客户'
                WHEN 2 THEN 'VIP客户'
                WHEN 3 THEN 'SVIP客户'
                ELSE '普通客户'
            END as customer_level_name,
            c.remark,
            c.create_time,
            c.lead_id,
            l.lead_source as lead_source_detail,
            l.referrer_info,
            u.actual_name as create_user_name,
            u.employee_id as responsible_employee_id,
            u.actual_name as responsible_employee_name,
            -- 预约统计
            COALESCE(app_stats.total_appointments, 0) as total_appointments,
            COALESCE(app_stats.completed_appointments, 0) as completed_appointments,
            COALESCE(app_stats.cancelled_appointments, 0) as cancelled_appointments,
            app_stats.last_appointment_time,
            app_stats.next_appointment_time,
            -- 病历统计
            COALESCE(med_stats.medical_record_count, 0) as medical_record_count,
            med_stats.last_visit_time,
            med_stats.next_visit_time,
            med_stats.first_visit_time,
            -- 跟进统计
            COALESCE(follow_stats.follow_up_count, 0) as follow_up_count,
            follow_stats.last_follow_up_time,
            -- 消费统计
            COALESCE(cons_stats.total_amount, 0) as total_amount,
            COALESCE(cons_stats.avg_amount, 0) as avg_amount,
            COALESCE(cons_stats.max_amount, 0) as max_amount,
            -- 评分信息（暂时设置默认值，后续可以通过算法计算）
            80.0 as satisfaction_score,
            75.0 as value_score,
            20.0 as churn_risk_score,
            85.0 as activity_score
        FROM t_customer c
        LEFT JOIN t_employee u ON c.create_user_id = u.employee_id
        LEFT JOIN t_lead l ON c.lead_id = l.lead_id
        -- 预约统计子查询
        LEFT JOIN (
            SELECT
                customer_id,
                COUNT(*) as total_appointments,
                SUM(CASE WHEN appointment_status = 3 THEN 1 ELSE 0 END) as completed_appointments,
                SUM(CASE WHEN appointment_status = 4 THEN 1 ELSE 0 END) as cancelled_appointments,
                MAX(CASE WHEN appointment_status != 4 THEN CONCAT(appointment_date, ' ', appointment_time) END) as last_appointment_time,
                MIN(CASE WHEN appointment_status = 1 AND CONCAT(appointment_date, ' ', appointment_time) > NOW()
                    THEN CONCAT(appointment_date, ' ', appointment_time) END) as next_appointment_time
            FROM t_appointment
            WHERE deleted_flag = 0
            GROUP BY customer_id
        ) app_stats ON c.customer_id = app_stats.customer_id
        -- 病历统计子查询
        LEFT JOIN (
            SELECT
                customer_id,
                COUNT(*) as medical_record_count,
                MAX(visit_date) as last_visit_time,
                MIN(CASE WHEN next_visit_date > CURDATE() THEN next_visit_date END) as next_visit_time,
                MIN(visit_date) as first_visit_time
            FROM t_medical_record
            WHERE deleted_flag = 0
            GROUP BY customer_id
        ) med_stats ON c.customer_id = med_stats.customer_id
        -- 跟进统计子查询
        LEFT JOIN (
            SELECT
                customer_id,
                COUNT(*) as follow_up_count,
                MAX(follow_time) as last_follow_up_time
            FROM t_lead_follow
            WHERE deleted_flag = 0
            GROUP BY customer_id
        ) follow_stats ON c.customer_id = follow_stats.customer_id
        -- 消费统计子查询（基于预约项目价格计算）
        LEFT JOIN (
            SELECT
                a.customer_id,
                SUM(p.project_price) as total_amount,
                AVG(p.project_price) as avg_amount,
                MAX(p.project_price) as max_amount
            FROM t_appointment a
            LEFT JOIN t_project p ON a.project_id = p.project_id
            WHERE a.deleted_flag = 0 AND a.appointment_status = 3
            GROUP BY a.customer_id
        ) cons_stats ON c.customer_id = cons_stats.customer_id
        WHERE c.customer_id = #{customerId} AND c.deleted_flag = 0
    </select>

</mapper>

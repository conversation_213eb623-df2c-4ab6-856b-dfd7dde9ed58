<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.hospital.dashboard.dao.DashboardDao">

    <!-- 获取今日待办事项 -->
    <select id="getTodayTodos" resultType="net.lab1024.sa.admin.module.business.hospital.dashboard.domain.vo.TodoItemVO">
        SELECT 
            'lead_follow' as type,
            CONCAT('线索跟进: ', l.customer_name) as title,
            CONCAT('客户电话: ', l.customer_phone, ', 下次跟进时间: ', DATE_FORMAT(lf.next_follow_time, '%H:%i')) as description,
            lf.next_follow_time as dueTime,
            CASE 
                WHEN lf.next_follow_time &lt; NOW() THEN 'high'
                WHEN lf.next_follow_time &lt; DATE_ADD(NOW(), INTERVAL 2 HOUR) THEN 'medium'
                ELSE 'low'
            END as priority
        FROM t_lead_follow lf
        INNER JOIN t_lead l ON lf.lead_id = l.lead_id
        WHERE DATE(lf.next_follow_time) = CURDATE()
          AND l.deleted_flag = 0
          AND lf.deleted_flag = 0
          AND l.lead_status IN (1, 2)
        
        UNION ALL
        
        SELECT 
            'appointment_confirm' as type,
            CONCAT('预约确认: ', a.customer_name) as title,
            CONCAT('预约时间: ', DATE_FORMAT(CONCAT(a.appointment_date, ' ', a.appointment_time), '%m-%d %H:%i'), ', 医生: ', a.doctor_name) as description,
            CONCAT(a.appointment_date, ' ', a.appointment_time) as dueTime,
            CASE 
                WHEN a.appointment_status = 1 THEN 'high'
                ELSE 'medium'
            END as priority
        FROM t_appointment a
        WHERE a.appointment_date = CURDATE()
          AND a.deleted_flag = 0
          AND a.appointment_status IN (1, 2)
        
        ORDER BY dueTime ASC
        LIMIT 20
    </select>

    <!-- 获取最近活动 -->
    <select id="getRecentActivities" resultType="net.lab1024.sa.admin.module.business.hospital.dashboard.domain.vo.ActivityVO">
        SELECT 
            'lead_created' as type,
            CONCAT('新增线索: ', customer_name) as title,
            CONCAT('来源: ', IFNULL(lead_source, '未知'), ', 电话: ', customer_phone) as description,
            create_time as activityTime,
            'UserAddOutlined' as icon
        FROM t_lead
        WHERE deleted_flag = 0
          AND create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        
        UNION ALL
        
        SELECT 
            'appointment_created' as type,
            CONCAT('新增预约: ', customer_name) as title,
            CONCAT('预约时间: ', DATE_FORMAT(CONCAT(appointment_date, ' ', appointment_time), '%m-%d %H:%i'), ', 医生: ', doctor_name) as description,
            create_time as activityTime,
            'CalendarOutlined' as icon
        FROM t_appointment
        WHERE deleted_flag = 0
          AND create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        
        UNION ALL
        
        SELECT 
            'customer_created' as type,
            CONCAT('新增客户: ', customer_name) as title,
            CONCAT('电话: ', customer_phone, ', 状态: ', 
                CASE customer_status 
                    WHEN 1 THEN '潜在客户'
                    WHEN 2 THEN '意向客户'
                    WHEN 3 THEN '成交客户'
                    ELSE '其他'
                END) as description,
            create_time as activityTime,
            'TeamOutlined' as icon
        FROM t_customer
        WHERE deleted_flag = 0
          AND create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        
        ORDER BY activityTime DESC
        LIMIT 20
    </select>

    <!-- 获取线索转化趋势 -->
    <select id="getLeadConversionTrend" resultType="net.lab1024.sa.admin.module.business.hospital.dashboard.domain.vo.ChartDataVO">
        SELECT 
            JSON_OBJECT(
                'labels', JSON_ARRAY(
                    DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 6 DAY), '%m-%d'),
                    DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 5 DAY), '%m-%d'),
                    DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 4 DAY), '%m-%d'),
                    DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 3 DAY), '%m-%d'),
                    DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 2 DAY), '%m-%d'),
                    DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 DAY), '%m-%d'),
                    DATE_FORMAT(CURDATE(), '%m-%d')
                ),
                'datasets', JSON_ARRAY(
                    JSON_OBJECT(
                        'label', '新增线索',
                        'data', JSON_ARRAY(
                            (SELECT COUNT(*) FROM t_lead WHERE DATE(create_time) = DATE_SUB(CURDATE(), INTERVAL 6 DAY) AND deleted_flag = 0),
                            (SELECT COUNT(*) FROM t_lead WHERE DATE(create_time) = DATE_SUB(CURDATE(), INTERVAL 5 DAY) AND deleted_flag = 0),
                            (SELECT COUNT(*) FROM t_lead WHERE DATE(create_time) = DATE_SUB(CURDATE(), INTERVAL 4 DAY) AND deleted_flag = 0),
                            (SELECT COUNT(*) FROM t_lead WHERE DATE(create_time) = DATE_SUB(CURDATE(), INTERVAL 3 DAY) AND deleted_flag = 0),
                            (SELECT COUNT(*) FROM t_lead WHERE DATE(create_time) = DATE_SUB(CURDATE(), INTERVAL 2 DAY) AND deleted_flag = 0),
                            (SELECT COUNT(*) FROM t_lead WHERE DATE(create_time) = DATE_SUB(CURDATE(), INTERVAL 1 DAY) AND deleted_flag = 0),
                            (SELECT COUNT(*) FROM t_lead WHERE DATE(create_time) = CURDATE() AND deleted_flag = 0)
                        ),
                        'borderColor', '#1890ff',
                        'backgroundColor', 'rgba(24, 144, 255, 0.1)'
                    ),
                    JSON_OBJECT(
                        'label', '转化预约',
                        'data', JSON_ARRAY(
                            (SELECT COUNT(*) FROM t_lead WHERE DATE(conversion_date) = DATE_SUB(CURDATE(), INTERVAL 6 DAY) AND lead_status = 3 AND deleted_flag = 0),
                            (SELECT COUNT(*) FROM t_lead WHERE DATE(conversion_date) = DATE_SUB(CURDATE(), INTERVAL 5 DAY) AND lead_status = 3 AND deleted_flag = 0),
                            (SELECT COUNT(*) FROM t_lead WHERE DATE(conversion_date) = DATE_SUB(CURDATE(), INTERVAL 4 DAY) AND lead_status = 3 AND deleted_flag = 0),
                            (SELECT COUNT(*) FROM t_lead WHERE DATE(conversion_date) = DATE_SUB(CURDATE(), INTERVAL 3 DAY) AND lead_status = 3 AND deleted_flag = 0),
                            (SELECT COUNT(*) FROM t_lead WHERE DATE(conversion_date) = DATE_SUB(CURDATE(), INTERVAL 2 DAY) AND lead_status = 3 AND deleted_flag = 0),
                            (SELECT COUNT(*) FROM t_lead WHERE DATE(conversion_date) = DATE_SUB(CURDATE(), INTERVAL 1 DAY) AND lead_status = 3 AND deleted_flag = 0),
                            (SELECT COUNT(*) FROM t_lead WHERE DATE(conversion_date) = CURDATE() AND lead_status = 3 AND deleted_flag = 0)
                        ),
                        'borderColor', '#52c41a',
                        'backgroundColor', 'rgba(82, 196, 26, 0.1)'
                    )
                )
            ) as chartData
    </select>

    <!-- 获取客户来源分布 -->
    <select id="getCustomerSourceDistribution" resultType="net.lab1024.sa.admin.module.business.hospital.dashboard.domain.vo.ChartDataVO">
        SELECT 
            JSON_OBJECT(
                'labels', JSON_ARRAY(
                    SELECT GROUP_CONCAT(DISTINCT IFNULL(lead_source, '未知') ORDER BY lead_source)
                    FROM t_lead 
                    WHERE deleted_flag = 0
                ),
                'datasets', JSON_ARRAY(
                    JSON_OBJECT(
                        'label', '线索来源分布',
                        'data', (
                            SELECT JSON_ARRAYAGG(cnt)
                            FROM (
                                SELECT COUNT(*) as cnt
                                FROM t_lead 
                                WHERE deleted_flag = 0
                                GROUP BY IFNULL(lead_source, '未知')
                                ORDER BY IFNULL(lead_source, '未知')
                            ) t
                        ),
                        'backgroundColor', JSON_ARRAY(
                            '#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', 
                            '#13c2c2', '#eb2f96', '#fa8c16', '#a0d911', '#2f54eb'
                        )
                    )
                )
            ) as chartData
    </select>

    <!-- 获取预约状态分布 -->
    <select id="getAppointmentStatusDistribution" resultType="net.lab1024.sa.admin.module.business.hospital.dashboard.domain.vo.ChartDataVO">
        SELECT 
            JSON_OBJECT(
                'labels', JSON_ARRAY('待确认', '已确认', '已完成', '已取消', '已改期'),
                'datasets', JSON_ARRAY(
                    JSON_OBJECT(
                        'label', '预约状态分布',
                        'data', JSON_ARRAY(
                            (SELECT COUNT(*) FROM t_appointment WHERE appointment_status = 1 AND deleted_flag = 0),
                            (SELECT COUNT(*) FROM t_appointment WHERE appointment_status = 2 AND deleted_flag = 0),
                            (SELECT COUNT(*) FROM t_appointment WHERE appointment_status = 3 AND deleted_flag = 0),
                            (SELECT COUNT(*) FROM t_appointment WHERE appointment_status = 4 AND deleted_flag = 0),
                            (SELECT COUNT(*) FROM t_appointment WHERE appointment_status = 5 AND deleted_flag = 0)
                        ),
                        'backgroundColor', JSON_ARRAY('#faad14', '#52c41a', '#1890ff', '#f5222d', '#722ed1')
                    )
                )
            ) as chartData
    </select>

    <!-- 获取月度收入趋势 -->
    <select id="getRevenueTrend" resultType="net.lab1024.sa.admin.module.business.hospital.dashboard.domain.vo.ChartDataVO">
        SELECT 
            JSON_OBJECT(
                'labels', (
                    SELECT JSON_ARRAYAGG(month_label)
                    FROM (
                        SELECT DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL n.n MONTH), '%Y-%m') as month_label
                        FROM (SELECT 5 as n UNION SELECT 4 UNION SELECT 3 UNION SELECT 2 UNION SELECT 1 UNION SELECT 0) n
                        ORDER BY month_label
                    ) months
                ),
                'datasets', JSON_ARRAY(
                    JSON_OBJECT(
                        'label', '月度收入(万元)',
                        'data', (
                            SELECT JSON_ARRAYAGG(IFNULL(revenue, 0))
                            FROM (
                                SELECT DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL n.n MONTH), '%Y-%m') as month_key,
                                       ROUND(IFNULL(SUM(cr.actual_amount), 0) / 10000, 2) as revenue
                                FROM (SELECT 5 as n UNION SELECT 4 UNION SELECT 3 UNION SELECT 2 UNION SELECT 1 UNION SELECT 0) n
                                LEFT JOIN t_charge_record cr ON DATE_FORMAT(cr.charge_date, '%Y-%m') = DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL n.n MONTH), '%Y-%m')
                                    AND cr.charge_status = 2 AND cr.deleted_flag = 0
                                GROUP BY month_key
                                ORDER BY month_key
                            ) monthly_revenue
                        ),
                        'borderColor', '#1890ff',
                        'backgroundColor', 'rgba(24, 144, 255, 0.1)',
                        'fill', true
                    )
                )
            ) as chartData
    </select>

    <!-- 获取员工绩效统计 -->
    <select id="getEmployeePerformance" resultType="net.lab1024.sa.admin.module.business.hospital.dashboard.domain.vo.ChartDataVO">
        SELECT 
            JSON_OBJECT(
                'labels', (
                    SELECT JSON_ARRAYAGG(employee_name)
                    FROM (
                        SELECT IFNULL(e.actual_name, '未分配') as employee_name
                        FROM t_lead l
                        LEFT JOIN t_employee e ON l.assigned_employee_id = e.employee_id
                        WHERE l.deleted_flag = 0
                        GROUP BY l.assigned_employee_id, e.actual_name
                        ORDER BY COUNT(*) DESC
                        LIMIT 10
                    ) top_employees
                ),
                'datasets', JSON_ARRAY(
                    JSON_OBJECT(
                        'label', '线索数量',
                        'data', (
                            SELECT JSON_ARRAYAGG(lead_count)
                            FROM (
                                SELECT COUNT(*) as lead_count
                                FROM t_lead l
                                LEFT JOIN t_employee e ON l.assigned_employee_id = e.employee_id
                                WHERE l.deleted_flag = 0
                                GROUP BY l.assigned_employee_id, e.actual_name
                                ORDER BY lead_count DESC
                                LIMIT 10
                            ) employee_leads
                        ),
                        'backgroundColor', '#1890ff'
                    ),
                    JSON_OBJECT(
                        'label', '转化数量',
                        'data', (
                            SELECT JSON_ARRAYAGG(conversion_count)
                            FROM (
                                SELECT COUNT(CASE WHEN l.lead_status IN (3, 4) THEN 1 END) as conversion_count
                                FROM t_lead l
                                LEFT JOIN t_employee e ON l.assigned_employee_id = e.employee_id
                                WHERE l.deleted_flag = 0
                                GROUP BY l.assigned_employee_id, e.actual_name
                                ORDER BY COUNT(*) DESC
                                LIMIT 10
                            ) employee_conversions
                        ),
                        'backgroundColor', '#52c41a'
                    )
                )
            ) as chartData
    </select>

    <!-- 获取部门统计 -->
    <select id="getDepartmentStatistics" resultType="net.lab1024.sa.admin.module.business.hospital.dashboard.domain.vo.ChartDataVO">
        SELECT 
            JSON_OBJECT(
                'labels', (
                    SELECT JSON_ARRAYAGG(dept_name)
                    FROM (
                        SELECT IFNULL(d.dept_name, '未分配') as dept_name
                        FROM t_lead l
                        LEFT JOIN t_employee e ON l.assigned_employee_id = e.employee_id
                        LEFT JOIN t_department d ON e.department_id = d.department_id
                        WHERE l.deleted_flag = 0
                        GROUP BY d.department_id, d.dept_name
                        ORDER BY COUNT(*) DESC
                        LIMIT 8
                    ) top_departments
                ),
                'datasets', JSON_ARRAY(
                    JSON_OBJECT(
                        'label', '线索分布',
                        'data', (
                            SELECT JSON_ARRAYAGG(lead_count)
                            FROM (
                                SELECT COUNT(*) as lead_count
                                FROM t_lead l
                                LEFT JOIN t_employee e ON l.assigned_employee_id = e.employee_id
                                LEFT JOIN t_department d ON e.department_id = d.department_id
                                WHERE l.deleted_flag = 0
                                GROUP BY d.department_id, d.dept_name
                                ORDER BY lead_count DESC
                                LIMIT 8
                            ) dept_leads
                        ),
                        'backgroundColor', JSON_ARRAY(
                            '#1890ff', '#52c41a', '#faad14', '#f5222d', 
                            '#722ed1', '#13c2c2', '#eb2f96', '#fa8c16'
                        )
                    )
                )
            ) as chartData
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.hospital.lead.dao.LeadStatisticsDao">

    <!-- 根据线索ID查询统计信息 -->
    <select id="selectByLeadId" resultType="net.lab1024.sa.admin.module.business.hospital.lead.domain.entity.LeadStatisticsEntity">
        SELECT 
            stat_id,
            lead_id,
            follow_count,
            response_count,
            response_rate,
            appointment_count,
            last_communication_time,
            first_follow_time,
            avg_response_time,
            create_time,
            update_time
        FROM t_lead_statistics
        WHERE lead_id = #{leadId}
    </select>

    <!-- 更新跟进统计 -->
    <update id="updateFollowStatistics">
        UPDATE t_lead_statistics 
        SET 
            follow_count = #{followCount},
            last_communication_time = #{lastCommunicationTime},
            update_time = NOW()
        WHERE lead_id = #{leadId}
    </update>

    <!-- 更新预约统计 -->
    <update id="updateAppointmentStatistics">
        UPDATE t_lead_statistics 
        SET 
            appointment_count = #{appointmentCount},
            update_time = NOW()
        WHERE lead_id = #{leadId}
    </update>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.hospital.followup.dao.FollowUpPlanDao">

    <!-- 分页查询回访计划 -->
    <select id="queryPage" resultType="net.lab1024.sa.admin.module.business.hospital.followup.domain.vo.FollowUpPlanVO">
        SELECT
            p.plan_id,
            p.plan_name,
            p.plan_description,
            p.plan_type,
            CASE p.plan_type
                WHEN 1 THEN '定期回访'
                WHEN 2 THEN '节日回访'
                WHEN 3 THEN '生日回访'
                WHEN 4 THEN '满意度调查'
                WHEN 5 THEN '产品推广'
                ELSE '未知'
            END AS plan_type_desc,
            p.target_customer_type,
            CASE p.target_customer_type
                WHEN 1 THEN '全部客户'
                WHEN 2 THEN '新客户'
                WHEN 3 THEN '老客户'
                WHEN 4 THEN 'VIP客户'
                WHEN 5 THEN '指定客户'
                ELSE '未知'
            END AS target_customer_type_desc,
            p.customer_filter_conditions,
            p.follow_up_method,
            CASE p.follow_up_method
                WHEN 1 THEN '电话回访'
                WHEN 2 THEN '短信回访'
                WHEN 3 THEN '微信回访'
                WHEN 4 THEN '邮件回访'
                WHEN 5 THEN '上门回访'
                ELSE '未知'
            END AS follow_up_method_desc,
            p.follow_up_content_template,
            p.plan_start_date,
            p.plan_end_date,
            p.execution_frequency,
            CASE p.execution_frequency
                WHEN 1 THEN '一次性'
                WHEN 2 THEN '每日'
                WHEN 3 THEN '每周'
                WHEN 4 THEN '每月'
                WHEN 5 THEN '每季度'
                WHEN 6 THEN '每年'
                ELSE '未知'
            END AS execution_frequency_desc,
            p.execution_time,
            p.execution_days,
            p.priority_level,
            CASE p.priority_level
                WHEN 1 THEN '低'
                WHEN 2 THEN '中'
                WHEN 3 THEN '高'
                WHEN 4 THEN '紧急'
                ELSE '未知'
            END AS priority_level_desc,
            p.plan_status,
            CASE p.plan_status
                WHEN 1 THEN '待执行'
                WHEN 2 THEN '执行中'
                WHEN 3 THEN '已暂停'
                WHEN 4 THEN '已完成'
                WHEN 5 THEN '已取消'
                ELSE '未知'
            END AS plan_status_desc,
            p.auto_generate_records,
            p.responsible_user_id,
            p.responsible_user_name,
            p.expected_customer_count,
            p.actual_customer_count,
            p.completed_count,
            p.success_rate,
            p.last_execution_time,
            p.next_execution_time,
            p.remark,
            p.create_user_id,
            p.create_time,
            p.update_user_id,
            p.update_time
        FROM t_follow_up_plan p
        WHERE p.deleted_flag = 0
        <if test="queryForm.planName != null and queryForm.planName != ''">
            AND p.plan_name LIKE CONCAT('%', #{queryForm.planName}, '%')
        </if>
        <if test="queryForm.planType != null">
            AND p.plan_type = #{queryForm.planType}
        </if>
        <if test="queryForm.targetCustomerType != null">
            AND p.target_customer_type = #{queryForm.targetCustomerType}
        </if>
        <if test="queryForm.followUpMethod != null">
            AND p.follow_up_method = #{queryForm.followUpMethod}
        </if>
        <if test="queryForm.planStatus != null">
            AND p.plan_status = #{queryForm.planStatus}
        </if>
        <if test="queryForm.priorityLevel != null">
            AND p.priority_level = #{queryForm.priorityLevel}
        </if>
        <if test="queryForm.responsibleUserName != null and queryForm.responsibleUserName != ''">
            AND p.responsible_user_name LIKE CONCAT('%', #{queryForm.responsibleUserName}, '%')
        </if>
        <if test="queryForm.planStartDateBegin != null">
            AND p.plan_start_date >= #{queryForm.planStartDateBegin}
        </if>
        <if test="queryForm.planStartDateEnd != null">
            AND p.plan_start_date &lt;= #{queryForm.planStartDateEnd}
        </if>
        <if test="queryForm.planEndDateBegin != null">
            AND p.plan_end_date >= #{queryForm.planEndDateBegin}
        </if>
        <if test="queryForm.planEndDateEnd != null">
            AND p.plan_end_date &lt;= #{queryForm.planEndDateEnd}
        </if>
        <if test="queryForm.createTimeBegin != null">
            AND DATE(p.create_time) >= #{queryForm.createTimeBegin}
        </if>
        <if test="queryForm.createTimeEnd != null">
            AND DATE(p.create_time) &lt;= #{queryForm.createTimeEnd}
        </if>
        ORDER BY p.create_time DESC
    </select>

    <!-- 获取需要执行的计划列表 -->
    <select id="getExecutablePlans" resultType="net.lab1024.sa.admin.module.business.hospital.followup.domain.entity.FollowUpPlanEntity">
        SELECT *
        FROM t_follow_up_plan
        WHERE deleted_flag = 0
          AND plan_status = 2
          AND (next_execution_time IS NULL OR next_execution_time &lt;= #{currentTime})
          AND (plan_end_date IS NULL OR plan_end_date >= CURDATE())
    </select>

    <!-- 获取计划统计数据 -->
    <select id="getPlanStatistics" resultType="net.lab1024.sa.admin.module.business.hospital.followup.domain.vo.FollowUpPlanVO">
        SELECT
            plan_type,
            COUNT(*) as total_count,
            SUM(CASE WHEN plan_status = 1 THEN 1 ELSE 0 END) as pending_count,
            SUM(CASE WHEN plan_status = 2 THEN 1 ELSE 0 END) as running_count,
            SUM(CASE WHEN plan_status = 3 THEN 1 ELSE 0 END) as paused_count,
            SUM(CASE WHEN plan_status = 4 THEN 1 ELSE 0 END) as completed_count,
            SUM(expected_customer_count) as total_expected_count,
            SUM(actual_customer_count) as total_actual_count,
            SUM(completed_count) as total_completed_count
        FROM t_follow_up_plan
        WHERE deleted_flag = 0
        GROUP BY plan_type
    </select>

    <!-- 更新计划执行统计 -->
    <update id="updatePlanStatistics">
        UPDATE t_follow_up_plan
        SET actual_customer_count = #{actualCustomerCount},
            completed_count = #{completedCount},
            success_rate = CASE 
                WHEN #{actualCustomerCount} > 0 
                THEN ROUND(#{completedCount} * 100.0 / #{actualCustomerCount}, 2)
                ELSE 0 
            END,
            last_execution_time = #{lastExecutionTime},
            next_execution_time = #{nextExecutionTime}
        WHERE plan_id = #{planId}
    </update>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.hospital.followup.dao.FollowUpPlanEnhancedDao">

    <select id="queryPage" resultType="net.lab1024.sa.admin.module.business.hospital.followup.domain.vo.FollowUpPlanEnhancedVO">
        SELECT
            plan_id,
            plan_no,
            plan_name,
            plan_description,
            plan_type,
            target_patient_type,
            filter_conditions,
            target_doctor_ids,
            target_department_ids,
            target_patient_tags,
            visit_date_range_start,
            visit_date_range_end,
            follow_up_method,
            follow_up_content_template,
            follow_up_questions,
            plan_start_date,
            plan_end_date,
            execution_frequency,
            execution_time,
            execution_days,
            delay_days_after_visit,
            priority_level,
            plan_status,
            auto_generate_records,
            responsible_user_id,
            responsible_user_name,
            department_id,
            department_name,
            expected_patient_count,
            actual_patient_count,
            completed_count,
            success_rate,
            last_execution_time,
            next_execution_time,
            total_executions,
            remark,
            create_time,
            update_time
        FROM t_follow_up_plan_enhanced
        <where>
            deleted_flag = 0
            <if test="query.planName != null and query.planName != ''">
                AND plan_name LIKE CONCAT('%', #{query.planName}, '%')
            </if>
            <if test="query.planType != null">
                AND plan_type = #{query.planType}
            </if>
            <if test="query.targetPatientType != null">
                AND target_patient_type = #{query.targetPatientType}
            </if>
            <if test="query.followUpMethod != null">
                AND follow_up_method = #{query.followUpMethod}
            </if>
            <if test="query.executionFrequency != null">
                AND execution_frequency = #{query.executionFrequency}
            </if>
            <if test="query.planStatus != null">
                AND plan_status = #{query.planStatus}
            </if>
            <if test="query.priorityLevel != null">
                AND priority_level = #{query.priorityLevel}
            </if>
            <if test="query.responsibleUserName != null and query.responsibleUserName != ''">
                AND responsible_user_name LIKE CONCAT('%', #{query.responsibleUserName}, '%')
            </if>
            <if test="query.departmentId != null">
                AND department_id = #{query.departmentId}
            </if>
            <if test="query.planStartDateBegin != null">
                AND plan_start_date >= #{query.planStartDateBegin}
            </if>
            <if test="query.planStartDateEnd != null">
                AND plan_start_date &lt;= #{query.planStartDateEnd}
            </if>
            <if test="query.planEndDateBegin != null">
                AND plan_end_date >= #{query.planEndDateBegin}
            </if>
            <if test="query.planEndDateEnd != null">
                AND plan_end_date &lt;= #{query.planEndDateEnd}
            </if>
            <if test="query.nextExecutionTimeBegin != null">
                AND DATE(next_execution_time) >= #{query.nextExecutionTimeBegin}
            </if>
            <if test="query.nextExecutionTimeEnd != null">
                AND DATE(next_execution_time) &lt;= #{query.nextExecutionTimeEnd}
            </if>
            <if test="query.createTimeBegin != null">
                AND DATE(create_time) >= #{query.createTimeBegin}
            </if>
            <if test="query.createTimeEnd != null">
                AND DATE(create_time) &lt;= #{query.createTimeEnd}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectByPlanNo" resultType="net.lab1024.sa.admin.module.business.hospital.followup.domain.entity.FollowUpPlanEnhancedEntity">
        SELECT * FROM t_follow_up_plan_enhanced
        WHERE plan_no = #{planNo}
          AND deleted_flag = 0
        <if test="excludePlanId != null">
            AND plan_id != #{excludePlanId}
        </if>
        LIMIT 1
    </select>

    <select id="getExecutablePlans" resultType="net.lab1024.sa.admin.module.business.hospital.followup.domain.entity.FollowUpPlanEnhancedEntity">
        SELECT * FROM t_follow_up_plan_enhanced
        WHERE deleted_flag = 0
          AND plan_status = 2
          AND next_execution_time &lt;= #{currentTime}
        ORDER BY priority_level DESC, next_execution_time ASC
    </select>

    <select id="selectByResponsibleUser" resultType="net.lab1024.sa.admin.module.business.hospital.followup.domain.vo.FollowUpPlanEnhancedVO">
        SELECT
            plan_id,
            plan_no,
            plan_name,
            plan_description,
            plan_type,
            target_patient_type,
            plan_status,
            priority_level,
            responsible_user_id,
            responsible_user_name,
            department_id,
            department_name,
            expected_patient_count,
            actual_patient_count,
            completed_count,
            success_rate,
            last_execution_time,
            next_execution_time,
            create_time,
            update_time
        FROM t_follow_up_plan_enhanced
        WHERE deleted_flag = 0
          AND responsible_user_id = #{responsibleUserId}
        <if test="planStatus != null">
            AND plan_status = #{planStatus}
        </if>
        ORDER BY create_time DESC
    </select>

    <select id="selectByDepartment" resultType="net.lab1024.sa.admin.module.business.hospital.followup.domain.vo.FollowUpPlanEnhancedVO">
        SELECT
            plan_id,
            plan_no,
            plan_name,
            plan_description,
            plan_type,
            target_patient_type,
            plan_status,
            priority_level,
            responsible_user_id,
            responsible_user_name,
            department_id,
            department_name,
            expected_patient_count,
            actual_patient_count,
            completed_count,
            success_rate,
            last_execution_time,
            next_execution_time,
            create_time,
            update_time
        FROM t_follow_up_plan_enhanced
        WHERE deleted_flag = 0
          AND department_id = #{departmentId}
        <if test="planStatus != null">
            AND plan_status = #{planStatus}
        </if>
        ORDER BY create_time DESC
    </select>

    <update id="updatePlanStatus">
        UPDATE t_follow_up_plan_enhanced
        SET plan_status = #{planStatus},
            update_user_id = #{updateUserId},
            update_time = #{updateTime}
        WHERE plan_id = #{planId}
          AND deleted_flag = 0
    </update>

    <update id="updateExecutionInfo">
        UPDATE t_follow_up_plan_enhanced
        SET actual_patient_count = #{actualPatientCount},
            last_execution_time = #{lastExecutionTime},
            next_execution_time = #{nextExecutionTime},
            total_executions = #{totalExecutions},
            update_user_id = #{updateUserId},
            update_time = #{updateTime}
        WHERE plan_id = #{planId}
          AND deleted_flag = 0
    </update>

    <update id="updateStatistics">
        UPDATE t_follow_up_plan_enhanced
        SET completed_count = #{completedCount},
            success_rate = #{successRate},
            update_user_id = #{updateUserId},
            update_time = #{updateTime}
        WHERE plan_id = #{planId}
          AND deleted_flag = 0
    </update>

    <select id="getPlanStatistics" resultType="net.lab1024.sa.admin.module.business.hospital.followup.domain.vo.FollowUpPlanEnhancedVO">
        SELECT
            plan_type,
            plan_status,
            COUNT(*) as plan_count,
            SUM(expected_patient_count) as total_expected_count,
            SUM(actual_patient_count) as total_actual_count,
            SUM(completed_count) as total_completed_count,
            AVG(success_rate) as avg_success_rate
        FROM t_follow_up_plan_enhanced
        <where>
            deleted_flag = 0
            <if test="query.planType != null">
                AND plan_type = #{query.planType}
            </if>
            <if test="query.planStatus != null">
                AND plan_status = #{query.planStatus}
            </if>
            <if test="query.responsibleUserName != null and query.responsibleUserName != ''">
                AND responsible_user_name LIKE CONCAT('%', #{query.responsibleUserName}, '%')
            </if>
            <if test="query.departmentId != null">
                AND department_id = #{query.departmentId}
            </if>
            <if test="query.createTimeBegin != null">
                AND DATE(create_time) >= #{query.createTimeBegin}
            </if>
            <if test="query.createTimeEnd != null">
                AND DATE(create_time) &lt;= #{query.createTimeEnd}
            </if>
        </where>
        GROUP BY plan_type, plan_status
        ORDER BY plan_type, plan_status
    </select>

    <select id="getPlanCountByStatus" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_follow_up_plan_enhanced
        WHERE deleted_flag = 0
          AND plan_status = #{planStatus}
        <if test="departmentId != null">
            AND department_id = #{departmentId}
        </if>
        <if test="responsibleUserId != null">
            AND responsible_user_id = #{responsibleUserId}
        </if>
    </select>

    <select id="getTodayPendingPlans" resultType="net.lab1024.sa.admin.module.business.hospital.followup.domain.vo.FollowUpPlanEnhancedVO">
        SELECT
            plan_id,
            plan_no,
            plan_name,
            plan_description,
            plan_type,
            target_patient_type,
            plan_status,
            priority_level,
            responsible_user_id,
            responsible_user_name,
            department_id,
            department_name,
            next_execution_time,
            create_time
        FROM t_follow_up_plan_enhanced
        WHERE deleted_flag = 0
          AND plan_status = 2
          AND DATE(next_execution_time) = CURDATE()
        <if test="responsibleUserId != null">
            AND responsible_user_id = #{responsibleUserId}
        </if>
        <if test="departmentId != null">
            AND department_id = #{departmentId}
        </if>
        ORDER BY priority_level DESC, next_execution_time ASC
    </select>

    <select id="getOverduePlans" resultType="net.lab1024.sa.admin.module.business.hospital.followup.domain.vo.FollowUpPlanEnhancedVO">
        SELECT
            plan_id,
            plan_no,
            plan_name,
            plan_description,
            plan_type,
            target_patient_type,
            plan_status,
            priority_level,
            responsible_user_id,
            responsible_user_name,
            department_id,
            department_name,
            next_execution_time,
            create_time
        FROM t_follow_up_plan_enhanced
        WHERE deleted_flag = 0
          AND plan_status = 2
          AND next_execution_time &lt; NOW()
        <if test="responsibleUserId != null">
            AND responsible_user_id = #{responsibleUserId}
        </if>
        <if test="departmentId != null">
            AND department_id = #{departmentId}
        </if>
        ORDER BY next_execution_time ASC
    </select>

    <select id="getRecentPlans" resultType="net.lab1024.sa.admin.module.business.hospital.followup.domain.vo.FollowUpPlanEnhancedVO">
        SELECT
            plan_id,
            plan_no,
            plan_name,
            plan_description,
            plan_type,
            target_patient_type,
            plan_status,
            priority_level,
            responsible_user_id,
            responsible_user_name,
            department_id,
            department_name,
            create_time
        FROM t_follow_up_plan_enhanced
        WHERE deleted_flag = 0
        <if test="departmentId != null">
            AND department_id = #{departmentId}
        </if>
        <if test="responsibleUserId != null">
            AND responsible_user_id = #{responsibleUserId}
        </if>
        ORDER BY create_time DESC
        LIMIT #{limit}
    </select>

</mapper>

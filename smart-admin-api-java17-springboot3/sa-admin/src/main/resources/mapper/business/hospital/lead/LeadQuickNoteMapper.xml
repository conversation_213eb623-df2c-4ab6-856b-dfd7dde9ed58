<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.hospital.lead.dao.LeadQuickNoteDao">

    <!-- 根据线索ID查询最新的快速笔记 -->
    <select id="selectLatestByLeadId" resultType="net.lab1024.sa.admin.module.business.hospital.lead.domain.entity.LeadQuickNoteEntity">
        SELECT 
            note_id,
            lead_id,
            note_content,
            create_user_id,
            create_user_name,
            create_time,
            update_time
        FROM t_lead_quick_note
        WHERE lead_id = #{leadId}
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 根据线索ID删除所有笔记 -->
    <delete id="deleteByLeadId">
        DELETE FROM t_lead_quick_note WHERE lead_id = #{leadId}
    </delete>

</mapper>

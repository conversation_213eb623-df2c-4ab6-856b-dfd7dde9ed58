<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.hospital.visit.dao.VisitPatientDao">

    <!-- 查询结果列 -->
    <sql id="base_columns">
        t_visit_patient.patient_id,
        t_visit_patient.patient_no,
        t_visit_patient.patient_name,
        t_visit_patient.patient_phone,
        t_visit_patient.patient_wechat,
        t_visit_patient.patient_email,
        t_visit_patient.gender,
        t_visit_patient.age,
        t_visit_patient.birthday,
        t_visit_patient.id_card,
        t_visit_patient.patient_address,
        t_visit_patient.occupation,
        t_visit_patient.visit_status,
        t_visit_patient.diagnosis_status,
        t_visit_patient.appointment_id,
        t_visit_patient.assigned_doctor_id,
        t_visit_patient.assigned_doctor_name,
        t_visit_patient.assigned_assistant_id,
        t_visit_patient.assigned_assistant_name,
        t_visit_patient.visit_date,
        t_visit_patient.visit_time,
        t_visit_patient.registration_time,
        t_visit_patient.completion_time,
        t_visit_patient.department_id,
        t_visit_patient.department_name,
        t_visit_patient.patient_source,
        t_visit_patient.patient_level,
        t_visit_patient.patient_tags,
        t_visit_patient.first_visit_date,
        t_visit_patient.last_visit_date,
        t_visit_patient.total_consumption,
        t_visit_patient.emergency_contact,
        t_visit_patient.emergency_phone,
        t_visit_patient.remark,
        t_visit_patient.create_time,
        t_visit_patient.update_time
    </sql>

    <!-- 分页查询患者 -->
    <select id="queryPage" resultType="net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.VisitPatientVO">
        SELECT 
        <include refid="base_columns"/>,
        -- 字典转换
        CASE t_visit_patient.gender 
            WHEN 1 THEN '男' 
            WHEN 2 THEN '女' 
            ELSE '未知' 
        END as gender_name,
        CASE t_visit_patient.visit_status 
            WHEN 1 THEN '未到诊' 
            WHEN 2 THEN '已到诊' 
            WHEN 3 THEN '诊疗中' 
            WHEN 4 THEN '已完成' 
            WHEN 5 THEN '已离院' 
            ELSE '未知' 
        END as visit_status_name,
        CASE t_visit_patient.patient_level 
            WHEN 1 THEN '普通患者' 
            WHEN 2 THEN 'VIP患者' 
            WHEN 3 THEN 'SVIP患者' 
            ELSE '普通患者' 
        END as patient_level_name,
        -- 统计信息
        COALESCE(diagnosis_stats.diagnosis_count, 0) as diagnosis_count,
        COALESCE(prescription_stats.prescription_count, 0) as prescription_count,
        COALESCE(follow_stats.follow_count, 0) as follow_count,
        diagnosis_stats.last_diagnosis_time,
        follow_stats.last_follow_time,
        COALESCE(pending_stats.pending_task_count, 0) as pending_task_count
        FROM t_visit_patient
        -- 诊断统计
        LEFT JOIN (
            SELECT patient_id, COUNT(*) as diagnosis_count, MAX(create_time) as last_diagnosis_time
            FROM t_diagnosis_record 
            WHERE deleted_flag = 0 
            GROUP BY patient_id
        ) diagnosis_stats ON t_visit_patient.patient_id = diagnosis_stats.patient_id
        -- 开单统计
        LEFT JOIN (
            SELECT patient_id, COUNT(*) as prescription_count
            FROM t_prescription_record 
            WHERE deleted_flag = 0 
            GROUP BY patient_id
        ) prescription_stats ON t_visit_patient.patient_id = prescription_stats.patient_id
        -- 跟进统计
        LEFT JOIN (
            SELECT patient_id, COUNT(*) as follow_count, MAX(create_time) as last_follow_time
            FROM t_visit_follow 
            WHERE deleted_flag = 0 
            GROUP BY patient_id
        ) follow_stats ON t_visit_patient.patient_id = follow_stats.patient_id
        -- 待处理任务统计
        LEFT JOIN (
            SELECT patient_id, COUNT(*) as pending_task_count
            FROM t_visit_follow 
            WHERE deleted_flag = 0 AND follow_status IN (1, 2)
            GROUP BY patient_id
        ) pending_stats ON t_visit_patient.patient_id = pending_stats.patient_id
        <where>
            t_visit_patient.deleted_flag = #{query.deletedFlag}
            <if test="query.patientName != null and query.patientName != ''">
                AND t_visit_patient.patient_name LIKE CONCAT('%', #{query.patientName}, '%')
            </if>
            <if test="query.patientPhone != null and query.patientPhone != ''">
                AND t_visit_patient.patient_phone LIKE CONCAT('%', #{query.patientPhone}, '%')
            </if>
            <if test="query.visitStatus != null">
                AND t_visit_patient.visit_status = #{query.visitStatus}
            </if>
            <if test="query.assignedDoctorId != null">
                AND t_visit_patient.assigned_doctor_id = #{query.assignedDoctorId}
            </if>
            <if test="query.assignedDoctorName != null and query.assignedDoctorName != ''">
                AND t_visit_patient.assigned_doctor_name LIKE CONCAT('%', #{query.assignedDoctorName}, '%')
            </if>
            <if test="query.assignedAssistantId != null">
                AND t_visit_patient.assigned_assistant_id = #{query.assignedAssistantId}
            </if>
            <if test="query.assignedAssistantName != null and query.assignedAssistantName != ''">
                AND t_visit_patient.assigned_assistant_name LIKE CONCAT('%', #{query.assignedAssistantName}, '%')
            </if>
            <if test="query.departmentId != null">
                AND t_visit_patient.department_id = #{query.departmentId}
            </if>
            <if test="query.departmentName != null and query.departmentName != ''">
                AND t_visit_patient.department_name LIKE CONCAT('%', #{query.departmentName}, '%')
            </if>
            <if test="query.visitDateStart != null">
                AND t_visit_patient.visit_date >= #{query.visitDateStart}
            </if>
            <if test="query.visitDateEnd != null">
                AND t_visit_patient.visit_date &lt;= #{query.visitDateEnd}
            </if>
            <if test="query.registrationTimeStart != null">
                AND t_visit_patient.registration_time >= #{query.registrationTimeStart}
            </if>
            <if test="query.registrationTimeEnd != null">
                AND t_visit_patient.registration_time &lt;= #{query.registrationTimeEnd}
            </if>
            <if test="query.patientSource != null and query.patientSource != ''">
                AND t_visit_patient.patient_source LIKE CONCAT('%', #{query.patientSource}, '%')
            </if>
            <if test="query.patientLevel != null">
                AND t_visit_patient.patient_level = #{query.patientLevel}
            </if>
            <if test="query.patientTags != null and query.patientTags != ''">
                AND t_visit_patient.patient_tags LIKE CONCAT('%', #{query.patientTags}, '%')
            </if>
            <if test="query.searchWord != null and query.searchWord != ''">
                AND (
                    t_visit_patient.patient_name LIKE CONCAT('%', #{query.searchWord}, '%')
                    OR t_visit_patient.patient_phone LIKE CONCAT('%', #{query.searchWord}, '%')
                    OR t_visit_patient.patient_no LIKE CONCAT('%', #{query.searchWord}, '%')
                    OR t_visit_patient.id_card LIKE CONCAT('%', #{query.searchWord}, '%')
                )
            </if>
            <!-- 数据权限过滤 -->
            <if test="query.dataScopeEmployeeIds != null and query.dataScopeEmployeeIds.size() > 0">
                AND (
                    t_visit_patient.assigned_doctor_id IN
                    <foreach collection="query.dataScopeEmployeeIds" open="(" close=")" separator="," item="employeeId">
                        #{employeeId}
                    </foreach>
                    OR t_visit_patient.assigned_assistant_id IN
                    <foreach collection="query.dataScopeEmployeeIds" open="(" close=")" separator="," item="employeeId">
                        #{employeeId}
                    </foreach>
                )
            </if>
            <if test="query.dataScopeDepartmentIds != null and query.dataScopeDepartmentIds.size() > 0">
                AND t_visit_patient.department_id IN
                <foreach collection="query.dataScopeDepartmentIds" open="(" close=")" separator="," item="departmentId">
                    #{departmentId}
                </foreach>
            </if>
        </where>
        <if test="query.sortItemList == null or query.sortItemList.size == 0">
            ORDER BY t_visit_patient.registration_time DESC, t_visit_patient.patient_id DESC
        </if>
    </select>

    <!-- 根据电话号码查询患者 -->
    <select id="selectByPhone" resultType="net.lab1024.sa.admin.module.business.hospital.visit.domain.entity.VisitPatientEntity">
        SELECT * FROM t_visit_patient
        WHERE patient_phone = #{patientPhone}
        AND deleted_flag = 0
        <if test="excludePatientId != null">
            AND patient_id != #{excludePatientId}
        </if>
        LIMIT 1
    </select>

    <!-- 根据身份证号查询患者 -->
    <select id="selectByIdCard" resultType="net.lab1024.sa.admin.module.business.hospital.visit.domain.entity.VisitPatientEntity">
        SELECT * FROM t_visit_patient
        WHERE id_card = #{idCard}
        AND deleted_flag = 0
        <if test="excludePatientId != null">
            AND patient_id != #{excludePatientId}
        </if>
        LIMIT 1
    </select>

    <!-- 根据患者编号查询患者 -->
    <select id="selectByPatientNo" resultType="net.lab1024.sa.admin.module.business.hospital.visit.domain.entity.VisitPatientEntity">
        SELECT * FROM t_visit_patient
        WHERE patient_no = #{patientNo}
        AND deleted_flag = 0
        <if test="excludePatientId != null">
            AND patient_id != #{excludePatientId}
        </if>
        LIMIT 1
    </select>

    <!-- 根据医生ID查询患者 -->
    <select id="selectByDoctorId" resultType="net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.VisitPatientVO">
        SELECT <include refid="base_columns"/>
        FROM t_visit_patient
        WHERE assigned_doctor_id = #{doctorId}
        AND deleted_flag = 0
        <if test="visitStatus != null">
            AND visit_status = #{visitStatus}
        </if>
        ORDER BY registration_time DESC
    </select>

    <!-- 根据治疗助理ID查询患者 -->
    <select id="selectByAssistantId" resultType="net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.VisitPatientVO">
        SELECT <include refid="base_columns"/>
        FROM t_visit_patient
        WHERE assigned_assistant_id = #{assistantId}
        AND deleted_flag = 0
        <if test="visitStatus != null">
            AND visit_status = #{visitStatus}
        </if>
        ORDER BY registration_time DESC
    </select>

    <!-- 根据部门ID查询患者 -->
    <select id="selectByDepartmentId" resultType="net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.VisitPatientVO">
        SELECT <include refid="base_columns"/>
        FROM t_visit_patient
        WHERE department_id = #{departmentId}
        AND deleted_flag = 0
        <if test="visitStatus != null">
            AND visit_status = #{visitStatus}
        </if>
        ORDER BY registration_time DESC
    </select>

    <!-- 更新患者到诊状态 -->
    <update id="updateVisitStatus">
        UPDATE t_visit_patient 
        SET visit_status = #{visitStatus},
            update_user_id = #{updateUserId},
            update_time = #{updateTime}
        WHERE patient_id = #{patientId}
    </update>

    <!-- 分配医生 -->
    <update id="assignDoctor">
        UPDATE t_visit_patient 
        SET assigned_doctor_id = #{doctorId},
            assigned_doctor_name = #{doctorName},
            update_user_id = #{updateUserId},
            update_time = #{updateTime}
        WHERE patient_id = #{patientId}
    </update>

    <!-- 分配治疗助理 -->
    <update id="assignAssistant">
        UPDATE t_visit_patient 
        SET assigned_assistant_id = #{assistantId},
            assigned_assistant_name = #{assistantName},
            update_user_id = #{updateUserId},
            update_time = #{updateTime}
        WHERE patient_id = #{patientId}
    </update>

    <!-- 获取患者统计数据 -->
    <select id="getPatientCount" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM t_visit_patient
        WHERE deleted_flag = 0
        <if test="departmentId != null">
            AND department_id = #{departmentId}
        </if>
        <if test="doctorId != null">
            AND assigned_doctor_id = #{doctorId}
        </if>
    </select>

    <!-- 根据状态统计患者数量 -->
    <select id="getPatientCountByStatus" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM t_visit_patient
        WHERE deleted_flag = 0
        AND visit_status = #{visitStatus}
        <if test="departmentId != null">
            AND department_id = #{departmentId}
        </if>
        <if test="doctorId != null">
            AND assigned_doctor_id = #{doctorId}
        </if>
    </select>

    <!-- 获取患者详情 -->
    <select id="getPatientDetail" resultType="net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.VisitPatientVO">
        SELECT 
        <include refid="base_columns"/>,
        -- 统计信息
        COALESCE(diagnosis_stats.diagnosis_count, 0) as diagnosis_count,
        COALESCE(prescription_stats.prescription_count, 0) as prescription_count,
        COALESCE(follow_stats.follow_count, 0) as follow_count,
        diagnosis_stats.last_diagnosis_time,
        follow_stats.last_follow_time,
        COALESCE(pending_stats.pending_task_count, 0) as pending_task_count
        FROM t_visit_patient
        -- 诊断统计
        LEFT JOIN (
            SELECT patient_id, COUNT(*) as diagnosis_count, MAX(create_time) as last_diagnosis_time
            FROM t_diagnosis_record 
            WHERE deleted_flag = 0 
            GROUP BY patient_id
        ) diagnosis_stats ON t_visit_patient.patient_id = diagnosis_stats.patient_id
        -- 开单统计
        LEFT JOIN (
            SELECT patient_id, COUNT(*) as prescription_count
            FROM t_prescription_record 
            WHERE deleted_flag = 0 
            GROUP BY patient_id
        ) prescription_stats ON t_visit_patient.patient_id = prescription_stats.patient_id
        -- 跟进统计
        LEFT JOIN (
            SELECT patient_id, COUNT(*) as follow_count, MAX(create_time) as last_follow_time
            FROM t_visit_follow 
            WHERE deleted_flag = 0 
            GROUP BY patient_id
        ) follow_stats ON t_visit_patient.patient_id = follow_stats.patient_id
        -- 待处理任务统计
        LEFT JOIN (
            SELECT patient_id, COUNT(*) as pending_task_count
            FROM t_visit_follow 
            WHERE deleted_flag = 0 AND follow_status IN (1, 2)
            GROUP BY patient_id
        ) pending_stats ON t_visit_patient.patient_id = pending_stats.patient_id
        WHERE t_visit_patient.patient_id = #{patientId}
        AND t_visit_patient.deleted_flag = 0
    </select>

    <!-- 批量更新删除状态 -->
    <update id="batchUpdateDeleted">
        UPDATE t_visit_patient
        SET deleted_flag = #{deletedFlag},
            update_user_id = #{updateUserId},
            update_time = #{updateTime}
        WHERE patient_id IN
        <foreach collection="patientIds" open="(" close=")" separator="," item="patientId">
            #{patientId}
        </foreach>
    </update>

    <!-- 根据预约ID查询到诊患者记录 -->
    <select id="selectByAppointmentId" resultType="net.lab1024.sa.admin.module.business.hospital.visit.domain.entity.VisitPatientEntity">
        SELECT * FROM t_visit_patient
        WHERE appointment_id = #{appointmentId}
        AND deleted_flag = 0
        LIMIT 1
    </select>

    <!-- 更新患者诊断状态 -->
    <update id="updateDiagnosisStatus">
        UPDATE t_visit_patient
        SET diagnosis_status = #{diagnosisStatus},
            update_user_id = #{updateUserId},
            update_user_name = #{updateUserName},
            update_time = NOW()
        WHERE patient_id = #{patientId}
        AND deleted_flag = 0
    </update>

</mapper>

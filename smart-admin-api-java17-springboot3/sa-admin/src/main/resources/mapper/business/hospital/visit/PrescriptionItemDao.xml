<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.hospital.visit.dao.PrescriptionItemDao">

    <!-- 根据开单ID查询项目明细 -->
    <select id="selectByPrescriptionId" resultType="net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.PrescriptionItemVO">
        SELECT
            pi.item_id,
            pi.prescription_id,
            pi.item_name,
            pi.item_type,
            CASE pi.item_type
                WHEN 'treatment' THEN '治疗'
                WHEN 'medicine' THEN '药品'
                WHEN 'examination' THEN '检查'
                WHEN 'material' THEN '材料'
                ELSE '未知'
            END AS item_type_name,
            pi.unit_price,
            pi.quantity,
            pi.subtotal,
            pi.item_description,
            pi.create_time,
            pi.update_time
        FROM t_prescription_item pi
        WHERE pi.prescription_id = #{prescriptionId}
        ORDER BY pi.create_time ASC
    </select>

    <!-- 批量插入项目明细 -->
    <insert id="batchInsert">
        INSERT INTO t_prescription_item (
            prescription_id,
            item_name,
            item_type,
            unit_price,
            quantity,
            subtotal,
            item_description,
            create_time,
            update_time
        ) VALUES
        <foreach collection="itemList" item="item" separator=",">
            (
                #{item.prescriptionId},
                #{item.itemName},
                #{item.itemType},
                #{item.unitPrice},
                #{item.quantity},
                #{item.subtotal},
                #{item.itemDescription},
                #{item.createTime},
                #{item.updateTime}
            )
        </foreach>
    </insert>

    <!-- 根据开单ID删除项目明细 -->
    <delete id="deleteByPrescriptionId">
        DELETE FROM t_prescription_item
        WHERE prescription_id = #{prescriptionId}
    </delete>

</mapper>

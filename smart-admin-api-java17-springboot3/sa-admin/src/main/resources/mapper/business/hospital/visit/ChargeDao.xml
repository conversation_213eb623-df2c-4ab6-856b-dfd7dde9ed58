<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.hospital.visit.dao.ChargeDao">

    <!-- 分页查询收费记录 -->
    <select id="queryPage" resultType="net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.ChargeVO">
        SELECT
            c.charge_id,
            c.prescription_id,
            c.diagnosis_id,
            c.patient_id,
            c.patient_no,
            c.patient_name,
            c.charge_no,
            c.total_amount,
            c.actual_amount,
            c.discount_amount,
            c.payment_account,
            CASE c.payment_account
                WHEN 'cash' THEN '现金'
                WHEN 'alipay' THEN '支付宝'
                WHEN 'wechat' THEN '微信支付'
                WHEN 'bank' THEN '银行卡'
                ELSE '未知'
            END AS payment_account_name,
            c.charge_status,
            CASE c.charge_status
                WHEN 1 THEN '已收费'
                WHEN 2 THEN '部分收费'
                WHEN 3 THEN '未收费'
                WHEN 4 THEN '已退费'
                ELSE '未知'
            END AS charge_status_name,
            c.charge_time,
            c.charge_note,
            c.cashier_id,
            c.cashier_name,
            c.create_user_name,
            c.create_time,
            c.update_user_name,
            c.update_time
        FROM t_charge c
        WHERE c.deleted_flag = 0
        <if test="queryForm.prescriptionId != null">
            AND c.prescription_id = #{queryForm.prescriptionId}
        </if>
        <if test="queryForm.diagnosisId != null">
            AND c.diagnosis_id = #{queryForm.diagnosisId}
        </if>
        <if test="queryForm.patientId != null">
            AND c.patient_id = #{queryForm.patientId}
        </if>
        <if test="queryForm.patientNo != null and queryForm.patientNo != ''">
            AND c.patient_no LIKE CONCAT('%', #{queryForm.patientNo}, '%')
        </if>
        <if test="queryForm.patientName != null and queryForm.patientName != ''">
            AND c.patient_name LIKE CONCAT('%', #{queryForm.patientName}, '%')
        </if>
        <if test="queryForm.chargeNo != null and queryForm.chargeNo != ''">
            AND c.charge_no LIKE CONCAT('%', #{queryForm.chargeNo}, '%')
        </if>
        <if test="queryForm.paymentAccount != null and queryForm.paymentAccount != ''">
            AND c.payment_account = #{queryForm.paymentAccount}
        </if>
        <if test="queryForm.chargeStatus != null">
            AND c.charge_status = #{queryForm.chargeStatus}
        </if>
        <if test="queryForm.cashierId != null">
            AND c.cashier_id = #{queryForm.cashierId}
        </if>
        <if test="queryForm.cashierName != null and queryForm.cashierName != ''">
            AND c.cashier_name LIKE CONCAT('%', #{queryForm.cashierName}, '%')
        </if>
        <if test="queryForm.chargeTimeStart != null">
            AND c.charge_time >= #{queryForm.chargeTimeStart}
        </if>
        <if test="queryForm.chargeTimeEnd != null">
            AND c.charge_time &lt;= #{queryForm.chargeTimeEnd}
        </if>
        <if test="queryForm.createTimeStart != null">
            AND c.create_time >= #{queryForm.createTimeStart}
        </if>
        <if test="queryForm.createTimeEnd != null">
            AND c.create_time &lt;= #{queryForm.createTimeEnd}
        </if>
        ORDER BY c.create_time DESC
    </select>

    <!-- 根据患者ID查询收费记录 -->
    <select id="selectByPatientId" resultType="net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.ChargeVO">
        SELECT
            c.charge_id,
            c.prescription_id,
            c.diagnosis_id,
            c.patient_id,
            c.patient_no,
            c.patient_name,
            c.charge_no,
            c.total_amount,
            c.actual_amount,
            c.discount_amount,
            c.payment_account,
            CASE c.payment_account
                WHEN 'cash' THEN '现金'
                WHEN 'alipay' THEN '支付宝'
                WHEN 'wechat' THEN '微信支付'
                WHEN 'bank' THEN '银行卡'
                ELSE '未知'
            END AS payment_account_name,
            c.charge_status,
            CASE c.charge_status
                WHEN 1 THEN '已收费'
                WHEN 2 THEN '部分收费'
                WHEN 3 THEN '未收费'
                WHEN 4 THEN '已退费'
                ELSE '未知'
            END AS charge_status_name,
            c.charge_time,
            c.charge_note,
            c.cashier_id,
            c.cashier_name,
            c.create_user_name,
            c.create_time,
            c.update_user_name,
            c.update_time
        FROM t_charge c
        WHERE c.deleted_flag = 0
        AND c.patient_id = #{patientId}
        ORDER BY c.create_time DESC
    </select>

    <!-- 根据开单ID查询收费记录 -->
    <select id="selectByPrescriptionId" resultType="net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.ChargeVO">
        SELECT
            c.charge_id,
            c.prescription_id,
            c.diagnosis_id,
            c.patient_id,
            c.patient_no,
            c.patient_name,
            c.charge_no,
            c.total_amount,
            c.actual_amount,
            c.discount_amount,
            c.payment_account,
            CASE c.payment_account
                WHEN 'cash' THEN '现金'
                WHEN 'alipay' THEN '支付宝'
                WHEN 'wechat' THEN '微信支付'
                WHEN 'bank' THEN '银行卡'
                ELSE '未知'
            END AS payment_account_name,
            c.charge_status,
            CASE c.charge_status
                WHEN 1 THEN '已收费'
                WHEN 2 THEN '部分收费'
                WHEN 3 THEN '未收费'
                WHEN 4 THEN '已退费'
                ELSE '未知'
            END AS charge_status_name,
            c.charge_time,
            c.charge_note,
            c.cashier_id,
            c.cashier_name,
            c.create_user_name,
            c.create_time,
            c.update_user_name,
            c.update_time
        FROM t_charge c
        WHERE c.deleted_flag = 0
        AND c.prescription_id = #{prescriptionId}
    </select>

    <!-- 根据诊断ID查询收费记录 -->
    <select id="selectByDiagnosisId" resultType="net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.ChargeVO">
        SELECT
            c.charge_id,
            c.prescription_id,
            c.diagnosis_id,
            c.patient_id,
            c.patient_no,
            c.patient_name,
            c.charge_no,
            c.total_amount,
            c.actual_amount,
            c.discount_amount,
            c.payment_account,
            CASE c.payment_account
                WHEN 'cash' THEN '现金'
                WHEN 'alipay' THEN '支付宝'
                WHEN 'wechat' THEN '微信支付'
                WHEN 'bank' THEN '银行卡'
                ELSE '未知'
            END AS payment_account_name,
            c.charge_status,
            CASE c.charge_status
                WHEN 1 THEN '已收费'
                WHEN 2 THEN '部分收费'
                WHEN 3 THEN '未收费'
                WHEN 4 THEN '已退费'
                ELSE '未知'
            END AS charge_status_name,
            c.charge_time,
            c.charge_note,
            c.cashier_id,
            c.cashier_name,
            c.create_user_name,
            c.create_time,
            c.update_user_name,
            c.update_time
        FROM t_charge c
        WHERE c.deleted_flag = 0
        AND c.diagnosis_id = #{diagnosisId}
    </select>

    <!-- 根据收费ID查询详情 -->
    <select id="selectDetailById" resultType="net.lab1024.sa.admin.module.business.hospital.visit.domain.vo.ChargeVO">
        SELECT
            c.charge_id,
            c.prescription_id,
            c.diagnosis_id,
            c.patient_id,
            c.patient_no,
            c.patient_name,
            c.charge_no,
            c.total_amount,
            c.actual_amount,
            c.discount_amount,
            c.payment_account,
            CASE c.payment_account
                WHEN 'cash' THEN '现金'
                WHEN 'alipay' THEN '支付宝'
                WHEN 'wechat' THEN '微信支付'
                WHEN 'bank' THEN '银行卡'
                ELSE '未知'
            END AS payment_account_name,
            c.charge_status,
            CASE c.charge_status
                WHEN 1 THEN '已收费'
                WHEN 2 THEN '部分收费'
                WHEN 3 THEN '未收费'
                WHEN 4 THEN '已退费'
                ELSE '未知'
            END AS charge_status_name,
            c.charge_time,
            c.charge_note,
            c.cashier_id,
            c.cashier_name,
            c.create_user_name,
            c.create_time,
            c.update_user_name,
            c.update_time
        FROM t_charge c
        WHERE c.deleted_flag = 0
        AND c.charge_id = #{chargeId}
    </select>

    <!-- 更新收费状态 -->
    <update id="updateChargeStatus">
        UPDATE t_charge
        SET charge_status = #{chargeStatus},
            update_user_id = #{updateUserId},
            update_user_name = #{updateUserName},
            update_time = NOW()
        WHERE charge_id = #{chargeId}
        AND deleted_flag = 0
    </update>

</mapper>

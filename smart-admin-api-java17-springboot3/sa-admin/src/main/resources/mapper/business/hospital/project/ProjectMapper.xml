<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.hospital.project.dao.ProjectDao">

    <!-- 项目VO结果映射 -->
    <resultMap id="ProjectVOMap" type="net.lab1024.sa.admin.module.business.hospital.project.domain.vo.ProjectVO">
        <id column="project_id" property="projectId"/>
        <result column="project_name" property="projectName"/>
        <result column="project_category" property="projectCategory"/>
        <result column="standard_price" property="projectPrice"/>
        <result column="duration_minutes" property="duration"/>
        <result column="project_status" property="projectStatus"/>
        <result column="project_status_name" property="projectStatusName"/>
        <result column="project_description" property="projectDescription"/>
        <result column="remark" property="precautions"/>
        <result column="sort" property="sort"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_user_name" property="createUserName"/>
    </resultMap>

    <!-- 分页查询项目 -->
    <select id="queryPage" resultMap="ProjectVOMap">
        SELECT
            p.project_id,
            p.project_name,
            p.project_category,
            p.standard_price,
            p.duration_minutes,
            p.project_status,
            CASE p.project_status
                WHEN 1 THEN '启用'
                WHEN 0 THEN '禁用'
                ELSE '未知'
            END AS project_status_name,
            p.project_description,
            p.remark,
            p.sort,
            p.create_time,
            p.update_time,
            e.actual_name AS create_user_name
        FROM t_project p
        LEFT JOIN t_employee e ON p.create_user_id = e.employee_id
        <where>
            p.deleted_flag = 0
            <if test="query.projectName != null and query.projectName != ''">
                AND p.project_name LIKE CONCAT('%', #{query.projectName}, '%')
            </if>
            <if test="query.projectCategory != null and query.projectCategory != ''">
                AND p.project_category = #{query.projectCategory}
            </if>
            <if test="query.projectStatus != null">
                AND p.project_status = #{query.projectStatus}
            </if>
            <if test="query.createTimeStart != null">
                AND p.create_time >= #{query.createTimeStart}
            </if>
            <if test="query.createTimeEnd != null">
                AND p.create_time &lt;= #{query.createTimeEnd}
            </if>
            <if test="query.searchWord != null and query.searchWord != ''">
                AND (
                    p.project_name LIKE CONCAT('%', #{query.searchWord}, '%')
                    OR p.project_category LIKE CONCAT('%', #{query.searchWord}, '%')
                    OR p.project_description LIKE CONCAT('%', #{query.searchWord}, '%')
                )
            </if>
        </where>
        ORDER BY p.sort ASC, p.create_time DESC
    </select>

    <!-- 根据项目名称查询项目 -->
    <select id="selectByName" resultType="net.lab1024.sa.admin.module.business.hospital.project.domain.entity.ProjectEntity">
        SELECT *
        FROM t_project
        WHERE project_name = #{projectName}
        AND deleted_flag = 0
        <if test="excludeProjectId != null">
            AND project_id != #{excludeProjectId}
        </if>
        LIMIT 1
    </select>

    <!-- 更新项目状态 -->
    <update id="updateStatus">
        UPDATE t_project
        SET project_status = #{projectStatus},
            update_time = NOW()
        WHERE project_id = #{projectId}
        AND deleted_flag = 0
    </update>

    <!-- 批量更新删除状态 -->
    <update id="batchUpdateDeleted">
        UPDATE t_project
        SET deleted_flag = #{deletedFlag},
            update_time = NOW()
        WHERE project_id IN
        <foreach collection="projectIdList" item="projectId" open="(" separator="," close=")">
            #{projectId}
        </foreach>
    </update>

    <!-- 查询所有启用的项目 -->
    <select id="selectAllEnabled" resultMap="ProjectVOMap">
        SELECT
            p.project_id,
            p.project_name,
            p.project_category,
            p.standard_price,
            p.duration_minutes,
            p.project_status,
            '启用' AS project_status_name,
            p.project_description,
            p.remark,
            p.sort,
            p.create_time,
            p.update_time
        FROM t_project p
        WHERE p.project_status = 1
        AND p.deleted_flag = 0
        ORDER BY p.sort ASC, p.project_name ASC
    </select>

    <!-- 根据分类查询项目 -->
    <select id="selectByCategory" resultMap="ProjectVOMap">
        SELECT
            p.project_id,
            p.project_name,
            p.project_category,
            p.standard_price,
            p.duration_minutes,
            p.project_status,
            CASE p.project_status
                WHEN 1 THEN '启用'
                WHEN 0 THEN '禁用'
                ELSE '未知'
            END AS project_status_name,
            p.project_description,
            p.remark,
            p.sort,
            p.create_time,
            p.update_time
        FROM t_project p
        WHERE p.project_category = #{projectCategory}
        AND p.project_status = 1
        AND p.deleted_flag = 0
        ORDER BY p.sort ASC, p.project_name ASC
    </select>

    <!-- 获取项目统计数据 -->
    <select id="getProjectCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_project
        WHERE deleted_flag = 0
    </select>

    <!-- 获取启用项目数量 -->
    <select id="getEnabledProjectCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_project
        WHERE project_status = 1
        AND deleted_flag = 0
    </select>

</mapper>

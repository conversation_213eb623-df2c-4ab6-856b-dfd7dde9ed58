<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.hospital.customer.dao.MedicalRecordDao">

    <!-- 病历VO结果映射 -->
    <resultMap id="MedicalRecordVOMap" type="net.lab1024.sa.admin.module.business.hospital.customer.domain.vo.MedicalRecordVO">
        <id column="record_id" property="recordId"/>
        <result column="lead_id" property="leadId"/>
        <result column="customer_id" property="customerId"/>
        <result column="customer_name" property="customerName"/>
        <result column="appointment_id" property="appointmentId"/>
        <result column="visit_date" property="visitDate"/>
        <result column="chief_complaint" property="chiefComplaint"/>
        <result column="present_illness" property="presentIllness"/>
        <result column="past_history" property="pastHistory"/>
        <result column="examination" property="examination"/>
        <result column="diagnosis" property="diagnosis"/>
        <result column="treatment" property="treatment"/>
        <result column="medication" property="medication"/>
        <result column="doctor_advice" property="doctorAdvice"/>
        <result column="next_visit_date" property="nextVisitDate"/>
        <result column="doctor_id" property="doctorId"/>
        <result column="doctor_name" property="doctorName"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_user_name" property="createUserName"/>
    </resultMap>

    <!-- 分页查询病历 -->
    <select id="queryPage" resultMap="MedicalRecordVOMap">
        SELECT
            mr.record_id,
            mr.lead_id,
            mr.customer_id,
            c.customer_name,
            mr.appointment_id,
            mr.visit_date,
            mr.chief_complaint,
            mr.present_illness,
            mr.past_history,
            mr.examination,
            mr.diagnosis,
            mr.treatment,
            mr.medication,
            mr.doctor_advice,
            mr.next_visit_date,
            mr.doctor_id,
            e.actual_name AS doctor_name,
            mr.remark,
            mr.create_time,
            mr.update_time,
            creator.actual_name AS create_user_name
        FROM t_medical_record mr
        LEFT JOIN t_customer c ON mr.customer_id = c.customer_id
        LEFT JOIN t_employee e ON mr.doctor_id = e.employee_id
        LEFT JOIN t_employee creator ON mr.create_user_id = creator.employee_id
        <where>
            mr.deleted_flag = 0
            <if test="query.customerId != null">
                AND mr.customer_id = #{query.customerId}
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                AND c.customer_name LIKE CONCAT('%', #{query.customerName}, '%')
            </if>
            <if test="query.doctorId != null">
                AND mr.doctor_id = #{query.doctorId}
            </if>
            <if test="query.visitDateStart != null">
                AND mr.visit_date >= #{query.visitDateStart}
            </if>
            <if test="query.visitDateEnd != null">
                AND mr.visit_date &lt;= #{query.visitDateEnd}
            </if>
            <if test="query.createTimeStart != null">
                AND mr.create_time >= #{query.createTimeStart}
            </if>
            <if test="query.createTimeEnd != null">
                AND mr.create_time &lt;= #{query.createTimeEnd}
            </if>
            <if test="query.searchWord != null and query.searchWord != ''">
                AND (
                    c.customer_name LIKE CONCAT('%', #{query.searchWord}, '%')
                    OR mr.chief_complaint LIKE CONCAT('%', #{query.searchWord}, '%')
                    OR mr.diagnosis LIKE CONCAT('%', #{query.searchWord}, '%')
                    OR e.actual_name LIKE CONCAT('%', #{query.searchWord}, '%')
                )
            </if>
        </where>
        ORDER BY mr.visit_date DESC, mr.create_time DESC
    </select>

    <!-- 根据客户ID查询病历 -->
    <select id="selectByCustomerId" resultMap="MedicalRecordVOMap">
        SELECT
            mr.record_id,
            mr.customer_id,
            c.customer_name,
            mr.appointment_id,
            mr.visit_date,
            mr.chief_complaint,
            mr.diagnosis,
            mr.treatment,
            mr.next_visit_date,
            mr.doctor_id,
            e.actual_name AS doctor_name,
            mr.create_time
        FROM t_medical_record mr
        LEFT JOIN t_customer c ON mr.customer_id = c.customer_id
        LEFT JOIN t_employee e ON mr.doctor_id = e.employee_id
        WHERE mr.customer_id = #{customerId}
        AND mr.deleted_flag = 0
        ORDER BY mr.visit_date DESC
    </select>

    <!-- 根据预约ID查询病历 -->
    <select id="selectByAppointmentId" resultType="net.lab1024.sa.admin.module.business.hospital.customer.domain.entity.MedicalRecordEntity">
        SELECT *
        FROM t_medical_record
        WHERE appointment_id = #{appointmentId}
        AND deleted_flag = 0
        LIMIT 1
    </select>

    <!-- 批量更新删除状态 -->
    <update id="batchUpdateDeleted">
        UPDATE t_medical_record
        SET deleted_flag = #{deletedFlag},
            update_time = NOW()
        WHERE record_id IN
        <foreach collection="recordIdList" item="recordId" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </update>

    <!-- 获取客户最近的病历 -->
    <select id="getRecentRecords" resultMap="MedicalRecordVOMap">
        SELECT
            mr.record_id,
            mr.visit_date,
            mr.chief_complaint,
            mr.diagnosis,
            mr.treatment,
            mr.doctor_id,
            e.actual_name AS doctor_name,
            mr.create_time
        FROM t_medical_record mr
        LEFT JOIN t_employee e ON mr.doctor_id = e.employee_id
        WHERE mr.customer_id = #{customerId}
        AND mr.deleted_flag = 0
        ORDER BY mr.visit_date DESC
        LIMIT #{limit}
    </select>

    <!-- 根据医生ID查询病历 -->
    <select id="selectByDoctorId" resultMap="MedicalRecordVOMap">
        SELECT
            mr.record_id,
            mr.customer_id,
            c.customer_name,
            mr.visit_date,
            mr.chief_complaint,
            mr.diagnosis,
            mr.treatment,
            mr.create_time
        FROM t_medical_record mr
        LEFT JOIN t_customer c ON mr.customer_id = c.customer_id
        WHERE mr.doctor_id = #{doctorId}
        AND mr.deleted_flag = 0
        <if test="startDate != null">
            AND mr.visit_date >= #{startDate}
        </if>
        <if test="endDate != null">
            AND mr.visit_date &lt;= #{endDate}
        </if>
        ORDER BY mr.visit_date DESC
    </select>

    <!-- 获取病历统计数据 -->
    <select id="getMedicalRecordCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_medical_record
        WHERE deleted_flag = 0
    </select>

    <!-- 根据客户ID统计病历数量 -->
    <select id="getRecordCountByCustomerId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_medical_record
        WHERE customer_id = #{customerId}
        AND deleted_flag = 0
    </select>

    <!-- 根据医生ID统计病历数量 -->
    <select id="getRecordCountByDoctorId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_medical_record
        WHERE doctor_id = #{doctorId}
        AND deleted_flag = 0
        <if test="startDate != null">
            AND visit_date >= #{startDate}
        </if>
        <if test="endDate != null">
            AND visit_date &lt;= #{endDate}
        </if>
    </select>

    <!-- 获取需要复诊的病历 -->
    <select id="getFollowUpRecords" resultMap="MedicalRecordVOMap">
        SELECT
            mr.record_id,
            mr.customer_id,
            c.customer_name,
            c.customer_phone,
            mr.visit_date,
            mr.next_visit_date,
            mr.diagnosis,
            mr.doctor_id,
            e.actual_name AS doctor_name
        FROM t_medical_record mr
        LEFT JOIN t_customer c ON mr.customer_id = c.customer_id
        LEFT JOIN t_employee e ON mr.doctor_id = e.employee_id
        WHERE mr.next_visit_date = #{date}
        AND mr.deleted_flag = 0
        AND c.deleted_flag = 0
        ORDER BY mr.next_visit_date ASC
    </select>

    <!-- 根据线索ID查询病历 -->
    <select id="selectByLeadId" resultMap="MedicalRecordVOMap">
        SELECT
            mr.record_id,
            mr.lead_id,
            mr.customer_id,
            c.customer_name,
            mr.visit_date,
            mr.chief_complaint,
            mr.diagnosis,
            mr.treatment,
            mr.next_visit_date,
            mr.doctor_id,
            e.actual_name AS doctor_name,
            mr.create_time
        FROM t_medical_record mr
        LEFT JOIN t_customer c ON mr.customer_id = c.customer_id
        LEFT JOIN t_employee e ON mr.doctor_id = e.employee_id
        WHERE mr.lead_id = #{leadId}
        AND mr.deleted_flag = 0
        ORDER BY mr.visit_date DESC
    </select>

</mapper>

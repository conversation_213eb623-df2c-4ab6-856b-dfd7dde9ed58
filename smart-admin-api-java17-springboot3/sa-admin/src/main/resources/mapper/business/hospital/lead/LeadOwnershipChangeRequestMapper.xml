<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.hospital.lead.dao.LeadOwnershipChangeRequestDao">

    <!-- 分页查询线索归属变更申请 -->
    <select id="queryPage" resultType="net.lab1024.sa.admin.module.business.hospital.lead.domain.vo.LeadOwnershipChangeRequestVO">
        SELECT
            r.request_id,
            r.lead_id,
            r.customer_phone,
            r.customer_name,
            r.original_owner_id,
            r.original_owner_name,
            r.new_owner_id,
            r.new_owner_name,
            r.department_id,
            r.department_name,
            r.department_manager_id,
            r.department_manager_name,
            r.request_reason,
            r.request_status,
            CASE r.request_status
                WHEN 1 THEN '待审批'
                WHEN 2 THEN '已同意'
                WHEN 3 THEN '已拒绝'
                WHEN 4 THEN '已撤销'
                ELSE '未知'
            END AS request_status_desc,
            r.approve_time,
            r.approve_reason,
            r.approve_user_id,
            r.approve_user_name,
            r.last_follow_time,
            r.days_since_last_follow,
            r.create_time,
            r.update_time,
            r.create_user_id,
            r.update_user_id
        FROM t_lead_ownership_change_request r
        WHERE r.deleted_flag = 0
        <if test="query.customerPhone != null and query.customerPhone != ''">
            AND r.customer_phone LIKE CONCAT('%', #{query.customerPhone}, '%')
        </if>
        <if test="query.customerName != null and query.customerName != ''">
            AND r.customer_name LIKE CONCAT('%', #{query.customerName}, '%')
        </if>
        <if test="query.originalOwnerName != null and query.originalOwnerName != ''">
            AND r.original_owner_name LIKE CONCAT('%', #{query.originalOwnerName}, '%')
        </if>
        <if test="query.newOwnerName != null and query.newOwnerName != ''">
            AND r.new_owner_name LIKE CONCAT('%', #{query.newOwnerName}, '%')
        </if>
        <if test="query.requestStatus != null">
            AND r.request_status = #{query.requestStatus}
        </if>
        <if test="query.createTimeStart != null">
            AND r.create_time >= #{query.createTimeStart}
        </if>
        <if test="query.createTimeEnd != null">
            AND r.create_time &lt;= #{query.createTimeEnd}
        </if>
        <if test="query.approveTimeStart != null">
            AND r.approve_time >= #{query.approveTimeStart}
        </if>
        <if test="query.approveTimeEnd != null">
            AND r.approve_time &lt;= #{query.approveTimeEnd}
        </if>
        <if test="query.departmentId != null">
            AND r.department_id = #{query.departmentId}
        </if>
        <if test="query.onlyMyRequests != null and query.onlyMyRequests == true and query.requestUserId != null">
            AND r.create_user_id = #{query.requestUserId}
        </if>
        <if test="query.onlyPendingApproval != null and query.onlyPendingApproval == true and query.requestUserId != null">
            AND r.department_manager_id = #{query.requestUserId}
            AND r.request_status = 1
        </if>
        ORDER BY r.create_time DESC
    </select>

    <!-- 根据线索ID查询待审批的申请 -->
    <select id="selectPendingByLeadId" resultType="net.lab1024.sa.admin.module.business.hospital.lead.domain.entity.LeadOwnershipChangeRequestEntity">
        SELECT * FROM t_lead_ownership_change_request
        WHERE lead_id = #{leadId}
        AND request_status = 1
        AND deleted_flag = 0
        LIMIT 1
    </select>

    <!-- 根据申请人ID查询申请列表 -->
    <select id="selectByNewOwnerId" resultType="net.lab1024.sa.admin.module.business.hospital.lead.domain.entity.LeadOwnershipChangeRequestEntity">
        SELECT * FROM t_lead_ownership_change_request
        WHERE new_owner_id = #{newOwnerId}
        <if test="requestStatus != null">
            AND request_status = #{requestStatus}
        </if>
        AND deleted_flag = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据部门负责人ID查询待审批申请 -->
    <select id="selectPendingByManagerId" resultType="net.lab1024.sa.admin.module.business.hospital.lead.domain.entity.LeadOwnershipChangeRequestEntity">
        SELECT * FROM t_lead_ownership_change_request
        WHERE department_manager_id = #{departmentManagerId}
        AND request_status = 1
        AND deleted_flag = 0
        ORDER BY create_time ASC
    </select>

    <!-- 统计某个线索的申请次数 -->
    <select id="countByLeadId" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM t_lead_ownership_change_request
        WHERE lead_id = #{leadId}
        AND deleted_flag = 0
    </select>

    <!-- 统计某个用户的申请次数 -->
    <select id="countByNewOwnerIdAndStatus" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM t_lead_ownership_change_request
        WHERE new_owner_id = #{newOwnerId}
        <if test="requestStatus != null">
            AND request_status = #{requestStatus}
        </if>
        AND deleted_flag = 0
    </select>

</mapper>

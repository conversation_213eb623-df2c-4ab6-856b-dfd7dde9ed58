<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.hospital.care.dao.CareRecordDao">

    <!-- 分页查询关怀记录 -->
    <select id="queryPage" resultType="net.lab1024.sa.admin.module.business.hospital.care.domain.vo.CareRecordVO">
        SELECT
            cr.care_id,
            cr.care_plan_id,
            cp.plan_name as care_plan_name,
            cr.customer_id,
            cr.customer_name,
            cr.customer_phone,
            cr.record_id,
            cr.care_type,
            cr.care_method,
            cr.template_id,
            ct.template_name,
            cr.care_content,
            cr.planned_time,
            cr.actual_time,
            cr.care_status,
            cr.executor_id,
            cr.executor_name,
            cr.execution_result,
            cr.customer_feedback,
            cr.satisfaction_score,
            cr.need_follow_up,
            cr.next_follow_up_time,
            cr.remark,
            cr.create_time,
            cr.update_time,
            e.actual_name as create_user_name
        FROM t_care_record cr
        LEFT JOIN t_care_plan cp ON cr.care_plan_id = cp.plan_id
        LEFT JOIN t_care_template ct ON cr.template_id = ct.template_id
        LEFT JOIN t_employee e ON cr.create_user_id = e.employee_id
        <where>
            cr.deleted_flag = 0
            <if test="query.carePlanId != null">
                AND cr.care_plan_id = #{query.carePlanId}
            </if>
            <if test="query.customerId != null">
                AND cr.customer_id = #{query.customerId}
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                AND cr.customer_name LIKE CONCAT('%', #{query.customerName}, '%')
            </if>
            <if test="query.customerPhone != null and query.customerPhone != ''">
                AND cr.customer_phone LIKE CONCAT('%', #{query.customerPhone}, '%')
            </if>
            <if test="query.recordId != null">
                AND cr.record_id = #{query.recordId}
            </if>
            <if test="query.careType != null">
                AND cr.care_type = #{query.careType}
            </if>
            <if test="query.careMethod != null">
                AND cr.care_method = #{query.careMethod}
            </if>
            <if test="query.templateId != null">
                AND cr.template_id = #{query.templateId}
            </if>
            <if test="query.careStatus != null">
                AND cr.care_status = #{query.careStatus}
            </if>
            <if test="query.executorId != null">
                AND cr.executor_id = #{query.executorId}
            </if>
            <if test="query.executorName != null and query.executorName != ''">
                AND cr.executor_name LIKE CONCAT('%', #{query.executorName}, '%')
            </if>
            <if test="query.startPlannedTime != null">
                AND cr.planned_time >= #{query.startPlannedTime}
            </if>
            <if test="query.endPlannedTime != null">
                AND cr.planned_time &lt;= #{query.endPlannedTime}
            </if>
            <if test="query.startActualTime != null">
                AND cr.actual_time >= #{query.startActualTime}
            </if>
            <if test="query.endActualTime != null">
                AND cr.actual_time &lt;= #{query.endActualTime}
            </if>
            <if test="query.satisfactionScore != null">
                AND cr.satisfaction_score = #{query.satisfactionScore}
            </if>
            <if test="query.needFollowUp != null">
                AND cr.need_follow_up = #{query.needFollowUp}
            </if>
            <if test="query.keywords != null and query.keywords != ''">
                AND (cr.customer_name LIKE CONCAT('%', #{query.keywords}, '%')
                     OR cr.customer_phone LIKE CONCAT('%', #{query.keywords}, '%')
                     OR cr.care_content LIKE CONCAT('%', #{query.keywords}, '%'))
            </if>
        </where>
        ORDER BY cr.create_time DESC
    </select>

    <!-- 根据客户ID查询关怀记录 -->
    <select id="selectByCustomerId" resultType="net.lab1024.sa.admin.module.business.hospital.care.domain.vo.CareRecordVO">
        SELECT
            cr.*,
            cp.plan_name as care_plan_name,
            ct.template_name,
            e.actual_name as create_user_name
        FROM t_care_record cr
        LEFT JOIN t_care_plan cp ON cr.care_plan_id = cp.plan_id
        LEFT JOIN t_care_template ct ON cr.template_id = ct.template_id
        LEFT JOIN t_employee e ON cr.create_user_id = e.employee_id
        WHERE cr.customer_id = #{customerId} AND cr.deleted_flag = 0
        ORDER BY cr.create_time DESC
    </select>

    <!-- 根据病历ID查询关怀记录 -->
    <select id="selectByRecordId" resultType="net.lab1024.sa.admin.module.business.hospital.care.domain.entity.CareRecordEntity">
        SELECT * FROM t_care_record
        WHERE record_id = #{recordId} AND deleted_flag = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据关怀计划ID查询关怀记录 -->
    <select id="selectByCarePlanId" resultType="net.lab1024.sa.admin.module.business.hospital.care.domain.entity.CareRecordEntity">
        SELECT * FROM t_care_record
        WHERE care_plan_id = #{carePlanId} AND deleted_flag = 0
        ORDER BY create_time DESC
    </select>

    <!-- 更新关怀状态 -->
    <update id="updateCareStatus">
        UPDATE t_care_record
        SET care_status = #{careStatus},
            actual_time = #{actualTime},
            update_time = NOW()
        WHERE care_id = #{careId}
    </update>

    <!-- 更新执行结果 -->
    <update id="updateExecutionResult">
        UPDATE t_care_record
        SET execution_result = #{executionResult},
            customer_feedback = #{customerFeedback},
            satisfaction_score = #{satisfactionScore},
            update_time = NOW()
        WHERE care_id = #{careId}
    </update>

    <!-- 获取待执行的关怀记录 -->
    <select id="getPendingCareRecords" resultType="net.lab1024.sa.admin.module.business.hospital.care.domain.entity.CareRecordEntity">
        SELECT * FROM t_care_record
        WHERE care_status = 1
          AND planned_time &lt;= #{currentTime}
          AND deleted_flag = 0
        ORDER BY planned_time ASC
    </select>

    <!-- 获取需要跟进的关怀记录 -->
    <select id="getFollowUpCareRecords" resultType="net.lab1024.sa.admin.module.business.hospital.care.domain.entity.CareRecordEntity">
        SELECT * FROM t_care_record
        WHERE need_follow_up = 1
          AND next_follow_up_time &lt;= #{currentTime}
          AND care_status = 3
          AND deleted_flag = 0
        ORDER BY next_follow_up_time ASC
    </select>

    <!-- 获取关怀统计信息 -->
    <select id="getCareStatistics" resultType="net.lab1024.sa.admin.module.business.hospital.care.domain.vo.CareRecordVO">
        SELECT
            COUNT(*) as totalCount,
            SUM(CASE WHEN care_status = 3 THEN 1 ELSE 0 END) as completedCount,
            SUM(CASE WHEN care_status = 1 THEN 1 ELSE 0 END) as pendingCount,
            AVG(satisfaction_score) as avgSatisfactionScore
        FROM t_care_record
        WHERE customer_id = #{customerId} AND deleted_flag = 0
    </select>

    <!-- 根据出院病历自动创建关怀记录 -->
    <insert id="createCareFromDischarge">
        INSERT INTO t_care_record (
            customer_id, customer_name, customer_phone, record_id,
            care_type, care_method, care_content, planned_time,
            care_status, create_time, create_user_id, deleted_flag
        )
        SELECT 
            mr.customer_id,
            mr.customer_name,
            c.phone as customer_phone,
            mr.record_id,
            1 as care_type, -- 出院关怀
            1 as care_method, -- 电话
            '出院关怀：请注意休息，按时服药，如有不适请及时联系医院。' as care_content,
            DATE_ADD(mr.discharge_date, INTERVAL 1 DAY) as planned_time,
            1 as care_status, -- 待执行
            NOW() as create_time,
            1 as create_user_id, -- 系统自动创建
            0 as deleted_flag
        FROM t_medical_record mr
        LEFT JOIN t_customer c ON mr.customer_id = c.customer_id
        WHERE mr.record_id = #{recordId}
          AND mr.record_status = 3 -- 已出院
          AND NOT EXISTS (
              SELECT 1 FROM t_care_record cr 
              WHERE cr.record_id = mr.record_id 
                AND cr.care_type = 1 
                AND cr.deleted_flag = 0
          )
    </insert>

</mapper>

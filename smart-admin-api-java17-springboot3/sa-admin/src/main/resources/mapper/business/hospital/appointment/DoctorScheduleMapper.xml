<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.lab1024.sa.admin.module.business.hospital.appointment.dao.DoctorScheduleDao">

    <!-- 医生排班VO结果映射 -->
    <resultMap id="DoctorScheduleVOMap" type="net.lab1024.sa.admin.module.business.hospital.appointment.domain.vo.DoctorScheduleVO">
        <id column="schedule_id" property="scheduleId"/>
        <result column="doctor_id" property="doctorId"/>
        <result column="doctor_name" property="doctorName"/>
        <result column="doctor_department" property="doctorDepartment"/>
        <result column="schedule_date" property="scheduleDate"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="max_appointments" property="maxAppointments"/>
        <result column="current_appointments" property="currentAppointments"/>
        <result column="remaining_appointments" property="remainingAppointments"/>
        <result column="status" property="status"/>
        <result column="status_name" property="statusName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 分页查询医生排班 -->
    <select id="queryPage" resultMap="DoctorScheduleVOMap">
        SELECT
            ds.schedule_id,
            ds.doctor_id,
            e.actual_name AS doctor_name,
            d.department_name AS doctor_department,
            ds.schedule_date,
            ds.start_time,
            ds.end_time,
            ds.max_appointments,
            ds.current_appointments,
            (ds.max_appointments - ds.current_appointments) AS remaining_appointments,
            ds.schedule_status AS status,
            CASE ds.schedule_status
                WHEN 1 THEN '正常'
                WHEN 2 THEN '暂停'
                ELSE '未知'
            END AS status_name,
            ds.create_time,
            ds.update_time
        FROM t_doctor_schedule ds
        LEFT JOIN t_employee e ON ds.doctor_id = e.employee_id
        LEFT JOIN t_department d ON e.department_id = d.department_id
        <where>
            ds.deleted_flag = 0
            <if test="query.doctorId != null">
                AND ds.doctor_id = #{query.doctorId}
            </if>
            <if test="query.scheduleDateStart != null">
                AND ds.schedule_date >= #{query.scheduleDateStart}
            </if>
            <if test="query.scheduleDateEnd != null">
                AND ds.schedule_date &lt;= #{query.scheduleDateEnd}
            </if>
            <if test="query.status != null">
                AND ds.schedule_status = #{query.status}
            </if>
        </where>
        ORDER BY ds.schedule_date DESC, ds.start_time ASC
    </select>

    <!-- 检查排班时间冲突 -->
    <select id="checkTimeConflict" resultType="net.lab1024.sa.admin.module.business.hospital.appointment.domain.entity.DoctorScheduleEntity">
        SELECT *
        FROM t_doctor_schedule
        WHERE doctor_id = #{doctorId}
        AND schedule_date = #{scheduleDate}
        AND deleted_flag = 0
        AND (
            (start_time &lt;= #{startTime} AND end_time > #{startTime})
            OR (start_time &lt; #{endTime} AND end_time >= #{endTime})
            OR (start_time >= #{startTime} AND end_time &lt;= #{endTime})
        )
        <if test="excludeScheduleId != null">
            AND schedule_id != #{excludeScheduleId}
        </if>
        LIMIT 1
    </select>

    <!-- 更新当前预约数 -->
    <update id="updateCurrentAppointments">
        UPDATE t_doctor_schedule
        SET current_appointments = current_appointments + #{increment},
            update_time = NOW()
        WHERE schedule_id = #{scheduleId}
        AND deleted_flag = 0
    </update>

    <!-- 批量更新删除状态 -->
    <update id="batchUpdateDeleted">
        UPDATE t_doctor_schedule
        SET deleted_flag = #{deletedFlag},
            update_time = NOW()
        WHERE schedule_id IN
        <foreach collection="scheduleIdList" item="scheduleId" open="(" separator="," close=")">
            #{scheduleId}
        </foreach>
    </update>

    <!-- 获取医生可预约时间 -->
    <select id="getAvailableTime" resultType="net.lab1024.sa.admin.module.business.hospital.appointment.domain.vo.AvailableTimeVO">
        SELECT
            ds.start_time AS time,
            CONCAT(TIME_FORMAT(ds.start_time, '%H:%i'), '-', TIME_FORMAT(ds.end_time, '%H:%i')) AS time_text,
            CASE WHEN ds.current_appointments &lt; ds.max_appointments AND ds.schedule_status = 1 THEN 1 ELSE 0 END AS available,
            ds.current_appointments AS appointed_count,
            ds.max_appointments AS max_count,
            ds.schedule_id
        FROM t_doctor_schedule ds
        WHERE ds.doctor_id = #{doctorId}
        AND ds.schedule_date = #{scheduleDate}
        AND ds.deleted_flag = 0
        ORDER BY ds.start_time ASC
    </select>

    <!-- 根据医生和日期时间查询排班 -->
    <select id="selectByDoctorAndDateTime" resultType="net.lab1024.sa.admin.module.business.hospital.appointment.domain.entity.DoctorScheduleEntity">
        SELECT *
        FROM t_doctor_schedule
        WHERE doctor_id = #{doctorId}
        AND schedule_date = #{scheduleDate}
        AND start_time &lt;= #{appointmentTime}
        AND end_time > #{appointmentTime}
        AND deleted_flag = 0
        AND schedule_status = 1
        LIMIT 1
    </select>

    <!-- 批量插入排班 -->
    <insert id="batchInsert">
        INSERT INTO t_doctor_schedule (
            doctor_id, schedule_date, start_time, end_time,
            max_appointments, current_appointments, schedule_status,
            create_time, update_time, deleted_flag
        ) VALUES
        <foreach collection="scheduleList" item="schedule" separator=",">
            (
                #{schedule.doctorId}, #{schedule.scheduleDate}, #{schedule.startTime}, #{schedule.endTime},
                #{schedule.maxAppointments}, #{schedule.currentAppointments}, #{schedule.status},
                #{schedule.createTime}, #{schedule.updateTime}, #{schedule.deletedFlag}
            )
        </foreach>
    </insert>

    <!-- 删除指定条件的排班 -->
    <delete id="deleteByCondition">
        UPDATE t_doctor_schedule
        SET deleted_flag = 1,
            update_time = NOW()
        WHERE doctor_id = #{doctorId}
        AND schedule_date IN
        <foreach collection="scheduleDates" item="date" open="(" separator="," close=")">
            #{date}
        </foreach>
        AND start_time = #{startTime}
        AND end_time = #{endTime}
        AND deleted_flag = 0
    </delete>

    <!-- 获取医生排班统计 -->
    <select id="getScheduleCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM t_doctor_schedule
        WHERE doctor_id = #{doctorId}
        <if test="startDate != null">
            AND schedule_date >= #{startDate}
        </if>
        <if test="endDate != null">
            AND schedule_date &lt;= #{endDate}
        </if>
        AND deleted_flag = 0
    </select>

</mapper>

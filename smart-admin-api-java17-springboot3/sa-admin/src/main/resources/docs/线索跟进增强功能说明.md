# 线索跟进增强功能说明

## 功能概述

本次实现了销售人员添加跟进记录时的跟进结果选项和自动状态流转功能，包括：

1. **客户有预约意向** - 自动弹出预约时间选择界面，创建预约记录，线索状态自动流转到"已预约"
2. **需要再次跟进** - 自动弹出下次跟进时间选择界面，设置跟进提醒，线索状态保持"跟进中"
3. **客户无意向/无效线索** - 要求填写关闭原因，线索状态自动流转到"已关闭"
4. **仅记录跟进** - 仅记录跟进内容，不执行后续操作

## 技术实现

### 后端实现

#### 1. 枚举类更新
- `FollowResultEnum`: 跟进结果枚举，定义了4种跟进结果类型
- `LeadStatusEnum`: 线索状态枚举，包含6种状态（新线索、跟进中、已预约、已到院、已转化、已关闭）

#### 2. 表单类
- `LeadFollowEnhancedAddForm`: 增强的跟进记录添加表单，支持后续操作相关字段

#### 3. 服务类
- `LeadFollowResultProcessor`: 跟进结果处理器，处理不同跟进结果的后续操作
- `LeadStatusFlowService`: 线索状态流转服务，管理状态流转规则和自动流转
- `LeadFollowReminderService`: 跟进提醒服务，管理跟进提醒的创建和处理
- `LeadStatusHistoryService`: 状态历史服务，记录线索状态变更历史

#### 4. 控制器
- `LeadFollowController`: 新增 `addEnhanced` 接口，支持增强跟进记录添加

#### 5. 数据库表
- `t_lead_follow_reminder`: 线索跟进提醒表
- `t_lead_status_history`: 线索状态变更历史表
- 修改 `t_lead_follow` 表，添加 `follow_result_type` 字段
- 修改 `t_appointment` 表，添加 `lead_id` 字段

### 前端实现

#### 1. 常量更新
- 更新 `FOLLOW_RESULT_ENUM` 跟进结果枚举
- 更新 `LEAD_STATUS_ENUM` 线索状态枚举，添加"已到院"状态

#### 2. 组件
- `lead-follow-enhanced-modal.vue`: 增强的跟进记录添加组件
- 支持动态表单，根据跟进结果显示不同的后续操作字段

#### 3. API
- 新增 `addEnhanced` 接口调用

#### 4. 页面集成
- 修改线索列表页面，将跟进按钮改为使用增强跟进记录组件

## 功能流程

### 1. 客户有预约意向流程
```
用户选择"客户有预约意向" 
-> 选择是否立即创建预约
-> 如果立即创建：填写预约信息（项目、医生、日期、时间）
-> 提交后：创建跟进记录 + 创建预约记录 + 线索状态流转到"已预约"
-> 如果稍后安排：仅创建跟进记录，状态保持"跟进中"
```

### 2. 需要再次跟进流程
```
用户选择"需要再次跟进"
-> 设置下次跟进时间（必填）
-> 选择是否设置提醒
-> 如果设置提醒：选择提醒方式和提前时间
-> 提交后：创建跟进记录 + 创建跟进提醒 + 线索状态保持"跟进中"
```

### 3. 客户无意向/无效线索流程
```
用户选择"客户无意向/无效线索"
-> 填写关闭原因（必填）
-> 提交后：创建跟进记录 + 线索状态流转到"已关闭"
```

### 4. 仅记录跟进流程
```
用户选择"仅记录跟进"
-> 提交后：仅创建跟进记录，线索状态保持"跟进中"
```

## 状态流转规则

```
新线索 -> 跟进中、已关闭
跟进中 -> 已预约、已关闭
已预约 -> 已到院、已关闭
已到院 -> 已转化、已关闭
已转化 -> 无后续状态
已关闭 -> 无后续状态
```

## 数据一致性保证

1. **事务管理**: 所有涉及多表操作的方法都使用 `@Transactional` 注解
2. **状态验证**: 在状态流转前验证当前状态是否允许流转到目标状态
3. **数据校验**: 对表单数据进行严格校验，确保必填字段不为空
4. **异常处理**: 完善的异常处理机制，操作失败时自动回滚

## 扩展功能

### 1. 跟进提醒系统
- 支持系统通知、短信、邮件三种提醒方式
- 支持提前15分钟、30分钟、1小时、2小时提醒
- 提供定时任务清理过期提醒

### 2. 状态变更历史
- 记录所有状态变更的详细信息
- 支持查询线索的完整状态流转历史
- 提供状态停留时长统计

### 3. 数据统计视图
- `v_lead_follow_stats`: 线索跟进统计视图
- `v_pending_follow_reminders`: 待跟进提醒视图

### 4. 存储过程和触发器
- `sp_cleanup_expired_reminders`: 自动清理过期提醒的存储过程
- `tr_lead_status_change`: 线索状态变更时自动记录历史的触发器

## 使用说明

### 1. 数据库初始化
执行 `lead_follow_enhancement.sql` 脚本创建必要的表结构和初始数据。

### 2. 权限配置
确保用户具有以下权限：
- `hospital:lead:follow:add`: 添加跟进记录权限
- `hospital:appointment:add`: 创建预约权限（如果选择立即创建预约）

### 3. 前端使用
在线索列表页面点击"跟进"按钮，选择相应的跟进结果，填写必要信息后提交。

### 4. 测试页面
访问 `lead-follow-test.vue` 页面可以进行功能测试。

## 注意事项

1. **预约功能依赖**: 如果选择立即创建预约，需要确保预约管理模块正常工作
2. **提醒功能**: 跟进提醒功能需要配置定时任务来发送提醒
3. **权限控制**: 确保用户具有相应的操作权限
4. **数据备份**: 在生产环境部署前请备份相关数据表

## 后续优化建议

1. **批量操作**: 支持批量设置跟进结果
2. **模板功能**: 支持跟进内容模板，提高录入效率
3. **智能提醒**: 基于客户行为智能推荐下次跟进时间
4. **数据分析**: 提供跟进效果分析报表
5. **移动端支持**: 开发移动端跟进记录功能

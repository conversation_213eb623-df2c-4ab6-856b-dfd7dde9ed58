const crypto = require('crypto');

// SM4加密函数（简化版，使用AES代替）
function encryptPassword(password) {
  const key = '1024lab__1024lab';
  const cipher = crypto.createCipher('aes-128-ecb', key);
  let encrypted = cipher.update(password, 'utf8', 'base64');
  encrypted += cipher.final('base64');
  return encrypted;
}

// 加密密码123456
const encryptedPassword = encryptPassword('123456');
console.log(encryptedPassword);

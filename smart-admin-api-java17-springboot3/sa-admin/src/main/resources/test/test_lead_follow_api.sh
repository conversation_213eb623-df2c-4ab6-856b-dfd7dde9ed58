#!/bin/bash

# 线索跟进增强功能API测试脚本
# 测试所有四种跟进结果类型和修复验证

echo "=== 线索跟进增强功能全面测试 ==="
echo "测试时间: $(date)"
echo ""

# 基础URL
BASE_URL="http://localhost:1024"

# 首先获取登录token
echo "正在获取登录token..."
LOGIN_RESPONSE=$(curl -X POST "${BASE_URL}/api/login" \
  -H "Content-Type: application/json" \
  -d '{"loginName":"admin","password":"123456"}' \
  -s)

echo "登录响应: $LOGIN_RESPONSE"
echo ""

# 测试1：客户有预约意向（立即创建预约）
echo "测试1：客户有预约意向（立即创建预约）"
curl -X POST "${BASE_URL}/api/lead-follow/add-enhanced" \
  -H "Content-Type: application/json" \
  -d '{
    "leadId": 1,
    "followType": 1,
    "followContent": "客户有预约意向，想要预约检查",
    "followResult": 1,
    "followResultType": 1,
    "createAppointmentNow": true,
    "appointmentDate": "2025-07-25",
    "appointmentTime": "10:00:00",
    "appointmentRemark": "客户要求上午时间"
  }' \
  -w "\n状态码: %{http_code}\n" \
  -s
echo ""

# 测试2：需要再次跟进
echo "测试2：需要再次跟进"
curl -X POST "${BASE_URL}/api/lead-follow/add-enhanced" \
  -H "Content-Type: application/json" \
  -d '{
    "leadId": 2,
    "followType": 1,
    "followContent": "客户还在考虑中，需要再次跟进",
    "followResult": 2,
    "followResultType": 2,
    "setFollowReminder": true,
    "reminderType": 1,
    "reminderMinutes": 60
  }' \
  -w "\n状态码: %{http_code}\n" \
  -s
echo ""

# 测试3：客户无意向/无效线索
echo "测试3：客户无意向/无效线索"
curl -X POST "${BASE_URL}/api/lead-follow/add-enhanced" \
  -H "Content-Type: application/json" \
  -d '{
    "leadId": 3,
    "followType": 1,
    "followContent": "客户表示暂时不需要相关服务",
    "followResult": 3,
    "followResultType": 3,
    "closeReason": "客户无需求"
  }' \
  -w "\n状态码: %{http_code}\n" \
  -s
echo ""

# 测试4：仅记录跟进
echo "测试4：仅记录跟进"
curl -X POST "${BASE_URL}/api/lead-follow/add-enhanced" \
  -H "Content-Type: application/json" \
  -d '{
    "leadId": 4,
    "followType": 1,
    "followContent": "与客户进行了初步沟通，了解了基本需求",
    "followResult": 4,
    "followResultType": 4
  }' \
  -w "\n状态码: %{http_code}\n" \
  -s
echo ""

# 测试5：稍后安排预约
echo "测试5：客户有预约意向（稍后安排预约）"
curl -X POST "${BASE_URL}/api/lead-follow/add-enhanced" \
  -H "Content-Type: application/json" \
  -d '{
    "leadId": 5,
    "followType": 1,
    "followContent": "客户有预约意向，但需要稍后安排具体时间",
    "followResult": 1,
    "followResultType": 1,
    "createAppointmentNow": false
  }' \
  -w "\n状态码: %{http_code}\n" \
  -s
echo ""

echo "=== 测试完成 ==="
echo "请检查数据库中的跟进记录和预约记录是否正确保存"

#!/bin/bash

# 线索归属变更功能API测试脚本
# 测试后端API接口是否正常工作

BASE_URL="http://localhost:1024"
CONTENT_TYPE="Content-Type: application/json"

echo "=== 线索归属变更功能API测试 ==="
echo "测试时间: $(date)"
echo "服务地址: $BASE_URL"
echo ""

# 1. 获取验证码
echo "1. 获取验证码..."
CAPTCHA_RESPONSE=$(curl -s -X GET "$BASE_URL/login/getCaptcha")
echo "验证码响应: $CAPTCHA_RESPONSE"

# 提取验证码UUID和文本（这里需要手动输入验证码）
CAPTCHA_UUID=$(echo $CAPTCHA_RESPONSE | grep -o '"captchaUuid":"[^"]*"' | cut -d'"' -f4)
CAPTCHA_TEXT=$(echo $CAPTCHA_RESPONSE | grep -o '"captchaText":"[^"]*"' | cut -d'"' -f4)
echo "验证码UUID: $CAPTCHA_UUID"
echo "验证码文本: $CAPTCHA_TEXT"

echo ""

# 2. 登录获取token
echo "2. 登录获取token..."
LOGIN_DATA="{
  \"loginName\": \"admin\",
  \"password\": \"123456\",
  \"captchaCode\": \"$CAPTCHA_TEXT\",
  \"captchaUuid\": \"$CAPTCHA_UUID\",
  \"loginDevice\": 1,
  \"rememberMe\": false
}"

LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/login" \
  -H "$CONTENT_TYPE" \
  -d "$LOGIN_DATA")

echo "登录响应: $LOGIN_RESPONSE"

# 提取token
TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
if [ -z "$TOKEN" ]; then
  echo "登录失败，无法获取token"
  exit 1
fi

echo "登录成功，Token: $TOKEN"
echo ""

# 设置认证头
AUTH_HEADER="Authorization: Bearer $TOKEN"

# 3. 测试重复检测API
echo "3. 测试重复检测API..."
DUPLICATE_RESPONSE=$(curl -s -X POST "$BASE_URL/api/lead/ownership/checkDuplicate" \
  -H "$CONTENT_TYPE" \
  -H "$AUTH_HEADER" \
  -d '{"customerPhone": "13800138002"}')

echo "重复检测响应: $DUPLICATE_RESPONSE"
echo ""

# 4. 测试申请转移API
echo "4. 测试申请转移API..."
APPLY_DATA="{
  \"leadId\": 2,
  \"changeType\": 1,
  \"reason\": \"测试申请转移功能\"
}"

APPLY_RESPONSE=$(curl -s -X POST "$BASE_URL/api/lead/ownership/apply" \
  -H "$CONTENT_TYPE" \
  -H "$AUTH_HEADER" \
  -d "$APPLY_DATA")

echo "申请转移响应: $APPLY_RESPONSE"
echo ""

# 5. 测试查询我的申请API
echo "5. 测试查询我的申请API..."
MY_REQUESTS_RESPONSE=$(curl -s -X POST "$BASE_URL/api/lead/ownership/myRequests" \
  -H "$CONTENT_TYPE" \
  -H "$AUTH_HEADER" \
  -d '{"pageNum": 1, "pageSize": 10}')

echo "我的申请响应: $MY_REQUESTS_RESPONSE"
echo ""

# 6. 测试查询待审批申请API
echo "6. 测试查询待审批申请API..."
PENDING_RESPONSE=$(curl -s -X POST "$BASE_URL/api/lead/ownership/pendingApproval" \
  -H "$CONTENT_TYPE" \
  -H "$AUTH_HEADER" \
  -d '{"pageNum": 1, "pageSize": 10}')

echo "待审批申请响应: $PENDING_RESPONSE"
echo ""

echo "=== API测试完成 ==="
echo "请检查上述响应结果，确认API是否正常工作"

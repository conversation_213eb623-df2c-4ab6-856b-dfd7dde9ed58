-- 线索归属变更管理相关表结构
-- 作者: 1024创新实验室-主任：卓大
-- 日期: 2025-07-30
-- 版本: v1.0

-- 1. 创建线索归属变更申请表
CREATE TABLE IF NOT EXISTS `t_lead_ownership_change_request` (
  `request_id` bigint NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `lead_id` bigint NOT NULL COMMENT '线索ID',
  `customer_name` varchar(100) NOT NULL COMMENT '客户姓名',
  `customer_phone` varchar(20) NOT NULL COMMENT '客户电话',
  `original_assigned_employee_id` bigint NOT NULL COMMENT '原归属员工ID',
  `original_assigned_employee_name` varchar(100) NOT NULL COMMENT '原归属员工姓名',
  `target_assigned_employee_id` bigint NOT NULL COMMENT '目标归属员工ID',
  `target_assigned_employee_name` varchar(100) NOT NULL COMMENT '目标归属员工姓名',
  `change_reason` text NOT NULL COMMENT '变更原因',
  `request_status` tinyint NOT NULL DEFAULT 1 COMMENT '申请状态：1-待审批，2-已同意，3-已拒绝，4-已撤销',
  `department_manager_id` bigint NOT NULL COMMENT '部门负责人ID（审批人）',
  `department_manager_name` varchar(100) NOT NULL COMMENT '部门负责人姓名',
  `approve_time` datetime DEFAULT NULL COMMENT '审批时间',
  `approve_user_id` bigint DEFAULT NULL COMMENT '审批人ID',
  `approve_user_name` varchar(100) DEFAULT NULL COMMENT '审批人姓名',
  `approve_remark` text DEFAULT NULL COMMENT '审批意见',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_user_name` varchar(100) NOT NULL COMMENT '创建人姓名',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user_id` bigint NOT NULL COMMENT '更新人ID',
  `update_user_name` varchar(100) NOT NULL COMMENT '更新人姓名',
  PRIMARY KEY (`request_id`),
  KEY `idx_lead_id` (`lead_id`),
  KEY `idx_customer_phone` (`customer_phone`),
  KEY `idx_request_status` (`request_status`),
  KEY `idx_create_user_id` (`create_user_id`),
  KEY `idx_department_manager_id` (`department_manager_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='线索归属变更申请表';

-- 2. 创建线索归属变更历史表
CREATE TABLE IF NOT EXISTS `t_lead_ownership_change_history` (
  `history_id` bigint NOT NULL AUTO_INCREMENT COMMENT '历史记录ID',
  `lead_id` bigint NOT NULL COMMENT '线索ID',
  `request_id` bigint DEFAULT NULL COMMENT '申请ID（如果是通过申请变更的）',
  `change_type` tinyint NOT NULL COMMENT '变更类型：1-申请提交，2-审批通过，3-审批拒绝，4-申请撤销，5-系统变更',
  `original_assigned_employee_id` bigint DEFAULT NULL COMMENT '原归属员工ID',
  `original_assigned_employee_name` varchar(100) DEFAULT NULL COMMENT '原归属员工姓名',
  `new_assigned_employee_id` bigint DEFAULT NULL COMMENT '新归属员工ID',
  `new_assigned_employee_name` varchar(100) DEFAULT NULL COMMENT '新归属员工姓名',
  `change_reason` text DEFAULT NULL COMMENT '变更原因',
  `change_remark` text DEFAULT NULL COMMENT '变更备注',
  `change_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '变更时间',
  `operator_id` bigint NOT NULL COMMENT '操作人ID',
  `operator_name` varchar(100) NOT NULL COMMENT '操作人姓名',
  PRIMARY KEY (`history_id`),
  KEY `idx_lead_id` (`lead_id`),
  KEY `idx_request_id` (`request_id`),
  KEY `idx_change_type` (`change_type`),
  KEY `idx_change_time` (`change_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='线索归属变更历史表';

-- 3. 添加线索归属变更相关的菜单权限
INSERT IGNORE INTO `t_menu` (`menu_id`, `menu_name`, `menu_type`, `parent_id`, `sort`, `path`, `component`, `perms_type`, `api_perms`, `web_perms`, `icon`, `context_menu_id`, `frame_flag`, `frame_url`, `cache_flag`, `visible_flag`, `disabled_flag`, `deleted_flag`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES
-- 线索归属变更管理主菜单
(2501, '线索归属变更', 1, 250, 5, '/hospital/lead/ownership-change', NULL, 1, NULL, NULL, 'SwapOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
-- 我的申请
(2502, '我的申请', 2, 2501, 1, '/hospital/lead/ownership-change/my-requests', '/business/hospital/lead/ownership-change-request.vue', 1, 'hospital:lead:ownership:apply', 'hospital:lead:ownership:apply', NULL, NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
-- 审批管理
(2503, '审批管理', 2, 2501, 2, '/hospital/lead/ownership-change/approval', '/business/hospital/lead/ownership-change-approval.vue', 1, 'hospital:lead:ownership:approve', 'hospital:lead:ownership:approve', NULL, NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
-- 变更历史
(2504, '变更历史', 3, 2501, 3, NULL, NULL, 1, 'hospital:lead:ownership:history', 'hospital:lead:ownership:history', NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
-- 查询权限
(2505, '查询权限', 3, 2501, 4, NULL, NULL, 1, 'hospital:lead:ownership:query', 'hospital:lead:ownership:query', NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 4. 为管理员角色分配权限
INSERT IGNORE INTO `t_role_menu` (`role_id`, `menu_id`) VALUES
(1, 2501),
(1, 2502),
(1, 2503),
(1, 2504),
(1, 2505);

-- 5. 添加线索归属变更状态枚举到字典
INSERT IGNORE INTO `t_dict_key` (`dict_key_id`, `key_name`, `key_desc`, `deleted_flag`, `create_user_id`, `create_user_name`, `create_time`, `update_user_id`, `update_user_name`, `update_time`) VALUES
(1025, 'LEAD_OWNERSHIP_CHANGE_STATUS', '线索归属变更状态', 0, 1, 'admin', NOW(), 1, 'admin', NOW());

INSERT IGNORE INTO `t_dict_value` (`dict_value_id`, `dict_key_id`, `value_name`, `value_desc`, `sort`, `remark`, `deleted_flag`, `create_user_id`, `create_user_name`, `create_time`, `update_user_id`, `update_user_name`, `update_time`) VALUES
(10251, 1025, '1', '待审批', 1, '申请已提交，等待审批', 0, 1, 'admin', NOW(), 1, 'admin', NOW()),
(10252, 1025, '2', '已同意', 2, '申请已通过审批', 0, 1, 'admin', NOW(), 1, 'admin', NOW()),
(10253, 1025, '3', '已拒绝', 3, '申请被拒绝', 0, 1, 'admin', NOW(), 1, 'admin', NOW()),
(10254, 1025, '4', '已撤销', 4, '申请已被撤销', 0, 1, 'admin', NOW(), 1, 'admin', NOW());

-- 6. 添加线索归属变更类型枚举到字典
INSERT IGNORE INTO `t_dict_key` (`dict_key_id`, `key_name`, `key_desc`, `deleted_flag`, `create_user_id`, `create_user_name`, `create_time`, `update_user_id`, `update_user_name`, `update_time`) VALUES
(1026, 'LEAD_OWNERSHIP_CHANGE_TYPE', '线索归属变更类型', 0, 1, 'admin', NOW(), 1, 'admin', NOW());

INSERT IGNORE INTO `t_dict_value` (`dict_value_id`, `dict_key_id`, `value_name`, `value_desc`, `sort`, `remark`, `deleted_flag`, `create_user_id`, `create_user_name`, `create_time`, `update_user_id`, `update_user_name`, `update_time`) VALUES
(10261, 1026, '1', '申请提交', 1, '用户提交变更申请', 0, 1, 'admin', NOW(), 1, 'admin', NOW()),
(10262, 1026, '2', '审批通过', 2, '管理员审批通过', 0, 1, 'admin', NOW(), 1, 'admin', NOW()),
(10263, 1026, '3', '审批拒绝', 3, '管理员审批拒绝', 0, 1, 'admin', NOW(), 1, 'admin', NOW()),
(10264, 1026, '4', '申请撤销', 4, '用户撤销申请', 0, 1, 'admin', NOW(), 1, 'admin', NOW()),
(10265, 1026, '5', '系统变更', 5, '系统自动变更', 0, 1, 'admin', NOW(), 1, 'admin', NOW());

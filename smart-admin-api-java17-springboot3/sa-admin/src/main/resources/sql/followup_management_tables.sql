-- 回访管理系统数据库表结构
-- Author: 1024创新实验室-主任：卓大
-- Date: 2025-07-29 10:00:00

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 1. 扩展现有客户表，增加治疗状态字段
-- ----------------------------

-- 为客户表添加治疗状态字段
ALTER TABLE `t_customer` 
ADD COLUMN `treatment_status` tinyint DEFAULT NULL COMMENT '治疗状态：1-用药中，2-住院中，3-疗程中，4-纠纷，5-已结束治疗，6-失联' AFTER `customer_status`,
ADD COLUMN `occupation` varchar(100) DEFAULT NULL COMMENT '职业' AFTER `address`;

-- 为治疗状态字段添加索引
ALTER TABLE `t_customer` ADD KEY `idx_treatment_status` (`treatment_status`);

-- ----------------------------
-- 2. 回访记录表
-- ----------------------------

-- 回访记录表
DROP TABLE IF EXISTS `t_follow_up_record`;
CREATE TABLE `t_follow_up_record` (
  `follow_up_id` bigint NOT NULL AUTO_INCREMENT COMMENT '回访记录ID',
  `customer_id` bigint NOT NULL COMMENT '客户ID',
  `customer_name` varchar(50) NOT NULL COMMENT '客户姓名',
  `customer_phone` varchar(20) NOT NULL COMMENT '客户电话',
  `follow_up_type` tinyint NOT NULL COMMENT '回访类型：1-用药跟进，2-住院跟进，3-疗程跟进，4-次卡核销跟进，5-复诊提醒跟进，6-康复指导跟进',
  `follow_up_method` tinyint NOT NULL COMMENT '回访方式：1-电话，2-微信，3-短信，4-邮件，5-上门',
  `follow_up_content` text NOT NULL COMMENT '回访内容',
  `follow_up_result` text COMMENT '回访结果',
  `follow_up_status` tinyint NOT NULL DEFAULT '1' COMMENT '回访状态：1-待回访，2-已回访，3-无法联系，4-已取消',
  `follow_up_user_id` bigint NOT NULL COMMENT '回访人员ID',
  `follow_up_user_name` varchar(50) NOT NULL COMMENT '回访人员姓名',
  `scheduled_time` datetime NOT NULL COMMENT '计划回访时间',
  `actual_time` datetime DEFAULT NULL COMMENT '实际回访时间',
  `next_follow_up_time` datetime DEFAULT NULL COMMENT '下次回访时间',
  `related_record_id` bigint DEFAULT NULL COMMENT '关联记录ID（病历ID、处方ID等）',
  `related_record_type` varchar(50) DEFAULT NULL COMMENT '关联记录类型（medical_record、prescription等）',
  `priority_level` tinyint DEFAULT '2' COMMENT '优先级：1-高，2-中，3-低',
  `satisfaction_score` tinyint DEFAULT NULL COMMENT '满意度评分：1-5分',
  `remark` text COMMENT '备注',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`follow_up_id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_follow_up_user` (`follow_up_user_id`),
  KEY `idx_follow_up_type` (`follow_up_type`),
  KEY `idx_follow_up_status` (`follow_up_status`),
  KEY `idx_scheduled_time` (`scheduled_time`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='回访记录表';

-- ----------------------------
-- 3. 回访计划表
-- ----------------------------

-- 回访计划表
DROP TABLE IF EXISTS `t_follow_up_plan`;
CREATE TABLE `t_follow_up_plan` (
  `plan_id` bigint NOT NULL AUTO_INCREMENT COMMENT '回访计划ID',
  `customer_id` bigint NOT NULL COMMENT '客户ID',
  `customer_name` varchar(50) NOT NULL COMMENT '客户姓名',
  `plan_type` tinyint NOT NULL COMMENT '计划类型：1-定期回访，2-特殊事件回访',
  `plan_name` varchar(100) NOT NULL COMMENT '计划名称',
  `follow_up_type` tinyint NOT NULL COMMENT '回访类型：1-用药跟进，2-住院跟进，3-疗程跟进，4-次卡核销跟进，5-复诊提醒跟进，6-康复指导跟进',
  `follow_up_frequency` varchar(50) NOT NULL COMMENT '回访频率（如：每周、每月、每3天等）',
  `frequency_days` int NOT NULL COMMENT '回访间隔天数',
  `start_date` date NOT NULL COMMENT '开始日期',
  `end_date` date DEFAULT NULL COMMENT '结束日期',
  `plan_status` tinyint NOT NULL DEFAULT '1' COMMENT '计划状态：1-启用，2-暂停，3-已结束',
  `assigned_user_id` bigint NOT NULL COMMENT '分配人员ID',
  `assigned_user_name` varchar(50) NOT NULL COMMENT '分配人员姓名',
  `total_count` int DEFAULT '0' COMMENT '计划总次数',
  `completed_count` int DEFAULT '0' COMMENT '已完成次数',
  `last_follow_up_time` datetime DEFAULT NULL COMMENT '最后回访时间',
  `next_follow_up_time` datetime DEFAULT NULL COMMENT '下次回访时间',
  `auto_create_flag` tinyint DEFAULT '1' COMMENT '自动创建回访记录：1-是，0-否',
  `remark` text COMMENT '备注',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`plan_id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_assigned_user` (`assigned_user_id`),
  KEY `idx_plan_status` (`plan_status`),
  KEY `idx_next_follow_up_time` (`next_follow_up_time`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='回访计划表';

-- ----------------------------
-- 4. 治疗状态变更历史表
-- ----------------------------

-- 治疗状态变更历史表
DROP TABLE IF EXISTS `t_treatment_status_history`;
CREATE TABLE `t_treatment_status_history` (
  `history_id` bigint NOT NULL AUTO_INCREMENT COMMENT '历史记录ID',
  `customer_id` bigint NOT NULL COMMENT '客户ID',
  `customer_name` varchar(50) NOT NULL COMMENT '客户姓名',
  `from_status` tinyint DEFAULT NULL COMMENT '原状态：1-用药中，2-住院中，3-疗程中，4-纠纷，5-已结束治疗，6-失联',
  `to_status` tinyint NOT NULL COMMENT '新状态：1-用药中，2-住院中，3-疗程中，4-纠纷，5-已结束治疗，6-失联',
  `change_reason` varchar(200) DEFAULT NULL COMMENT '变更原因',
  `change_description` text COMMENT '变更说明',
  `change_user_id` bigint NOT NULL COMMENT '变更人员ID',
  `change_user_name` varchar(50) NOT NULL COMMENT '变更人员姓名',
  `change_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '变更时间',
  `remark` text COMMENT '备注',
  PRIMARY KEY (`history_id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_change_time` (`change_time`),
  KEY `idx_change_user` (`change_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='治疗状态变更历史表';

-- ----------------------------
-- 5. 回访提醒表
-- ----------------------------

-- 回访提醒表
DROP TABLE IF EXISTS `t_follow_up_reminder`;
CREATE TABLE `t_follow_up_reminder` (
  `reminder_id` bigint NOT NULL AUTO_INCREMENT COMMENT '提醒ID',
  `follow_up_id` bigint DEFAULT NULL COMMENT '回访记录ID',
  `plan_id` bigint DEFAULT NULL COMMENT '回访计划ID',
  `customer_id` bigint NOT NULL COMMENT '客户ID',
  `reminder_time` datetime NOT NULL COMMENT '提醒时间',
  `reminder_type` tinyint NOT NULL DEFAULT '1' COMMENT '提醒方式：1-系统通知，2-短信，3-邮件',
  `reminder_status` tinyint NOT NULL DEFAULT '1' COMMENT '提醒状态：1-待提醒，2-已发送，3-已取消，4-已完成',
  `reminder_content` text COMMENT '提醒内容',
  `processed_time` datetime DEFAULT NULL COMMENT '处理时间',
  `processed_result` varchar(500) DEFAULT NULL COMMENT '处理结果',
  `is_sent` tinyint DEFAULT '0' COMMENT '是否已发送：1-是，0-否',
  `sent_time` datetime DEFAULT NULL COMMENT '实际发送时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`reminder_id`),
  KEY `idx_follow_up_id` (`follow_up_id`),
  KEY `idx_plan_id` (`plan_id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_reminder_time` (`reminder_time`),
  KEY `idx_reminder_status` (`reminder_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='回访提醒表';

-- ----------------------------
-- 6. 数据字典初始化
-- ----------------------------

-- 插入治疗状态字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`)
VALUES ('治疗状态', 'TREATMENT_STATUS', '患者治疗状态字典，用于回访管理模块', 0, NOW(), NOW());

-- 获取字典ID并插入字典数据
SET @treatment_status_dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'TREATMENT_STATUS');

-- 插入治疗状态字典数据
INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@treatment_status_dict_id, '1', '用药中', '患者正在用药治疗', 100, 0, NOW(), NOW()),
(@treatment_status_dict_id, '2', '住院中', '患者正在住院治疗', 90, 0, NOW(), NOW()),
(@treatment_status_dict_id, '3', '疗程中', '患者正在进行疗程治疗', 80, 0, NOW(), NOW()),
(@treatment_status_dict_id, '4', '纠纷', '患者存在纠纷问题', 70, 0, NOW(), NOW()),
(@treatment_status_dict_id, '5', '已结束治疗', '患者已完成治疗', 60, 0, NOW(), NOW()),
(@treatment_status_dict_id, '6', '失联', '无法联系到患者', 50, 0, NOW(), NOW());

-- 插入回访类型字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`)
VALUES ('回访类型', 'FOLLOW_UP_TYPE', '回访类型字典，用于回访管理模块', 0, NOW(), NOW());

-- 获取字典ID并插入字典数据
SET @follow_up_type_dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'FOLLOW_UP_TYPE');

-- 插入回访类型字典数据
INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@follow_up_type_dict_id, '1', '用药跟进', '跟进患者用药情况', 100, 0, NOW(), NOW()),
(@follow_up_type_dict_id, '2', '住院跟进', '跟进患者住院情况', 90, 0, NOW(), NOW()),
(@follow_up_type_dict_id, '3', '疗程跟进', '跟进患者疗程进展', 80, 0, NOW(), NOW()),
(@follow_up_type_dict_id, '4', '次卡核销跟进', '跟进患者次卡使用情况', 70, 0, NOW(), NOW()),
(@follow_up_type_dict_id, '5', '复诊提醒跟进', '提醒患者复诊', 60, 0, NOW(), NOW()),
(@follow_up_type_dict_id, '6', '康复指导跟进', '提供康复指导服务', 50, 0, NOW(), NOW());

-- 插入回访方式字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`)
VALUES ('回访方式', 'FOLLOW_UP_METHOD', '回访方式字典，用于回访管理模块', 0, NOW(), NOW());

-- 获取字典ID并插入字典数据
SET @follow_up_method_dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'FOLLOW_UP_METHOD');

-- 插入回访方式字典数据
INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@follow_up_method_dict_id, '1', '电话', '电话回访', 100, 0, NOW(), NOW()),
(@follow_up_method_dict_id, '2', '微信', '微信回访', 90, 0, NOW(), NOW()),
(@follow_up_method_dict_id, '3', '短信', '短信回访', 80, 0, NOW(), NOW()),
(@follow_up_method_dict_id, '4', '邮件', '邮件回访', 70, 0, NOW(), NOW()),
(@follow_up_method_dict_id, '5', '上门', '上门回访', 60, 0, NOW(), NOW());

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

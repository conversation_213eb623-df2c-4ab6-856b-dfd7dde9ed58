-- 诊断流程相关表结构设计
-- 作者: 1024创新实验室
-- 日期: 2025-07-29

-- 1. 诊断记录表
DROP TABLE IF EXISTS `t_diagnosis`;
CREATE TABLE `t_diagnosis` (
  `diagnosis_id` bigint NOT NULL AUTO_INCREMENT COMMENT '诊断ID',
  `patient_id` bigint NOT NULL COMMENT '患者ID',
  `patient_no` varchar(50) NOT NULL COMMENT '患者编号',
  `patient_name` varchar(100) NOT NULL COMMENT '患者姓名',
  `diagnosis_result` varchar(500) NOT NULL COMMENT '诊断结果',
  `diagnosis_type` tinyint NOT NULL DEFAULT '1' COMMENT '诊断类型：1-初诊，2-复诊，3-会诊',
  `diagnosis_description` text COMMENT '诊断说明',
  `diagnosis_doctor_id` bigint NOT NULL COMMENT '诊断医生ID',
  `diagnosis_doctor_name` varchar(100) NOT NULL COMMENT '诊断医生姓名',
  `diagnosis_time` datetime NOT NULL COMMENT '诊断时间',
  `diagnosis_status` tinyint NOT NULL DEFAULT '1' COMMENT '诊断状态：1-已诊断，2-已开单，3-已收费，4-已完成',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_user_name` varchar(100) NOT NULL COMMENT '创建人姓名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint COMMENT '更新人ID',
  `update_user_name` varchar(100) COMMENT '更新人姓名',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识：0-未删除，1-已删除',
  PRIMARY KEY (`diagnosis_id`),
  KEY `idx_patient_id` (`patient_id`),
  KEY `idx_patient_no` (`patient_no`),
  KEY `idx_diagnosis_doctor_id` (`diagnosis_doctor_id`),
  KEY `idx_diagnosis_time` (`diagnosis_time`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='诊断记录表';

-- 2. 开单记录表
DROP TABLE IF EXISTS `t_prescription`;
CREATE TABLE `t_prescription` (
  `prescription_id` bigint NOT NULL AUTO_INCREMENT COMMENT '开单ID',
  `diagnosis_id` bigint NOT NULL COMMENT '诊断ID',
  `patient_id` bigint NOT NULL COMMENT '患者ID',
  `patient_no` varchar(50) NOT NULL COMMENT '患者编号',
  `patient_name` varchar(100) NOT NULL COMMENT '患者姓名',
  `prescription_no` varchar(50) NOT NULL COMMENT '开单编号',
  `total_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总金额',
  `prescription_note` text COMMENT '开单说明',
  `prescription_status` tinyint NOT NULL DEFAULT '1' COMMENT '开单状态：1-已开单，2-已收费，3-已完成',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_user_name` varchar(100) NOT NULL COMMENT '创建人姓名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint COMMENT '更新人ID',
  `update_user_name` varchar(100) COMMENT '更新人姓名',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识：0-未删除，1-已删除',
  PRIMARY KEY (`prescription_id`),
  UNIQUE KEY `uk_prescription_no` (`prescription_no`),
  KEY `idx_diagnosis_id` (`diagnosis_id`),
  KEY `idx_patient_id` (`patient_id`),
  KEY `idx_patient_no` (`patient_no`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='开单记录表';

-- 3. 开单项目明细表
DROP TABLE IF EXISTS `t_prescription_item`;
CREATE TABLE `t_prescription_item` (
  `item_id` bigint NOT NULL AUTO_INCREMENT COMMENT '项目ID',
  `prescription_id` bigint NOT NULL COMMENT '开单ID',
  `item_name` varchar(200) NOT NULL COMMENT '项目名称',
  `item_type` varchar(50) NOT NULL COMMENT '项目类型：treatment-治疗，medicine-药品，examination-检查，material-材料',
  `unit_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '单价',
  `quantity` int NOT NULL DEFAULT '1' COMMENT '数量',
  `subtotal` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '小计',
  `item_description` varchar(500) COMMENT '项目说明',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`item_id`),
  KEY `idx_prescription_id` (`prescription_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='开单项目明细表';

-- 4. 收费记录表
DROP TABLE IF EXISTS `t_charge`;
CREATE TABLE `t_charge` (
  `charge_id` bigint NOT NULL AUTO_INCREMENT COMMENT '收费ID',
  `prescription_id` bigint NOT NULL COMMENT '开单ID',
  `diagnosis_id` bigint NOT NULL COMMENT '诊断ID',
  `patient_id` bigint NOT NULL COMMENT '患者ID',
  `patient_no` varchar(50) NOT NULL COMMENT '患者编号',
  `patient_name` varchar(100) NOT NULL COMMENT '患者姓名',
  `charge_no` varchar(50) NOT NULL COMMENT '收费编号',
  `total_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '应收金额',
  `actual_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实收金额',
  `discount_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '优惠金额',
  `payment_account` varchar(50) NOT NULL COMMENT '收款账户：cash-现金，alipay-支付宝，wechat-微信支付，bank-银行卡',
  `charge_status` tinyint NOT NULL DEFAULT '1' COMMENT '收费状态：1-已收费，2-部分收费，3-未收费，4-已退费',
  `charge_time` datetime NOT NULL COMMENT '收费时间',
  `charge_note` text COMMENT '收费备注',
  `cashier_id` bigint NOT NULL COMMENT '收费员ID',
  `cashier_name` varchar(100) NOT NULL COMMENT '收费员姓名',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_user_name` varchar(100) NOT NULL COMMENT '创建人姓名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint COMMENT '更新人ID',
  `update_user_name` varchar(100) COMMENT '更新人姓名',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标识：0-未删除，1-已删除',
  PRIMARY KEY (`charge_id`),
  UNIQUE KEY `uk_charge_no` (`charge_no`),
  KEY `idx_prescription_id` (`prescription_id`),
  KEY `idx_diagnosis_id` (`diagnosis_id`),
  KEY `idx_patient_id` (`patient_id`),
  KEY `idx_patient_no` (`patient_no`),
  KEY `idx_charge_time` (`charge_time`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='收费记录表';

-- 5. 更新患者表，添加诊断流程状态字段
ALTER TABLE `t_visit_patient` 
ADD COLUMN `diagnosis_status` tinyint NOT NULL DEFAULT '1' COMMENT '诊断流程状态：1-待诊断，2-已诊断，3-已开单，4-已收费，5-已分配助理，6-治疗中，7-已完成' AFTER `visit_status`;

-- 添加索引
ALTER TABLE `t_visit_patient` ADD INDEX `idx_diagnosis_status` (`diagnosis_status`);

-- 初始化数据：更新现有患者的诊断状态
UPDATE `t_visit_patient` SET `diagnosis_status` = 1 WHERE `diagnosis_status` IS NULL OR `diagnosis_status` = 0;

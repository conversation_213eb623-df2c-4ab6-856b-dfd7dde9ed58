-- 医院营销与客户管理系统权限配置
-- Author: 1024创新实验室-主任：卓大
-- Date: 2024-01-01 10:00:00

-- 1. 菜单权限配置
INSERT IGNORE INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
-- 医院营销管理主菜单
(5001, '医院营销管理', 1, 0, 50, '/hospital', NULL, 1, NULL, NULL, 'MedicineBoxOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 仪表盘菜单
(5005, '仪表盘', 2, 5001, 5, '/hospital/dashboard', '/views/business/hospital/dashboard/dashboard-overview.vue', 2, NULL, 'hospital:dashboard:query', 'DashboardOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 线索管理菜单
(5010, '线索管理', 1, 5001, 10, '/hospital/lead', NULL, 1, NULL, NULL, 'UserAddOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5011, '线索列表', 2, 5010, 10, '/hospital/lead/list', '/views/business/hospital/lead/lead-list.vue', 2, NULL, 'hospital:lead:query', 'UnorderedListOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5012, '线索跟进', 2, 5010, 20, '/hospital/lead/follow', '/views/business/hospital/lead/lead-follow.vue', 2, NULL, 'hospital:lead:follow:query', 'CommentOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 预约管理菜单
(5020, '预约管理', 1, 5001, 20, '/hospital/appointment', NULL, 1, NULL, NULL, 'CalendarOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5021, '预约列表', 2, 5020, 10, '/hospital/appointment/list', '/views/business/hospital/appointment/appointment-list.vue', 2, NULL, 'hospital:appointment:query', 'UnorderedListOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5022, '医生排班', 2, 5020, 20, '/hospital/appointment/schedule', '/views/business/hospital/appointment/doctor-schedule.vue', 2, NULL, 'hospital:schedule:query', 'ScheduleOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5023, '项目管理', 2, 5020, 30, '/hospital/appointment/project', '/views/business/hospital/appointment/project-list.vue', 2, NULL, 'hospital:project:query', 'ProjectOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 客户管理菜单
(5030, '客户管理', 1, 5001, 30, '/hospital/customer', NULL, 1, NULL, NULL, 'TeamOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5031, '客户列表', 2, 5030, 10, '/hospital/customer/list', '/views/business/hospital/customer/customer-list.vue', 2, NULL, 'hospital:customer:query', 'UnorderedListOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5032, '病历管理', 2, 5030, 20, '/hospital/customer/medical', '/views/business/hospital/customer/medical-record-list.vue', 2, NULL, 'hospital:record:query', 'FileTextOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 数据统计菜单
(5040, '数据统计', 1, 5001, 40, '/hospital/statistics', NULL, 1, NULL, NULL, 'BarChartOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5041, '线索转化统计', 2, 5040, 10, '/hospital/statistics/lead-conversion', '/views/business/hospital/statistics/lead-conversion-stats.vue', 2, NULL, 'hospital:statistics:leadConversion', 'LineChartOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5042, '预约到诊统计', 2, 5040, 20, '/hospital/statistics/appointment-arrival', '/views/business/hospital/statistics/appointment-arrival-stats.vue', 2, NULL, 'hospital:statistics:appointmentArrival', 'PieChartOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5043, '客户满意度统计', 2, 5040, 30, '/hospital/statistics/customer-satisfaction', '/views/business/hospital/statistics/customer-satisfaction-stats.vue', 2, NULL, 'hospital:statistics:customerSatisfaction', 'SmileOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5044, '医生工作量统计', 2, 5040, 40, '/hospital/statistics/doctor-workload', '/views/business/hospital/statistics/doctor-workload-stats.vue', 2, NULL, 'hospital:statistics:doctorWorkload', 'UserOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 财务与收费管理菜单
(5050, '财务管理', 1, 5001, 50, '/hospital/finance', NULL, 1, NULL, NULL, 'AccountBookOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5051, '收费管理', 2, 5050, 10, '/hospital/finance/billing', '/views/business/hospital/finance/billing-list.vue', 2, NULL, 'hospital:finance:billing', 'MoneyCollectOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5052, '收费统计', 2, 5050, 20, '/hospital/finance/billing-stats', '/views/business/hospital/finance/billing-stats.vue', 2, NULL, 'hospital:finance:billingStats', 'FundOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5053, '财务报表', 2, 5050, 30, '/hospital/finance/reports', '/views/business/hospital/finance/finance-reports.vue', 2, NULL, 'hospital:finance:reports', 'FileTextOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5054, '收入分析', 2, 5050, 40, '/hospital/finance/revenue-analysis', '/views/business/hospital/finance/revenue-analysis.vue', 2, NULL, 'hospital:finance:revenueAnalysis', 'RiseOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 出院关怀与SOP菜单
(5060, '关怀管理', 1, 5001, 60, '/hospital/care', NULL, 1, NULL, NULL, 'HeartOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5061, '出院关怀', 2, 5060, 10, '/hospital/care/discharge-care', '/views/business/hospital/care/discharge-care-list.vue', 2, NULL, 'hospital:care:discharge', 'MedicineBoxOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5062, '关怀模板', 2, 5060, 20, '/hospital/care/templates', '/views/business/hospital/care/care-template-list.vue', 2, NULL, 'hospital:care:template', 'FileProtectOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5063, 'SOP流程', 2, 5060, 30, '/hospital/care/sop', '/views/business/hospital/care/sop-process-list.vue', 2, NULL, 'hospital:care:sop', 'ApartmentOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5064, '满意度回访', 2, 5060, 40, '/hospital/care/satisfaction', '/views/business/hospital/care/satisfaction-survey.vue', 2, NULL, 'hospital:care:satisfaction', 'SmileOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5065, '复购提醒', 2, 5060, 50, '/hospital/care/repurchase', '/views/business/hospital/care/repurchase-reminder.vue', 2, NULL, 'hospital:care:repurchase', 'BellOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 知识库管理菜单
(5070, '知识库', 1, 5001, 70, '/hospital/knowledge', NULL, 1, NULL, NULL, 'BookOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5071, '文档管理', 2, 5070, 10, '/hospital/knowledge/documents', '/views/business/hospital/knowledge/document-list.vue', 2, NULL, 'hospital:knowledge:document', 'FileTextOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5072, '分类管理', 2, 5070, 20, '/hospital/knowledge/categories', '/views/business/hospital/knowledge/category-list.vue', 2, NULL, 'hospital:knowledge:category', 'FolderOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5073, '标签管理', 2, 5070, 30, '/hospital/knowledge/tags', '/views/business/hospital/knowledge/tag-list.vue', 2, NULL, 'hospital:knowledge:tag', 'TagsOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5074, '知识搜索', 2, 5070, 40, '/hospital/knowledge/search', '/views/business/hospital/knowledge/knowledge-search.vue', 2, NULL, 'hospital:knowledge:search', 'SearchOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5075, '知识共享', 2, 5070, 50, '/hospital/knowledge/sharing', '/views/business/hospital/knowledge/knowledge-sharing.vue', 2, NULL, 'hospital:knowledge:sharing', 'ShareAltOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 营销活动管理菜单
(5080, '营销活动', 1, 5001, 80, '/hospital/marketing', NULL, 1, NULL, NULL, 'ThunderboltOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5081, '活动管理', 2, 5080, 10, '/hospital/marketing/campaigns', '/views/business/hospital/marketing/campaign-list.vue', 2, NULL, 'hospital:marketing:campaign', 'CalendarOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5082, '活动推广', 2, 5080, 20, '/hospital/marketing/promotion', '/views/business/hospital/marketing/promotion-list.vue', 2, NULL, 'hospital:marketing:promotion', 'SoundOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5083, '效果跟踪', 2, 5080, 30, '/hospital/marketing/tracking', '/views/business/hospital/marketing/effect-tracking.vue', 2, NULL, 'hospital:marketing:tracking', 'LineChartOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5084, 'ROI分析', 2, 5080, 40, '/hospital/marketing/roi-analysis', '/views/business/hospital/marketing/roi-analysis.vue', 2, NULL, 'hospital:marketing:roiAnalysis', 'FundOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5085, '活动报表', 2, 5080, 50, '/hospital/marketing/reports', '/views/business/hospital/marketing/campaign-reports.vue', 2, NULL, 'hospital:marketing:reports', 'BarChartOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW());

-- 2. API权限配置
INSERT IGNORE INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
-- 仪表盘API权限
(5091, '仪表盘查询', 3, 5005, 10, NULL, NULL, 2, '/dashboard/overview', 'hospital:dashboard:query', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5092, '仪表盘数据统计', 3, 5005, 20, NULL, NULL, 2, '/dashboard/statistics', 'hospital:dashboard:statistics', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),

-- 线索管理API权限
(5101, '线索查询', 3, 5011, 10, NULL, NULL, 2, '/lead/query', 'hospital:lead:query', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5102, '线索新增', 3, 5011, 20, NULL, NULL, 2, '/lead/add', 'hospital:lead:add', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5103, '线索编辑', 3, 5011, 30, NULL, NULL, 2, '/lead/update', 'hospital:lead:update', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5104, '线索删除', 3, 5011, 40, NULL, NULL, 2, '/lead/delete', 'hospital:lead:delete', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5105, '线索分配', 3, 5011, 50, NULL, NULL, 2, '/lead/assign', 'hospital:lead:assign', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5106, '线索转客户', 3, 5011, 60, NULL, NULL, 2, '/lead/convert', 'hospital:lead:convert', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5107, '线索导入', 3, 5011, 70, NULL, NULL, 2, '/lead/import', 'hospital:lead:import', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5108, '线索导出', 3, 5011, 80, NULL, NULL, 2, '/lead/export', 'hospital:lead:export', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),

-- 线索跟进API权限
(5111, '跟进记录查询', 3, 5012, 10, NULL, NULL, 2, '/leadFollow/query', 'hospital:lead:follow:query', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5112, '跟进记录新增', 3, 5012, 20, NULL, NULL, 2, '/leadFollow/add', 'hospital:lead:follow:add', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5113, '跟进记录编辑', 3, 5012, 30, NULL, NULL, 2, '/leadFollow/update', 'hospital:lead:follow:update', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5114, '跟进记录删除', 3, 5012, 40, NULL, NULL, 2, '/leadFollow/delete', 'hospital:lead:follow:delete', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),

-- 预约管理API权限
(5121, '预约查询', 3, 5021, 10, NULL, NULL, 2, '/appointment/query', 'hospital:appointment:query', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5122, '预约新增', 3, 5021, 20, NULL, NULL, 2, '/appointment/add', 'hospital:appointment:add', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5123, '预约编辑', 3, 5021, 30, NULL, NULL, 2, '/appointment/update', 'hospital:appointment:update', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5124, '预约删除', 3, 5021, 40, NULL, NULL, 2, '/appointment/delete', 'hospital:appointment:delete', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5125, '预约状态变更', 3, 5021, 50, NULL, NULL, 2, '/appointment/updateStatus', 'hospital:appointment:status', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),

-- 医生排班API权限
(5131, '排班查询', 3, 5022, 10, NULL, NULL, 2, '/doctorSchedule/query', 'hospital:schedule:query', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5132, '排班新增', 3, 5022, 20, NULL, NULL, 2, '/doctorSchedule/add', 'hospital:schedule:add', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5133, '排班编辑', 3, 5022, 30, NULL, NULL, 2, '/doctorSchedule/update', 'hospital:schedule:update', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5134, '排班删除', 3, 5022, 40, NULL, NULL, 2, '/doctorSchedule/delete', 'hospital:schedule:delete', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),

-- 项目管理API权限
(5141, '项目查询', 3, 5023, 10, NULL, NULL, 2, '/project/query', 'hospital:project:query', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5142, '项目新增', 3, 5023, 20, NULL, NULL, 2, '/project/add', 'hospital:project:add', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5143, '项目编辑', 3, 5023, 30, NULL, NULL, 2, '/project/update', 'hospital:project:update', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5144, '项目删除', 3, 5023, 40, NULL, NULL, 2, '/project/delete', 'hospital:project:delete', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),

-- 客户管理API权限
(5151, '客户查询', 3, 5031, 10, NULL, NULL, 2, '/customer/query', 'hospital:customer:query', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5152, '客户新增', 3, 5031, 20, NULL, NULL, 2, '/customer/add', 'hospital:customer:add', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5153, '客户编辑', 3, 5031, 30, NULL, NULL, 2, '/customer/update', 'hospital:customer:update', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5154, '客户删除', 3, 5031, 40, NULL, NULL, 2, '/customer/delete', 'hospital:customer:delete', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5155, '客户分配', 3, 5031, 50, NULL, NULL, 2, '/customer/assign', 'hospital:customer:assign', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5156, '客户转换', 3, 5031, 60, NULL, NULL, 2, '/customer/leadToCustomer', 'hospital:customer:convert', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),

-- 病历管理API权限
(5161, '病历查询', 3, 5032, 10, NULL, NULL, 2, '/medicalRecord/query', 'hospital:record:query', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5162, '病历新增', 3, 5032, 20, NULL, NULL, 2, '/medicalRecord/add', 'hospital:record:add', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5163, '病历编辑', 3, 5032, 30, NULL, NULL, 2, '/medicalRecord/update', 'hospital:record:update', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5164, '病历删除', 3, 5032, 40, NULL, NULL, 2, '/medicalRecord/delete', 'hospital:record:delete', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),

-- 数据统计API权限
(5171, '线索转化统计查询', 3, 5041, 10, NULL, NULL, 2, '/statistics/leadConversion', 'hospital:statistics:leadConversion', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5172, '预约到诊统计查询', 3, 5042, 10, NULL, NULL, 2, '/statistics/appointmentArrival', 'hospital:statistics:appointmentArrival', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5173, '客户满意度统计查询', 3, 5043, 10, NULL, NULL, 2, '/statistics/customerSatisfaction', 'hospital:statistics:customerSatisfaction', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5174, '医生工作量统计查询', 3, 5044, 10, NULL, NULL, 2, '/statistics/doctorWorkload', 'hospital:statistics:doctorWorkload', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5175, '统计数据导出', 3, 5040, 50, NULL, NULL, 2, '/statistics/export', 'hospital:statistics:export', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),

-- 财务管理API权限
(5181, '收费管理查询', 3, 5051, 10, NULL, NULL, 2, '/finance/billing/query', 'hospital:finance:billing', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5182, '收费管理新增', 3, 5051, 20, NULL, NULL, 2, '/finance/billing/add', 'hospital:finance:billing:add', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5183, '收费管理编辑', 3, 5051, 30, NULL, NULL, 2, '/finance/billing/update', 'hospital:finance:billing:update', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5184, '收费管理删除', 3, 5051, 40, NULL, NULL, 2, '/finance/billing/delete', 'hospital:finance:billing:delete', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5185, '收费统计查询', 3, 5052, 10, NULL, NULL, 2, '/finance/billingStats', 'hospital:finance:billingStats', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5186, '财务报表查询', 3, 5053, 10, NULL, NULL, 2, '/finance/reports', 'hospital:finance:reports', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5187, '收入分析查询', 3, 5054, 10, NULL, NULL, 2, '/finance/revenueAnalysis', 'hospital:finance:revenueAnalysis', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),

-- 关怀管理API权限
(5191, '出院关怀查询', 3, 5061, 10, NULL, NULL, 2, '/care/discharge/query', 'hospital:care:discharge', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5192, '出院关怀新增', 3, 5061, 20, NULL, NULL, 2, '/care/discharge/add', 'hospital:care:discharge:add', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5193, '出院关怀编辑', 3, 5061, 30, NULL, NULL, 2, '/care/discharge/update', 'hospital:care:discharge:update', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5194, '关怀模板查询', 3, 5062, 10, NULL, NULL, 2, '/care/template/query', 'hospital:care:template', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5195, '关怀模板管理', 3, 5062, 20, NULL, NULL, 2, '/care/template/manage', 'hospital:care:template:manage', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5196, 'SOP流程查询', 3, 5063, 10, NULL, NULL, 2, '/care/sop/query', 'hospital:care:sop', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5197, 'SOP流程管理', 3, 5063, 20, NULL, NULL, 2, '/care/sop/manage', 'hospital:care:sop:manage', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5198, '满意度回访查询', 3, 5064, 10, NULL, NULL, 2, '/care/satisfaction/query', 'hospital:care:satisfaction', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5199, '满意度回访管理', 3, 5064, 20, NULL, NULL, 2, '/care/satisfaction/manage', 'hospital:care:satisfaction:manage', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5200, '复购提醒查询', 3, 5065, 10, NULL, NULL, 2, '/care/repurchase/query', 'hospital:care:repurchase', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5201, '复购提醒管理', 3, 5065, 20, NULL, NULL, 2, '/care/repurchase/manage', 'hospital:care:repurchase:manage', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),

-- 知识库管理API权限
(5211, '文档查询', 3, 5071, 10, NULL, NULL, 2, '/knowledge/document/query', 'hospital:knowledge:document', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5212, '文档管理', 3, 5071, 20, NULL, NULL, 2, '/knowledge/document/manage', 'hospital:knowledge:document:manage', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5213, '分类查询', 3, 5072, 10, NULL, NULL, 2, '/knowledge/category/query', 'hospital:knowledge:category', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5214, '分类管理', 3, 5072, 20, NULL, NULL, 2, '/knowledge/category/manage', 'hospital:knowledge:category:manage', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5215, '标签查询', 3, 5073, 10, NULL, NULL, 2, '/knowledge/tag/query', 'hospital:knowledge:tag', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5216, '标签管理', 3, 5073, 20, NULL, NULL, 2, '/knowledge/tag/manage', 'hospital:knowledge:tag:manage', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5217, '知识搜索', 3, 5074, 10, NULL, NULL, 2, '/knowledge/search', 'hospital:knowledge:search', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5218, '知识共享查询', 3, 5075, 10, NULL, NULL, 2, '/knowledge/sharing/query', 'hospital:knowledge:sharing', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5219, '知识共享管理', 3, 5075, 20, NULL, NULL, 2, '/knowledge/sharing/manage', 'hospital:knowledge:sharing:manage', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),

-- 营销活动管理API权限
(5221, '活动查询', 3, 5081, 10, NULL, NULL, 2, '/marketing/campaign/query', 'hospital:marketing:campaign', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5222, '活动管理', 3, 5081, 20, NULL, NULL, 2, '/marketing/campaign/manage', 'hospital:marketing:campaign:manage', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5223, '活动推广查询', 3, 5082, 10, NULL, NULL, 2, '/marketing/promotion/query', 'hospital:marketing:promotion', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5224, '活动推广管理', 3, 5082, 20, NULL, NULL, 2, '/marketing/promotion/manage', 'hospital:marketing:promotion:manage', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5225, '效果跟踪查询', 3, 5083, 10, NULL, NULL, 2, '/marketing/tracking/query', 'hospital:marketing:tracking', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5226, 'ROI分析查询', 3, 5084, 10, NULL, NULL, 2, '/marketing/roiAnalysis', 'hospital:marketing:roiAnalysis', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW()),
(5227, '活动报表查询', 3, 5085, 10, NULL, NULL, 2, '/marketing/reports', 'hospital:marketing:reports', NULL, NULL, 0, NULL, 0, 0, 0, 0, 1, NOW(), 1, NOW());

-- 3. 角色权限配置
-- 医院营销管理员角色（如果不存在则插入）
INSERT IGNORE INTO t_role (role_id, role_name, role_code, remark) VALUES
(101, '医院营销管理员', 'HOSPITAL_ADMIN', '医院营销系统管理员，拥有所有权限'),
(102, '营销主管', 'MARKETING_MANAGER', '营销主管，负责线索分配和团队管理'),
(103, '营销专员', 'MARKETING_SPECIALIST', '营销专员，负责线索跟进和客户服务'),
(104, '客服人员', 'CUSTOMER_SERVICE', '客服人员，负责客户服务和预约管理'),
(105, '医生', 'DOCTOR', '医生，负责病历管理和医疗服务');

-- 角色菜单权限关联（医院营销管理员 - 全部权限）
INSERT IGNORE INTO t_role_menu (role_id, menu_id)
SELECT 101, menu_id FROM t_menu WHERE menu_id BETWEEN 5001 AND 5200;

-- 角色菜单权限关联（营销主管 - 线索、预约、客户管理权限）
INSERT IGNORE INTO t_role_menu (role_id, menu_id) VALUES
-- 主菜单
(102, 5001),
-- 仪表盘
(102, 5005), (102, 5091), (102, 5092),
-- 线索管理
(102, 5010), (102, 5011), (102, 5012),
(102, 5101), (102, 5102), (102, 5103), (102, 5104), (102, 5105), (102, 5106), (102, 5107), (102, 5108),
(102, 5111), (102, 5112), (102, 5113), (102, 5114),
-- 预约管理
(102, 5020), (102, 5021), (102, 5022), (102, 5023),
(102, 5121), (102, 5122), (102, 5123), (102, 5124), (102, 5125),
(102, 5131), (102, 5132), (102, 5133), (102, 5134),
(102, 5141), (102, 5142), (102, 5143), (102, 5144),
-- 客户管理
(102, 5030), (102, 5031),
(102, 5151), (102, 5152), (102, 5153), (102, 5154), (102, 5155),
-- 数据统计
(102, 5040), (102, 5041), (102, 5042), (102, 5043), (102, 5044),
(102, 5171), (102, 5172), (102, 5173), (102, 5174), (102, 5175),
-- 财务管理
(102, 5050), (102, 5051), (102, 5052), (102, 5053), (102, 5054),
(102, 5181), (102, 5182), (102, 5183), (102, 5184), (102, 5185), (102, 5186), (102, 5187),
-- 关怀管理
(102, 5060), (102, 5061), (102, 5062), (102, 5063), (102, 5064), (102, 5065),
(102, 5191), (102, 5192), (102, 5193), (102, 5194), (102, 5195), (102, 5196), (102, 5197), (102, 5198), (102, 5199), (102, 5200), (102, 5201),
-- 知识库管理
(102, 5070), (102, 5071), (102, 5072), (102, 5073), (102, 5074), (102, 5075),
(102, 5211), (102, 5212), (102, 5213), (102, 5214), (102, 5215), (102, 5216), (102, 5217), (102, 5218), (102, 5219),
-- 营销活动管理
(102, 5080), (102, 5081), (102, 5082), (102, 5083), (102, 5084), (102, 5085),
(102, 5221), (102, 5222), (102, 5223), (102, 5224), (102, 5225), (102, 5226), (102, 5227);

-- 角色菜单权限关联（营销专员 - 线索跟进、预约创建权限）
INSERT IGNORE INTO t_role_menu (role_id, menu_id) VALUES
-- 主菜单
(103, 5001),
-- 仪表盘（只读）
(103, 5005), (103, 5091),
-- 线索管理（只读和跟进）
(103, 5010), (103, 5011), (103, 5012),
(103, 5101), (103, 5111), (103, 5112),
-- 预约管理（创建和查看）
(103, 5020), (103, 5021),
(103, 5121), (103, 5122),
-- 客户管理（只读）
(103, 5030), (103, 5031),
(103, 5151),
-- 知识库（只读）
(103, 5070), (103, 5071), (103, 5074), (103, 5075),
(103, 5211), (103, 5217), (103, 5218),
-- 营销活动（查看）
(103, 5080), (103, 5081), (103, 5083),
(103, 5221), (103, 5225);

-- 角色菜单权限关联（客服人员 - 客户服务、预约管理权限）
INSERT IGNORE INTO t_role_menu (role_id, menu_id) VALUES
-- 主菜单
(104, 5001),
-- 仪表盘（只读）
(104, 5005), (104, 5091),
-- 预约管理
(104, 5020), (104, 5021),
(104, 5121), (104, 5122), (104, 5123), (104, 5125),
-- 客户管理
(104, 5030), (104, 5031),
(104, 5151), (104, 5152), (104, 5153),
-- 关怀管理（客服主要职责）
(104, 5060), (104, 5061), (104, 5062), (104, 5064), (104, 5065),
(104, 5191), (104, 5192), (104, 5193), (104, 5194), (104, 5198), (104, 5199), (104, 5200), (104, 5201),
-- 知识库（只读）
(104, 5070), (104, 5071), (104, 5074),
(104, 5211), (104, 5217);

-- 角色菜单权限关联（医生 - 病历管理权限）
INSERT IGNORE INTO t_role_menu (role_id, menu_id) VALUES
-- 主菜单
(105, 5001),
-- 仪表盘（只读）
(105, 5005), (105, 5091),
-- 客户管理（只读）
(105, 5030), (105, 5031), (105, 5032),
(105, 5151),
-- 病历管理
(105, 5161), (105, 5162), (105, 5163),
-- 医生工作量统计（只读）
(105, 5044), (105, 5174),
-- 知识库（只读）
(105, 5070), (105, 5071), (105, 5074),
(105, 5211), (105, 5217);

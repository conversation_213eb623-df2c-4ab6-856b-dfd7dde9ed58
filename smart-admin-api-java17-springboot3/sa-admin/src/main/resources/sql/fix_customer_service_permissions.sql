-- 修复客服人员跟进记录权限
-- 为客服人员角色添加线索跟进相关权限

-- 1. 查看客服赵的角色信息
SELECT 
    e.employee_id, 
    e.actual_name, 
    e.login_name, 
    r.role_id, 
    r.role_name 
FROM t_employee e 
LEFT JOIN t_role_employee re ON e.employee_id = re.employee_id 
LEFT JOIN t_role r ON re.role_id = r.role_id 
WHERE e.login_name = 'kefu_zhao';

-- 2. 查看现有的跟进权限配置
SELECT 
    m.menu_id,
    m.menu_name,
    m.web_perms,
    rm.role_id
FROM t_menu m
LEFT JOIN t_role_menu rm ON m.menu_id = rm.menu_id
WHERE m.web_perms LIKE '%hospital:lead:follow%'
ORDER BY m.menu_id, rm.role_id;

-- 3. 为客服人员角色添加线索跟进权限
-- 首先找到客服相关的角色ID
INSERT IGNORE INTO t_role_menu (role_id, menu_id)
SELECT DISTINCT re.role_id, m.menu_id
FROM t_employee e
JOIN t_role_employee re ON e.employee_id = re.employee_id
JOIN t_role r ON re.role_id = r.role_id
CROSS JOIN t_menu m
WHERE e.login_name = 'kefu_zhao'
  AND m.web_perms IN (
    'hospital:lead:follow:query',
    'hospital:lead:follow:add',
    'hospital:lead:follow:update'
  );

-- 4. 为所有客服角色添加跟进权限（如果有多个客服角色）
INSERT IGNORE INTO t_role_menu (role_id, menu_id)
SELECT r.role_id, m.menu_id
FROM t_role r
CROSS JOIN t_menu m
WHERE (r.role_name LIKE '%客服%' OR r.role_code LIKE '%CUSTOMER_SERVICE%')
  AND m.web_perms IN (
    'hospital:lead:follow:query',
    'hospital:lead:follow:add',
    'hospital:lead:follow:update'
  );

-- 5. 验证权限配置
SELECT 
    r.role_id,
    r.role_name,
    m.menu_id,
    m.menu_name,
    m.web_perms
FROM t_role r
JOIN t_role_menu rm ON r.role_id = rm.role_id
JOIN t_menu m ON rm.menu_id = m.menu_id
WHERE m.web_perms LIKE '%hospital:lead:follow%'
  AND (r.role_name LIKE '%客服%' OR r.role_code LIKE '%CUSTOMER_SERVICE%')
ORDER BY r.role_id, m.menu_id;

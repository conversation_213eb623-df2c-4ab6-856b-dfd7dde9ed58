-- 到诊管理模块权限配置
-- Author: 1024创新实验室-主任：卓大
-- Date: 2025-07-29 10:00:00
-- Description: 到诊管理模块的菜单和权限配置

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 1. 删除现有的客户管理相关菜单 (如果存在)
-- ----------------------------

-- 删除客户管理相关菜单 (保留数据，只是标记删除)
UPDATE t_menu SET deleted_flag = 1 WHERE menu_name LIKE '%客户%' AND deleted_flag = 0;

-- ----------------------------
-- 2. 创建到诊管理菜单结构
-- ----------------------------

-- 主菜单：到诊管理
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(7001, '到诊管理', 1, 0, 2, '/visit', NULL, NULL, NULL, NULL, 'UserOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 二级菜单：患者列表
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(7002, '患者列表', 2, 7001, 1, '/visit/patient/list', '/business/hospital/visit/patient-list.vue', 1, 'visit:patient:query', 'visit:patient:query', 'UnorderedListOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW());

-- 二级菜单：诊断管理
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(7003, '诊断管理', 2, 7001, 2, '/visit/diagnosis/list', '/business/hospital/visit/diagnosis-list.vue', 1, 'visit:diagnosis:query', 'visit:diagnosis:query', 'FileTextOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW());

-- 二级菜单：开单管理
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(7004, '开单管理', 2, 7001, 3, '/visit/prescription/list', '/business/hospital/visit/prescription-list.vue', 1, 'visit:prescription:query', 'visit:prescription:query', 'FileAddOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW());

-- 二级菜单：收费管理
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(7005, '收费管理', 2, 7001, 4, '/visit/charge/list', '/business/hospital/visit/charge-list.vue', 1, 'visit:charge:query', 'visit:charge:query', 'DollarOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW());

-- 二级菜单：跟进管理
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(7006, '跟进管理', 2, 7001, 5, '/visit/follow/list', '/business/hospital/visit/follow-list.vue', 1, 'visit:follow:query', 'visit:follow:query', 'PhoneOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW());

-- ----------------------------
-- 3. 患者管理功能权限点
-- ----------------------------

-- 患者查询
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(7101, '患者查询', 3, 7002, 1, NULL, NULL, 1, 'visit:patient:query', 'visit:patient:query', NULL, 7002, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 患者新增
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(7102, '患者新增', 3, 7002, 2, NULL, NULL, 1, 'visit:patient:add', 'visit:patient:add', NULL, 7002, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 患者编辑
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(7103, '患者编辑', 3, 7002, 3, NULL, NULL, 1, 'visit:patient:update', 'visit:patient:update', NULL, 7002, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 患者删除
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(7104, '患者删除', 3, 7002, 4, NULL, NULL, 1, 'visit:patient:delete', 'visit:patient:delete', NULL, 7002, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 患者分配
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(7105, '患者分配', 3, 7002, 5, NULL, NULL, 1, 'visit:patient:assign', 'visit:patient:assign', NULL, 7002, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 状态更新
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(7106, '状态更新', 3, 7002, 6, NULL, NULL, 1, 'visit:patient:status', 'visit:patient:status', NULL, 7002, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- ----------------------------
-- 4. 诊断管理功能权限点
-- ----------------------------

-- 诊断查询
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(6111, '诊断查询', 3, 6003, 1, NULL, NULL, 1, 'visit:diagnosis:query', 'visit:diagnosis:query', NULL, 6003, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 诊断新增
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(6112, '诊断新增', 3, 6003, 2, NULL, NULL, 1, 'visit:diagnosis:add', 'visit:diagnosis:add', NULL, 6003, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 诊断编辑
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(6113, '诊断编辑', 3, 6003, 3, NULL, NULL, 1, 'visit:diagnosis:update', 'visit:diagnosis:update', NULL, 6003, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 诊断删除
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(6114, '诊断删除', 3, 6003, 4, NULL, NULL, 1, 'visit:diagnosis:delete', 'visit:diagnosis:delete', NULL, 6003, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 诊断审核
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(6115, '诊断审核', 3, 6003, 5, NULL, NULL, 1, 'visit:diagnosis:audit', 'visit:diagnosis:audit', NULL, 6003, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- ----------------------------
-- 5. 开单管理功能权限点
-- ----------------------------

-- 开单查询
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(6121, '开单查询', 3, 6004, 1, NULL, NULL, 1, 'visit:prescription:query', 'visit:prescription:query', NULL, 6004, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 开单新增
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(6122, '开单新增', 3, 6004, 2, NULL, NULL, 1, 'visit:prescription:add', 'visit:prescription:add', NULL, 6004, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 开单编辑
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(6123, '开单编辑', 3, 6004, 3, NULL, NULL, 1, 'visit:prescription:update', 'visit:prescription:update', NULL, 6004, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 开单删除
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(6124, '开单删除', 3, 6004, 4, NULL, NULL, 1, 'visit:prescription:delete', 'visit:prescription:delete', NULL, 6004, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 开单审核
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(6125, '开单审核', 3, 6004, 5, NULL, NULL, 1, 'visit:prescription:audit', 'visit:prescription:audit', NULL, 6004, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 开单执行
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(6126, '开单执行', 3, 6004, 6, NULL, NULL, 1, 'visit:prescription:execute', 'visit:prescription:execute', NULL, 6004, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- ----------------------------
-- 6. 收费管理功能权限点
-- ----------------------------

-- 收费查询
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(6131, '收费查询', 3, 6005, 1, NULL, NULL, 1, 'visit:charge:query', 'visit:charge:query', NULL, 6005, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 收费新增
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(6132, '收费新增', 3, 6005, 2, NULL, NULL, 1, 'visit:charge:add', 'visit:charge:add', NULL, 6005, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 收费编辑
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(6133, '收费编辑', 3, 6005, 3, NULL, NULL, 1, 'visit:charge:update', 'visit:charge:update', NULL, 6005, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 收费删除
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(6134, '收费删除', 3, 6005, 4, NULL, NULL, 1, 'visit:charge:delete', 'visit:charge:delete', NULL, 6005, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 退费处理
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(6135, '退费处理', 3, 6005, 5, NULL, NULL, 1, 'visit:charge:refund', 'visit:charge:refund', NULL, 6005, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- ----------------------------
-- 7. 跟进管理功能权限点
-- ----------------------------

-- 跟进查询
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(6141, '跟进查询', 3, 6006, 1, NULL, NULL, 1, 'visit:follow:query', 'visit:follow:query', NULL, 6006, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 跟进新增
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(6142, '跟进新增', 3, 6006, 2, NULL, NULL, 1, 'visit:follow:add', 'visit:follow:add', NULL, 6006, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 跟进编辑
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(6143, '跟进编辑', 3, 6006, 3, NULL, NULL, 1, 'visit:follow:update', 'visit:follow:update', NULL, 6006, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 跟进删除
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(6144, '跟进删除', 3, 6006, 4, NULL, NULL, 1, 'visit:follow:delete', 'visit:follow:delete', NULL, 6006, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 跟进分配
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(6145, '跟进分配', 3, 6006, 5, NULL, NULL, 1, 'visit:follow:assign', 'visit:follow:assign', NULL, 6006, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 设置外键检查
SET FOREIGN_KEY_CHECKS = 1;

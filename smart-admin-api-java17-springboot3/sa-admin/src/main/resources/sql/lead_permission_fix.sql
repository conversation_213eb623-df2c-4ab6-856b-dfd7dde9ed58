-- 线索管理权限修复脚本
-- 数据权限类型：2 = LEAD (在DataScopeTypeEnum中定义)
-- 权限级别：0=本人, 1=本部门, 2=本部门及下属部门, 10=全部

-- 1. 为技术总监角色(roleId=1)配置线索数据权限 - 全部权限
INSERT IGNORE INTO t_role_data_scope (data_scope_type, view_type, role_id, create_time, update_time)
VALUES (2, 10, 1, NOW(), NOW());

-- 2. 为网络部角色(roleId=1016)配置线索数据权限 - 本人权限
INSERT IGNORE INTO t_role_data_scope (data_scope_type, view_type, role_id, create_time, update_time)
VALUES (2, 0, 1016, NOW(), NOW());

-- 3. 查看权限配置结果
SELECT
    rds.role_id,
    r.role_name,
    rds.data_scope_type,
    rds.view_type,
    CASE rds.view_type
        WHEN 0 THEN '本人'
        WHEN 1 THEN '本部门'
        WHEN 2 THEN '本部门及下属部门'
        WHEN 10 THEN '全部'
        ELSE '未知'
    END as view_type_name
FROM t_role_data_scope rds
LEFT JOIN t_role r ON rds.role_id = r.role_id
WHERE rds.data_scope_type = 2
ORDER BY rds.role_id;

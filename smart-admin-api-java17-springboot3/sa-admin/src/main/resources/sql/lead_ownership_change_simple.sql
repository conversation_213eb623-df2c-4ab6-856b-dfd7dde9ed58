-- 线索归属变更功能的简化SQL脚本

-- 1. 创建线索归属变更申请表
CREATE TABLE IF NOT EXISTS `t_lead_ownership_change` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `lead_id` bigint NOT NULL COMMENT '线索ID',
  `applicant_user_id` bigint NOT NULL COMMENT '申请人用户ID',
  `target_user_id` bigint DEFAULT NULL COMMENT '目标用户ID（转移给谁）',
  `change_type` int NOT NULL COMMENT '变更类型：1-申请转移给自己，2-转移给他人，3-退回公海',
  `reason` varchar(500) DEFAULT NULL COMMENT '申请理由',
  `status` int NOT NULL DEFAULT '1' COMMENT '状态：1-待审批，2-已同意，3-已拒绝，4-已撤销',
  `approver_user_id` bigint DEFAULT NULL COMMENT '审批人用户ID',
  `approval_time` datetime DEFAULT NULL COMMENT '审批时间',
  `approval_remark` varchar(500) DEFAULT NULL COMMENT '审批备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_lead_id` (`lead_id`),
  KEY `idx_applicant_user_id` (`applicant_user_id`),
  KEY `idx_target_user_id` (`target_user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='线索归属变更申请表';

-- 2. 添加线索归属变更相关的菜单权限
INSERT IGNORE INTO `t_menu` (`menu_id`, `menu_name`, `menu_type`, `parent_id`, `sort`, `path`, `component`, `perms_type`, `api_perms`, `web_perms`, `icon`, `context_menu_id`, `frame_flag`, `frame_url`, `cache_flag`, `visible_flag`, `disabled_flag`, `deleted_flag`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES
-- 线索归属变更管理主菜单
(2501, '线索归属变更', 1, 250, 5, '/hospital/lead/ownership-change', NULL, 1, NULL, NULL, 'SwapOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
-- 我的申请
(2502, '我的申请', 2, 2501, 1, '/hospital/lead/ownership-change/my-requests', '/business/hospital/lead/ownership-change-request.vue', 1, 'hospital:lead:ownership:apply', 'hospital:lead:ownership:apply', NULL, NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
-- 审批管理
(2503, '审批管理', 2, 2501, 2, '/hospital/lead/ownership-change/approval', '/business/hospital/lead/ownership-change-approval.vue', 1, 'hospital:lead:ownership:approve', 'hospital:lead:ownership:approve', NULL, NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW());

-- 3. 为管理员角色添加权限
INSERT IGNORE INTO `t_role_menu` (`role_id`, `menu_id`) VALUES
(1, 2501),
(1, 2502),
(1, 2503);

-- 4. 添加字典数据
INSERT IGNORE INTO `t_dict` (`dict_id`, `dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`) VALUES
(1001, '线索归属变更状态', 'LEAD_OWNERSHIP_CHANGE_STATUS', '线索归属变更申请状态', 0, NOW(), NOW()),
(1002, '线索归属变更类型', 'LEAD_OWNERSHIP_CHANGE_TYPE', '线索归属变更申请类型', 0, NOW(), NOW());

INSERT IGNORE INTO `t_dict_data` (`dict_data_id`, `dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
-- 线索归属变更状态
(10001, 1001, '1', '待审批', '申请已提交，等待审批', 1, 0, NOW(), NOW()),
(10002, 1001, '2', '已同意', '申请已通过审批', 2, 0, NOW(), NOW()),
(10003, 1001, '3', '已拒绝', '申请被拒绝', 3, 0, NOW(), NOW()),
(10004, 1001, '4', '已撤销', '申请已被撤销', 4, 0, NOW(), NOW()),
-- 线索归属变更类型
(10005, 1002, '1', '申请转移给自己', '申请将线索转移给自己', 1, 0, NOW(), NOW()),
(10006, 1002, '2', '转移给他人', '将线索转移给其他人', 2, 0, NOW(), NOW()),
(10007, 1002, '3', '退回公海', '将线索退回到公海池', 3, 0, NOW(), NOW());

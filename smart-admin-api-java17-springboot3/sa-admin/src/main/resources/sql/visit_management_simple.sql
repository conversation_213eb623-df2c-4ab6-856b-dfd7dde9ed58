-- 到诊管理模块菜单权限配置（简化版）

-- 删除可能存在的旧菜单
DELETE FROM t_menu WHERE menu_id >= 8000 AND menu_id < 9000;

-- 到诊管理主菜单
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(8001, '到诊管理', 1, 0, 7, '/visit', NULL, 1, NULL, NULL, 'UserOutlined', 8001, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 患者列表菜单
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(8002, '患者列表', 2, 8001, 1, '/visit/patient/list', NULL, 1, NULL, NULL, 'UnorderedListOutlined', 8001, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 患者查询权限
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(8101, '患者查询', 3, 8002, 1, NULL, NULL, 1, 'visit:patient:query', 'visit:patient:query', NULL, 8002, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 患者新增权限
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(8102, '患者新增', 3, 8002, 2, NULL, NULL, 1, 'visit:patient:add', 'visit:patient:add', NULL, 8002, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 患者编辑权限
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(8103, '患者编辑', 3, 8002, 3, NULL, NULL, 1, 'visit:patient:update', 'visit:patient:update', NULL, 8002, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 患者删除权限
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
(8104, '患者删除', 3, 8002, 4, NULL, NULL, 1, 'visit:patient:delete', 'visit:patient:delete', NULL, 8002, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 为超级管理员角色分配权限
INSERT INTO t_role_menu (role_id, menu_id) VALUES
(1, 8001),
(1, 8002),
(1, 8101),
(1, 8102),
(1, 8103),
(1, 8104);

-- 为医院管理员角色分配权限（如果存在）
INSERT IGNORE INTO t_role_menu (role_id, menu_id)
SELECT 2, 8001 WHERE EXISTS (SELECT 1 FROM t_role WHERE role_id = 2);

INSERT IGNORE INTO t_role_menu (role_id, menu_id)
SELECT 2, 8002 WHERE EXISTS (SELECT 1 FROM t_role WHERE role_id = 2);

INSERT IGNORE INTO t_role_menu (role_id, menu_id)
SELECT 2, 8101 WHERE EXISTS (SELECT 1 FROM t_role WHERE role_id = 2);

INSERT IGNORE INTO t_role_menu (role_id, menu_id)
SELECT 2, 8102 WHERE EXISTS (SELECT 1 FROM t_role WHERE role_id = 2);

INSERT IGNORE INTO t_role_menu (role_id, menu_id)
SELECT 2, 8103 WHERE EXISTS (SELECT 1 FROM t_role WHERE role_id = 2);

INSERT IGNORE INTO t_role_menu (role_id, menu_id)
SELECT 2, 8104 WHERE EXISTS (SELECT 1 FROM t_role WHERE role_id = 2);

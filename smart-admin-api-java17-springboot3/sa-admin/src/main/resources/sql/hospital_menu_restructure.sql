-- 医院营销系统菜单结构重构 - 将三级菜单改为二级菜单
-- Author: 1024创新实验室-主任：卓大
-- Date: 2024-01-01 12:00:00

-- 1. 删除原有的医院营销管理主菜单（三级结构）
DELETE FROM t_role_menu WHERE menu_id = 5001;
DELETE FROM t_menu WHERE menu_id = 5001;

-- 2. 删除原有的目录级菜单（线索管理、预约管理、客户管理等目录）
DELETE FROM t_role_menu WHERE menu_id IN (5010, 5020, 5030, 5040, 5050, 5060, 5070, 5080);
DELETE FROM t_menu WHERE menu_id IN (5010, 5020, 5030, 5040, 5050, 5060, 5070, 5080);

-- 3. 重新配置为二级菜单结构
INSERT IGNORE INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES

-- 医院营销系统 - 一级菜单（作为所有医院功能的容器）
(5005, '医院营销系统', 1, 0, 5, '/hospital', NULL, 1, NULL, NULL, 'MedicineBoxOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 仪表盘
(5006, '仪表盘', 2, 5005, 10, '/hospital/dashboard', '/views/business/hospital/dashboard/dashboard-overview.vue', 2, NULL, 'hospital:dashboard:query', 'DashboardOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5007, '数据概览', 2, 5005, 11, '/dashboard/overview', '/views/business/hospital/dashboard/dashboard-overview.vue', 2, NULL, 'hospital:dashboard:query', 'DashboardOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 线索管理
(5011, '线索列表', 2, 5005, 20, '/hospital/lead/list', '/views/business/hospital/lead/lead-list.vue', 2, NULL, 'hospital:lead:query', 'UnorderedListOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5012, '线索跟进', 2, 5005, 21, '/hospital/lead/follow', '/views/business/hospital/lead/lead-follow.vue', 2, NULL, 'hospital:lead:follow:query', 'CommentOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 预约管理
(5021, '预约列表', 2, 5005, 30, '/hospital/appointment/list', '/views/business/hospital/appointment/appointment-list.vue', 2, NULL, 'hospital:appointment:query', 'UnorderedListOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5022, '医生排班', 2, 5005, 31, '/hospital/appointment/schedule', '/views/business/hospital/appointment/doctor-schedule.vue', 2, NULL, 'hospital:schedule:query', 'ScheduleOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5023, '项目管理', 2, 5005, 32, '/hospital/appointment/project', '/views/business/hospital/project/project-list.vue', 2, NULL, 'hospital:project:query', 'ProjectOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 客户管理
(5031, '客户列表', 2, 5005, 40, '/hospital/customer/list', '/views/business/hospital/customer/customer-list.vue', 2, NULL, 'hospital:customer:query', 'UnorderedListOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5032, '病历管理', 2, 5005, 41, '/hospital/customer/medical', '/views/business/hospital/customer/medical-record-list.vue', 2, NULL, 'hospital:record:query', 'FileTextOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 数据统计
(5041, '线索转化统计', 2, 5005, 50, '/hospital/statistics/lead-conversion', '/views/business/hospital/statistics/lead-conversion-stats.vue', 2, NULL, 'hospital:statistics:lead', 'LineChartOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5042, '预约到诊统计', 2, 5005, 51, '/hospital/statistics/appointment-arrival', '/views/business/hospital/statistics/appointment-arrival-stats.vue', 2, NULL, 'hospital:statistics:appointment', 'CalendarOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5043, '客户满意度统计', 2, 5005, 52, '/hospital/statistics/customer-satisfaction', '/views/business/hospital/statistics/customer-satisfaction-stats.vue', 2, NULL, 'hospital:statistics:satisfaction', 'SmileOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5044, '医生工作量统计', 2, 5005, 53, '/hospital/statistics/doctor-workload', '/views/business/hospital/statistics/doctor-workload-stats.vue', 2, NULL, 'hospital:statistics:doctor', 'UserOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 财务管理
(5051, '收费管理', 2, 5005, 60, '/hospital/finance/billing', '/views/business/hospital/finance/billing-list.vue', 2, NULL, 'hospital:finance:billing', 'MoneyCollectOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5052, '收费统计', 2, 5005, 61, '/hospital/finance/billing-stats', '/views/business/hospital/finance/billing-stats.vue', 2, NULL, 'hospital:finance:stats', 'PieChartOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5053, '财务报表', 2, 5005, 62, '/hospital/finance/reports', '/views/business/hospital/finance/finance-reports.vue', 2, NULL, 'hospital:finance:reports', 'FileTextOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5054, '收入分析', 2, 5005, 63, '/hospital/finance/revenue-analysis', '/views/business/hospital/finance/revenue-analysis.vue', 2, NULL, 'hospital:finance:revenue', 'RiseOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 关怀管理
(5061, '出院关怀', 2, 5005, 70, '/hospital/care/discharge-care', '/views/business/hospital/care/discharge-care-list.vue', 2, NULL, 'hospital:care:discharge', 'MedicineBoxOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5062, '关怀模板', 2, 5005, 71, '/hospital/care/templates', '/views/business/hospital/care/care-template-list.vue', 2, NULL, 'hospital:care:template', 'FileTextOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5063, 'SOP流程', 2, 5005, 72, '/hospital/care/sop', '/views/business/hospital/care/sop-process-list.vue', 2, NULL, 'hospital:care:sop', 'ApartmentOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5064, '满意度回访', 2, 5005, 73, '/hospital/care/satisfaction', '/views/business/hospital/care/satisfaction-survey.vue', 2, NULL, 'hospital:care:satisfaction', 'SmileOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5065, '复购提醒', 2, 5005, 74, '/hospital/care/repurchase', '/views/business/hospital/care/repurchase-reminder.vue', 2, NULL, 'hospital:care:repurchase', 'BellOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 知识库
(5071, '文档管理', 2, 5005, 80, '/hospital/knowledge/documents', '/views/business/hospital/knowledge/document-list.vue', 2, NULL, 'hospital:knowledge:document', 'FileTextOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5072, '分类管理', 2, 5005, 81, '/hospital/knowledge/categories', '/views/business/hospital/knowledge/category-list.vue', 2, NULL, 'hospital:knowledge:category', 'FolderOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5073, '标签管理', 2, 5005, 82, '/hospital/knowledge/tags', '/views/business/hospital/knowledge/tag-list.vue', 2, NULL, 'hospital:knowledge:tag', 'TagOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5074, '知识搜索', 2, 5005, 83, '/hospital/knowledge/search', '/views/business/hospital/knowledge/knowledge-search.vue', 2, NULL, 'hospital:knowledge:search', 'SearchOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5075, '知识共享', 2, 5005, 84, '/hospital/knowledge/sharing', '/views/business/hospital/knowledge/knowledge-sharing.vue', 2, NULL, 'hospital:knowledge:sharing', 'ShareAltOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 营销活动
(5081, '活动管理', 2, 5005, 90, '/hospital/marketing/campaigns', '/views/business/hospital/marketing/campaign-list.vue', 2, NULL, 'hospital:marketing:campaign', 'CalendarOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5082, '活动推广', 2, 5005, 91, '/hospital/marketing/promotion', '/views/business/hospital/marketing/promotion-list.vue', 2, NULL, 'hospital:marketing:promotion', 'SoundOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5083, '效果跟踪', 2, 5005, 92, '/hospital/marketing/tracking', '/views/business/hospital/marketing/effect-tracking.vue', 2, NULL, 'hospital:marketing:tracking', 'LineChartOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5084, 'ROI分析', 2, 5005, 93, '/hospital/marketing/roi-analysis', '/views/business/hospital/marketing/roi-analysis.vue', 2, NULL, 'hospital:marketing:roi', 'BarChartOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5085, '活动报表', 2, 5005, 94, '/hospital/marketing/reports', '/views/business/hospital/marketing/campaign-reports.vue', 2, NULL, 'hospital:marketing:reports', 'FileTextOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW());

-- 4. 重新配置角色权限关联（医院营销管理员 - 全部权限）
INSERT IGNORE INTO t_role_menu (role_id, menu_id)
SELECT 101, menu_id FROM t_menu WHERE menu_id BETWEEN 5005 AND 5200;

-- 5. 重新配置角色权限关联（营销主管 - 主要业务权限）
INSERT IGNORE INTO t_role_menu (role_id, menu_id) VALUES
-- 仪表盘
(102, 5005), (102, 5006),
-- 线索管理
(102, 5010), (102, 5011), (102, 5012),
-- 预约管理
(102, 5020), (102, 5021), (102, 5022), (102, 5023),
-- 客户管理
(102, 5030), (102, 5031), (102, 5032),
-- 数据统计
(102, 5040), (102, 5041), (102, 5042), (102, 5043), (102, 5044),
-- 财务管理
(102, 5050), (102, 5051), (102, 5052), (102, 5053), (102, 5054),
-- 关怀管理
(102, 5060), (102, 5061), (102, 5062), (102, 5063), (102, 5064), (102, 5065),
-- 知识库
(102, 5070), (102, 5071), (102, 5072), (102, 5073), (102, 5074), (102, 5075),
-- 营销活动
(102, 5080), (102, 5081), (102, 5082), (102, 5083), (102, 5084), (102, 5085);

-- 6. 重新配置角色权限关联（营销专员 - 基础操作权限）
INSERT IGNORE INTO t_role_menu (role_id, menu_id) VALUES
-- 仪表盘（只读）
(103, 5005), (103, 5006),
-- 线索管理（跟进权限）
(103, 5010), (103, 5011), (103, 5012),
-- 预约管理（创建和查看）
(103, 5020), (103, 5021),
-- 客户管理（只读）
(103, 5030), (103, 5031),
-- 知识库（只读）
(103, 5070), (103, 5071), (103, 5074), (103, 5075),
-- 营销活动（查看）
(103, 5080), (103, 5081), (103, 5083);

-- 7. 重新配置角色权限关联（客服人员 - 客户服务权限）
INSERT IGNORE INTO t_role_menu (role_id, menu_id) VALUES
-- 仪表盘（只读）
(104, 5005), (104, 5006),
-- 预约管理
(104, 5020), (104, 5021),
-- 客户管理
(104, 5030), (104, 5031), (104, 5032),
-- 关怀管理（主要职责）
(104, 5060), (104, 5061), (104, 5062), (104, 5064), (104, 5065),
-- 知识库（只读）
(104, 5070), (104, 5071), (104, 5074);

-- 8. 重新配置角色权限关联（医生 - 病历管理权限）
INSERT IGNORE INTO t_role_menu (role_id, menu_id) VALUES
-- 仪表盘（只读）
(105, 5005), (105, 5006),
-- 客户管理（病历相关）
(105, 5030), (105, 5031), (105, 5032),
-- 医生工作量统计（只读）
(105, 5040), (105, 5044),
-- 知识库（只读）
(105, 5070), (105, 5071), (105, 5074);

-- 到诊管理模块数据库表结构
-- Author: 1024创新实验室-主任：卓大
-- Date: 2025-07-29 10:00:00
-- Description: 将客户管理重构为到诊管理，增加严格的权限控制

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 1. 到诊患者表 (基于现有t_customer表扩展)
-- ----------------------------

-- 到诊患者表
DROP TABLE IF EXISTS `t_visit_patient`;
CREATE TABLE `t_visit_patient` (
  `patient_id` bigint NOT NULL AUTO_INCREMENT COMMENT '患者ID',
  `patient_no` varchar(50) NOT NULL COMMENT '患者编号',
  `patient_name` varchar(50) NOT NULL COMMENT '患者姓名',
  `patient_phone` varchar(20) NOT NULL COMMENT '患者电话',
  `patient_wechat` varchar(50) DEFAULT NULL COMMENT '微信号',
  `patient_email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `gender` tinyint DEFAULT NULL COMMENT '性别：1-男，2-女',
  `age` int DEFAULT NULL COMMENT '年龄',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `id_card` varchar(20) DEFAULT NULL COMMENT '身份证号',
  `patient_address` varchar(200) DEFAULT NULL COMMENT '地址',
  `occupation` varchar(50) DEFAULT NULL COMMENT '职业',
  
  -- 到诊管理相关字段
  `visit_status` tinyint NOT NULL DEFAULT '1' COMMENT '到诊状态：1-未到诊，2-已到诊，3-诊疗中，4-已完成，5-已离院',
  `appointment_id` bigint DEFAULT NULL COMMENT '关联预约ID',
  `assigned_doctor_id` bigint DEFAULT NULL COMMENT '分配医生ID',
  `assigned_doctor_name` varchar(50) DEFAULT NULL COMMENT '分配医生姓名',
  `assigned_assistant_id` bigint DEFAULT NULL COMMENT '分配治疗助理ID',
  `assigned_assistant_name` varchar(50) DEFAULT NULL COMMENT '分配治疗助理姓名',
  `visit_date` date DEFAULT NULL COMMENT '到诊日期',
  `visit_time` time DEFAULT NULL COMMENT '到诊时间',
  `registration_time` datetime DEFAULT NULL COMMENT '登记时间',
  `completion_time` datetime DEFAULT NULL COMMENT '完成时间',
  
  -- 权限控制字段
  `department_id` bigint DEFAULT NULL COMMENT '所属部门ID',
  `department_name` varchar(50) DEFAULT NULL COMMENT '所属部门名称',
  `data_scope_employee_ids` text COMMENT '数据权限员工ID列表(JSON格式)',
  
  -- 患者信息字段
  `patient_source` varchar(100) DEFAULT NULL COMMENT '患者来源',
  `patient_level` tinyint DEFAULT '1' COMMENT '患者等级：1-普通患者，2-VIP患者，3-SVIP患者',
  `patient_tags` varchar(500) DEFAULT NULL COMMENT '患者标签',
  `first_visit_date` date DEFAULT NULL COMMENT '首次就诊日期',
  `last_visit_date` date DEFAULT NULL COMMENT '最后就诊日期',
  `total_consumption` decimal(10,2) DEFAULT '0.00' COMMENT '总消费金额',
  `emergency_contact` varchar(50) DEFAULT NULL COMMENT '紧急联系人',
  `emergency_phone` varchar(20) DEFAULT NULL COMMENT '紧急联系人电话',
  
  `remark` text COMMENT '备注',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`patient_id`),
  UNIQUE KEY `uk_patient_no` (`patient_no`),
  UNIQUE KEY `uk_patient_phone` (`patient_phone`),
  KEY `idx_visit_status` (`visit_status`),
  KEY `idx_assigned_doctor` (`assigned_doctor_id`),
  KEY `idx_assigned_assistant` (`assigned_assistant_id`),
  KEY `idx_department_id` (`department_id`),
  KEY `idx_visit_date` (`visit_date`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='到诊患者表';

-- ----------------------------
-- 2. 医生助理关系表
-- ----------------------------

-- 医生助理关系表
DROP TABLE IF EXISTS `t_doctor_assistant_relation`;
CREATE TABLE `t_doctor_assistant_relation` (
  `relation_id` bigint NOT NULL AUTO_INCREMENT COMMENT '关系ID',
  `doctor_id` bigint NOT NULL COMMENT '医生ID',
  `doctor_name` varchar(50) NOT NULL COMMENT '医生姓名',
  `assistant_id` bigint NOT NULL COMMENT '治疗助理ID',
  `assistant_name` varchar(50) NOT NULL COMMENT '治疗助理姓名',
  `department_id` bigint NOT NULL COMMENT '部门ID',
  `department_name` varchar(50) NOT NULL COMMENT '部门名称',
  `relation_type` tinyint NOT NULL DEFAULT '1' COMMENT '关系类型：1-直接下属，2-协作关系',
  `start_date` date NOT NULL COMMENT '关系开始日期',
  `end_date` date DEFAULT NULL COMMENT '关系结束日期',
  `relation_status` tinyint NOT NULL DEFAULT '1' COMMENT '关系状态：1-有效，0-无效',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`relation_id`),
  UNIQUE KEY `uk_doctor_assistant` (`doctor_id`, `assistant_id`),
  KEY `idx_doctor_id` (`doctor_id`),
  KEY `idx_assistant_id` (`assistant_id`),
  KEY `idx_department_id` (`department_id`),
  KEY `idx_relation_status` (`relation_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='医生助理关系表';

-- ----------------------------
-- 3. 诊断记录表
-- ----------------------------

-- 诊断记录表
DROP TABLE IF EXISTS `t_diagnosis_record`;
CREATE TABLE `t_diagnosis_record` (
  `diagnosis_id` bigint NOT NULL AUTO_INCREMENT COMMENT '诊断记录ID',
  `diagnosis_no` varchar(50) NOT NULL COMMENT '诊断编号',
  `patient_id` bigint NOT NULL COMMENT '患者ID',
  `patient_name` varchar(50) NOT NULL COMMENT '患者姓名',
  `patient_phone` varchar(20) NOT NULL COMMENT '患者电话',
  `doctor_id` bigint NOT NULL COMMENT '医生ID',
  `doctor_name` varchar(50) NOT NULL COMMENT '医生姓名',
  `department_id` bigint NOT NULL COMMENT '科室ID',
  `department_name` varchar(50) NOT NULL COMMENT '科室名称',
  `diagnosis_date` date NOT NULL COMMENT '诊断日期',
  `diagnosis_time` time NOT NULL COMMENT '诊断时间',
  
  -- 诊断内容
  `chief_complaint` text COMMENT '主诉',
  `present_illness` text COMMENT '现病史',
  `past_history` text COMMENT '既往史',
  `family_history` text COMMENT '家族史',
  `personal_history` text COMMENT '个人史',
  `physical_examination` text COMMENT '体格检查',
  `auxiliary_examination` text COMMENT '辅助检查',
  `preliminary_diagnosis` text COMMENT '初步诊断',
  `final_diagnosis` text COMMENT '最终诊断',
  `treatment_plan` text COMMENT '治疗方案',
  `medication_advice` text COMMENT '用药建议',
  `lifestyle_advice` text COMMENT '生活建议',
  `follow_up_plan` text COMMENT '随访计划',
  `next_visit_date` date DEFAULT NULL COMMENT '下次复诊日期',
  
  `diagnosis_status` tinyint NOT NULL DEFAULT '1' COMMENT '诊断状态：1-草稿，2-已完成，3-已审核',
  `template_id` bigint DEFAULT NULL COMMENT '诊断模板ID',
  `template_name` varchar(100) DEFAULT NULL COMMENT '诊断模板名称',
  `diagnosis_duration` int DEFAULT NULL COMMENT '诊断时长(分钟)',
  `remark` text COMMENT '备注',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`diagnosis_id`),
  UNIQUE KEY `uk_diagnosis_no` (`diagnosis_no`),
  KEY `idx_patient_id` (`patient_id`),
  KEY `idx_doctor_id` (`doctor_id`),
  KEY `idx_department_id` (`department_id`),
  KEY `idx_diagnosis_date` (`diagnosis_date`),
  KEY `idx_diagnosis_status` (`diagnosis_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='诊断记录表';

-- ----------------------------
-- 4. 开单记录表
-- ----------------------------

-- 开单记录表
DROP TABLE IF EXISTS `t_prescription_record`;
CREATE TABLE `t_prescription_record` (
  `prescription_id` bigint NOT NULL AUTO_INCREMENT COMMENT '开单记录ID',
  `prescription_no` varchar(50) NOT NULL COMMENT '开单编号',
  `patient_id` bigint NOT NULL COMMENT '患者ID',
  `patient_name` varchar(50) NOT NULL COMMENT '患者姓名',
  `patient_phone` varchar(20) NOT NULL COMMENT '患者电话',
  `doctor_id` bigint NOT NULL COMMENT '医生ID',
  `doctor_name` varchar(50) NOT NULL COMMENT '医生姓名',
  `department_id` bigint NOT NULL COMMENT '科室ID',
  `department_name` varchar(50) NOT NULL COMMENT '科室名称',
  `diagnosis_id` bigint DEFAULT NULL COMMENT '关联诊断记录ID',
  
  `prescription_type` tinyint NOT NULL COMMENT '开单类型：1-药品处方，2-检查单，3-治疗单，4-手术单',
  `prescription_date` date NOT NULL COMMENT '开单日期',
  `prescription_time` time NOT NULL COMMENT '开单时间',
  
  -- 开单内容 (JSON格式存储详细信息)
  `prescription_items` longtext NOT NULL COMMENT '开单项目详情(JSON格式)',
  `total_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总金额',
  `discount_amount` decimal(10,2) DEFAULT '0.00' COMMENT '优惠金额',
  `final_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '最终金额',
  
  `prescription_status` tinyint NOT NULL DEFAULT '1' COMMENT '开单状态：1-待审核，2-已审核，3-已执行，4-已取消',
  `audit_user_id` bigint DEFAULT NULL COMMENT '审核人ID',
  `audit_user_name` varchar(50) DEFAULT NULL COMMENT '审核人姓名',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_remark` varchar(500) DEFAULT NULL COMMENT '审核备注',
  
  `execute_user_id` bigint DEFAULT NULL COMMENT '执行人ID',
  `execute_user_name` varchar(50) DEFAULT NULL COMMENT '执行人姓名',
  `execute_time` datetime DEFAULT NULL COMMENT '执行时间',
  `execute_remark` varchar(500) DEFAULT NULL COMMENT '执行备注',
  
  `urgency_level` tinyint DEFAULT '2' COMMENT '紧急程度：1-紧急，2-普通，3-延缓',
  `valid_days` int DEFAULT '7' COMMENT '有效天数',
  `expire_date` date DEFAULT NULL COMMENT '过期日期',
  
  `remark` text COMMENT '备注',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`prescription_id`),
  UNIQUE KEY `uk_prescription_no` (`prescription_no`),
  KEY `idx_patient_id` (`patient_id`),
  KEY `idx_doctor_id` (`doctor_id`),
  KEY `idx_department_id` (`department_id`),
  KEY `idx_diagnosis_id` (`diagnosis_id`),
  KEY `idx_prescription_date` (`prescription_date`),
  KEY `idx_prescription_status` (`prescription_status`),
  KEY `idx_prescription_type` (`prescription_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='开单记录表';

-- ----------------------------
-- 5. 跟进记录表 (针对到诊患者的跟进)
-- ----------------------------

-- 跟进记录表
DROP TABLE IF EXISTS `t_visit_follow`;
CREATE TABLE `t_visit_follow` (
  `follow_id` bigint NOT NULL AUTO_INCREMENT COMMENT '跟进记录ID',
  `patient_id` bigint NOT NULL COMMENT '患者ID',
  `patient_name` varchar(50) NOT NULL COMMENT '患者姓名',
  `patient_phone` varchar(20) NOT NULL COMMENT '患者电话',
  `diagnosis_id` bigint DEFAULT NULL COMMENT '关联诊断记录ID',
  `prescription_id` bigint DEFAULT NULL COMMENT '关联开单记录ID',
  
  `follow_type` tinyint NOT NULL COMMENT '跟进类型：1-术后跟进，2-用药跟进，3-复诊提醒，4-康复指导，5-满意度调查',
  `follow_method` tinyint NOT NULL COMMENT '跟进方式：1-电话，2-微信，3-短信，4-邮件，5-上门',
  `follow_content` text NOT NULL COMMENT '跟进内容',
  `follow_result` varchar(500) DEFAULT NULL COMMENT '跟进结果',
  `patient_feedback` text COMMENT '患者反馈',
  `satisfaction_score` tinyint DEFAULT NULL COMMENT '满意度评分（1-5分）',
  
  `assigned_user_id` bigint NOT NULL COMMENT '分配跟进人ID',
  `assigned_user_name` varchar(50) NOT NULL COMMENT '分配跟进人姓名',
  `assigned_by_user_id` bigint NOT NULL COMMENT '分配人ID',
  `assigned_by_user_name` varchar(50) NOT NULL COMMENT '分配人姓名',
  `assigned_time` datetime NOT NULL COMMENT '分配时间',
  
  `planned_follow_time` datetime NOT NULL COMMENT '计划跟进时间',
  `actual_follow_time` datetime DEFAULT NULL COMMENT '实际跟进时间',
  `next_follow_time` datetime DEFAULT NULL COMMENT '下次跟进时间',
  
  `follow_status` tinyint NOT NULL DEFAULT '1' COMMENT '跟进状态：1-待跟进，2-跟进中，3-已完成，4-已取消，5-跟进失败',
  `priority_level` tinyint DEFAULT '2' COMMENT '优先级：1-高，2-中，3-低',
  `reminder_flag` tinyint DEFAULT '1' COMMENT '是否提醒：0-否，1-是',
  `reminder_time` datetime DEFAULT NULL COMMENT '提醒时间',
  
  `department_id` bigint NOT NULL COMMENT '部门ID',
  `department_name` varchar(50) NOT NULL COMMENT '部门名称',
  
  `remark` text COMMENT '备注',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`follow_id`),
  KEY `idx_patient_id` (`patient_id`),
  KEY `idx_diagnosis_id` (`diagnosis_id`),
  KEY `idx_prescription_id` (`prescription_id`),
  KEY `idx_assigned_user` (`assigned_user_id`),
  KEY `idx_assigned_by_user` (`assigned_by_user_id`),
  KEY `idx_department_id` (`department_id`),
  KEY `idx_planned_follow_time` (`planned_follow_time`),
  KEY `idx_follow_status` (`follow_status`),
  KEY `idx_follow_type` (`follow_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='跟进记录表';

-- ----------------------------
-- 6. 诊断模板表
-- ----------------------------

-- 诊断模板表
DROP TABLE IF EXISTS `t_diagnosis_template`;
CREATE TABLE `t_diagnosis_template` (
  `template_id` bigint NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `template_description` text COMMENT '模板描述',
  `department_id` bigint NOT NULL COMMENT '科室ID',
  `department_name` varchar(50) NOT NULL COMMENT '科室名称',
  `disease_category` varchar(100) DEFAULT NULL COMMENT '疾病分类',
  `template_content` longtext NOT NULL COMMENT '模板内容(JSON格式)',
  `template_type` tinyint NOT NULL DEFAULT '1' COMMENT '模板类型：1-通用模板，2-专科模板，3-个人模板',
  `usage_count` int DEFAULT '0' COMMENT '使用次数',
  `template_status` tinyint NOT NULL DEFAULT '1' COMMENT '模板状态：1-启用，0-禁用',
  `is_default` tinyint DEFAULT '0' COMMENT '是否默认模板：0-否，1-是',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`template_id`),
  KEY `idx_department_id` (`department_id`),
  KEY `idx_template_type` (`template_type`),
  KEY `idx_template_status` (`template_status`),
  KEY `idx_disease_category` (`disease_category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='诊断模板表';

-- ----------------------------
-- 7. 字典数据初始化
-- ----------------------------

-- 插入到诊状态字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`)
VALUES ('到诊状态', 'VISIT_STATUS', '到诊管理模块患者状态字典', 0, NOW(), NOW());

SET @visit_status_dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'VISIT_STATUS');

INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@visit_status_dict_id, '1', '未到诊', '患者已预约但尚未到诊', 100, 0, NOW(), NOW()),
(@visit_status_dict_id, '2', '已到诊', '患者已到诊，等待诊疗', 90, 0, NOW(), NOW()),
(@visit_status_dict_id, '3', '诊疗中', '患者正在接受诊疗', 80, 0, NOW(), NOW()),
(@visit_status_dict_id, '4', '已完成', '诊疗已完成，等待离院', 70, 0, NOW(), NOW()),
(@visit_status_dict_id, '5', '已离院', '患者已离院', 60, 0, NOW(), NOW());

-- 插入诊断状态字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`)
VALUES ('诊断状态', 'DIAGNOSIS_STATUS', '诊断记录状态字典', 0, NOW(), NOW());

SET @diagnosis_status_dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'DIAGNOSIS_STATUS');

INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@diagnosis_status_dict_id, '1', '草稿', '诊断记录草稿状态', 100, 0, NOW(), NOW()),
(@diagnosis_status_dict_id, '2', '已完成', '诊断记录已完成', 90, 0, NOW(), NOW()),
(@diagnosis_status_dict_id, '3', '已审核', '诊断记录已审核', 80, 0, NOW(), NOW());

-- 插入开单类型字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`)
VALUES ('开单类型', 'PRESCRIPTION_TYPE', '开单记录类型字典', 0, NOW(), NOW());

SET @prescription_type_dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'PRESCRIPTION_TYPE');

INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@prescription_type_dict_id, '1', '药品处方', '药品处方单', 100, 0, NOW(), NOW()),
(@prescription_type_dict_id, '2', '检查单', '医疗检查单', 90, 0, NOW(), NOW()),
(@prescription_type_dict_id, '3', '治疗单', '治疗项目单', 80, 0, NOW(), NOW()),
(@prescription_type_dict_id, '4', '手术单', '手术项目单', 70, 0, NOW(), NOW());

-- 插入开单状态字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`)
VALUES ('开单状态', 'PRESCRIPTION_STATUS', '开单记录状态字典', 0, NOW(), NOW());

SET @prescription_status_dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'PRESCRIPTION_STATUS');

INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@prescription_status_dict_id, '1', '待审核', '开单待审核状态', 100, 0, NOW(), NOW()),
(@prescription_status_dict_id, '2', '已审核', '开单已审核状态', 90, 0, NOW(), NOW()),
(@prescription_status_dict_id, '3', '已执行', '开单已执行状态', 80, 0, NOW(), NOW()),
(@prescription_status_dict_id, '4', '已取消', '开单已取消状态', 70, 0, NOW(), NOW());

-- 插入跟进类型字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`)
VALUES ('跟进类型', 'FOLLOW_TYPE', '跟进记录类型字典', 0, NOW(), NOW());

SET @follow_type_dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'FOLLOW_TYPE');

INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@follow_type_dict_id, '1', '术后跟进', '手术后跟进回访', 100, 0, NOW(), NOW()),
(@follow_type_dict_id, '2', '用药跟进', '用药情况跟进', 90, 0, NOW(), NOW()),
(@follow_type_dict_id, '3', '复诊提醒', '复诊时间提醒', 80, 0, NOW(), NOW()),
(@follow_type_dict_id, '4', '康复指导', '康复指导跟进', 70, 0, NOW(), NOW()),
(@follow_type_dict_id, '5', '满意度调查', '服务满意度调查', 60, 0, NOW(), NOW());

-- 插入跟进方式字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`)
VALUES ('跟进方式', 'FOLLOW_METHOD', '跟进方式字典', 0, NOW(), NOW());

SET @follow_method_dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'FOLLOW_METHOD');

INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@follow_method_dict_id, '1', '电话', '电话跟进', 100, 0, NOW(), NOW()),
(@follow_method_dict_id, '2', '微信', '微信跟进', 90, 0, NOW(), NOW()),
(@follow_method_dict_id, '3', '短信', '短信跟进', 80, 0, NOW(), NOW()),
(@follow_method_dict_id, '4', '邮件', '邮件跟进', 70, 0, NOW(), NOW()),
(@follow_method_dict_id, '5', '上门', '上门跟进', 60, 0, NOW(), NOW());

-- 插入跟进状态字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`)
VALUES ('跟进状态', 'FOLLOW_STATUS', '跟进状态字典', 0, NOW(), NOW());

SET @follow_status_dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'FOLLOW_STATUS');

INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@follow_status_dict_id, '1', '待跟进', '等待跟进', 100, 0, NOW(), NOW()),
(@follow_status_dict_id, '2', '跟进中', '正在跟进', 90, 0, NOW(), NOW()),
(@follow_status_dict_id, '3', '已完成', '跟进已完成', 80, 0, NOW(), NOW()),
(@follow_status_dict_id, '4', '已取消', '跟进已取消', 70, 0, NOW(), NOW()),
(@follow_status_dict_id, '5', '跟进失败', '跟进失败', 60, 0, NOW(), NOW());

-- 设置外键检查
SET FOREIGN_KEY_CHECKS = 1;

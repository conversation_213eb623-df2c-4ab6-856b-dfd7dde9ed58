-- 线索跟进增强功能相关表结构
-- 作者：1024创新实验室-主任：卓大
-- 日期：2025-07-22

-- 1. 线索跟进提醒表
CREATE TABLE IF NOT EXISTS `t_lead_follow_reminder` (
  `reminder_id` bigint NOT NULL AUTO_INCREMENT COMMENT '提醒ID',
  `lead_id` bigint NOT NULL COMMENT '线索ID',
  `follow_time` datetime NOT NULL COMMENT '计划跟进时间',
  `reminder_time` datetime NOT NULL COMMENT '提醒时间',
  `reminder_type` tinyint NOT NULL DEFAULT 1 COMMENT '提醒方式：1系统通知 2短信 3邮件',
  `reminder_minutes` int NOT NULL DEFAULT 30 COMMENT '提前提醒分钟数',
  `reminder_status` tinyint NOT NULL DEFAULT 1 COMMENT '提醒状态：1待提醒 2已发送 3已取消 4已完成',
  `sent_time` datetime NULL COMMENT '实际发送时间',
  `reminder_content` varchar(500) NULL COMMENT '提醒内容',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `update_user_id` bigint NULL COMMENT '更新人ID',
  `deleted_flag` tinyint NOT NULL DEFAULT 0 COMMENT '删除标志：0未删除 1已删除',
  PRIMARY KEY (`reminder_id`),
  KEY `idx_lead_id` (`lead_id`),
  KEY `idx_reminder_time` (`reminder_time`),
  KEY `idx_reminder_status` (`reminder_status`),
  KEY `idx_follow_time` (`follow_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='线索跟进提醒表';

-- 2. 线索状态变更历史表
CREATE TABLE IF NOT EXISTS `t_lead_status_history` (
  `history_id` bigint NOT NULL AUTO_INCREMENT COMMENT '历史记录ID',
  `lead_id` bigint NOT NULL COMMENT '线索ID',
  `from_status` tinyint NOT NULL COMMENT '原状态',
  `to_status` tinyint NOT NULL COMMENT '新状态',
  `trigger_type` varchar(50) NOT NULL COMMENT '触发类型：FOLLOW_ADD,APPOINTMENT_CREATE,ARRIVAL_CONFIRM,CONVERSION_COMPLETE,MANUAL_CLOSE,MANUAL',
  `remark` varchar(500) NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_name` varchar(100) NOT NULL COMMENT '创建人姓名',
  PRIMARY KEY (`history_id`),
  KEY `idx_lead_id` (`lead_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_trigger_type` (`trigger_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='线索状态变更历史表';

-- 3. 修改线索跟进表，添加跟进结果字段（如果不存在）
-- 先检查列是否存在，如果不存在则添加
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = DATABASE()
       AND TABLE_NAME = 't_lead_follow'
       AND COLUMN_NAME = 'follow_result_type') = 0,
    'ALTER TABLE `t_lead_follow` ADD COLUMN `follow_result_type` tinyint NULL COMMENT ''跟进结果类型：1客户有预约意向 2需要再次跟进 3客户无意向/无效线索 4仅记录跟进'' AFTER `follow_result`',
    'SELECT ''Column follow_result_type already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 修改预约表，添加关联线索ID字段（如果不存在）
-- 先检查表是否存在
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
     WHERE TABLE_SCHEMA = DATABASE()
       AND TABLE_NAME = 't_appointment') > 0,
    (SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
         WHERE TABLE_SCHEMA = DATABASE()
           AND TABLE_NAME = 't_appointment'
           AND COLUMN_NAME = 'lead_id') = 0,
        'ALTER TABLE `t_appointment` ADD COLUMN `lead_id` bigint NULL COMMENT ''关联线索ID'' AFTER `appointment_id`',
        'SELECT ''Column lead_id already exists in t_appointment'' as message'
    )),
    'SELECT ''Table t_appointment does not exist'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 5. 为预约表添加线索ID索引（如果表存在且索引不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
     WHERE TABLE_SCHEMA = DATABASE()
       AND TABLE_NAME = 't_appointment') > 0,
    (SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
         WHERE TABLE_SCHEMA = DATABASE()
           AND TABLE_NAME = 't_appointment'
           AND INDEX_NAME = 'idx_lead_id') = 0,
        'ALTER TABLE `t_appointment` ADD INDEX `idx_lead_id` (`lead_id`)',
        'SELECT ''Index idx_lead_id already exists in t_appointment'' as message'
    )),
    'SELECT ''Table t_appointment does not exist'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 6. 修改预约表字段注释，标明医生和项目为可选
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
     WHERE TABLE_SCHEMA = DATABASE()
       AND TABLE_NAME = 't_appointment') > 0,
    'ALTER TABLE `t_appointment`
     MODIFY COLUMN `project_id` BIGINT COMMENT ''预约项目ID（可选）'',
     MODIFY COLUMN `doctor_id` BIGINT COMMENT ''预约医生ID（可选）''',
    'SELECT ''Table t_appointment does not exist'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 7. 插入一些示例数据（可选）
-- 线索跟进提醒示例数据
INSERT IGNORE INTO `t_lead_follow_reminder` (`reminder_id`, `lead_id`, `follow_time`, `reminder_time`, `reminder_type`, `reminder_minutes`, `reminder_status`, `reminder_content`, `create_user_id`) VALUES
(1, 1, '2025-07-23 10:00:00', '2025-07-23 09:30:00', 1, 30, 1, '提醒：需要跟进客户张三', 1),
(2, 2, '2025-07-23 14:00:00', '2025-07-23 13:30:00', 1, 30, 1, '提醒：需要跟进客户李四', 1);

-- 线索状态变更历史示例数据
INSERT IGNORE INTO `t_lead_status_history` (`history_id`, `lead_id`, `from_status`, `to_status`, `trigger_type`, `remark`, `create_name`) VALUES
(1, 1, 1, 2, 'FOLLOW_ADD', '添加跟进记录，自动流转到跟进中状态', '管理员'),
(2, 1, 2, 3, 'APPOINTMENT_CREATE', '创建预约记录，自动流转到已预约状态', '管理员'),
(3, 2, 1, 2, 'FOLLOW_ADD', '添加跟进记录，自动流转到跟进中状态', '管理员'),
(4, 2, 2, 6, 'MANUAL_CLOSE', '手动关闭线索：客户无意向', '管理员');

-- 7. 创建视图：线索跟进统计视图
CREATE OR REPLACE VIEW `v_lead_follow_stats` AS
SELECT 
    l.lead_id,
    l.customer_name,
    l.customer_phone,
    l.lead_status,
    l.lead_source,
    COUNT(lf.follow_id) as follow_count,
    MAX(lf.create_time) as last_follow_time,
    COUNT(CASE WHEN lf.follow_result_type = 1 THEN 1 END) as appointment_interested_count,
    COUNT(CASE WHEN lf.follow_result_type = 2 THEN 1 END) as need_follow_up_count,
    COUNT(CASE WHEN lf.follow_result_type = 3 THEN 1 END) as no_interest_count,
    COUNT(CASE WHEN lf.follow_result_type = 4 THEN 1 END) as record_only_count
FROM t_lead l
LEFT JOIN t_lead_follow lf ON l.lead_id = lf.lead_id AND lf.deleted_flag = 0
WHERE l.deleted_flag = 0
GROUP BY l.lead_id, l.customer_name, l.customer_phone, l.lead_status, l.lead_source;

-- 8. 创建视图：待跟进提醒视图
CREATE OR REPLACE VIEW `v_pending_follow_reminders` AS
SELECT 
    r.reminder_id,
    r.lead_id,
    r.follow_time,
    r.reminder_time,
    r.reminder_type,
    r.reminder_minutes,
    r.reminder_status,
    r.reminder_content,
    l.customer_name,
    l.customer_phone,
    l.lead_source,
    l.lead_status,
    CASE 
        WHEN r.reminder_type = 1 THEN '系统通知'
        WHEN r.reminder_type = 2 THEN '短信提醒'
        WHEN r.reminder_type = 3 THEN '邮件提醒'
        ELSE '未知'
    END as reminder_type_name,
    CASE 
        WHEN r.reminder_status = 1 THEN '待提醒'
        WHEN r.reminder_status = 2 THEN '已发送'
        WHEN r.reminder_status = 3 THEN '已取消'
        WHEN r.reminder_status = 4 THEN '已完成'
        ELSE '未知'
    END as reminder_status_name,
    CASE 
        WHEN r.follow_time < NOW() THEN 1
        ELSE 0
    END as overdue,
    TIMESTAMPDIFF(MINUTE, NOW(), r.follow_time) as minutes_to_follow
FROM t_lead_follow_reminder r
INNER JOIN t_lead l ON r.lead_id = l.lead_id
WHERE r.deleted_flag = 0 AND l.deleted_flag = 0;

-- 9. 创建索引优化查询性能
-- 线索表索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = DATABASE()
       AND TABLE_NAME = 't_lead'
       AND INDEX_NAME = 'idx_status_create_time') = 0,
    'ALTER TABLE `t_lead` ADD INDEX `idx_status_create_time` (`lead_status`, `create_time`)',
    'SELECT ''Index idx_status_create_time already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = DATABASE()
       AND TABLE_NAME = 't_lead'
       AND INDEX_NAME = 'idx_assigned_employee') = 0,
    'ALTER TABLE `t_lead` ADD INDEX `idx_assigned_employee` (`assigned_employee_id`)',
    'SELECT ''Index idx_assigned_employee already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 跟进记录表索引
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = DATABASE()
       AND TABLE_NAME = 't_lead_follow'
       AND INDEX_NAME = 'idx_lead_create_time') = 0,
    'ALTER TABLE `t_lead_follow` ADD INDEX `idx_lead_create_time` (`lead_id`, `create_time`)',
    'SELECT ''Index idx_lead_create_time already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = DATABASE()
       AND TABLE_NAME = 't_lead_follow'
       AND INDEX_NAME = 'idx_follow_result_type') = 0,
    'ALTER TABLE `t_lead_follow` ADD INDEX `idx_follow_result_type` (`follow_result_type`)',
    'SELECT ''Index idx_follow_result_type already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 预约表索引（如果表存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
     WHERE TABLE_SCHEMA = DATABASE()
       AND TABLE_NAME = 't_appointment') > 0,
    (SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
         WHERE TABLE_SCHEMA = DATABASE()
           AND TABLE_NAME = 't_appointment'
           AND INDEX_NAME = 'idx_status_date') = 0,
        'ALTER TABLE `t_appointment` ADD INDEX `idx_status_date` (`appointment_status`, `appointment_date`)',
        'SELECT ''Index idx_status_date already exists'' as message'
    )),
    'SELECT ''Table t_appointment does not exist'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 10. 创建存储过程：自动清理过期提醒
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS `sp_cleanup_expired_reminders`()
BEGIN
    -- 将超过7天未处理的提醒标记为已取消
    UPDATE t_lead_follow_reminder 
    SET reminder_status = 3, 
        update_time = NOW()
    WHERE reminder_status = 1 
      AND reminder_time < DATE_SUB(NOW(), INTERVAL 7 DAY)
      AND deleted_flag = 0;
      
    -- 记录清理日志
    SELECT CONCAT('清理了 ', ROW_COUNT(), ' 条过期提醒记录') as cleanup_result;
END //
DELIMITER ;

-- 11. 创建触发器：线索状态变更时自动记录历史
DELIMITER //
CREATE TRIGGER IF NOT EXISTS `tr_lead_status_change` 
AFTER UPDATE ON `t_lead`
FOR EACH ROW
BEGIN
    IF OLD.lead_status != NEW.lead_status THEN
        INSERT INTO t_lead_status_history (
            lead_id, 
            from_status, 
            to_status, 
            trigger_type, 
            remark, 
            create_name
        ) VALUES (
            NEW.lead_id,
            OLD.lead_status,
            NEW.lead_status,
            'AUTO_TRIGGER',
            CONCAT('状态从 ', OLD.lead_status, ' 变更为 ', NEW.lead_status),
            COALESCE((SELECT actual_name FROM t_employee WHERE employee_id = NEW.update_user_id), '系统')
        );
    END IF;
END //
DELIMITER ;

-- 完成脚本
SELECT '线索跟进增强功能数据库结构创建完成！' as result;

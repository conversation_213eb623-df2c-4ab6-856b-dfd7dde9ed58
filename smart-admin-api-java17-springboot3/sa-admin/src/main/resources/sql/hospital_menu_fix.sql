-- 医院营销系统菜单修复脚本
-- Author: 1024创新实验室-主任：卓大
-- Date: 2024-01-01 12:00:00

-- 1. 删除所有现有的医院菜单
DELETE FROM t_role_menu WHERE menu_id BETWEEN 5001 AND 5200;
DELETE FROM t_menu WHERE menu_id BETWEEN 5001 AND 5200;

-- 2. 重新创建医院营销系统菜单（二级菜单结构）
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES

-- 医院营销系统 - 一级菜单（作为所有医院功能的容器）
(5001, '医院营销系统', 1, 0, 50, '/hospital', NULL, 1, NULL, NULL, 'MedicineBoxOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 仪表盘
(5005, '仪表盘', 2, 5001, 10, '/hospital/dashboard', '/views/business/hospital/dashboard/dashboard-overview.vue', 2, NULL, 'hospital:dashboard:query', 'DashboardOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 线索管理
(5011, '线索列表', 2, 5001, 20, '/hospital/lead/list', '/views/business/hospital/lead/lead-list.vue', 2, NULL, 'hospital:lead:query', 'UnorderedListOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5012, '线索跟进', 2, 5001, 21, '/hospital/lead/follow', '/views/business/hospital/lead/lead-follow.vue', 2, NULL, 'hospital:lead:follow:query', 'CommentOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 预约管理
(5021, '预约列表', 2, 5001, 30, '/hospital/appointment/list', '/views/business/hospital/appointment/appointment-list.vue', 2, NULL, 'hospital:appointment:query', 'UnorderedListOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5022, '医生排班', 2, 5001, 31, '/hospital/appointment/schedule', '/views/business/hospital/appointment/doctor-schedule.vue', 2, NULL, 'hospital:schedule:query', 'ScheduleOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5023, '项目管理', 2, 5001, 32, '/hospital/appointment/project', '/views/business/hospital/project/project-list.vue', 2, NULL, 'hospital:project:query', 'ProjectOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 客户管理
(5031, '客户列表', 2, 5001, 40, '/hospital/customer/list', '/views/business/hospital/customer/customer-list.vue', 2, NULL, 'hospital:customer:query', 'UnorderedListOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5032, '病历管理', 2, 5001, 41, '/hospital/customer/medical', '/views/business/hospital/customer/medical-record-list.vue', 2, NULL, 'hospital:record:query', 'FileTextOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 知识库
(5071, '文档管理', 2, 5001, 70, '/hospital/knowledge/documents', '/views/business/hospital/knowledge/document-list.vue', 2, NULL, 'hospital:knowledge:document', 'FileTextOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5072, '分类管理', 2, 5001, 71, '/hospital/knowledge/categories', '/views/business/hospital/knowledge/category-list.vue', 2, NULL, 'hospital:knowledge:category', 'FolderOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5073, '标签管理', 2, 5001, 72, '/hospital/knowledge/tags', '/views/business/hospital/knowledge/tag-list.vue', 2, NULL, 'hospital:knowledge:tag', 'TagOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5074, '知识搜索', 2, 5001, 73, '/hospital/knowledge/search', '/views/business/hospital/knowledge/knowledge-search.vue', 2, NULL, 'hospital:knowledge:search', 'SearchOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5075, '知识共享', 2, 5001, 74, '/hospital/knowledge/sharing', '/views/business/hospital/knowledge/knowledge-sharing.vue', 2, NULL, 'hospital:knowledge:sharing', 'ShareAltOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 营销活动
(5081, '活动管理', 2, 5001, 80, '/hospital/marketing/campaigns', '/views/business/hospital/marketing/campaign-list.vue', 2, NULL, 'hospital:marketing:campaign', 'CalendarOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5082, '活动推广', 2, 5001, 81, '/hospital/marketing/promotion', '/views/business/hospital/marketing/promotion-list.vue', 2, NULL, 'hospital:marketing:promotion', 'SoundOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5083, '效果跟踪', 2, 5001, 82, '/hospital/marketing/tracking', '/views/business/hospital/marketing/effect-tracking.vue', 2, NULL, 'hospital:marketing:tracking', 'LineChartOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5084, 'ROI分析', 2, 5001, 83, '/hospital/marketing/roi-analysis', '/views/business/hospital/marketing/roi-analysis.vue', 2, NULL, 'hospital:marketing:roi', 'BarChartOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5085, '活动报表', 2, 5001, 84, '/hospital/marketing/reports', '/views/business/hospital/marketing/campaign-reports.vue', 2, NULL, 'hospital:marketing:reports', 'FileTextOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW());

-- 3. 重新配置角色权限关联
-- 超级管理员 (role_id = 1) - 全部权限
INSERT INTO t_role_menu (role_id, menu_id)
SELECT 1, menu_id FROM t_menu WHERE menu_id BETWEEN 5001 AND 5200;

-- 如果有其他角色需要医院权限，可以在这里添加
-- 例如：医院管理员角色
-- INSERT INTO t_role_menu (role_id, menu_id)
-- SELECT 医院管理员角色ID, menu_id FROM t_menu WHERE menu_id BETWEEN 5001 AND 5200;

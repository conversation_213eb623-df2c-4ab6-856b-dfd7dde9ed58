-- 创建预约状态字典配置脚本
-- Author: 1024创新实验室-主任：卓大
-- Date: 2025-07-23

-- 插入预约状态字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`) 
VALUES ('预约状态', 'APPOINTMENT_STATUS', '预约状态字典，用于预约管理', 0, NOW(), NOW());

-- 获取字典ID
SET @dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'APPOINTMENT_STATUS');

-- 插入预约状态字典数据
INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@dict_id, '1', '未到诊', '未到诊状态', 100, 0, NOW(), NOW()),
(@dict_id, '2', '已到诊', '已到诊状态', 90, 0, NOW(), NOW());

-- 查询验证创建结果
SELECT d.dict_name, d.dict_code, dd.data_value, dd.data_label 
FROM t_dict d 
LEFT JOIN t_dict_data dd ON d.dict_id = dd.dict_id 
WHERE d.dict_code = 'APPOINTMENT_STATUS'
ORDER BY dd.sort_order DESC;

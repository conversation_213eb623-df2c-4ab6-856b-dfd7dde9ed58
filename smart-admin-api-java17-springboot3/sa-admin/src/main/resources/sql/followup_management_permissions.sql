-- 回访管理系统菜单和权限配置
-- Author: 1024创新实验室-主任：卓大
-- Date: 2025-07-29 10:00:00

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 1. 菜单配置
-- ----------------------------

-- 回访管理主菜单
INSERT IGNORE INTO `t_menu` (`menu_id`, `menu_name`, `menu_type`, `parent_id`, `path`, `component`, `perms_type`, `api_perms`, `web_perms`, `icon`, `context_menu_id`, `frame_flag`, `frame_url`, `cache_flag`, `visible_flag`, `disabled_flag`, `sort`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES
(7000, '回访管理', 1, 6000, '/hospital/followup', NULL, 1, NULL, NULL, 'PhoneOutlined', NULL, 0, NULL, 0, 1, 0, 40, 1, NOW(), 1, NOW());

-- 回访记录管理菜单
INSERT IGNORE INTO `t_menu` (`menu_id`, `menu_name`, `menu_type`, `parent_id`, `path`, `component`, `perms_type`, `api_perms`, `web_perms`, `icon`, `context_menu_id`, `frame_flag`, `frame_url`, `cache_flag`, `visible_flag`, `disabled_flag`, `sort`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES
(7001, '回访记录', 2, 7000, '/hospital/followup/record', '/business/hospital/followup/record/FollowUpRecordList.vue', 1, NULL, NULL, 'FileTextOutlined', NULL, 0, NULL, 1, 1, 0, 10, 1, NOW(), 1, NOW());

-- 回访计划管理菜单
INSERT IGNORE INTO `t_menu` (`menu_id`, `menu_name`, `menu_type`, `parent_id`, `path`, `component`, `perms_type`, `api_perms`, `web_perms`, `icon`, `context_menu_id`, `frame_flag`, `frame_url`, `cache_flag`, `visible_flag`, `disabled_flag`, `sort`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES
(7002, '回访计划', 2, 7000, '/hospital/followup/plan', '/business/hospital/followup/plan/FollowUpPlanList.vue', 1, NULL, NULL, 'ScheduleOutlined', NULL, 0, NULL, 1, 1, 0, 20, 1, NOW(), 1, NOW());

-- 客户360视图菜单
INSERT IGNORE INTO `t_menu` (`menu_id`, `menu_name`, `menu_type`, `parent_id`, `path`, `component`, `perms_type`, `api_perms`, `web_perms`, `icon`, `context_menu_id`, `frame_flag`, `frame_url`, `cache_flag`, `visible_flag`, `disabled_flag`, `sort`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES
(7003, '客户360视图', 2, 7000, '/hospital/followup/customer360', '/business/hospital/followup/customer360/Customer360View.vue', 1, NULL, NULL, 'UserOutlined', NULL, 0, NULL, 1, 1, 0, 30, 1, NOW(), 1, NOW());

-- 治疗状态管理菜单
INSERT IGNORE INTO `t_menu` (`menu_id`, `menu_name`, `menu_type`, `parent_id`, `path`, `component`, `perms_type`, `api_perms`, `web_perms`, `icon`, `context_menu_id`, `frame_flag`, `frame_url`, `cache_flag`, `visible_flag`, `disabled_flag`, `sort`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES
(7004, '治疗状态管理', 2, 7000, '/hospital/followup/status', '/business/hospital/followup/status/TreatmentStatusList.vue', 1, NULL, NULL, 'HeartOutlined', NULL, 0, NULL, 1, 1, 0, 40, 1, NOW(), 1, NOW());

-- ----------------------------
-- 2. 功能权限点配置
-- ----------------------------

-- 回访记录权限点
INSERT IGNORE INTO `t_menu` (`menu_id`, `menu_name`, `menu_type`, `parent_id`, `path`, `component`, `perms_type`, `api_perms`, `web_perms`, `icon`, `context_menu_id`, `frame_flag`, `frame_url`, `cache_flag`, `visible_flag`, `disabled_flag`, `sort`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES
(7101, '回访记录查询', 3, 7001, NULL, NULL, 1, 'hospital:followup:record:query', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 10, 1, NOW(), 1, NOW()),
(7102, '回访记录新增', 3, 7001, NULL, NULL, 1, 'hospital:followup:record:add', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 20, 1, NOW(), 1, NOW()),
(7103, '回访记录更新', 3, 7001, NULL, NULL, 1, 'hospital:followup:record:update', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 30, 1, NOW(), 1, NOW()),
(7104, '回访记录删除', 3, 7001, NULL, NULL, 1, 'hospital:followup:record:delete', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 40, 1, NOW(), 1, NOW()),
(7105, '回访记录详情', 3, 7001, NULL, NULL, 1, 'hospital:followup:record:detail', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 50, 1, NOW(), 1, NOW());

-- 回访计划权限点
INSERT IGNORE INTO `t_menu` (`menu_id`, `menu_name`, `menu_type`, `parent_id`, `path`, `component`, `perms_type`, `api_perms`, `web_perms`, `icon`, `context_menu_id`, `frame_flag`, `frame_url`, `cache_flag`, `visible_flag`, `disabled_flag`, `sort`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES
(7111, '回访计划查询', 3, 7002, NULL, NULL, 1, 'hospital:followup:plan:query', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 10, 1, NOW(), 1, NOW()),
(7112, '回访计划新增', 3, 7002, NULL, NULL, 1, 'hospital:followup:plan:add', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 20, 1, NOW(), 1, NOW()),
(7113, '回访计划更新', 3, 7002, NULL, NULL, 1, 'hospital:followup:plan:update', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 30, 1, NOW(), 1, NOW()),
(7114, '回访计划删除', 3, 7002, NULL, NULL, 1, 'hospital:followup:plan:delete', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 40, 1, NOW(), 1, NOW()),
(7115, '回访计划详情', 3, 7002, NULL, NULL, 1, 'hospital:followup:plan:detail', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 50, 1, NOW(), 1, NOW()),
(7116, '回访计划启用/暂停', 3, 7002, NULL, NULL, 1, 'hospital:followup:plan:status', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 60, 1, NOW(), 1, NOW());

-- 客户360视图权限点
INSERT IGNORE INTO `t_menu` (`menu_id`, `menu_name`, `menu_type`, `parent_id`, `path`, `component`, `perms_type`, `api_perms`, `web_perms`, `icon`, `context_menu_id`, `frame_flag`, `frame_url`, `cache_flag`, `visible_flag`, `disabled_flag`, `sort`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES
(7121, '客户360视图查询', 3, 7003, NULL, NULL, 1, 'hospital:followup:customer360:query', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 10, 1, NOW(), 1, NOW()),
(7122, '客户基本信息查看', 3, 7003, NULL, NULL, 1, 'hospital:followup:customer360:basic', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 20, 1, NOW(), 1, NOW()),
(7123, '客户治疗历史查看', 3, 7003, NULL, NULL, 1, 'hospital:followup:customer360:history', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 30, 1, NOW(), 1, NOW()),
(7124, '客户回访记录查看', 3, 7003, NULL, NULL, 1, 'hospital:followup:customer360:followup', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 40, 1, NOW(), 1, NOW());

-- 治疗状态管理权限点
INSERT IGNORE INTO `t_menu` (`menu_id`, `menu_name`, `menu_type`, `parent_id`, `path`, `component`, `perms_type`, `api_perms`, `web_perms`, `icon`, `context_menu_id`, `frame_flag`, `frame_url`, `cache_flag`, `visible_flag`, `disabled_flag`, `sort`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES
(7131, '治疗状态查询', 3, 7004, NULL, NULL, 1, 'hospital:followup:status:query', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 10, 1, NOW(), 1, NOW()),
(7132, '治疗状态更新', 3, 7004, NULL, NULL, 1, 'hospital:followup:status:update', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 20, 1, NOW(), 1, NOW()),
(7133, '治疗状态历史查看', 3, 7004, NULL, NULL, 1, 'hospital:followup:status:history', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 30, 1, NOW(), 1, NOW());

-- ----------------------------
-- 3. 角色权限分配
-- ----------------------------

-- 查询现有角色ID
SET @admin_role_id = (SELECT role_id FROM t_role WHERE role_code = 'ADMIN' LIMIT 1);
SET @doctor_role_id = (SELECT role_id FROM t_role WHERE role_code = 'DOCTOR' LIMIT 1);
SET @assistant_role_id = (SELECT role_id FROM t_role WHERE role_code = 'ASSISTANT' LIMIT 1);
SET @receptionist_role_id = (SELECT role_id FROM t_role WHERE role_code = 'RECEPTIONIST' LIMIT 1);

-- 管理员角色权限（全部权限）
INSERT IGNORE INTO `t_role_menu` (`role_id`, `menu_id`, `create_time`, `update_time`) VALUES
-- 主菜单和页面菜单
(@admin_role_id, 7000, NOW(), NOW()),
(@admin_role_id, 7001, NOW(), NOW()),
(@admin_role_id, 7002, NOW(), NOW()),
(@admin_role_id, 7003, NOW(), NOW()),
(@admin_role_id, 7004, NOW(), NOW()),
-- 回访记录权限
(@admin_role_id, 7101, NOW(), NOW()),
(@admin_role_id, 7102, NOW(), NOW()),
(@admin_role_id, 7103, NOW(), NOW()),
(@admin_role_id, 7104, NOW(), NOW()),
(@admin_role_id, 7105, NOW(), NOW()),
-- 回访计划权限
(@admin_role_id, 7111, NOW(), NOW()),
(@admin_role_id, 7112, NOW(), NOW()),
(@admin_role_id, 7113, NOW(), NOW()),
(@admin_role_id, 7114, NOW(), NOW()),
(@admin_role_id, 7115, NOW(), NOW()),
(@admin_role_id, 7116, NOW(), NOW()),
-- 客户360视图权限
(@admin_role_id, 7121, NOW(), NOW()),
(@admin_role_id, 7122, NOW(), NOW()),
(@admin_role_id, 7123, NOW(), NOW()),
(@admin_role_id, 7124, NOW(), NOW()),
-- 治疗状态管理权限
(@admin_role_id, 7131, NOW(), NOW()),
(@admin_role_id, 7132, NOW(), NOW()),
(@admin_role_id, 7133, NOW(), NOW());

-- 医生角色权限（查看和部分管理权限）
INSERT IGNORE INTO `t_role_menu` (`role_id`, `menu_id`, `create_time`, `update_time`) VALUES
-- 主菜单和页面菜单
(@doctor_role_id, 7000, NOW(), NOW()),
(@doctor_role_id, 7001, NOW(), NOW()),
(@doctor_role_id, 7002, NOW(), NOW()),
(@doctor_role_id, 7003, NOW(), NOW()),
(@doctor_role_id, 7004, NOW(), NOW()),
-- 回访记录权限（查看、新增、更新、详情）
(@doctor_role_id, 7101, NOW(), NOW()),
(@doctor_role_id, 7102, NOW(), NOW()),
(@doctor_role_id, 7103, NOW(), NOW()),
(@doctor_role_id, 7105, NOW(), NOW()),
-- 回访计划权限（查看、新增、更新、详情、状态管理）
(@doctor_role_id, 7111, NOW(), NOW()),
(@doctor_role_id, 7112, NOW(), NOW()),
(@doctor_role_id, 7113, NOW(), NOW()),
(@doctor_role_id, 7115, NOW(), NOW()),
(@doctor_role_id, 7116, NOW(), NOW()),
-- 客户360视图权限（全部查看权限）
(@doctor_role_id, 7121, NOW(), NOW()),
(@doctor_role_id, 7122, NOW(), NOW()),
(@doctor_role_id, 7123, NOW(), NOW()),
(@doctor_role_id, 7124, NOW(), NOW()),
-- 治疗状态管理权限（查看、更新、历史）
(@doctor_role_id, 7131, NOW(), NOW()),
(@doctor_role_id, 7132, NOW(), NOW()),
(@doctor_role_id, 7133, NOW(), NOW());

-- 治疗助理角色权限（主要执行权限）
INSERT IGNORE INTO `t_role_menu` (`role_id`, `menu_id`, `create_time`, `update_time`) VALUES
-- 主菜单和页面菜单
(@assistant_role_id, 7000, NOW(), NOW()),
(@assistant_role_id, 7001, NOW(), NOW()),
(@assistant_role_id, 7002, NOW(), NOW()),
(@assistant_role_id, 7003, NOW(), NOW()),
-- 回访记录权限（查看、新增、更新、详情）
(@assistant_role_id, 7101, NOW(), NOW()),
(@assistant_role_id, 7102, NOW(), NOW()),
(@assistant_role_id, 7103, NOW(), NOW()),
(@assistant_role_id, 7105, NOW(), NOW()),
-- 回访计划权限（查看、详情）
(@assistant_role_id, 7111, NOW(), NOW()),
(@assistant_role_id, 7115, NOW(), NOW()),
-- 客户360视图权限（基本信息、回访记录）
(@assistant_role_id, 7121, NOW(), NOW()),
(@assistant_role_id, 7122, NOW(), NOW()),
(@assistant_role_id, 7124, NOW(), NOW());

-- 前台角色权限（基础查看权限）
INSERT IGNORE INTO `t_role_menu` (`role_id`, `menu_id`, `create_time`, `update_time`) VALUES
-- 主菜单和页面菜单
(@receptionist_role_id, 7000, NOW(), NOW()),
(@receptionist_role_id, 7001, NOW(), NOW()),
(@receptionist_role_id, 7003, NOW(), NOW()),
-- 回访记录权限（查看、详情）
(@receptionist_role_id, 7101, NOW(), NOW()),
(@receptionist_role_id, 7105, NOW(), NOW()),
-- 客户360视图权限（基本信息）
(@receptionist_role_id, 7121, NOW(), NOW()),
(@receptionist_role_id, 7122, NOW(), NOW());

-- ----------------------------
-- 4. 数据权限配置
-- ----------------------------

-- 回访记录数据权限
INSERT IGNORE INTO `t_role_data_scope` (`data_scope_type`, `view_type`, `role_id`, `create_time`, `update_time`) VALUES
-- 管理员：全部数据
(7, 10, @admin_role_id, NOW(), NOW()),
-- 医生：本部门数据
(7, 1, @doctor_role_id, NOW(), NOW()),
-- 治疗助理：本人数据
(7, 0, @assistant_role_id, NOW(), NOW()),
-- 前台：本部门数据
(7, 1, @receptionist_role_id, NOW(), NOW());

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

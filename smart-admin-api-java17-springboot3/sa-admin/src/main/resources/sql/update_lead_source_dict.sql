-- 更新线索来源字典数据脚本
-- Author: 1024创新实验室-主任：卓大
-- Date: 2025-07-22

-- 获取线索来源字典ID
SET @dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'LEAD_SOURCE');

-- 删除现有的线索来源字典数据
DELETE FROM t_dict_data WHERE dict_id = @dict_id;

-- 插入新的线索来源字典数据（按重要性排序）
INSERT INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@dict_id, 'SHANGWUTONG', '商务通', '商务通在线客服系统', 100, 0, NOW(), NOW()),
(@dict_id, 'BAIDU', '百度', '百度搜索引擎和百度推广', 90, 0, NOW(), NOW()),
(@dict_id, 'DOUYIN', '抖音', '抖音短视频平台', 80, 0, NOW(), NOW()),
(@dict_id, 'KUAISHOU', '快手', '快手短视频平台', 70, 0, NOW(), NOW());

-- 查询验证更新结果
SELECT d.dict_name, d.dict_code, dd.data_value, dd.data_label, dd.sort_order
FROM t_dict d 
LEFT JOIN t_dict_data dd ON d.dict_id = dd.dict_id 
WHERE d.dict_code = 'LEAD_SOURCE'
ORDER BY dd.sort_order DESC;

-- 创建症状字典配置脚本
-- Author: 1024创新实验室-主任：卓大
-- Date: 2025-07-22

-- 插入症状字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`) 
VALUES ('症状类型', 'SYMPTOM_TYPE', '症状类型字典，用于线索管理模块的症状字段', 0, NOW(), NOW());

-- 获取字典ID
SET @dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'SYMPTOM_TYPE');

-- 插入症状字典数据（按字母顺序排序）
INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@dict_id, 'DEPRESSION', '抑郁症', '抑郁症相关症状', 290, 0, NOW(), NOW()),
(@dict_id, 'SEXUAL_ORIENTATION_DISORDER', '性取向异常', '性取向异常相关症状', 280, 0, NOW(), NOW()),
(@dict_id, 'AUTISM', '自闭症', '自闭症相关症状', 270, 0, NOW(), NOW()),
(@dict_id, 'EPILEPSY', '癫痫', '癫痫相关症状', 260, 0, NOW(), NOW()),
(@dict_id, 'TICS', '抽动症', '抽动症相关症状', 250, 0, NOW(), NOW()),
(@dict_id, 'INTERNET_ADDICTION', '网瘾', '网络成瘾相关症状', 240, 0, NOW(), NOW()),
(@dict_id, 'ALCOHOL_ADDICTION', '酒瘾', '酒精成瘾相关症状', 230, 0, NOW(), NOW()),
(@dict_id, 'HYPOCHONDRIASIS', '疑病症', '疑病症相关症状', 220, 0, NOW(), NOW()),
(@dict_id, 'BIPOLAR_DISORDER', '双相情感障碍', '双相情感障碍相关症状', 210, 0, NOW(), NOW()),
(@dict_id, 'PERSONALITY_DISORDER', '人格障碍', '人格障碍相关症状', 200, 0, NOW(), NOW()),
(@dict_id, 'MENIERES_DISEASE', '美尼尔综合症', '美尼尔综合症相关症状', 190, 0, NOW(), NOW()),
(@dict_id, 'NEUROGENIC_HEADACHE', '神经性头痛', '神经性头痛相关症状', 180, 0, NOW(), NOW()),
(@dict_id, 'STRESS_DISORDER', '应激障碍', '应激障碍相关症状', 170, 0, NOW(), NOW()),
(@dict_id, 'SOMATIZATION_DISORDER', '躯体化障碍', '躯体化障碍相关症状', 160, 0, NOW(), NOW()),
(@dict_id, 'MOOD_DISORDER', '心境障碍', '心境障碍相关症状', 150, 0, NOW(), NOW()),
(@dict_id, 'HYSTERIA', '癔症', '癔症相关症状', 140, 0, NOW(), NOW()),
(@dict_id, 'MENOPAUSE_SYNDROME', '更年期综合症', '更年期综合症相关症状', 130, 0, NOW(), NOW()),
(@dict_id, 'ADHD', '多动症', '注意力缺陷多动障碍相关症状', 120, 0, NOW(), NOW()),
(@dict_id, 'ANXIETY', '焦虑症', '焦虑症相关症状', 110, 0, NOW(), NOW()),
(@dict_id, 'INSOMNIA', '失眠症', '失眠症相关症状', 100, 0, NOW(), NOW()),
(@dict_id, 'SCHIZOPHRENIA', '精神分裂症', '精神分裂症相关症状', 90, 0, NOW(), NOW()),
(@dict_id, 'PSYCHOLOGICAL_DISORDER', '心理障碍', '心理障碍相关症状', 80, 0, NOW(), NOW()),
(@dict_id, 'MENTAL_DISORDER', '精神障碍', '精神障碍相关症状', 70, 0, NOW(), NOW()),
(@dict_id, 'MANIA', '躁狂症', '躁狂症相关症状', 60, 0, NOW(), NOW()),
(@dict_id, 'NEURASTHENIA', '神经衰弱', '神经衰弱相关症状', 50, 0, NOW(), NOW()),
(@dict_id, 'SLEEP_DISORDER', '睡眠障碍', '睡眠障碍相关症状', 40, 0, NOW(), NOW()),
(@dict_id, 'OCD', '强迫症', '强迫症相关症状', 30, 0, NOW(), NOW()),
(@dict_id, 'PSYCHOLOGICAL_DISORDER_SYNDROME', '心理障碍症', '心理障碍症相关症状', 25, 0, NOW(), NOW()),
(@dict_id, 'PHOBIA', '恐惧症', '恐惧症相关症状', 20, 0, NOW(), NOW()),
(@dict_id, 'AUTONOMIC_DYSFUNCTION', '植物神经紊乱', '植物神经紊乱相关症状', 15, 0, NOW(), NOW()),
(@dict_id, 'NEUROSIS', '神经官能症', '神经官能症相关症状', 12, 0, NOW(), NOW()),
(@dict_id, 'HEADACHE_DIZZINESS', '头痛头晕', '头痛头晕相关症状', 10, 0, NOW(), NOW()),
(@dict_id, 'SCHOOL_PHOBIA', '厌学症', '厌学症相关症状', 5, 0, NOW(), NOW());

-- 查询验证创建结果
SELECT d.dict_name, d.dict_code, COUNT(dd.dict_data_id) as symptom_count
FROM t_dict d 
LEFT JOIN t_dict_data dd ON d.dict_id = dd.dict_id 
WHERE d.dict_code = 'SYMPTOM_TYPE'
GROUP BY d.dict_id;

-- 线索归属变更管理测试数据
-- 作者: 1024创新实验室-主任：卓大
-- 日期: 2025-07-30
-- 版本: v1.0

-- 注意：此脚本仅用于测试环境，生产环境请谨慎使用

-- 1. 插入测试线索数据（如果不存在）
INSERT IGNORE INTO `t_lead` (
  `lead_id`, `lead_source`, `customer_name`, `customer_phone`, `work_phone`, `customer_wechat`, 
  `region`, `gender`, `age`, `lead_status`, `lead_quality`, `symptom`, `remark`, `chat_record`,
  `assigned_employee_id`, `assigned_employee_name`, `department_id`, `department_name`,
  `create_time`, `create_user_id`, `create_user_name`, `update_time`, `update_user_id`, `update_user_name`
) VALUES
(1001, 1, '张三', '13800138001', 'WX001', 'zhangsan_wx', '北京市朝阳区', 1, 35, 1, 1, '[\"头痛\",\"失眠\"]', '客户咨询头痛问题', '客户通过微信咨询，表示经常头痛失眠', 44, '善逸', 26, '客服部', NOW(), 1, 'admin', NOW(), 1, 'admin'),
(1002, 2, '李四', '13800138002', 'WX002', 'lisi_wx', '上海市浦东新区', 2, 28, 1, 2, '[\"胃痛\",\"消化不良\"]', '客户咨询胃部不适', '客户电话咨询，胃部经常不适', 44, '善逸', 26, '客服部', NOW(), 1, 'admin', NOW(), 1, 'admin'),
(1003, 3, '王五', '13800138003', 'WX003', 'wangwu_wx', '广州市天河区', 1, 42, 2, 1, '[\"腰痛\",\"关节痛\"]', '客户咨询腰痛问题', '客户网站留言，腰痛严重影响工作', 45, '伊之助', 26, '客服部', NOW(), 1, 'admin', NOW(), 1, 'admin'),
(1004, 1, '赵六', '13800138004', 'WX004', 'zhaoliu_wx', '深圳市南山区', 1, 38, 1, 3, '[\"高血压\",\"头晕\"]', '客户咨询高血压', '客户微信咨询，血压偏高需要调理', 44, '善逸', 26, '客服部', NOW(), 1, 'admin', NOW(), 1, 'admin');

-- 2. 插入测试变更申请数据
INSERT IGNORE INTO `t_lead_ownership_change_request` (
  `request_id`, `lead_id`, `customer_name`, `customer_phone`, 
  `original_assigned_employee_id`, `original_assigned_employee_name`,
  `target_assigned_employee_id`, `target_assigned_employee_name`,
  `change_reason`, `request_status`, 
  `department_manager_id`, `department_manager_name`,
  `approve_time`, `approve_user_id`, `approve_user_name`, `approve_remark`,
  `create_time`, `create_user_id`, `create_user_name`, 
  `update_time`, `update_user_id`, `update_user_name`
) VALUES
-- 待审批的申请
(1, 1001, '张三', '13800138001', 44, '善逸', 45, '伊之助', '客户要求更换跟进人员，希望有更专业的服务', 1, 1, 'admin', NULL, NULL, NULL, NULL, NOW(), 44, '善逸', NOW(), 44, '善逸'),
-- 已同意的申请
(2, 1002, '李四', '13800138002', 44, '善逸', 45, '伊之助', '客户病情复杂，需要更有经验的跟进人员', 2, 1, 'admin', NOW(), 1, 'admin', '同意变更，伊之助在胃病方面更有经验', NOW() - INTERVAL 1 DAY, 44, '善逸', NOW(), 1, 'admin'),
-- 已拒绝的申请
(3, 1003, '王五', '13800138003', 45, '伊之助', 44, '善逸', '个人原因，希望更换跟进人员', 3, 1, 'admin', NOW() - INTERVAL 2 HOUR, 1, 'admin', '当前跟进人员专业对口，不建议更换', NOW() - INTERVAL 1 DAY, 45, '伊之助', NOW() - INTERVAL 2 HOUR, 1, 'admin'),
-- 已撤销的申请
(4, 1004, '赵六', '13800138004', 44, '善逸', 45, '伊之助', '客户投诉服务态度', 4, 1, 'admin', NULL, NULL, NULL, NULL, NOW() - INTERVAL 3 DAY, 44, '善逸', NOW() - INTERVAL 2 DAY, 44, '善逸');

-- 3. 插入测试变更历史数据
INSERT IGNORE INTO `t_lead_ownership_change_history` (
  `history_id`, `lead_id`, `request_id`, `change_type`,
  `original_assigned_employee_id`, `original_assigned_employee_name`,
  `new_assigned_employee_id`, `new_assigned_employee_name`,
  `change_reason`, `change_remark`,
  `change_time`, `operator_id`, `operator_name`
) VALUES
-- 申请1的提交记录
(1, 1001, 1, 1, 44, '善逸', 45, '伊之助', '客户要求更换跟进人员，希望有更专业的服务', '申请已提交，等待审批', NOW(), 44, '善逸'),

-- 申请2的完整流程
(2, 1002, 2, 1, 44, '善逸', 45, '伊之助', '客户病情复杂，需要更有经验的跟进人员', '申请已提交，等待审批', NOW() - INTERVAL 1 DAY, 44, '善逸'),
(3, 1002, 2, 2, 44, '善逸', 45, '伊之助', '客户病情复杂，需要更有经验的跟进人员', '审批通过，线索归属已变更', NOW(), 1, 'admin'),

-- 申请3的完整流程
(4, 1003, 3, 1, 45, '伊之助', 44, '善逸', '个人原因，希望更换跟进人员', '申请已提交，等待审批', NOW() - INTERVAL 1 DAY, 45, '伊之助'),
(5, 1003, 3, 3, 45, '伊之助', 44, '善逸', '个人原因，希望更换跟进人员', '审批拒绝：当前跟进人员专业对口，不建议更换', NOW() - INTERVAL 2 HOUR, 1, 'admin'),

-- 申请4的流程
(6, 1004, 4, 1, 44, '善逸', 45, '伊之助', '客户投诉服务态度', '申请已提交，等待审批', NOW() - INTERVAL 3 DAY, 44, '善逸'),
(7, 1004, 4, 4, 44, '善逸', 45, '伊之助', '客户投诉服务态度', '申请已撤销', NOW() - INTERVAL 2 DAY, 44, '善逸'),

-- 系统变更记录示例
(8, 1002, NULL, 5, 44, '善逸', 45, '伊之助', '系统自动分配', '根据审批结果自动变更线索归属', NOW(), 1, 'admin');

-- 4. 更新线索表中已变更的归属信息（申请2已通过）
UPDATE `t_lead` SET 
  `assigned_employee_id` = 45,
  `assigned_employee_name` = '伊之助',
  `update_time` = NOW(),
  `update_user_id` = 1,
  `update_user_name` = 'admin'
WHERE `lead_id` = 1002;

-- 5. 验证数据插入结果
SELECT '=== 线索归属变更申请表数据 ===' as info;
SELECT request_id, customer_name, customer_phone, request_status, 
       original_assigned_employee_name, target_assigned_employee_name, 
       create_time, approve_time
FROM t_lead_ownership_change_request 
ORDER BY request_id;

SELECT '=== 线索归属变更历史表数据 ===' as info;
SELECT history_id, lead_id, request_id, change_type, 
       original_assigned_employee_name, new_assigned_employee_name,
       change_time, operator_name
FROM t_lead_ownership_change_history 
ORDER BY history_id;

SELECT '=== 线索表相关数据 ===' as info;
SELECT lead_id, customer_name, customer_phone, assigned_employee_name, lead_status
FROM t_lead 
WHERE lead_id IN (1001, 1002, 1003, 1004)
ORDER BY lead_id;

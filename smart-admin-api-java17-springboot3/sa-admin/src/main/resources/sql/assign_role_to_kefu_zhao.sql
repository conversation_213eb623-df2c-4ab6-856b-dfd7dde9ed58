-- 为客服赵分配客服专员角色
-- 确保客服赵有正确的权限

-- 1. 查看所有员工
SELECT employee_id, actual_name, login_name FROM t_employee;

-- 2. 查看所有角色
SELECT role_id, role_name, role_code FROM t_role;

-- 3. 为客服赵分配客服专员角色（假设客服赵的employee_id是某个值，客服专员角色id是1009）
-- 首先删除可能存在的角色分配
DELETE FROM t_role_employee WHERE employee_id IN (
    SELECT employee_id FROM t_employee WHERE login_name = 'kefu_zhao'
);

-- 然后重新分配客服专员角色
INSERT INTO t_role_employee (employee_id, role_id, create_time, update_time)
SELECT e.employee_id, 1009, NOW(), NOW()
FROM t_employee e
WHERE e.login_name = 'kefu_zhao';

-- 4. 验证角色分配
SELECT 
    e.employee_id, 
    e.actual_name, 
    e.login_name, 
    r.role_id, 
    r.role_name 
FROM t_employee e 
LEFT JOIN t_role_employee re ON e.employee_id = re.employee_id 
LEFT JOIN t_role r ON re.role_id = r.role_id 
WHERE e.login_name = 'kefu_zhao';

-- 5. 验证权限
SELECT 
    r.role_id,
    r.role_name,
    m.menu_id,
    m.menu_name,
    m.web_perms
FROM t_role r
JOIN t_role_menu rm ON r.role_id = rm.role_id
JOIN t_menu m ON rm.menu_id = m.menu_id
WHERE r.role_id = 1009 AND m.web_perms LIKE '%hospital:lead:follow%'
ORDER BY m.menu_id;

-- Hospital 模块数据权限初始化脚本
-- 为现有角色添加 hospital 模块的数据权限配置

-- 删除已存在的 hospital 模块数据权限配置（如果有的话）
DELETE FROM t_role_data_scope WHERE data_scope_type IN (2, 3);

-- 为超级管理员角色（role_id = 1）添加全部数据权限
INSERT INTO t_role_data_scope (data_scope_type, view_type, role_id, create_time, update_time) VALUES
-- 线索数据权限：全部数据
(2, 1, 1, NOW(), NOW()),
-- 客户数据权限：全部数据
(3, 1, 1, NOW(), NOW());

-- 为部门管理员角色添加部门及下属部门数据权限
-- 注意：这里需要根据实际的角色ID进行调整
INSERT INTO t_role_data_scope (data_scope_type, view_type, role_id, create_time, update_time) 
SELECT 2, 2, role_id, NOW(), NOW() FROM t_role WHERE role_name LIKE '%管理%' AND role_id != 1
UNION ALL
SELECT 3, 2, role_id, NOW(), NOW() FROM t_role WHERE role_name LIKE '%管理%' AND role_id != 1;

-- 为普通员工角色添加本人数据权限
-- 注意：这里需要根据实际的角色ID进行调整
INSERT INTO t_role_data_scope (data_scope_type, view_type, role_id, create_time, update_time) 
SELECT 2, 4, role_id, NOW(), NOW() FROM t_role WHERE role_name LIKE '%员工%' OR role_name LIKE '%客服%'
UNION ALL
SELECT 3, 4, role_id, NOW(), NOW() FROM t_role WHERE role_name LIKE '%员工%' OR role_name LIKE '%客服%';

-- 为部门主管角色添加本部门数据权限
INSERT INTO t_role_data_scope (data_scope_type, view_type, role_id, create_time, update_time) 
SELECT 2, 3, role_id, NOW(), NOW() FROM t_role WHERE role_name LIKE '%主管%'
UNION ALL
SELECT 3, 3, role_id, NOW(), NOW() FROM t_role WHERE role_name LIKE '%主管%';

-- 查看配置结果
SELECT 
    r.role_name,
    rds.data_scope_type,
    CASE rds.data_scope_type 
        WHEN 2 THEN '线索'
        WHEN 3 THEN '客户'
        ELSE '其他'
    END AS data_scope_name,
    rds.view_type,
    CASE rds.view_type 
        WHEN 1 THEN '全部数据'
        WHEN 2 THEN '本部门及下属部门'
        WHEN 3 THEN '本部门'
        WHEN 4 THEN '仅本人'
        ELSE '未知'
    END AS view_type_name
FROM t_role_data_scope rds
JOIN t_role r ON rds.role_id = r.role_id
WHERE rds.data_scope_type IN (2, 3)
ORDER BY r.role_name, rds.data_scope_type;

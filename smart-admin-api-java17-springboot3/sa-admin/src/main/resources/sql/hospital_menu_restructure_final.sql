-- 医院营销系统菜单结构重构脚本 - 最终版
-- 移除"医院营销系统"顶级主菜单，将二级菜单提升为主菜单
-- Author: 1024创新实验室-主任：卓大
-- Date: 2025-07-23 13:40:00

-- 1. 删除所有现有的医院菜单和权限关联
DELETE FROM t_role_menu WHERE menu_id BETWEEN 5001 AND 5300;
DELETE FROM t_menu WHERE menu_id BETWEEN 5001 AND 5300;

-- 2. 创建新的菜单结构（三个主菜单）
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES

-- 线索管理 (主菜单)
(5001, '线索管理', 1, 0, 10, '/lead', NULL, 1, NULL, NULL, 'UserAddOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5002, '线索列表', 2, 5001, 1, '/lead/list', NULL, 1, 'hospital:lead:query', NULL, 'UnorderedListOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5003, '线索跟进', 2, 5001, 2, '/lead/follow', NULL, 1, 'hospital:lead:follow:query', NULL, 'CommentOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 预约管理 (主菜单)
(5010, '预约管理', 1, 0, 20, '/appointment', NULL, 1, NULL, NULL, 'CalendarOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5011, '预约列表', 2, 5010, 1, '/appointment/list', NULL, 1, 'hospital:appointment:query', NULL, 'UnorderedListOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5012, '预约统计', 2, 5010, 2, '/appointment/statistics', NULL, 1, 'hospital:appointment:statistics', NULL, 'BarChartOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 数据分析 (主菜单)
(5020, '数据分析', 1, 0, 30, '/analytics', NULL, 1, NULL, NULL, 'DashboardOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5021, '仪表盘', 2, 5020, 1, '/analytics/dashboard', NULL, 1, 'hospital:dashboard:query', NULL, 'BarChartOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 3. 创建API权限点
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES

-- 线索管理API权限
(5101, '线索查询', 3, 5002, 1, NULL, NULL, 2, 'hospital:lead:query', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5102, '线索新增', 3, 5002, 2, NULL, NULL, 2, 'hospital:lead:add', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5103, '线索编辑', 3, 5002, 3, NULL, NULL, 2, 'hospital:lead:edit', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5104, '线索删除', 3, 5002, 4, NULL, NULL, 2, 'hospital:lead:delete', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5105, '线索导出', 3, 5002, 5, NULL, NULL, 2, 'hospital:lead:export', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 线索跟进API权限
(5111, '跟进查询', 3, 5003, 1, NULL, NULL, 2, 'hospital:lead:follow:query', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5112, '跟进新增', 3, 5003, 2, NULL, NULL, 2, 'hospital:lead:follow:add', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5113, '跟进编辑', 3, 5003, 3, NULL, NULL, 2, 'hospital:lead:follow:edit', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5114, '跟进删除', 3, 5003, 4, NULL, NULL, 2, 'hospital:lead:follow:delete', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 预约管理API权限
(5121, '预约查询', 3, 5011, 1, NULL, NULL, 2, 'hospital:appointment:query', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5122, '预约新增', 3, 5011, 2, NULL, NULL, 2, 'hospital:appointment:add', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5123, '预约编辑', 3, 5011, 3, NULL, NULL, 2, 'hospital:appointment:edit', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5124, '预约删除', 3, 5011, 4, NULL, NULL, 2, 'hospital:appointment:delete', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5125, '确认到诊', 3, 5011, 5, NULL, NULL, 2, 'hospital:appointment:arrive', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 预约统计API权限
(5131, '预约统计查询', 3, 5012, 1, NULL, NULL, 2, 'hospital:appointment:statistics', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 仪表盘API权限
(5141, '仪表盘查询', 3, 5021, 1, NULL, NULL, 2, 'hospital:dashboard:query', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5142, '仪表盘统计', 3, 5021, 2, NULL, NULL, 2, 'hospital:dashboard:statistics', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 4. 为超级管理员分配所有权限
INSERT INTO t_role_menu (role_id, menu_id) VALUES
-- 主菜单
(1, 5001), (1, 5010), (1, 5020),
-- 二级菜单
(1, 5002), (1, 5003), (1, 5011), (1, 5012), (1, 5021),
-- API权限
(1, 5101), (1, 5102), (1, 5103), (1, 5104), (1, 5105),
(1, 5111), (1, 5112), (1, 5113), (1, 5114),
(1, 5121), (1, 5122), (1, 5123), (1, 5124), (1, 5125),
(1, 5131), (1, 5141), (1, 5142);

-- 5. 验证菜单结构
SELECT 
    m.menu_id,
    m.menu_name,
    m.menu_type,
    m.parent_id,
    m.path,
    m.icon,
    m.sort,
    CASE m.menu_type 
        WHEN 1 THEN '目录' 
        WHEN 2 THEN '菜单' 
        WHEN 3 THEN '功能点' 
    END as menu_type_name
FROM t_menu m 
WHERE m.menu_id BETWEEN 5001 AND 5200 
    AND m.deleted_flag = 0
ORDER BY m.parent_id, m.sort;

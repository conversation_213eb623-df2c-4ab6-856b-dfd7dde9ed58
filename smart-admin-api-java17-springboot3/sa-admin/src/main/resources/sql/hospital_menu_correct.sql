-- 医院营销系统正确菜单结构脚本
-- Author: 1024创新实验室-主任：卓大
-- Date: 2024-01-01 12:00:00

-- 1. 删除所有现有的医院菜单
DELETE FROM t_role_menu WHERE menu_id BETWEEN 5001 AND 5300;
DELETE FROM t_menu WHERE menu_id BETWEEN 5001 AND 5300;

-- 2. 创建正确的医院菜单结构（多个一级菜单）
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES

-- 仪表盘 (一级菜单)
(5001, '仪表盘', 1, 0, 51, '/dashboard', NULL, 1, NULL, NULL, 'DashboardOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5002, '数据概览', 2, 5001, 10, '/dashboard/overview', '/views/business/hospital/dashboard/dashboard-overview.vue', 2, NULL, 'hospital:dashboard:query', 'BarChartOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 线索管理 (一级菜单)
(5010, '线索管理', 1, 0, 52, '/lead', NULL, 1, NULL, NULL, 'UserAddOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5011, '线索列表', 2, 5010, 10, '/lead/list', '/views/business/hospital/lead/lead-list.vue', 2, NULL, 'hospital:lead:query', 'UnorderedListOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5012, '线索跟进', 2, 5010, 20, '/lead/follow', '/views/business/hospital/lead/lead-follow.vue', 2, NULL, 'hospital:lead:follow:query', 'CommentOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 预约管理 (一级菜单)
(5020, '预约管理', 1, 0, 53, '/appointment', NULL, 1, NULL, NULL, 'CalendarOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5021, '预约列表', 2, 5020, 10, '/appointment/list', '/views/business/hospital/appointment/appointment-list.vue', 2, NULL, 'hospital:appointment:query', 'UnorderedListOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5022, '医生排班', 2, 5020, 20, '/appointment/schedule', '/views/business/hospital/dashboard/dashboard-overview.vue', 2, NULL, 'hospital:schedule:query', 'ScheduleOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5023, '项目管理', 2, 5020, 30, '/appointment/project', '/views/business/hospital/project/project-list.vue', 2, NULL, 'hospital:project:query', 'ProjectOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 客户管理 (一级菜单)
(5030, '客户管理', 1, 0, 54, '/customer', NULL, 1, NULL, NULL, 'TeamOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5031, '客户列表', 2, 5030, 10, '/customer/list', '/views/business/hospital/customer/customer-list.vue', 2, NULL, 'hospital:customer:query', 'UnorderedListOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5032, '病历管理', 2, 5030, 20, '/customer/medical', '/views/business/hospital/dashboard/dashboard-overview.vue', 2, NULL, 'hospital:record:query', 'FileTextOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 数据统计 (一级菜单)
(5040, '数据统计', 1, 0, 55, '/statistics', NULL, 1, NULL, NULL, 'BarChartOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5041, '线索转化统计', 2, 5040, 10, '/statistics/lead-conversion', '/views/business/hospital/dashboard/dashboard-overview.vue', 2, NULL, 'hospital:statistics:lead', 'LineChartOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5042, '预约到诊统计', 2, 5040, 20, '/statistics/appointment-visit', '/views/business/hospital/dashboard/dashboard-overview.vue', 2, NULL, 'hospital:statistics:appointment', 'AreaChartOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5043, '客户满意度统计', 2, 5040, 30, '/statistics/satisfaction', '/views/business/hospital/dashboard/dashboard-overview.vue', 2, NULL, 'hospital:statistics:satisfaction', 'SmileOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5044, '医生工作量统计', 2, 5040, 40, '/statistics/doctor-workload', '/views/business/hospital/dashboard/dashboard-overview.vue', 2, NULL, 'hospital:statistics:doctor', 'UserOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 财务管理 (一级菜单)
(5050, '财务管理', 1, 0, 56, '/finance', NULL, 1, NULL, NULL, 'AccountBookOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5051, '收费管理', 2, 5050, 10, '/finance/billing', '/views/business/hospital/dashboard/dashboard-overview.vue', 2, NULL, 'hospital:finance:billing', 'MoneyCollectOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5052, '收费统计', 2, 5050, 20, '/finance/billing-stats', '/views/business/hospital/dashboard/dashboard-overview.vue', 2, NULL, 'hospital:finance:stats', 'BarChartOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5053, '财务报表', 2, 5050, 30, '/finance/reports', '/views/business/hospital/dashboard/dashboard-overview.vue', 2, NULL, 'hospital:finance:reports', 'FileTextOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5054, '收入分析', 2, 5050, 40, '/finance/income-analysis', '/views/business/hospital/dashboard/dashboard-overview.vue', 2, NULL, 'hospital:finance:analysis', 'FundOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 关怀管理 (一级菜单)
(5060, '关怀管理', 1, 0, 57, '/care', NULL, 1, NULL, NULL, 'HeartOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5061, '出院关怀', 2, 5060, 10, '/care/discharge', '/views/business/hospital/dashboard/dashboard-overview.vue', 2, NULL, 'hospital:care:discharge', 'MedicineBoxOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5062, '关怀模板', 2, 5060, 20, '/care/templates', '/views/business/hospital/dashboard/dashboard-overview.vue', 2, NULL, 'hospital:care:template', 'FileOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5063, 'SOP流程', 2, 5060, 30, '/care/sop', '/views/business/hospital/dashboard/dashboard-overview.vue', 2, NULL, 'hospital:care:sop', 'ApartmentOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5064, '满意度回访', 2, 5060, 40, '/care/satisfaction-visit', '/views/business/hospital/dashboard/dashboard-overview.vue', 2, NULL, 'hospital:care:visit', 'PhoneOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5065, '复购提醒', 2, 5060, 50, '/care/repurchase-reminder', '/views/business/hospital/dashboard/dashboard-overview.vue', 2, NULL, 'hospital:care:reminder', 'BellOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 知识库 (一级菜单)
(5070, '知识库', 1, 0, 58, '/knowledge', NULL, 1, NULL, NULL, 'BookOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5071, '文档管理', 2, 5070, 10, '/knowledge/documents', '/views/business/hospital/dashboard/dashboard-overview.vue', 2, NULL, 'hospital:knowledge:document', 'FileTextOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5072, '分类管理', 2, 5070, 20, '/knowledge/categories', '/views/business/hospital/knowledge/category-list.vue', 2, NULL, 'hospital:knowledge:category', 'FolderOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5073, '标签管理', 2, 5070, 30, '/knowledge/tags', '/views/business/hospital/knowledge/tag-list.vue', 2, NULL, 'hospital:knowledge:tag', 'TagOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5074, '知识搜索', 2, 5070, 40, '/knowledge/search', '/views/business/hospital/knowledge/knowledge-search.vue', 2, NULL, 'hospital:knowledge:search', 'SearchOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5075, '知识共享', 2, 5070, 50, '/knowledge/sharing', '/views/business/hospital/knowledge/knowledge-sharing.vue', 2, NULL, 'hospital:knowledge:sharing', 'ShareAltOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 营销活动 (一级菜单)
(5080, '营销活动', 1, 0, 59, '/marketing', NULL, 1, NULL, NULL, 'SoundOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5081, '活动管理', 2, 5080, 10, '/marketing/campaigns', '/views/business/hospital/marketing/campaign-list.vue', 2, NULL, 'hospital:marketing:campaign', 'CalendarOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5082, '活动推广', 2, 5080, 20, '/marketing/promotion', '/views/business/hospital/marketing/promotion-list.vue', 2, NULL, 'hospital:marketing:promotion', 'SoundOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5083, '效果跟踪', 2, 5080, 30, '/marketing/tracking', '/views/business/hospital/marketing/effect-tracking.vue', 2, NULL, 'hospital:marketing:tracking', 'LineChartOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5084, 'ROI分析', 2, 5080, 40, '/marketing/roi-analysis', '/views/business/hospital/marketing/roi-analysis.vue', 2, NULL, 'hospital:marketing:roi', 'BarChartOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
(5085, '活动报表', 2, 5080, 50, '/marketing/reports', '/views/business/hospital/marketing/campaign-reports.vue', 2, NULL, 'hospital:marketing:reports', 'FileTextOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW());

-- 3. 重新配置角色权限关联
-- 超级管理员 (role_id = 1) - 全部权限
INSERT INTO t_role_menu (role_id, menu_id)
SELECT 1, menu_id FROM t_menu WHERE menu_id BETWEEN 5001 AND 5300;

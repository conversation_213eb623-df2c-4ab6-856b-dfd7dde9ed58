-- 为smart_admin_v3数据库初始化线索来源和症状字典数据
-- Author: 1024创新实验室-主任：卓大
-- Date: 2025-07-22

-- ========================================
-- 1. 创建线索来源字典
-- ========================================

-- 插入线索来源字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`) 
VALUES ('线索来源', 'LEAD_SOURCE', '线索来源字典，用于线索管理模块', 0, NOW(), NOW());

-- 获取线索来源字典ID并插入字典数据
SET @lead_source_dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'LEAD_SOURCE');

-- 删除现有的线索来源字典数据（如果存在）
DELETE FROM t_dict_data WHERE dict_id = @lead_source_dict_id;

-- 插入新的线索来源字典数据（按重要性排序）
INSERT INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@lead_source_dict_id, 'SHANGWUTONG', '商务通', '商务通在线客服系统', 100, 0, NOW(), NOW()),
(@lead_source_dict_id, 'BAIDU', '百度', '百度搜索引擎和百度推广', 90, 0, NOW(), NOW()),
(@lead_source_dict_id, 'DOUYIN', '抖音', '抖音短视频平台', 80, 0, NOW(), NOW()),
(@lead_source_dict_id, 'KUAISHOU', '快手', '快手短视频平台', 70, 0, NOW(), NOW());

-- ========================================
-- 2. 创建症状字典
-- ========================================

-- 插入症状字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`) 
VALUES ('症状类型', 'SYMPTOM_TYPE', '症状类型字典，用于线索管理模块的症状字段', 0, NOW(), NOW());

-- 获取症状字典ID
SET @symptom_dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'SYMPTOM_TYPE');

-- 插入症状字典数据（按字母顺序排序）
INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@symptom_dict_id, 'DEPRESSION', '抑郁症', '抑郁症相关症状', 290, 0, NOW(), NOW()),
(@symptom_dict_id, 'SEXUAL_ORIENTATION_DISORDER', '性取向异常', '性取向异常相关症状', 280, 0, NOW(), NOW()),
(@symptom_dict_id, 'AUTISM', '自闭症', '自闭症相关症状', 270, 0, NOW(), NOW()),
(@symptom_dict_id, 'EPILEPSY', '癫痫', '癫痫相关症状', 260, 0, NOW(), NOW()),
(@symptom_dict_id, 'TICS', '抽动症', '抽动症相关症状', 250, 0, NOW(), NOW()),
(@symptom_dict_id, 'INTERNET_ADDICTION', '网瘾', '网络成瘾相关症状', 240, 0, NOW(), NOW()),
(@symptom_dict_id, 'ALCOHOL_ADDICTION', '酒瘾', '酒精成瘾相关症状', 230, 0, NOW(), NOW()),
(@symptom_dict_id, 'HYPOCHONDRIASIS', '疑病症', '疑病症相关症状', 220, 0, NOW(), NOW()),
(@symptom_dict_id, 'BIPOLAR_DISORDER', '双相情感障碍', '双相情感障碍相关症状', 210, 0, NOW(), NOW()),
(@symptom_dict_id, 'PERSONALITY_DISORDER', '人格障碍', '人格障碍相关症状', 200, 0, NOW(), NOW()),
(@symptom_dict_id, 'MENIERES_DISEASE', '美尼尔综合症', '美尼尔综合症相关症状', 190, 0, NOW(), NOW()),
(@symptom_dict_id, 'NEUROGENIC_HEADACHE', '神经性头痛', '神经性头痛相关症状', 180, 0, NOW(), NOW()),
(@symptom_dict_id, 'STRESS_DISORDER', '应激障碍', '应激障碍相关症状', 170, 0, NOW(), NOW()),
(@symptom_dict_id, 'SOMATIZATION_DISORDER', '躯体化障碍', '躯体化障碍相关症状', 160, 0, NOW(), NOW()),
(@symptom_dict_id, 'MOOD_DISORDER', '心境障碍', '心境障碍相关症状', 150, 0, NOW(), NOW()),
(@symptom_dict_id, 'HYSTERIA', '癔症', '癔症相关症状', 140, 0, NOW(), NOW()),
(@symptom_dict_id, 'MENOPAUSE_SYNDROME', '更年期综合症', '更年期综合症相关症状', 130, 0, NOW(), NOW()),
(@symptom_dict_id, 'ADHD', '多动症', '注意力缺陷多动障碍相关症状', 120, 0, NOW(), NOW()),
(@symptom_dict_id, 'ANXIETY', '焦虑症', '焦虑症相关症状', 110, 0, NOW(), NOW()),
(@symptom_dict_id, 'INSOMNIA', '失眠症', '失眠症相关症状', 100, 0, NOW(), NOW()),
(@symptom_dict_id, 'SCHIZOPHRENIA', '精神分裂症', '精神分裂症相关症状', 90, 0, NOW(), NOW()),
(@symptom_dict_id, 'PSYCHOLOGICAL_DISORDER', '心理障碍', '心理障碍相关症状', 80, 0, NOW(), NOW()),
(@symptom_dict_id, 'MENTAL_DISORDER', '精神障碍', '精神障碍相关症状', 70, 0, NOW(), NOW()),
(@symptom_dict_id, 'MANIA', '躁狂症', '躁狂症相关症状', 60, 0, NOW(), NOW()),
(@symptom_dict_id, 'NEURASTHENIA', '神经衰弱', '神经衰弱相关症状', 50, 0, NOW(), NOW()),
(@symptom_dict_id, 'SLEEP_DISORDER', '睡眠障碍', '睡眠障碍相关症状', 40, 0, NOW(), NOW()),
(@symptom_dict_id, 'OCD', '强迫症', '强迫症相关症状', 30, 0, NOW(), NOW()),
(@symptom_dict_id, 'PSYCHOLOGICAL_DISORDER_SYNDROME', '心理障碍症', '心理障碍症相关症状', 25, 0, NOW(), NOW()),
(@symptom_dict_id, 'PHOBIA', '恐惧症', '恐惧症相关症状', 20, 0, NOW(), NOW()),
(@symptom_dict_id, 'AUTONOMIC_DYSFUNCTION', '植物神经紊乱', '植物神经紊乱相关症状', 15, 0, NOW(), NOW()),
(@symptom_dict_id, 'NEUROSIS', '神经官能症', '神经官能症相关症状', 12, 0, NOW(), NOW()),
(@symptom_dict_id, 'HEADACHE_DIZZINESS', '头痛头晕', '头痛头晕相关症状', 10, 0, NOW(), NOW()),
(@symptom_dict_id, 'SCHOOL_PHOBIA', '厌学症', '厌学症相关症状', 5, 0, NOW(), NOW());

-- ========================================
-- 3. 验证创建结果
-- ========================================

-- 查询验证线索来源字典
SELECT '线索来源字典验证' as verification_type, d.dict_name, d.dict_code, dd.data_value, dd.data_label, dd.sort_order
FROM t_dict d 
LEFT JOIN t_dict_data dd ON d.dict_id = dd.dict_id 
WHERE d.dict_code = 'LEAD_SOURCE'
ORDER BY dd.sort_order DESC;

-- 查询验证症状字典（显示前10个）
SELECT '症状字典验证' as verification_type, d.dict_name, d.dict_code, COUNT(dd.dict_data_id) as total_count
FROM t_dict d
LEFT JOIN t_dict_data dd ON d.dict_id = dd.dict_id
WHERE d.dict_code = 'SYMPTOM_TYPE'
GROUP BY d.dict_id;

-- ========================================
-- 4. 创建性别字典
-- ========================================

-- 插入性别字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`)
VALUES ('性别', 'GENDER', '性别字典，用于客户和线索管理', 0, NOW(), NOW());

-- 获取性别字典ID
SET @gender_dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'GENDER');

-- 插入性别字典数据
INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@gender_dict_id, 'MALE', '男', '男性', 100, 0, NOW(), NOW()),
(@gender_dict_id, 'FEMALE', '女', '女性', 90, 0, NOW(), NOW());

-- ========================================
-- 5. 创建客户来源字典
-- ========================================

-- 插入客户来源字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`)
VALUES ('客户来源', 'CUSTOMER_SOURCE', '客户来源字典，用于客户管理模块', 0, NOW(), NOW());

-- 获取客户来源字典ID
SET @customer_source_dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'CUSTOMER_SOURCE');

-- 插入客户来源字典数据
INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@customer_source_dict_id, 'ONLINE_PROMOTION', '网络推广', '通过网络渠道获得的客户', 100, 0, NOW(), NOW()),
(@customer_source_dict_id, 'FRIEND_REFERRAL', '朋友介绍', '通过朋友介绍获得的客户', 90, 0, NOW(), NOW()),
(@customer_source_dict_id, 'TELEMARKETING', '电话营销', '通过电话营销获得的客户', 80, 0, NOW(), NOW()),
(@customer_source_dict_id, 'STORE_CONSULTATION', '门店咨询', '直接到门店咨询的客户', 70, 0, NOW(), NOW()),
(@customer_source_dict_id, 'ACTIVITY_PROMOTION', '活动推广', '通过活动推广获得的客户', 60, 0, NOW(), NOW()),
(@customer_source_dict_id, 'OLD_CUSTOMER_REFERRAL', '老客户介绍', '老客户介绍的新客户', 50, 0, NOW(), NOW()),
(@customer_source_dict_id, 'OTHER', '其他', '其他来源的客户', 10, 0, NOW(), NOW());

-- ========================================
-- 6. 创建客户标签字典
-- ========================================

-- 插入客户标签字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`)
VALUES ('客户标签', 'CUSTOMER_TAG', '客户标签字典，用于客户分类管理', 0, NOW(), NOW());

-- 获取客户标签字典ID
SET @customer_tag_dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'CUSTOMER_TAG');

-- 插入客户标签字典数据
INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@customer_tag_dict_id, 'VIP_CUSTOMER', 'VIP客户', '高价值客户', 100, 0, NOW(), NOW()),
(@customer_tag_dict_id, 'PRICE_SENSITIVE', '价格敏感', '对价格比较敏感的客户', 90, 0, NOW(), NOW()),
(@customer_tag_dict_id, 'SERVICE_SENSITIVE', '服务敏感', '对服务质量要求高的客户', 80, 0, NOW(), NOW()),
(@customer_tag_dict_id, 'HIGH_QUALITY_REQUIREMENT', '品质要求高', '对产品品质要求高的客户', 70, 0, NOW(), NOW()),
(@customer_tag_dict_id, 'QUICK_DECISION', '决策快', '决策速度快的客户', 60, 0, NOW(), NOW()),
(@customer_tag_dict_id, 'NEED_FOLLOW_UP', '需要跟进', '需要持续跟进的客户', 50, 0, NOW(), NOW()),
(@customer_tag_dict_id, 'RETURN_CUSTOMER', '复诊客户', '多次就诊的客户', 40, 0, NOW(), NOW()),
(@customer_tag_dict_id, 'REFERRAL_CUSTOMER', '推荐客户', '被他人推荐的客户', 30, 0, NOW(), NOW());

-- ========================================
-- 7. 创建客户等级字典
-- ========================================

-- 插入客户等级字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`)
VALUES ('客户等级', 'CUSTOMER_LEVEL', '客户等级字典，用于客户分级管理', 0, NOW(), NOW());

-- 获取客户等级字典ID
SET @customer_level_dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'CUSTOMER_LEVEL');

-- 插入客户等级字典数据
INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@customer_level_dict_id, 'SVIP', 'SVIP客户', '超级VIP客户，最高等级', 100, 0, NOW(), NOW()),
(@customer_level_dict_id, 'VIP', 'VIP客户', 'VIP客户，高等级', 90, 0, NOW(), NOW()),
(@customer_level_dict_id, 'ORDINARY', '普通客户', '普通客户，基础等级', 80, 0, NOW(), NOW());

-- ========================================
-- 8. 创建项目分类字典
-- ========================================

-- 插入项目分类字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`)
VALUES ('项目分类', 'PROJECT_CATEGORY', '项目分类字典，用于项目管理模块', 0, NOW(), NOW());

-- 获取项目分类字典ID
SET @project_category_dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'PROJECT_CATEGORY');

-- 插入项目分类字典数据
INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@project_category_dict_id, 'ORAL_TREATMENT', '口腔治疗', '口腔疾病治疗项目', 100, 0, NOW(), NOW()),
(@project_category_dict_id, 'ORAL_RESTORATION', '口腔修复', '口腔修复相关项目', 90, 0, NOW(), NOW()),
(@project_category_dict_id, 'ORTHODONTICS', '口腔正畸', '牙齿矫正相关项目', 80, 0, NOW(), NOW()),
(@project_category_dict_id, 'ORAL_IMPLANT', '口腔种植', '牙齿种植相关项目', 70, 0, NOW(), NOW()),
(@project_category_dict_id, 'ORAL_COSMETIC', '口腔美容', '口腔美容相关项目', 60, 0, NOW(), NOW()),
(@project_category_dict_id, 'PEDIATRIC_DENTISTRY', '儿童口腔', '儿童口腔相关项目', 50, 0, NOW(), NOW()),
(@project_category_dict_id, 'ORAL_PREVENTION', '口腔预防', '口腔预防保健项目', 40, 0, NOW(), NOW()),
(@project_category_dict_id, 'OTHER', '其他', '其他类型项目', 10, 0, NOW(), NOW());

-- ========================================
-- 9. 创建职业类型字典
-- ========================================

-- 插入职业类型字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`)
VALUES ('职业类型', 'OCCUPATION_TYPE', '职业类型字典，用于客户职业分类', 0, NOW(), NOW());

-- 获取职业类型字典ID
SET @occupation_dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'OCCUPATION_TYPE');

-- 插入职业类型字典数据
INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@occupation_dict_id, 'CIVIL_SERVANT', '公务员', '政府机关工作人员', 100, 0, NOW(), NOW()),
(@occupation_dict_id, 'ENTERPRISE_EMPLOYEE', '企业职员', '企业单位工作人员', 90, 0, NOW(), NOW()),
(@occupation_dict_id, 'TEACHER', '教师', '教育行业从业者', 80, 0, NOW(), NOW()),
(@occupation_dict_id, 'DOCTOR', '医生', '医疗行业从业者', 70, 0, NOW(), NOW()),
(@occupation_dict_id, 'ENGINEER', '工程师', '技术行业从业者', 60, 0, NOW(), NOW()),
(@occupation_dict_id, 'BUSINESSMAN', '商人', '商业经营者', 50, 0, NOW(), NOW()),
(@occupation_dict_id, 'FREELANCER', '自由职业', '自由职业者', 40, 0, NOW(), NOW()),
(@occupation_dict_id, 'STUDENT', '学生', '在校学生', 30, 0, NOW(), NOW()),
(@occupation_dict_id, 'RETIREE', '退休人员', '已退休人员', 20, 0, NOW(), NOW()),
(@occupation_dict_id, 'OTHER', '其他', '其他职业', 10, 0, NOW(), NOW());

-- ========================================
-- 10. 创建科室类型字典
-- ========================================

-- 插入科室类型字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`)
VALUES ('科室类型', 'DEPARTMENT_TYPE', '科室类型字典，用于医院科室分类', 0, NOW(), NOW());

-- 获取科室类型字典ID
SET @department_dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'DEPARTMENT_TYPE');

-- 插入科室类型字典数据
INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@department_dict_id, 'ORAL_MEDICINE', '口腔内科', '口腔内科诊疗科室', 100, 0, NOW(), NOW()),
(@department_dict_id, 'ORAL_SURGERY', '口腔外科', '口腔外科手术科室', 90, 0, NOW(), NOW()),
(@department_dict_id, 'ORTHODONTICS', '正畸科', '牙齿矫正科室', 80, 0, NOW(), NOW()),
(@department_dict_id, 'PROSTHODONTICS', '修复科', '口腔修复科室', 70, 0, NOW(), NOW()),
(@department_dict_id, 'ORAL_IMPLANT', '种植科', '牙齿种植科室', 60, 0, NOW(), NOW()),
(@department_dict_id, 'PEDIATRIC_DENTISTRY', '儿童口腔科', '儿童口腔诊疗科室', 50, 0, NOW(), NOW()),
(@department_dict_id, 'ORAL_PREVENTION', '预防科', '口腔预防保健科室', 40, 0, NOW(), NOW()),
(@department_dict_id, 'ORAL_RADIOLOGY', '口腔放射科', '口腔影像诊断科室', 30, 0, NOW(), NOW()),
(@department_dict_id, 'ORAL_PATHOLOGY', '口腔病理科', '口腔病理诊断科室', 20, 0, NOW(), NOW()),
(@department_dict_id, 'OTHER', '其他科室', '其他科室', 10, 0, NOW(), NOW());

-- ========================================
-- 11. 创建关怀类型字典
-- ========================================

-- 插入关怀类型字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`)
VALUES ('关怀类型', 'CARE_TYPE', '关怀类型字典，用于客户关怀管理', 0, NOW(), NOW());

-- 获取关怀类型字典ID
SET @care_type_dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'CARE_TYPE');

-- 插入关怀类型字典数据
INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@care_type_dict_id, 'DISCHARGE_CARE', '出院关怀', '患者出院后的关怀服务', 100, 0, NOW(), NOW()),
(@care_type_dict_id, 'FOLLOW_UP_REMINDER', '复诊提醒', '提醒患者按时复诊', 90, 0, NOW(), NOW()),
(@care_type_dict_id, 'BIRTHDAY_CARE', '生日关怀', '客户生日祝福关怀', 80, 0, NOW(), NOW()),
(@care_type_dict_id, 'HOLIDAY_CARE', '节日关怀', '节假日问候关怀', 70, 0, NOW(), NOW()),
(@care_type_dict_id, 'SATISFACTION_SURVEY', '满意度调查', '服务满意度调查', 60, 0, NOW(), NOW());

-- ========================================
-- 12. 创建关怀方式字典
-- ========================================

-- 插入关怀方式字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`)
VALUES ('关怀方式', 'CARE_METHOD', '关怀方式字典，用于关怀执行方式', 0, NOW(), NOW());

-- 获取关怀方式字典ID
SET @care_method_dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'CARE_METHOD');

-- 插入关怀方式字典数据
INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@care_method_dict_id, 'PHONE', '电话', '电话关怀', 100, 0, NOW(), NOW()),
(@care_method_dict_id, 'SMS', '短信', '短信关怀', 90, 0, NOW(), NOW()),
(@care_method_dict_id, 'WECHAT', '微信', '微信关怀', 80, 0, NOW(), NOW()),
(@care_method_dict_id, 'EMAIL', '邮件', '邮件关怀', 70, 0, NOW(), NOW()),
(@care_method_dict_id, 'HOME_VISIT', '上门', '上门关怀', 60, 0, NOW(), NOW());

-- ========================================
-- 13. 创建执行状态字典
-- ========================================

-- 插入执行状态字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`)
VALUES ('执行状态', 'EXECUTE_STATUS', '执行状态字典，用于任务执行状态管理', 0, NOW(), NOW());

-- 获取执行状态字典ID
SET @execute_status_dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'EXECUTE_STATUS');

-- 插入执行状态字典数据
INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@execute_status_dict_id, 'PENDING', '待执行', '任务待执行状态', 100, 0, NOW(), NOW()),
(@execute_status_dict_id, 'IN_PROGRESS', '执行中', '任务正在执行', 90, 0, NOW(), NOW()),
(@execute_status_dict_id, 'COMPLETED', '已完成', '任务执行完成', 80, 0, NOW(), NOW()),
(@execute_status_dict_id, 'CANCELLED', '已取消', '任务已取消', 70, 0, NOW(), NOW()),
(@execute_status_dict_id, 'FAILED', '执行失败', '任务执行失败', 60, 0, NOW(), NOW());

-- ========================================
-- 14. 创建病历状态字典
-- ========================================

-- 插入病历状态字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`)
VALUES ('病历状态', 'MEDICAL_RECORD_STATUS', '病历状态字典，用于病历管理', 0, NOW(), NOW());

-- 获取病历状态字典ID
SET @medical_record_status_dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'MEDICAL_RECORD_STATUS');

-- 插入病历状态字典数据
INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@medical_record_status_dict_id, 'DRAFT', '草稿', '病历草稿状态', 100, 0, NOW(), NOW()),
(@medical_record_status_dict_id, 'COMPLETED', '已完成', '病历已完成', 90, 0, NOW(), NOW());

-- ========================================
-- 15. 创建排班状态字典
-- ========================================

-- 插入排班状态字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`)
VALUES ('排班状态', 'SCHEDULE_STATUS', '排班状态字典，用于医生排班管理', 0, NOW(), NOW());

-- 获取排班状态字典ID
SET @schedule_status_dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'SCHEDULE_STATUS');

-- 插入排班状态字典数据
INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@schedule_status_dict_id, 'NORMAL', '正常', '正常排班状态', 100, 0, NOW(), NOW()),
(@schedule_status_dict_id, 'SUSPENDED', '停诊', '停诊状态', 90, 0, NOW(), NOW());

-- ========================================
-- 16. 创建满意度评分字典
-- ========================================

-- 插入满意度评分字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`)
VALUES ('满意度评分', 'SATISFACTION_SCORE', '满意度评分字典，用于客户满意度评价', 0, NOW(), NOW());

-- 获取满意度评分字典ID
SET @satisfaction_score_dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'SATISFACTION_SCORE');

-- 插入满意度评分字典数据
INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@satisfaction_score_dict_id, '5', '非常满意', '5分-非常满意', 100, 0, NOW(), NOW()),
(@satisfaction_score_dict_id, '4', '满意', '4分-满意', 90, 0, NOW(), NOW()),
(@satisfaction_score_dict_id, '3', '一般', '3分-一般', 80, 0, NOW(), NOW()),
(@satisfaction_score_dict_id, '2', '不满意', '2分-不满意', 70, 0, NOW(), NOW()),
(@satisfaction_score_dict_id, '1', '非常不满意', '1分-非常不满意', 60, 0, NOW(), NOW());

-- ========================================
-- 17. 创建模板分类字典
-- ========================================

-- 插入模板分类字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`)
VALUES ('模板分类', 'TEMPLATE_CATEGORY', '模板分类字典，用于关怀模板分类', 0, NOW(), NOW());

-- 获取模板分类字典ID
SET @template_category_dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'TEMPLATE_CATEGORY');

-- 插入模板分类字典数据
INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@template_category_dict_id, 'DISCHARGE_TEMPLATE', '出院模板', '出院关怀模板', 100, 0, NOW(), NOW()),
(@template_category_dict_id, 'FOLLOW_UP_TEMPLATE', '复诊模板', '复诊提醒模板', 90, 0, NOW(), NOW()),
(@template_category_dict_id, 'BIRTHDAY_TEMPLATE', '生日模板', '生日关怀模板', 80, 0, NOW(), NOW()),
(@template_category_dict_id, 'HOLIDAY_TEMPLATE', '节日模板', '节日关怀模板', 70, 0, NOW(), NOW()),
(@template_category_dict_id, 'SURVEY_TEMPLATE', '调查模板', '满意度调查模板', 60, 0, NOW(), NOW());

-- ========================================
-- 18. 创建模板标签字典
-- ========================================

-- 插入模板标签字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`)
VALUES ('模板标签', 'TEMPLATE_TAG', '模板标签字典，用于模板标签管理', 0, NOW(), NOW());

-- 获取模板标签字典ID
SET @template_tag_dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'TEMPLATE_TAG');

-- 插入模板标签字典数据
INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@template_tag_dict_id, 'WARM_CARE', '温馨关怀', '温馨关怀类标签', 100, 0, NOW(), NOW()),
(@template_tag_dict_id, 'PROFESSIONAL_GUIDANCE', '专业指导', '专业指导类标签', 90, 0, NOW(), NOW()),
(@template_tag_dict_id, 'HEALTH_REMINDER', '健康提醒', '健康提醒类标签', 80, 0, NOW(), NOW()),
(@template_tag_dict_id, 'FOLLOW_UP_NOTICE', '复诊通知', '复诊通知类标签', 70, 0, NOW(), NOW()),
(@template_tag_dict_id, 'SATISFACTION_FEEDBACK', '满意度反馈', '满意度反馈类标签', 60, 0, NOW(), NOW());

-- ========================================
-- 19. 验证所有字典创建结果
-- ========================================

-- 查询验证所有新创建的字典
SELECT '所有字典验证' as verification_type, d.dict_name, d.dict_code, COUNT(dd.dict_data_id) as data_count
FROM t_dict d
LEFT JOIN t_dict_data dd ON d.dict_id = dd.dict_id
WHERE d.dict_code IN (
    'LEAD_SOURCE', 'SYMPTOM_TYPE', 'GENDER', 'CUSTOMER_SOURCE', 'CUSTOMER_TAG',
    'CUSTOMER_LEVEL', 'PROJECT_CATEGORY', 'OCCUPATION_TYPE', 'DEPARTMENT_TYPE',
    'CARE_TYPE', 'CARE_METHOD', 'EXECUTE_STATUS', 'MEDICAL_RECORD_STATUS',
    'SCHEDULE_STATUS', 'SATISFACTION_SCORE', 'TEMPLATE_CATEGORY', 'TEMPLATE_TAG'
)
GROUP BY d.dict_id, d.dict_name, d.dict_code
ORDER BY d.dict_code;

-- 查询验证字典数据总数
SELECT '字典数据统计' as verification_type,
       COUNT(DISTINCT d.dict_id) as dict_count,
       COUNT(dd.dict_data_id) as total_data_count
FROM t_dict d
LEFT JOIN t_dict_data dd ON d.dict_id = dd.dict_id
WHERE d.dict_code IN (
    'LEAD_SOURCE', 'SYMPTOM_TYPE', 'GENDER', 'CUSTOMER_SOURCE', 'CUSTOMER_TAG',
    'CUSTOMER_LEVEL', 'PROJECT_CATEGORY', 'OCCUPATION_TYPE', 'DEPARTMENT_TYPE',
    'CARE_TYPE', 'CARE_METHOD', 'EXECUTE_STATUS', 'MEDICAL_RECORD_STATUS',
    'SCHEDULE_STATUS', 'SATISFACTION_SCORE', 'TEMPLATE_CATEGORY', 'TEMPLATE_TAG'
);

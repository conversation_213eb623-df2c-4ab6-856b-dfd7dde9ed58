-- 线索跟进提醒表
CREATE TABLE `t_lead_follow_reminder` (
  `reminder_id` bigint NOT NULL AUTO_INCREMENT COMMENT '提醒ID',
  `lead_id` bigint NOT NULL COMMENT '线索ID',
  `follow_time` datetime NOT NULL COMMENT '计划跟进时间',
  `reminder_time` datetime NOT NULL COMMENT '提醒时间',
  `reminder_type` int NOT NULL DEFAULT '1' COMMENT '提醒方式：1系统通知 2短信 3邮件',
  `reminder_minutes` int NOT NULL DEFAULT '30' COMMENT '提前提醒分钟数',
  `reminder_status` int NOT NULL DEFAULT '1' COMMENT '提醒状态：1待提醒 2已发送 3已取消 4已完成',
  `sent_time` datetime DEFAULT NULL COMMENT '实际发送时间',
  `reminder_content` varchar(500) DEFAULT NULL COMMENT '提醒内容',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  PRIMARY KEY (`reminder_id`),
  KEY `idx_lead_id` (`lead_id`),
  KEY `idx_create_user_id` (`create_user_id`),
  KEY `idx_reminder_time` (`reminder_time`),
  KEY `idx_follow_time` (`follow_time`),
  KEY `idx_reminder_status` (`reminder_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='线索跟进提醒表';

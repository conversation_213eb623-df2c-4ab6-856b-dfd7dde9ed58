-- 医院营销与客户管理系统数据库表结构
-- Author: 1024创新实验室-主任：卓大
-- Date: 2024-01-01 10:00:00

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 1. 线索管理相关表
-- ----------------------------

-- 线索表
DROP TABLE IF EXISTS `t_lead`;
CREATE TABLE `t_lead` (
  `lead_id` bigint NOT NULL AUTO_INCREMENT COMMENT '线索ID',
  `lead_source` varchar(100) DEFAULT NULL COMMENT '线索来源',
  `customer_name` varchar(50) NOT NULL COMMENT '客户姓名',
  `customer_phone` varchar(20) NOT NULL COMMENT '客户电话',
  `customer_wechat` varchar(50) DEFAULT NULL COMMENT '微信号',
  `gender` tinyint DEFAULT NULL COMMENT '性别：1-男，2-女',
  `age` int DEFAULT NULL COMMENT '年龄',
  `interested_project` varchar(200) DEFAULT NULL COMMENT '感兴趣项目',
  `lead_status` tinyint NOT NULL DEFAULT '1' COMMENT '线索状态：1-新线索，2-跟进中，3-已预约，4-已转化，5-已关闭',
  `lead_quality` tinyint DEFAULT NULL COMMENT '线索质量：1-A级，2-B级，3-C级',
  `assigned_employee_id` bigint DEFAULT NULL COMMENT '分配员工ID',
  `remark` text COMMENT '备注',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`lead_id`),
  KEY `idx_customer_phone` (`customer_phone`),
  KEY `idx_lead_status` (`lead_status`),
  KEY `idx_assigned_employee` (`assigned_employee_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='线索表';

-- ----------------------------
-- 线索来源字典数据初始化
-- ----------------------------

-- 插入线索来源字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`)
VALUES ('线索来源', 'LEAD_SOURCE', '线索来源字典，用于线索管理模块', 0, NOW(), NOW());

-- 获取字典ID并插入字典数据
SET @dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'LEAD_SOURCE');

-- 插入线索来源字典数据
INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@dict_id, 'ONLINE_CONSULTATION', '在线咨询', '官网、APP等在线咨询渠道', 100, 0, NOW(), NOW()),
(@dict_id, 'PHONE_CONSULTATION', '电话咨询', '客户主动电话咨询', 90, 0, NOW(), NOW()),
(@dict_id, 'WECHAT_CONSULTATION', '微信咨询', '微信公众号、微信群等咨询', 80, 0, NOW(), NOW()),
(@dict_id, 'FRIEND_REFERRAL', '朋友推荐', '老客户推荐新客户', 70, 0, NOW(), NOW()),
(@dict_id, 'ADVERTISEMENT', '广告投放', '百度、抖音、微信等广告投放', 60, 0, NOW(), NOW()),
(@dict_id, 'SOCIAL_MEDIA', '社交媒体', '微博、抖音、小红书等社交平台', 50, 0, NOW(), NOW()),
(@dict_id, 'OFFLINE_ACTIVITY', '线下活动', '健康讲座、义诊活动等', 40, 0, NOW(), NOW()),
(@dict_id, 'PARTNER_REFERRAL', '合作伙伴', '合作机构推荐', 30, 0, NOW(), NOW()),
(@dict_id, 'SEARCH_ENGINE', '搜索引擎', 'SEO自然搜索流量', 20, 0, NOW(), NOW()),
(@dict_id, 'OTHER', '其他', '其他未分类来源', 10, 0, NOW(), NOW());

-- 线索跟进记录表
DROP TABLE IF EXISTS `t_lead_follow`;
CREATE TABLE `t_lead_follow` (
  `follow_id` bigint NOT NULL AUTO_INCREMENT COMMENT '跟进记录ID',
  `lead_id` bigint NOT NULL COMMENT '线索ID',
  `follow_type` tinyint NOT NULL COMMENT '跟进方式：1-电话，2-微信，3-短信，4-邮件，5-上门',
  `follow_content` text NOT NULL COMMENT '跟进内容',
  `follow_result` varchar(500) DEFAULT NULL COMMENT '跟进结果',
  `next_follow_time` datetime DEFAULT NULL COMMENT '下次跟进时间',
  `follow_user_id` bigint NOT NULL COMMENT '跟进人ID',
  `follow_user_name` varchar(50) NOT NULL COMMENT '跟进人姓名',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`follow_id`),
  KEY `idx_lead_id` (`lead_id`),
  KEY `idx_follow_user` (`follow_user_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='线索跟进记录表';

-- ----------------------------
-- 2. 项目管理相关表
-- ----------------------------

-- 项目表
DROP TABLE IF EXISTS `t_project`;
CREATE TABLE `t_project` (
  `project_id` bigint NOT NULL AUTO_INCREMENT COMMENT '项目ID',
  `project_name` varchar(100) NOT NULL COMMENT '项目名称',
  `project_category` varchar(50) DEFAULT NULL COMMENT '项目分类',
  `project_description` text COMMENT '项目描述',
  `standard_price` decimal(10,2) DEFAULT NULL COMMENT '标准价格',
  `duration_minutes` int DEFAULT NULL COMMENT '项目时长（分钟）',
  `project_status` tinyint NOT NULL DEFAULT '1' COMMENT '项目状态：1-启用，0-禁用',
  `sort` int DEFAULT '0' COMMENT '排序',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`project_id`),
  KEY `idx_project_status` (`project_status`),
  KEY `idx_project_category` (`project_category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='项目表';

-- ----------------------------
-- 3. 预约管理相关表
-- ----------------------------

-- 预约表
DROP TABLE IF EXISTS `t_appointment`;
CREATE TABLE `t_appointment` (
  `appointment_id` bigint NOT NULL AUTO_INCREMENT COMMENT '预约ID',
  `appointment_no` varchar(50) NOT NULL COMMENT '预约编号',
  `customer_id` bigint DEFAULT NULL COMMENT '客户ID',
  `customer_name` varchar(50) NOT NULL COMMENT '客户姓名',
  `customer_phone` varchar(20) NOT NULL COMMENT '客户电话',
  `project_id` bigint NOT NULL COMMENT '项目ID',
  `project_name` varchar(100) NOT NULL COMMENT '项目名称',
  `doctor_id` bigint NOT NULL COMMENT '医生ID',
  `doctor_name` varchar(50) NOT NULL COMMENT '医生姓名',
  `appointment_date` date NOT NULL COMMENT '预约日期',
  `appointment_time` time NOT NULL COMMENT '预约时间',
  `appointment_status` tinyint NOT NULL DEFAULT '1' COMMENT '预约状态：1-待确认，2-已确认，3-已完成，4-已取消，5-已改期',
  `cancel_reason` varchar(200) DEFAULT NULL COMMENT '取消原因',
  `remark` text COMMENT '备注',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`appointment_id`),
  UNIQUE KEY `uk_appointment_no` (`appointment_no`),
  KEY `idx_customer_phone` (`customer_phone`),
  KEY `idx_appointment_date` (`appointment_date`),
  KEY `idx_doctor_id` (`doctor_id`),
  KEY `idx_appointment_status` (`appointment_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='预约表';

-- 医生排班表
DROP TABLE IF EXISTS `t_doctor_schedule`;
CREATE TABLE `t_doctor_schedule` (
  `schedule_id` bigint NOT NULL AUTO_INCREMENT COMMENT '排班ID',
  `doctor_id` bigint NOT NULL COMMENT '医生ID',
  `doctor_name` varchar(50) NOT NULL COMMENT '医生姓名',
  `schedule_date` date NOT NULL COMMENT '排班日期',
  `start_time` time NOT NULL COMMENT '开始时间',
  `end_time` time NOT NULL COMMENT '结束时间',
  `max_appointments` int NOT NULL DEFAULT '10' COMMENT '最大预约数',
  `current_appointments` int NOT NULL DEFAULT '0' COMMENT '当前预约数',
  `schedule_status` tinyint NOT NULL DEFAULT '1' COMMENT '排班状态：1-正常，0-停诊',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`schedule_id`),
  KEY `idx_doctor_date` (`doctor_id`, `schedule_date`),
  KEY `idx_schedule_date` (`schedule_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='医生排班表';

-- ----------------------------
-- 4. 客户管理相关表
-- ----------------------------

-- 客户表
DROP TABLE IF EXISTS `t_customer`;
CREATE TABLE `t_customer` (
  `customer_id` bigint NOT NULL AUTO_INCREMENT COMMENT '客户ID',
  `customer_no` varchar(50) NOT NULL COMMENT '客户编号',
  `customer_name` varchar(50) NOT NULL COMMENT '客户姓名',
  `customer_phone` varchar(20) NOT NULL COMMENT '客户电话',
  `customer_wechat` varchar(50) DEFAULT NULL COMMENT '微信号',
  `customer_email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `gender` tinyint DEFAULT NULL COMMENT '性别：1-男，2-女',
  `age` int DEFAULT NULL COMMENT '年龄',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `id_card` varchar(20) DEFAULT NULL COMMENT '身份证号',
  `address` varchar(200) DEFAULT NULL COMMENT '地址',
  `customer_source` varchar(100) DEFAULT NULL COMMENT '客户来源',
  `customer_status` tinyint NOT NULL DEFAULT '1' COMMENT '客户状态：1-潜在客户，2-意向客户，3-成交客户，4-流失客户',
  `customer_level` tinyint DEFAULT '1' COMMENT '客户等级：1-普通客户，2-VIP客户，3-SVIP客户',
  `customer_tags` varchar(500) DEFAULT NULL COMMENT '客户标签',
  `responsible_employee_id` bigint DEFAULT NULL COMMENT '负责员工ID',
  `responsible_employee_name` varchar(50) DEFAULT NULL COMMENT '负责员工姓名',
  `first_visit_date` date DEFAULT NULL COMMENT '首次就诊日期',
  `last_visit_date` date DEFAULT NULL COMMENT '最后就诊日期',
  `total_consumption` decimal(10,2) DEFAULT '0.00' COMMENT '总消费金额',
  `remark` text COMMENT '备注',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`customer_id`),
  UNIQUE KEY `uk_customer_no` (`customer_no`),
  UNIQUE KEY `uk_customer_phone` (`customer_phone`),
  KEY `idx_customer_status` (`customer_status`),
  KEY `idx_responsible_employee` (`responsible_employee_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户表';

-- 病历表
DROP TABLE IF EXISTS `t_medical_record`;
CREATE TABLE `t_medical_record` (
  `record_id` bigint NOT NULL AUTO_INCREMENT COMMENT '病历ID',
  `record_no` varchar(50) NOT NULL COMMENT '病历编号',
  `customer_id` bigint NOT NULL COMMENT '客户ID',
  `customer_name` varchar(50) NOT NULL COMMENT '客户姓名',
  `visit_date` date NOT NULL COMMENT '就诊日期',
  `doctor_id` bigint NOT NULL COMMENT '医生ID',
  `doctor_name` varchar(50) NOT NULL COMMENT '医生姓名',
  `department` varchar(50) DEFAULT NULL COMMENT '科室',
  `chief_complaint` text COMMENT '主诉',
  `present_illness` text COMMENT '现病史',
  `past_history` text COMMENT '既往史',
  `physical_examination` text COMMENT '体格检查',
  `auxiliary_examination` text COMMENT '辅助检查',
  `diagnosis` text COMMENT '诊断',
  `treatment_plan` text COMMENT '治疗方案',
  `prescription` text COMMENT '处方',
  `next_visit_date` date DEFAULT NULL COMMENT '下次复诊日期',
  `record_status` tinyint NOT NULL DEFAULT '1' COMMENT '病历状态：1-草稿，2-已完成',
  `remark` text COMMENT '备注',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`record_id`),
  UNIQUE KEY `uk_record_no` (`record_no`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_visit_date` (`visit_date`),
  KEY `idx_doctor_id` (`doctor_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='病历表';

-- ----------------------------
-- 5. 客户标签相关表
-- ----------------------------

-- 客户标签表
DROP TABLE IF EXISTS `t_customer_tag`;
CREATE TABLE `t_customer_tag` (
  `tag_id` bigint NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `tag_name` varchar(50) NOT NULL COMMENT '标签名称',
  `tag_description` varchar(200) DEFAULT NULL COMMENT '标签描述',
  `tag_color` varchar(20) DEFAULT NULL COMMENT '标签颜色',
  `category_id` bigint DEFAULT NULL COMMENT '标签分类ID',
  `category_name` varchar(50) DEFAULT NULL COMMENT '标签分类名称',
  `tag_type` tinyint NOT NULL DEFAULT '1' COMMENT '标签类型：1-手动标签，2-自动标签',
  `auto_rules` text COMMENT '自动打标规则（JSON格式）',
  `tag_status` tinyint NOT NULL DEFAULT '1' COMMENT '标签状态：1-启用，0-禁用',
  `usage_count` int DEFAULT '0' COMMENT '使用次数',
  `sort` int DEFAULT '0' COMMENT '排序',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`tag_id`),
  UNIQUE KEY `uk_tag_name` (`tag_name`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_tag_status` (`tag_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户标签表';

-- 客户标签关联表
DROP TABLE IF EXISTS `t_customer_tag_relation`;
CREATE TABLE `t_customer_tag_relation` (
  `relation_id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `customer_id` bigint NOT NULL COMMENT '客户ID',
  `tag_id` bigint NOT NULL COMMENT '标签ID',
  `tag_name` varchar(50) NOT NULL COMMENT '标签名称',
  `tag_color` varchar(20) DEFAULT NULL COMMENT '标签颜色',
  `tag_method` tinyint NOT NULL DEFAULT '1' COMMENT '打标方式：1-手动打标，2-自动打标',
  `tag_reason` varchar(200) DEFAULT NULL COMMENT '打标原因',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`relation_id`),
  UNIQUE KEY `uk_customer_tag` (`customer_id`, `tag_id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_tag_id` (`tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户标签关联表';

-- ----------------------------
-- 6. 财务管理相关表
-- ----------------------------

-- 收费记录表
DROP TABLE IF EXISTS `t_charge_record`;
CREATE TABLE `t_charge_record` (
  `charge_id` bigint NOT NULL AUTO_INCREMENT COMMENT '收费记录ID',
  `charge_no` varchar(50) NOT NULL COMMENT '收费单号',
  `customer_id` bigint DEFAULT NULL COMMENT '客户ID',
  `customer_name` varchar(50) NOT NULL COMMENT '客户姓名',
  `customer_phone` varchar(20) NOT NULL COMMENT '客户电话',
  `appointment_id` bigint DEFAULT NULL COMMENT '预约ID',
  `record_id` bigint DEFAULT NULL COMMENT '病历ID',
  `project_id` bigint NOT NULL COMMENT '项目ID',
  `project_name` varchar(100) NOT NULL COMMENT '项目名称',
  `project_category` varchar(50) DEFAULT NULL COMMENT '项目分类',
  `standard_price` decimal(10,2) NOT NULL COMMENT '标准价格',
  `charge_amount` decimal(10,2) NOT NULL COMMENT '实际收费金额',
  `discount_amount` decimal(10,2) DEFAULT '0.00' COMMENT '优惠金额',
  `discount_reason` varchar(200) DEFAULT NULL COMMENT '优惠原因',
  `charge_date` date NOT NULL COMMENT '收费日期',
  `charge_status` tinyint NOT NULL DEFAULT '1' COMMENT '收费状态：1-待收费，2-已收费，3-部分收费，4-已退费',
  `payment_method` tinyint DEFAULT NULL COMMENT '支付方式：1-现金，2-微信，3-支付宝，4-银行卡，5-组合支付',
  `payment_details` text COMMENT '支付详情（JSON格式）',
  `charge_employee_id` bigint DEFAULT NULL COMMENT '收费员工ID',
  `charge_employee_name` varchar(50) DEFAULT NULL COMMENT '收费员工姓名',
  `charge_department_id` bigint DEFAULT NULL COMMENT '收费科室ID',
  `charge_department_name` varchar(50) DEFAULT NULL COMMENT '收费科室名称',
  `invoice_no` varchar(50) DEFAULT NULL COMMENT '发票号码',
  `invoice_flag` tinyint DEFAULT '0' COMMENT '是否开具发票：0-否，1-是',
  `remark` text COMMENT '备注',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`charge_id`),
  UNIQUE KEY `uk_charge_no` (`charge_no`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_charge_date` (`charge_date`),
  KEY `idx_charge_status` (`charge_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='收费记录表';

-- ----------------------------
-- 7. 关怀管理相关表
-- ----------------------------

-- 关怀计划表
DROP TABLE IF EXISTS `t_care_plan`;
CREATE TABLE `t_care_plan` (
  `plan_id` bigint NOT NULL AUTO_INCREMENT COMMENT '关怀计划ID',
  `plan_name` varchar(100) NOT NULL COMMENT '计划名称',
  `plan_description` text COMMENT '计划描述',
  `care_type` tinyint NOT NULL COMMENT '关怀类型：1-出院关怀，2-复诊提醒，3-生日关怀，4-节日关怀，5-满意度调查',
  `trigger_conditions` text COMMENT '触发条件（JSON格式）',
  `execute_timing` tinyint NOT NULL DEFAULT '1' COMMENT '执行时机：1-立即执行，2-延迟执行，3-定时执行',
  `delay_minutes` int DEFAULT NULL COMMENT '延迟时间（分钟）',
  `schedule_time` varchar(20) DEFAULT NULL COMMENT '定时执行时间',
  `care_method` tinyint NOT NULL COMMENT '关怀方式：1-电话，2-短信，3-微信，4-邮件，5-上门',
  `template_id` bigint DEFAULT NULL COMMENT '关怀模板ID',
  `template_name` varchar(100) DEFAULT NULL COMMENT '关怀模板名称',
  `target_customers` text COMMENT '目标客户群体（JSON格式）',
  `execute_department_id` bigint DEFAULT NULL COMMENT '执行部门ID',
  `execute_department_name` varchar(50) DEFAULT NULL COMMENT '执行部门名称',
  `default_executor_id` bigint DEFAULT NULL COMMENT '默认执行人ID',
  `default_executor_name` varchar(50) DEFAULT NULL COMMENT '默认执行人姓名',
  `auto_execute_flag` tinyint DEFAULT '0' COMMENT '是否自动执行：0-否，1-是',
  `max_retry_count` int DEFAULT '3' COMMENT '最大重试次数',
  `retry_interval` int DEFAULT '60' COMMENT '重试间隔（分钟）',
  `plan_status` tinyint NOT NULL DEFAULT '1' COMMENT '计划状态：1-启用，0-禁用',
  `priority` tinyint DEFAULT '2' COMMENT '优先级：1-低，2-中，3-高',
  `sort` int DEFAULT '0' COMMENT '排序',
  `remark` text COMMENT '备注',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`plan_id`),
  KEY `idx_care_type` (`care_type`),
  KEY `idx_plan_status` (`plan_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='关怀计划表';

-- 关怀记录表
DROP TABLE IF EXISTS `t_care_record`;
CREATE TABLE `t_care_record` (
  `care_id` bigint NOT NULL AUTO_INCREMENT COMMENT '关怀记录ID',
  `care_plan_id` bigint DEFAULT NULL COMMENT '关怀计划ID',
  `customer_id` bigint NOT NULL COMMENT '客户ID',
  `customer_name` varchar(50) NOT NULL COMMENT '客户姓名',
  `customer_phone` varchar(20) NOT NULL COMMENT '客户电话',
  `record_id` bigint DEFAULT NULL COMMENT '病历ID',
  `care_type` tinyint NOT NULL COMMENT '关怀类型：1-出院关怀，2-复诊提醒，3-生日关怀，4-节日关怀，5-满意度调查',
  `care_method` tinyint NOT NULL COMMENT '关怀方式：1-电话，2-短信，3-微信，4-邮件，5-上门',
  `template_id` bigint DEFAULT NULL COMMENT '关怀模板ID',
  `care_content` text NOT NULL COMMENT '关怀内容',
  `planned_time` datetime DEFAULT NULL COMMENT '计划执行时间',
  `actual_time` datetime DEFAULT NULL COMMENT '实际执行时间',
  `execute_status` tinyint NOT NULL DEFAULT '1' COMMENT '执行状态：1-待执行，2-执行中，3-已完成，4-已取消，5-执行失败',
  `execute_result` text COMMENT '执行结果',
  `customer_feedback` text COMMENT '客户反馈',
  `satisfaction_score` tinyint DEFAULT NULL COMMENT '满意度评分（1-5分）',
  `need_follow_up` tinyint DEFAULT '0' COMMENT '是否需要后续跟进：0-否，1-是',
  `follow_up_plan` text COMMENT '后续跟进计划',
  `execute_user_id` bigint DEFAULT NULL COMMENT '执行人员ID',
  `execute_user_name` varchar(50) DEFAULT NULL COMMENT '执行人员姓名',
  `execute_department_id` bigint DEFAULT NULL COMMENT '执行部门ID',
  `execute_department_name` varchar(50) DEFAULT NULL COMMENT '执行部门名称',
  `auto_execute_flag` tinyint DEFAULT '0' COMMENT '自动执行标识：0-否，1-是',
  `retry_count` int DEFAULT '0' COMMENT '重试次数',
  `max_retry_count` int DEFAULT '3' COMMENT '最大重试次数',
  `remark` text COMMENT '备注',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`care_id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_care_type` (`care_type`),
  KEY `idx_execute_status` (`execute_status`),
  KEY `idx_planned_time` (`planned_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='关怀记录表';

-- 关怀模板表
DROP TABLE IF EXISTS `t_care_template`;
CREATE TABLE `t_care_template` (
  `template_id` bigint NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `template_description` text COMMENT '模板描述',
  `care_type` tinyint NOT NULL COMMENT '关怀类型：1-出院关怀，2-复诊提醒，3-生日关怀，4-节日关怀，5-满意度调查',
  `care_method` tinyint NOT NULL COMMENT '关怀方式：1-电话，2-短信，3-微信，4-邮件，5-上门',
  `template_content` text NOT NULL COMMENT '模板内容',
  `template_variables` text COMMENT '模板变量（JSON格式）',
  `use_scene` varchar(200) DEFAULT NULL COMMENT '使用场景',
  `applicable_projects` text COMMENT '适用项目（JSON格式）',
  `applicable_customers` text COMMENT '适用客户群体（JSON格式）',
  `template_category` varchar(50) DEFAULT NULL COMMENT '模板分类',
  `template_tags` varchar(200) DEFAULT NULL COMMENT '模板标签',
  `usage_count` int DEFAULT '0' COMMENT '使用次数',
  `avg_satisfaction` decimal(3,2) DEFAULT NULL COMMENT '平均满意度',
  `template_status` tinyint NOT NULL DEFAULT '1' COMMENT '模板状态：1-启用，0-禁用',
  `default_flag` tinyint DEFAULT '0' COMMENT '是否默认模板：0-否，1-是',
  `sort` int DEFAULT '0' COMMENT '排序',
  `remark` text COMMENT '备注',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`template_id`),
  KEY `idx_care_type` (`care_type`),
  KEY `idx_care_method` (`care_method`),
  KEY `idx_template_status` (`template_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='关怀模板表';

-- 8. 知识库管理相关表
-- ----------------------------

-- 知识库分类表
DROP TABLE IF EXISTS `t_knowledge_category`;
CREATE TABLE `t_knowledge_category` (
  `category_id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `category_name` varchar(100) NOT NULL COMMENT '分类名称',
  `category_description` text COMMENT '分类描述',
  `parent_id` bigint DEFAULT '0' COMMENT '父分类ID',
  `category_level` tinyint DEFAULT '1' COMMENT '分类层级',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `category_status` tinyint NOT NULL DEFAULT '1' COMMENT '分类状态：1-启用，0-禁用',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`category_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_category_status` (`category_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识库分类表';

-- 知识库标签表
DROP TABLE IF EXISTS `t_knowledge_tag`;
CREATE TABLE `t_knowledge_tag` (
  `tag_id` bigint NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `tag_name` varchar(50) NOT NULL COMMENT '标签名称',
  `tag_color` varchar(20) DEFAULT '#1890ff' COMMENT '标签颜色',
  `tag_description` varchar(200) DEFAULT NULL COMMENT '标签描述',
  `use_count` int DEFAULT '0' COMMENT '使用次数',
  `tag_status` tinyint NOT NULL DEFAULT '1' COMMENT '标签状态：1-启用，0-禁用',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`tag_id`),
  UNIQUE KEY `uk_tag_name` (`tag_name`),
  KEY `idx_tag_status` (`tag_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识库标签表';

-- 知识库文档表
DROP TABLE IF EXISTS `t_knowledge_document`;
CREATE TABLE `t_knowledge_document` (
  `document_id` bigint NOT NULL AUTO_INCREMENT COMMENT '文档ID',
  `document_title` varchar(200) NOT NULL COMMENT '文档标题',
  `document_content` longtext COMMENT '文档内容',
  `document_summary` varchar(500) DEFAULT NULL COMMENT '文档摘要',
  `category_id` bigint DEFAULT NULL COMMENT '分类ID',
  `category_name` varchar(100) DEFAULT NULL COMMENT '分类名称',
  `document_type` tinyint DEFAULT '1' COMMENT '文档类型：1-文本，2-图片，3-视频，4-文件',
  `file_url` varchar(500) DEFAULT NULL COMMENT '文件URL',
  `file_size` bigint DEFAULT NULL COMMENT '文件大小（字节）',
  `view_count` int DEFAULT '0' COMMENT '查看次数',
  `like_count` int DEFAULT '0' COMMENT '点赞次数',
  `share_count` int DEFAULT '0' COMMENT '分享次数',
  `document_status` tinyint NOT NULL DEFAULT '1' COMMENT '文档状态：1-草稿，2-已发布，3-已下线',
  `is_public` tinyint DEFAULT '1' COMMENT '是否公开：1-公开，0-私有',
  `author_id` bigint NOT NULL COMMENT '作者ID',
  `author_name` varchar(50) NOT NULL COMMENT '作者姓名',
  `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`document_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_author_id` (`author_id`),
  KEY `idx_document_status` (`document_status`),
  KEY `idx_publish_time` (`publish_time`),
  FULLTEXT KEY `ft_title_content` (`document_title`,`document_content`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识库文档表';

-- 知识库文档标签关联表
DROP TABLE IF EXISTS `t_knowledge_document_tag`;
CREATE TABLE `t_knowledge_document_tag` (
  `relation_id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `document_id` bigint NOT NULL COMMENT '文档ID',
  `tag_id` bigint NOT NULL COMMENT '标签ID',
  `tag_name` varchar(50) NOT NULL COMMENT '标签名称',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`relation_id`),
  UNIQUE KEY `uk_document_tag` (`document_id`,`tag_id`),
  KEY `idx_document_id` (`document_id`),
  KEY `idx_tag_id` (`tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识库文档标签关联表';

-- 9. 营销活动管理相关表
-- ----------------------------

-- 营销活动表
DROP TABLE IF EXISTS `t_marketing_campaign`;
CREATE TABLE `t_marketing_campaign` (
  `campaign_id` bigint NOT NULL AUTO_INCREMENT COMMENT '活动ID',
  `campaign_name` varchar(200) NOT NULL COMMENT '活动名称',
  `campaign_description` text COMMENT '活动描述',
  `campaign_type` tinyint NOT NULL COMMENT '活动类型：1-线上活动，2-线下活动，3-混合活动',
  `campaign_category` varchar(50) DEFAULT NULL COMMENT '活动分类',
  `target_audience` varchar(200) DEFAULT NULL COMMENT '目标受众',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime NOT NULL COMMENT '结束时间',
  `budget_amount` decimal(12,2) DEFAULT '0.00' COMMENT '预算金额',
  `actual_cost` decimal(12,2) DEFAULT '0.00' COMMENT '实际花费',
  `expected_participants` int DEFAULT '0' COMMENT '预期参与人数',
  `actual_participants` int DEFAULT '0' COMMENT '实际参与人数',
  `expected_leads` int DEFAULT '0' COMMENT '预期线索数',
  `actual_leads` int DEFAULT '0' COMMENT '实际线索数',
  `campaign_status` tinyint NOT NULL DEFAULT '1' COMMENT '活动状态：1-筹备中，2-进行中，3-已结束，4-已取消',
  `responsible_user_id` bigint NOT NULL COMMENT '负责人ID',
  `responsible_user_name` varchar(50) NOT NULL COMMENT '负责人姓名',
  `remark` text COMMENT '备注',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`campaign_id`),
  KEY `idx_campaign_status` (`campaign_status`),
  KEY `idx_responsible_user` (`responsible_user_id`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_end_time` (`end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='营销活动表';

-- 营销活动推广渠道表
DROP TABLE IF EXISTS `t_marketing_promotion`;
CREATE TABLE `t_marketing_promotion` (
  `promotion_id` bigint NOT NULL AUTO_INCREMENT COMMENT '推广ID',
  `campaign_id` bigint NOT NULL COMMENT '活动ID',
  `promotion_channel` varchar(50) NOT NULL COMMENT '推广渠道',
  `promotion_content` text COMMENT '推广内容',
  `promotion_cost` decimal(10,2) DEFAULT '0.00' COMMENT '推广费用',
  `click_count` int DEFAULT '0' COMMENT '点击次数',
  `view_count` int DEFAULT '0' COMMENT '浏览次数',
  `conversion_count` int DEFAULT '0' COMMENT '转化次数',
  `promotion_status` tinyint NOT NULL DEFAULT '1' COMMENT '推广状态：1-进行中，2-已暂停，3-已结束',
  `start_time` datetime NOT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`promotion_id`),
  KEY `idx_campaign_id` (`campaign_id`),
  KEY `idx_promotion_status` (`promotion_status`),
  KEY `idx_start_time` (`start_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='营销活动推广渠道表';

-- 营销活动效果跟踪表
DROP TABLE IF EXISTS `t_marketing_tracking`;
CREATE TABLE `t_marketing_tracking` (
  `tracking_id` bigint NOT NULL AUTO_INCREMENT COMMENT '跟踪ID',
  `campaign_id` bigint NOT NULL COMMENT '活动ID',
  `promotion_id` bigint DEFAULT NULL COMMENT '推广ID',
  `tracking_date` date NOT NULL COMMENT '跟踪日期',
  `metric_type` varchar(50) NOT NULL COMMENT '指标类型',
  `metric_value` decimal(12,2) NOT NULL COMMENT '指标值',
  `metric_unit` varchar(20) DEFAULT NULL COMMENT '指标单位',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`tracking_id`),
  KEY `idx_campaign_id` (`campaign_id`),
  KEY `idx_promotion_id` (`promotion_id`),
  KEY `idx_tracking_date` (`tracking_date`),
  KEY `idx_metric_type` (`metric_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='营销活动效果跟踪表';

-- 设置外键检查
SET FOREIGN_KEY_CHECKS = 1;

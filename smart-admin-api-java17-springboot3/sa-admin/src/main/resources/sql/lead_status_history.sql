-- 线索状态变更历史表
CREATE TABLE `t_lead_status_history` (
  `history_id` bigint NOT NULL AUTO_INCREMENT COMMENT '历史记录ID',
  `lead_id` bigint NOT NULL COMMENT '线索ID',
  `from_status` int NOT NULL COMMENT '原状态：1新线索 2跟进中 3已预约 4已到院 5已转化 6已关闭',
  `to_status` int NOT NULL COMMENT '新状态：1新线索 2跟进中 3已预约 4已到院 5已转化 6已关闭',
  `trigger_type` varchar(50) NOT NULL COMMENT '触发类型：FOLLOW_ADD添加跟进 APPOINTMENT_CREATE创建预约 ARRIVAL_CONFIRM确认到院 CONVERSION_COMPLETE完成转化 MANUAL_CLOSE手动关闭 MANUAL手动操作',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_name` varchar(50) NOT NULL COMMENT '创建人',
  PRIMARY KEY (`history_id`),
  KEY `idx_lead_id` (`lead_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_to_status` (`to_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='线索状态变更历史表';

-- 为现有线索创建初始状态记录（可选，用于数据迁移）
INSERT INTO `t_lead_status_history` (`lead_id`, `from_status`, `to_status`, `trigger_type`, `remark`, `create_time`, `create_name`)
SELECT
    `lead_id`,
    1 as `from_status`,
    COALESCE(`lead_status`, 1) as `to_status`,
    'SYSTEM_INIT' as `trigger_type`,
    '系统初始化状态记录' as `remark`,
    COALESCE(`create_time`, NOW()) as `create_time`,
    'system' as `create_name`
FROM `t_lead`
WHERE NOT EXISTS (
    SELECT 1 FROM `t_lead_status_history` h WHERE h.`lead_id` = `t_lead`.`lead_id`
);

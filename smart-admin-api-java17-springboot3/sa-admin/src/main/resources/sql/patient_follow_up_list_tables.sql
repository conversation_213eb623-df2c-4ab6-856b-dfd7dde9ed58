-- 患者回访列表数据库表结构
-- Author: 1024创新实验室-主任：卓大
-- Date: 2025-08-01 10:00:00
-- Description: 患者回访列表功能，当患者分配助理时自动创建回访记录

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 患者回访列表表
-- ----------------------------

-- 患者回访列表表
DROP TABLE IF EXISTS `t_patient_follow_up_list`;
CREATE TABLE `t_patient_follow_up_list` (
  `follow_up_list_id` bigint NOT NULL AUTO_INCREMENT COMMENT '回访列表ID',
  `patient_id` bigint NOT NULL COMMENT '患者ID',
  `patient_name` varchar(50) NOT NULL COMMENT '患者姓名',
  `patient_phone` varchar(20) NOT NULL COMMENT '患者电话',
  `patient_wechat` varchar(50) DEFAULT NULL COMMENT '患者微信',
  `gender` tinyint DEFAULT NULL COMMENT '性别：1-男，2-女',
  `age` int DEFAULT NULL COMMENT '年龄',
  
  -- 分配信息
  `assigned_assistant_id` bigint NOT NULL COMMENT '分配的助理ID',
  `assigned_assistant_name` varchar(50) NOT NULL COMMENT '分配的助理姓名',
  `assigned_doctor_id` bigint DEFAULT NULL COMMENT '分配的医生ID',
  `assigned_doctor_name` varchar(50) DEFAULT NULL COMMENT '分配的医生姓名',
  `department_id` bigint DEFAULT NULL COMMENT '所属部门ID',
  `department_name` varchar(50) DEFAULT NULL COMMENT '所属部门名称',
  
  -- 患者诊断信息
  `visit_date` date DEFAULT NULL COMMENT '到诊日期',
  `diagnosis_status` tinyint DEFAULT NULL COMMENT '诊断状态：1-待诊断，2-诊断中，3-已诊断，4-治疗中，5-治疗完成，6-复诊，7-已完成',
  `treatment_plan` text COMMENT '治疗方案',
  `medication_advice` text COMMENT '用药建议',
  `next_visit_date` date DEFAULT NULL COMMENT '下次复诊日期',
  
  -- 回访状态
  `follow_up_status` tinyint NOT NULL DEFAULT '1' COMMENT '回访状态：1-待回访，2-回访中，3-已回访，4-暂停回访，5-结束回访',
  `follow_up_priority` tinyint DEFAULT '2' COMMENT '回访优先级：1-高，2-中，3-低',
  `last_follow_up_time` datetime DEFAULT NULL COMMENT '最后回访时间',
  `next_follow_up_time` datetime DEFAULT NULL COMMENT '下次回访时间',
  `follow_up_count` int DEFAULT '0' COMMENT '回访次数',
  
  -- 患者标签和备注
  `patient_tags` varchar(500) DEFAULT NULL COMMENT '患者标签',
  `follow_up_notes` text COMMENT '回访备注',
  `special_attention` text COMMENT '特别关注事项',
  
  -- 权限控制
  `data_scope_employee_ids` text COMMENT '数据权限员工ID列表(JSON格式)',
  
  -- 系统字段
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`follow_up_list_id`),
  UNIQUE KEY `uk_patient_assistant` (`patient_id`, `assigned_assistant_id`),
  KEY `idx_patient_id` (`patient_id`),
  KEY `idx_assigned_assistant` (`assigned_assistant_id`),
  KEY `idx_assigned_doctor` (`assigned_doctor_id`),
  KEY `idx_department_id` (`department_id`),
  KEY `idx_follow_up_status` (`follow_up_status`),
  KEY `idx_follow_up_priority` (`follow_up_priority`),
  KEY `idx_next_follow_up_time` (`next_follow_up_time`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='患者回访列表表';

-- ----------------------------
-- 回访列表操作记录表
-- ----------------------------

-- 回访列表操作记录表
DROP TABLE IF EXISTS `t_patient_follow_up_operation`;
CREATE TABLE `t_patient_follow_up_operation` (
  `operation_id` bigint NOT NULL AUTO_INCREMENT COMMENT '操作记录ID',
  `follow_up_list_id` bigint NOT NULL COMMENT '回访列表ID',
  `patient_id` bigint NOT NULL COMMENT '患者ID',
  `operation_type` tinyint NOT NULL COMMENT '操作类型：1-创建回访，2-更新状态，3-添加备注，4-分配助理，5-暂停回访，6-恢复回访，7-结束回访',
  `operation_content` text COMMENT '操作内容',
  `old_value` text COMMENT '原值',
  `new_value` text COMMENT '新值',
  `operation_user_id` bigint NOT NULL COMMENT '操作人ID',
  `operation_user_name` varchar(50) NOT NULL COMMENT '操作人姓名',
  `operation_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  
  PRIMARY KEY (`operation_id`),
  KEY `idx_follow_up_list_id` (`follow_up_list_id`),
  KEY `idx_patient_id` (`patient_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_operation_time` (`operation_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='回访列表操作记录表';

-- 设置外键约束
SET FOREIGN_KEY_CHECKS = 1;

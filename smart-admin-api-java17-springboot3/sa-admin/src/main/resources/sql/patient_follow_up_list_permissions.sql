-- 患者回访列表权限配置
-- 作者: 1024创新实验室-主任：卓大
-- 日期: 2025-08-01

-- 1. 添加患者回访列表菜单
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time)
VALUES (
    7020,
    '患者回访列表',
    3,
    7000,
    1,
    '/followup/patient-list',
    '/views/business/hospital/followup/patient-follow-up-list.vue',
    1,
    'hospital:followup:patient-list:query,hospital:followup:patient-list:update',
    '',
    'TeamOutlined',
    NULL,
    0,
    '',
    1,
    1,
    0,
    0,
    1,
    NOW(),
    1,
    NOW()
);

-- 2. 添加患者回访列表相关权限点
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time)
VALUES
-- 查询权限
(70201, '患者回访列表-查询', 2, 7020, 1, '', '', 1, 'hospital:followup:patient-list:query', '', '', NULL, 0, '', 1, 1, 0, 0, 1, NOW(), 1, NOW()),
-- 更新权限
(70202, '患者回访列表-更新', 2, 7020, 2, '', '', 1, 'hospital:followup:patient-list:update', '', '', NULL, 0, '', 1, 1, 0, 0, 1, NOW(), 1, NOW()),
-- 批量操作权限
(70203, '患者回访列表-批量操作', 2, 7020, 3, '', '', 1, 'hospital:followup:patient-list:batch', '', '', NULL, 0, '', 1, 1, 0, 0, 1, NOW(), 1, NOW());

-- 3. 为助理角色分配患者回访列表权限
INSERT INTO t_role_menu (role_id, menu_id)
SELECT r.role_id, m.menu_id
FROM t_role r, t_menu m
WHERE r.role_name = '助理'
AND m.menu_id IN (7020, 70201, 70202, 70203)
AND NOT EXISTS (
    SELECT 1 FROM t_role_menu rm
    WHERE rm.role_id = r.role_id AND rm.menu_id = m.menu_id
);

-- 4. 为医生角色分配患者回访列表查询权限
INSERT INTO t_role_menu (role_id, menu_id)
SELECT r.role_id, m.menu_id
FROM t_role r, t_menu m
WHERE r.role_name = '医生'
AND m.menu_id IN (7020, 70201)
AND NOT EXISTS (
    SELECT 1 FROM t_role_menu rm
    WHERE rm.role_id = r.role_id AND rm.menu_id = m.menu_id
);

-- 5. 为管理员角色分配所有患者回访列表权限
INSERT INTO t_role_menu (role_id, menu_id)
SELECT r.role_id, m.menu_id
FROM t_role r, t_menu m
WHERE r.role_name IN ('超级管理员', '管理员')
AND m.menu_id IN (7020, 70201, 70202, 70203)
AND NOT EXISTS (
    SELECT 1 FROM t_role_menu rm
    WHERE rm.role_id = r.role_id AND rm.menu_id = m.menu_id
);

-- 6. 更新回访管理主菜单的排序，确保患者回访列表在第一位
UPDATE t_menu SET sort = 2 WHERE menu_id = 7001 AND menu_name = '回访记录';
UPDATE t_menu SET sort = 3 WHERE menu_id = 7002 AND menu_name = '回访计划';
UPDATE t_menu SET sort = 4 WHERE menu_id = 8108 AND menu_name = '回访计划管理';

-- 验证插入结果
SELECT 
    m.menu_id,
    m.menu_name,
    m.menu_type,
    m.parent_id,
    m.sort,
    m.path,
    m.api_perms,
    m.visible_flag,
    m.disabled_flag
FROM t_menu m 
WHERE m.menu_id IN (7020, 70201, 70202, 70203)
ORDER BY m.menu_id;

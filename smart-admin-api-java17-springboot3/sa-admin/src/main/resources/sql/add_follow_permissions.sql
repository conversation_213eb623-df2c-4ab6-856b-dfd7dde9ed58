-- 添加线索跟进权限菜单
-- 为线索跟进功能添加完整的权限配置

-- 1. 添加跟进权限菜单（如果不存在）
INSERT IGNORE INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES
-- 跟进新增权限
(7008, '线索跟进新增', 3, 5012, 20, NULL, NULL, 2, '/api/lead-follow/add', 'hospital:lead:follow:add', NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
-- 跟进编辑权限
(7009, '线索跟进编辑', 3, 5012, 30, NULL, NULL, 2, '/api/lead-follow/update', 'hospital:lead:follow:update', NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
-- 跟进删除权限
(7010, '线索跟进删除', 3, 5012, 40, NULL, NULL, 2, '/api/lead-follow/delete', 'hospital:lead:follow:delete', NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 2. 为超级管理员添加所有跟进权限
INSERT IGNORE INTO t_role_menu (role_id, menu_id) VALUES
(1, 7008), (1, 7009), (1, 7010);

-- 3. 为客服相关角色添加跟进权限
-- 查找客服相关角色
INSERT IGNORE INTO t_role_menu (role_id, menu_id)
SELECT r.role_id, m.menu_id
FROM t_role r
CROSS JOIN t_menu m
WHERE (r.role_name LIKE '%客服%' OR r.role_code LIKE '%CUSTOMER_SERVICE%')
  AND m.menu_id IN (7007, 7008, 7009);

-- 4. 为营销专员角色添加跟进权限
INSERT IGNORE INTO t_role_menu (role_id, menu_id)
SELECT r.role_id, m.menu_id
FROM t_role r
CROSS JOIN t_menu m
WHERE (r.role_name LIKE '%营销专员%' OR r.role_name LIKE '%专员%')
  AND m.menu_id IN (7007, 7008, 7009);

-- 5. 查看客服赵的角色信息
SELECT 
    e.employee_id, 
    e.actual_name, 
    e.login_name, 
    r.role_id, 
    r.role_name 
FROM t_employee e 
LEFT JOIN t_role_employee re ON e.employee_id = re.employee_id 
LEFT JOIN t_role r ON re.role_id = r.role_id 
WHERE e.login_name = 'kefu_zhao';

-- 6. 验证权限配置
SELECT 
    r.role_id,
    r.role_name,
    m.menu_id,
    m.menu_name,
    m.web_perms
FROM t_role r
JOIN t_role_menu rm ON r.role_id = rm.role_id
JOIN t_menu m ON rm.menu_id = m.menu_id
WHERE m.web_perms LIKE '%hospital:lead:follow%'
ORDER BY r.role_id, m.menu_id;

-- 线索来源字典数据初始化脚本
-- Author: 1024创新实验室-主任：卓大
-- Date: 2025-07-22

-- 插入线索来源字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`) 
VALUES ('线索来源', 'LEAD_SOURCE', '线索来源字典，用于线索管理模块', 0, NOW(), NOW());

-- 获取字典ID并插入字典数据
SET @dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'LEAD_SOURCE');

-- 插入线索来源字典数据
INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@dict_id, 'ONLINE_CONSULTATION', '在线咨询', '官网、APP等在线咨询渠道', 100, 0, NOW(), NOW()),
(@dict_id, 'PHONE_CONSULTATION', '电话咨询', '客户主动电话咨询', 90, 0, NOW(), NOW()),
(@dict_id, 'WECHAT_CONSULTATION', '微信咨询', '微信公众号、微信群等咨询', 80, 0, NOW(), NOW()),
(@dict_id, 'FRIEND_REFERRAL', '朋友推荐', '老客户推荐新客户', 70, 0, NOW(), NOW()),
(@dict_id, 'ADVERTISEMENT', '广告投放', '百度、抖音、微信等广告投放', 60, 0, NOW(), NOW()),
(@dict_id, 'SOCIAL_MEDIA', '社交媒体', '微博、抖音、小红书等社交平台', 50, 0, NOW(), NOW()),
(@dict_id, 'OFFLINE_ACTIVITY', '线下活动', '健康讲座、义诊活动等', 40, 0, NOW(), NOW()),
(@dict_id, 'PARTNER_REFERRAL', '合作伙伴', '合作机构推荐', 30, 0, NOW(), NOW()),
(@dict_id, 'SEARCH_ENGINE', '搜索引擎', 'SEO自然搜索流量', 20, 0, NOW(), NOW()),
(@dict_id, 'OTHER', '其他', '其他未分类来源', 10, 0, NOW(), NOW());

-- 查询验证
SELECT d.dict_name, d.dict_code, dd.data_value, dd.data_label, dd.sort_order
FROM t_dict d 
LEFT JOIN t_dict_data dd ON d.dict_id = dd.dict_id 
WHERE d.dict_code = 'LEAD_SOURCE'
ORDER BY dd.sort_order DESC;

-- 回访计划增强版数据库表结构
-- Author: 1024创新实验室-主任：卓大
-- Date: 2025-07-30 10:00:00
-- Description: 回访计划功能模块，与到诊患者建立关联关系

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 1. 回访计划表 (增强版)
-- ----------------------------

-- 删除现有表并重新创建
DROP TABLE IF EXISTS `t_follow_up_plan_enhanced`;
CREATE TABLE `t_follow_up_plan_enhanced` (
  `plan_id` bigint NOT NULL AUTO_INCREMENT COMMENT '计划ID',
  `plan_no` varchar(50) NOT NULL COMMENT '计划编号',
  `plan_name` varchar(100) NOT NULL COMMENT '计划名称',
  `plan_description` text COMMENT '计划描述',
  
  -- 计划类型和目标
  `plan_type` tinyint NOT NULL COMMENT '计划类型：1-定期回访，2-节日回访，3-特殊回访，4-术后回访，5-用药跟进',
  `target_patient_type` tinyint NOT NULL COMMENT '目标患者类型：1-所有已分配医生患者，2-指定医生患者，3-指定科室患者，4-指定标签患者',
  
  -- 筛选条件 (JSON格式存储复杂筛选条件)
  `filter_conditions` longtext COMMENT '筛选条件(JSON格式)：医生ID列表、科室ID列表、患者标签、时间范围等',
  `target_doctor_ids` text COMMENT '目标医生ID列表(逗号分隔)',
  `target_department_ids` text COMMENT '目标科室ID列表(逗号分隔)',
  `target_patient_tags` varchar(500) COMMENT '目标患者标签',
  `visit_date_range_start` date COMMENT '到诊日期范围开始',
  `visit_date_range_end` date COMMENT '到诊日期范围结束',
  
  -- 回访设置
  `follow_up_method` tinyint NOT NULL COMMENT '回访方式：1-电话，2-微信，3-短信，4-邮件，5-上门',
  `follow_up_content_template` text COMMENT '回访内容模板',
  `follow_up_questions` longtext COMMENT '回访问题列表(JSON格式)',
  
  -- 执行设置
  `plan_start_date` date NOT NULL COMMENT '计划开始日期',
  `plan_end_date` date COMMENT '计划结束日期',
  `execution_frequency` tinyint NOT NULL COMMENT '执行频率：1-每日，2-每周，3-每月，4-每季度，5-每年，6-一次性',
  `execution_time` time COMMENT '执行时间',
  `execution_days` varchar(20) COMMENT '执行日期(周几或月几号)',
  `delay_days_after_visit` int DEFAULT '0' COMMENT '到诊后延迟天数',
  
  -- 优先级和状态
  `priority_level` tinyint DEFAULT '2' COMMENT '优先级：1-高，2-中，3-低',
  `plan_status` tinyint NOT NULL DEFAULT '1' COMMENT '计划状态：1-草稿，2-执行中，3-已暂停，4-已完成，5-已取消',
  `auto_generate_records` tinyint DEFAULT '1' COMMENT '是否自动生成回访记录：0-否，1-是',
  
  -- 分配信息
  `responsible_user_id` bigint NOT NULL COMMENT '负责人ID',
  `responsible_user_name` varchar(50) NOT NULL COMMENT '负责人姓名',
  `department_id` bigint NOT NULL COMMENT '所属部门ID',
  `department_name` varchar(50) NOT NULL COMMENT '所属部门名称',
  
  -- 统计信息
  `expected_patient_count` int DEFAULT '0' COMMENT '预期患者数量',
  `actual_patient_count` int DEFAULT '0' COMMENT '实际患者数量',
  `completed_count` int DEFAULT '0' COMMENT '已完成数量',
  `success_rate` decimal(5,2) DEFAULT '0.00' COMMENT '成功率(%)',
  
  -- 执行时间记录
  `last_execution_time` datetime COMMENT '最后执行时间',
  `next_execution_time` datetime COMMENT '下次执行时间',
  `total_executions` int DEFAULT '0' COMMENT '总执行次数',
  
  `remark` text COMMENT '备注',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`plan_id`),
  UNIQUE KEY `uk_plan_no` (`plan_no`),
  KEY `idx_plan_type` (`plan_type`),
  KEY `idx_target_patient_type` (`target_patient_type`),
  KEY `idx_plan_status` (`plan_status`),
  KEY `idx_responsible_user` (`responsible_user_id`),
  KEY `idx_department_id` (`department_id`),
  KEY `idx_next_execution_time` (`next_execution_time`),
  KEY `idx_plan_start_date` (`plan_start_date`),
  KEY `idx_priority_level` (`priority_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='回访计划表(增强版)';

-- ----------------------------
-- 2. 回访计划患者关联表
-- ----------------------------

-- 回访计划患者关联表
DROP TABLE IF EXISTS `t_follow_up_plan_patient`;
CREATE TABLE `t_follow_up_plan_patient` (
  `relation_id` bigint NOT NULL AUTO_INCREMENT COMMENT '关联ID',
  `plan_id` bigint NOT NULL COMMENT '计划ID',
  `patient_id` bigint NOT NULL COMMENT '患者ID',
  `patient_name` varchar(50) NOT NULL COMMENT '患者姓名',
  `patient_phone` varchar(20) NOT NULL COMMENT '患者电话',
  `assigned_doctor_id` bigint NOT NULL COMMENT '分配医生ID',
  `assigned_doctor_name` varchar(50) NOT NULL COMMENT '分配医生姓名',
  `department_id` bigint NOT NULL COMMENT '科室ID',
  `department_name` varchar(50) NOT NULL COMMENT '科室名称',
  
  -- 关联信息
  `visit_date` date COMMENT '到诊日期',
  `diagnosis_status` tinyint COMMENT '诊断状态',
  `patient_tags` varchar(500) COMMENT '患者标签',
  `inclusion_reason` varchar(200) COMMENT '纳入原因',
  
  -- 执行状态
  `execution_status` tinyint NOT NULL DEFAULT '1' COMMENT '执行状态：1-待执行，2-执行中，3-已完成，4-已跳过，5-执行失败',
  `scheduled_time` datetime COMMENT '计划执行时间',
  `actual_execution_time` datetime COMMENT '实际执行时间',
  `follow_up_record_id` bigint COMMENT '关联的回访记录ID',
  
  -- 优先级
  `priority_level` tinyint DEFAULT '2' COMMENT '优先级：1-高，2-中，3-低',
  `retry_count` int DEFAULT '0' COMMENT '重试次数',
  `max_retry_count` int DEFAULT '3' COMMENT '最大重试次数',
  
  `remark` varchar(500) COMMENT '备注',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`relation_id`),
  UNIQUE KEY `uk_plan_patient` (`plan_id`, `patient_id`),
  KEY `idx_plan_id` (`plan_id`),
  KEY `idx_patient_id` (`patient_id`),
  KEY `idx_assigned_doctor` (`assigned_doctor_id`),
  KEY `idx_department_id` (`department_id`),
  KEY `idx_execution_status` (`execution_status`),
  KEY `idx_scheduled_time` (`scheduled_time`),
  KEY `idx_visit_date` (`visit_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='回访计划患者关联表';

-- ----------------------------
-- 3. 回访记录表 (增强版，关联回访计划)
-- ----------------------------

-- 删除现有表并重新创建
DROP TABLE IF EXISTS `t_follow_up_record_enhanced`;
CREATE TABLE `t_follow_up_record_enhanced` (
  `record_id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `record_no` varchar(50) NOT NULL COMMENT '记录编号',
  
  -- 关联信息
  `plan_id` bigint COMMENT '关联计划ID',
  `plan_patient_relation_id` bigint COMMENT '关联计划患者关系ID',
  `patient_id` bigint NOT NULL COMMENT '患者ID',
  `patient_name` varchar(50) NOT NULL COMMENT '患者姓名',
  `patient_phone` varchar(20) NOT NULL COMMENT '患者电话',
  `assigned_doctor_id` bigint COMMENT '分配医生ID',
  `assigned_doctor_name` varchar(50) COMMENT '分配医生姓名',
  
  -- 回访信息
  `follow_up_type` tinyint NOT NULL COMMENT '回访类型：1-用药跟进，2-住院跟进，3-疗程跟进，4-次卡核销跟进，5-复诊提醒跟进，6-康复指导跟进',
  `follow_up_method` tinyint NOT NULL COMMENT '回访方式：1-电话，2-微信，3-短信，4-邮件，5-上门',
  `follow_up_content` text NOT NULL COMMENT '回访内容',
  `follow_up_questions` longtext COMMENT '回访问题及答案(JSON格式)',
  `follow_up_result` varchar(500) COMMENT '回访结果',
  `patient_feedback` text COMMENT '患者反馈',
  `satisfaction_score` tinyint COMMENT '满意度评分（1-5分）',
  
  -- 时间信息
  `scheduled_time` datetime NOT NULL COMMENT '计划回访时间',
  `actual_time` datetime COMMENT '实际回访时间',
  `duration_minutes` int COMMENT '回访时长(分钟)',
  
  -- 执行人信息
  `follow_up_user_id` bigint NOT NULL COMMENT '回访人ID',
  `follow_up_user_name` varchar(50) NOT NULL COMMENT '回访人姓名',
  `department_id` bigint NOT NULL COMMENT '部门ID',
  `department_name` varchar(50) NOT NULL COMMENT '部门名称',
  
  -- 状态信息
  `follow_up_status` tinyint NOT NULL DEFAULT '1' COMMENT '回访状态：1-待回访，2-已回访，3-无法联系，4-已取消',
  `priority_level` tinyint DEFAULT '2' COMMENT '优先级：1-高，2-中，3-低',
  `is_auto_generated` tinyint DEFAULT '0' COMMENT '是否自动生成：0-否，1-是',
  
  -- 后续跟进
  `need_follow_up` tinyint DEFAULT '0' COMMENT '是否需要后续跟进：0-否，1-是',
  `next_follow_up_date` date COMMENT '下次跟进日期',
  `follow_up_advice` text COMMENT '跟进建议',
  
  `remark` text COMMENT '备注',
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`record_id`),
  UNIQUE KEY `uk_record_no` (`record_no`),
  KEY `idx_plan_id` (`plan_id`),
  KEY `idx_plan_patient_relation` (`plan_patient_relation_id`),
  KEY `idx_patient_id` (`patient_id`),
  KEY `idx_assigned_doctor` (`assigned_doctor_id`),
  KEY `idx_follow_up_user` (`follow_up_user_id`),
  KEY `idx_department_id` (`department_id`),
  KEY `idx_scheduled_time` (`scheduled_time`),
  KEY `idx_follow_up_status` (`follow_up_status`),
  KEY `idx_follow_up_type` (`follow_up_type`),
  KEY `idx_is_auto_generated` (`is_auto_generated`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='回访记录表(增强版)';

-- ----------------------------
-- 4. 回访计划执行日志表
-- ----------------------------

-- 回访计划执行日志表
DROP TABLE IF EXISTS `t_follow_up_plan_execution_log`;
CREATE TABLE `t_follow_up_plan_execution_log` (
  `log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `plan_id` bigint NOT NULL COMMENT '计划ID',
  `execution_time` datetime NOT NULL COMMENT '执行时间',
  `execution_type` tinyint NOT NULL COMMENT '执行类型：1-自动执行，2-手动执行',
  `execution_status` tinyint NOT NULL COMMENT '执行状态：1-成功，2-失败，3-部分成功',
  
  -- 执行结果
  `target_patient_count` int DEFAULT '0' COMMENT '目标患者数量',
  `actual_generated_count` int DEFAULT '0' COMMENT '实际生成记录数量',
  `success_count` int DEFAULT '0' COMMENT '成功数量',
  `failed_count` int DEFAULT '0' COMMENT '失败数量',
  `skipped_count` int DEFAULT '0' COMMENT '跳过数量',
  
  -- 执行详情
  `execution_details` longtext COMMENT '执行详情(JSON格式)',
  `error_message` text COMMENT '错误信息',
  `execution_duration_ms` bigint COMMENT '执行耗时(毫秒)',
  
  -- 执行人信息
  `execute_user_id` bigint COMMENT '执行人ID',
  `execute_user_name` varchar(50) COMMENT '执行人姓名',
  
  `remark` varchar(500) COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`log_id`),
  KEY `idx_plan_id` (`plan_id`),
  KEY `idx_execution_time` (`execution_time`),
  KEY `idx_execution_status` (`execution_status`),
  KEY `idx_execute_user` (`execute_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='回访计划执行日志表';

-- ----------------------------
-- 5. 字典数据初始化
-- ----------------------------

-- 插入回访计划类型字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`)
VALUES ('回访计划类型', 'FOLLOW_UP_PLAN_TYPE', '回访计划类型字典', 0, NOW(), NOW());

SET @plan_type_dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'FOLLOW_UP_PLAN_TYPE');

INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@plan_type_dict_id, '1', '定期回访', '定期回访计划', 100, 0, NOW(), NOW()),
(@plan_type_dict_id, '2', '节日回访', '节日关怀回访', 90, 0, NOW(), NOW()),
(@plan_type_dict_id, '3', '特殊回访', '特殊情况回访', 80, 0, NOW(), NOW()),
(@plan_type_dict_id, '4', '术后回访', '手术后回访', 70, 0, NOW(), NOW()),
(@plan_type_dict_id, '5', '用药跟进', '用药情况跟进', 60, 0, NOW(), NOW());

-- 插入目标患者类型字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`)
VALUES ('目标患者类型', 'TARGET_PATIENT_TYPE', '回访计划目标患者类型', 0, NOW(), NOW());

SET @target_type_dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'TARGET_PATIENT_TYPE');

INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@target_type_dict_id, '1', '所有已分配医生患者', '所有已分配医生的患者', 100, 0, NOW(), NOW()),
(@target_type_dict_id, '2', '指定医生患者', '指定医生的患者', 90, 0, NOW(), NOW()),
(@target_type_dict_id, '3', '指定科室患者', '指定科室的患者', 80, 0, NOW(), NOW()),
(@target_type_dict_id, '4', '指定标签患者', '指定标签的患者', 70, 0, NOW(), NOW());

-- 插入执行频率字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`)
VALUES ('执行频率', 'EXECUTION_FREQUENCY', '回访计划执行频率', 0, NOW(), NOW());

SET @frequency_dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'EXECUTION_FREQUENCY');

INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@frequency_dict_id, '1', '每日', '每日执行', 100, 0, NOW(), NOW()),
(@frequency_dict_id, '2', '每周', '每周执行', 90, 0, NOW(), NOW()),
(@frequency_dict_id, '3', '每月', '每月执行', 80, 0, NOW(), NOW()),
(@frequency_dict_id, '4', '每季度', '每季度执行', 70, 0, NOW(), NOW()),
(@frequency_dict_id, '5', '每年', '每年执行', 60, 0, NOW(), NOW()),
(@frequency_dict_id, '6', '一次性', '一次性执行', 50, 0, NOW(), NOW());

-- 插入计划状态字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`)
VALUES ('回访计划状态', 'FOLLOW_UP_PLAN_STATUS', '回访计划状态字典', 0, NOW(), NOW());

SET @plan_status_dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'FOLLOW_UP_PLAN_STATUS');

INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@plan_status_dict_id, '1', '草稿', '计划草稿状态', 100, 0, NOW(), NOW()),
(@plan_status_dict_id, '2', '执行中', '计划执行中', 90, 0, NOW(), NOW()),
(@plan_status_dict_id, '3', '已暂停', '计划已暂停', 80, 0, NOW(), NOW()),
(@plan_status_dict_id, '4', '已完成', '计划已完成', 70, 0, NOW(), NOW()),
(@plan_status_dict_id, '5', '已取消', '计划已取消', 60, 0, NOW(), NOW());

-- 插入执行状态字典
INSERT IGNORE INTO `t_dict` (`dict_name`, `dict_code`, `remark`, `disabled_flag`, `create_time`, `update_time`)
VALUES ('执行状态', 'EXECUTION_STATUS', '回访计划执行状态', 0, NOW(), NOW());

SET @execution_status_dict_id = (SELECT dict_id FROM t_dict WHERE dict_code = 'EXECUTION_STATUS');

INSERT IGNORE INTO `t_dict_data` (`dict_id`, `data_value`, `data_label`, `remark`, `sort_order`, `disabled_flag`, `create_time`, `update_time`) VALUES
(@execution_status_dict_id, '1', '待执行', '等待执行', 100, 0, NOW(), NOW()),
(@execution_status_dict_id, '2', '执行中', '正在执行', 90, 0, NOW(), NOW()),
(@execution_status_dict_id, '3', '已完成', '执行完成', 80, 0, NOW(), NOW()),
(@execution_status_dict_id, '4', '已跳过', '执行跳过', 70, 0, NOW(), NOW()),
(@execution_status_dict_id, '5', '执行失败', '执行失败', 60, 0, NOW(), NOW());

-- ----------------------------
-- 6. 权限配置
-- ----------------------------

-- 插入回访计划管理菜单
INSERT IGNORE INTO `t_menu` (`menu_name`, `menu_type`, `parent_id`, `sort`, `path`, `component`, `perms_type`, `api_perms`, `web_perms`, `icon`, `context_menu_id`, `frame_flag`, `frame_url`, `cache_flag`, `visible_flag`, `disabled_flag`, `deleted_flag`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES
('回访计划管理', 2, (SELECT menu_id FROM (SELECT menu_id FROM t_menu WHERE menu_name = '回访管理' AND deleted_flag = 0 LIMIT 1) AS temp), 20, '/hospital/followup/plan', '/views/business/hospital/followup/plan/FollowUpPlanList.vue', 1, 'hospital:followup:plan:query', 'hospital:followup:plan:query', 'CalendarOutlined', NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW());

-- 获取回访计划管理菜单ID
SET @plan_menu_id = (SELECT menu_id FROM t_menu WHERE menu_name = '回访计划管理' AND deleted_flag = 0 LIMIT 1);

-- 插入回访计划管理相关权限
INSERT IGNORE INTO `t_menu` (`menu_name`, `menu_type`, `parent_id`, `sort`, `path`, `component`, `perms_type`, `api_perms`, `web_perms`, `icon`, `context_menu_id`, `frame_flag`, `frame_url`, `cache_flag`, `visible_flag`, `disabled_flag`, `deleted_flag`, `create_user_id`, `create_time`, `update_user_id`, `update_time`) VALUES
('新增回访计划', 3, @plan_menu_id, 10, NULL, NULL, 1, 'hospital:followup:plan:add', 'hospital:followup:plan:add', NULL, NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
('编辑回访计划', 3, @plan_menu_id, 20, NULL, NULL, 1, 'hospital:followup:plan:update', 'hospital:followup:plan:update', NULL, NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
('删除回访计划', 3, @plan_menu_id, 30, NULL, NULL, 1, 'hospital:followup:plan:delete', 'hospital:followup:plan:delete', NULL, NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
('查看回访计划详情', 3, @plan_menu_id, 40, NULL, NULL, 1, 'hospital:followup:plan:detail', 'hospital:followup:plan:detail', NULL, NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
('执行回访计划', 3, @plan_menu_id, 50, NULL, NULL, 1, 'hospital:followup:plan:execute', 'hospital:followup:plan:execute', NULL, NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
('启动回访计划', 3, @plan_menu_id, 60, NULL, NULL, 1, 'hospital:followup:plan:start', 'hospital:followup:plan:start', NULL, NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
('暂停回访计划', 3, @plan_menu_id, 70, NULL, NULL, 1, 'hospital:followup:plan:pause', 'hospital:followup:plan:pause', NULL, NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
('导出回访计划', 3, @plan_menu_id, 80, NULL, NULL, 1, 'hospital:followup:plan:export', 'hospital:followup:plan:export', NULL, NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW()),
('回访计划统计', 3, @plan_menu_id, 90, NULL, NULL, 1, 'hospital:followup:plan:statistics', 'hospital:followup:plan:statistics', NULL, NULL, 0, NULL, 1, 1, 0, 0, 1, NOW(), 1, NOW());

-- 设置外键检查
SET FOREIGN_KEY_CHECKS = 1;

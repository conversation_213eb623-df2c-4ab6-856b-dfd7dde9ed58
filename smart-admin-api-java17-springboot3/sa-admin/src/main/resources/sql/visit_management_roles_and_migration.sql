-- 到诊管理模块角色配置和数据迁移
-- Author: 1024创新实验室-主任：卓大
-- Date: 2025-07-29 10:00:00
-- Description: 创建医生端角色配置和数据迁移脚本

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 1. 创建医生端相关角色
-- ----------------------------

-- 医生角色
INSERT IGNORE INTO t_role (role_id, role_name, role_code, remark, create_time, update_time) VALUES
(101, '医生', 'DOCTOR', '医生角色，可以查看分配给自己的患者，进行诊断、开单等操作', NOW(), NOW());

-- 治疗助理角色
INSERT IGNORE INTO t_role (role_id, role_name, role_code, remark, create_time, update_time) VALUES
(102, '治疗助理', 'ASSISTANT', '治疗助理角色，可以查看分配给自己的患者，进行跟进等操作', NOW(), NOW());

-- 医生主管角色 (可以管理下属助理)
INSERT IGNORE INTO t_role (role_id, role_name, role_code, remark, create_time, update_time) VALUES
(103, '医生主管', 'DOCTOR_MANAGER', '医生主管角色，可以查看自己的患者和下属助理的患者', NOW(), NOW());

-- 科室主任角色
INSERT IGNORE INTO t_role (role_id, role_name, role_code, remark, create_time, update_time) VALUES
(104, '科室主任', 'DEPARTMENT_DIRECTOR', '科室主任角色，可以查看本科室所有患者数据', NOW(), NOW());

-- 护士长角色
INSERT IGNORE INTO t_role (role_id, role_name, role_code, remark, create_time, update_time) VALUES
(105, '护士长', 'HEAD_NURSE', '护士长角色，可以查看本科室护理相关数据', NOW(), NOW());

-- 收费员角色
INSERT IGNORE INTO t_role (role_id, role_name, role_code, remark, create_time, update_time) VALUES
(106, '收费员', 'CASHIER', '收费员角色，主要负责收费管理', NOW(), NOW());

-- ----------------------------
-- 2. 为角色分配权限
-- ----------------------------

-- 医生角色权限 (基础诊疗权限)
INSERT IGNORE INTO t_role_menu (role_id, menu_id, create_time, update_time) VALUES
-- 主菜单
(101, 6001, NOW(), NOW()),
-- 二级菜单
(101, 6002, NOW(), NOW()), -- 患者列表
(101, 6003, NOW(), NOW()), -- 诊断管理
(101, 6004, NOW(), NOW()), -- 开单管理
(101, 6005, NOW(), NOW()), -- 收费管理
-- 患者管理权限
(101, 6101, NOW(), NOW()), -- 患者查询
(101, 6103, NOW(), NOW()), -- 患者编辑
(101, 6106, NOW(), NOW()), -- 状态更新
-- 诊断管理权限
(101, 6111, NOW(), NOW()), -- 诊断查询
(101, 6112, NOW(), NOW()), -- 诊断新增
(101, 6113, NOW(), NOW()), -- 诊断编辑
-- 开单管理权限
(101, 6121, NOW(), NOW()), -- 开单查询
(101, 6122, NOW(), NOW()), -- 开单新增
(101, 6123, NOW(), NOW()), -- 开单编辑
-- 收费查询权限
(101, 6131, NOW(), NOW()); -- 收费查询

-- 治疗助理角色权限 (跟进和基础查看权限)
INSERT IGNORE INTO t_role_menu (role_id, menu_id, create_time, update_time) VALUES
-- 主菜单
(102, 6001, NOW(), NOW()),
-- 二级菜单
(102, 6002, NOW(), NOW()), -- 患者列表
(102, 6006, NOW(), NOW()), -- 跟进管理
-- 患者管理权限
(102, 6101, NOW(), NOW()), -- 患者查询
(102, 6106, NOW(), NOW()), -- 状态更新
-- 跟进管理权限
(102, 6141, NOW(), NOW()), -- 跟进查询
(102, 6142, NOW(), NOW()), -- 跟进新增
(102, 6143, NOW(), NOW()); -- 跟进编辑

-- 医生主管角色权限 (医生权限 + 管理权限)
INSERT IGNORE INTO t_role_menu (role_id, menu_id, create_time, update_time) VALUES
-- 主菜单
(103, 6001, NOW(), NOW()),
-- 二级菜单
(103, 6002, NOW(), NOW()), -- 患者列表
(103, 6003, NOW(), NOW()), -- 诊断管理
(103, 6004, NOW(), NOW()), -- 开单管理
(103, 6005, NOW(), NOW()), -- 收费管理
(103, 6006, NOW(), NOW()), -- 跟进管理
-- 患者管理权限
(103, 6101, NOW(), NOW()), -- 患者查询
(103, 6102, NOW(), NOW()), -- 患者新增
(103, 6103, NOW(), NOW()), -- 患者编辑
(103, 6105, NOW(), NOW()), -- 患者分配
(103, 6106, NOW(), NOW()), -- 状态更新
-- 诊断管理权限
(103, 6111, NOW(), NOW()), -- 诊断查询
(103, 6112, NOW(), NOW()), -- 诊断新增
(103, 6113, NOW(), NOW()), -- 诊断编辑
(103, 6115, NOW(), NOW()), -- 诊断审核
-- 开单管理权限
(103, 6121, NOW(), NOW()), -- 开单查询
(103, 6122, NOW(), NOW()), -- 开单新增
(103, 6123, NOW(), NOW()), -- 开单编辑
(103, 6125, NOW(), NOW()), -- 开单审核
-- 收费管理权限
(103, 6131, NOW(), NOW()), -- 收费查询
(103, 6132, NOW(), NOW()), -- 收费新增
(103, 6133, NOW(), NOW()), -- 收费编辑
-- 跟进管理权限
(103, 6141, NOW(), NOW()), -- 跟进查询
(103, 6142, NOW(), NOW()), -- 跟进新增
(103, 6143, NOW(), NOW()), -- 跟进编辑
(103, 6145, NOW(), NOW()); -- 跟进分配

-- 科室主任角色权限 (全部权限)
INSERT IGNORE INTO t_role_menu (role_id, menu_id, create_time, update_time) 
SELECT 104, menu_id, NOW(), NOW() FROM t_menu WHERE menu_id BETWEEN 6001 AND 6199 AND deleted_flag = 0;

-- 收费员角色权限 (主要是收费相关)
INSERT IGNORE INTO t_role_menu (role_id, menu_id, create_time, update_time) VALUES
-- 主菜单
(106, 6001, NOW(), NOW()),
-- 二级菜单
(106, 6002, NOW(), NOW()), -- 患者列表
(106, 6005, NOW(), NOW()), -- 收费管理
-- 患者查询权限
(106, 6101, NOW(), NOW()), -- 患者查询
-- 收费管理权限
(106, 6131, NOW(), NOW()), -- 收费查询
(106, 6132, NOW(), NOW()), -- 收费新增
(106, 6133, NOW(), NOW()), -- 收费编辑
(106, 6135, NOW(), NOW()); -- 退费处理

-- ----------------------------
-- 3. 配置数据权限范围
-- ----------------------------

-- 医生角色：只能查看分配给自己的数据
INSERT IGNORE INTO t_role_data_scope (data_scope_type, view_type, role_id, create_time, update_time) VALUES
(1, 4, 101, NOW(), NOW()), -- 患者数据：本人数据
(2, 4, 101, NOW(), NOW()); -- 诊疗数据：本人数据

-- 治疗助理角色：只能查看分配给自己的数据
INSERT IGNORE INTO t_role_data_scope (data_scope_type, view_type, role_id, create_time, update_time) VALUES
(1, 4, 102, NOW(), NOW()), -- 患者数据：本人数据
(3, 4, 102, NOW(), NOW()); -- 跟进数据：本人数据

-- 医生主管角色：可以查看自己和下属的数据
INSERT IGNORE INTO t_role_data_scope (data_scope_type, view_type, role_id, create_time, update_time) VALUES
(1, 3, 103, NOW(), NOW()), -- 患者数据：本人及下属数据
(2, 3, 103, NOW(), NOW()), -- 诊疗数据：本人及下属数据
(3, 3, 103, NOW(), NOW()); -- 跟进数据：本人及下属数据

-- 科室主任角色：可以查看本部门所有数据
INSERT IGNORE INTO t_role_data_scope (data_scope_type, view_type, role_id, create_time, update_time) VALUES
(1, 2, 104, NOW(), NOW()), -- 患者数据：本部门数据
(2, 2, 104, NOW(), NOW()), -- 诊疗数据：本部门数据
(3, 2, 104, NOW(), NOW()); -- 跟进数据：本部门数据

-- 收费员角色：可以查看本部门收费相关数据
INSERT IGNORE INTO t_role_data_scope (data_scope_type, view_type, role_id, create_time, update_time) VALUES
(1, 2, 106, NOW(), NOW()), -- 患者数据：本部门数据
(4, 2, 106, NOW(), NOW()); -- 收费数据：本部门数据

-- ----------------------------
-- 4. 数据迁移：从客户表迁移到患者表
-- ----------------------------

-- 迁移现有客户数据到患者表
INSERT INTO t_visit_patient (
    patient_no, patient_name, patient_phone, patient_wechat, patient_email,
    gender, age, birthday, id_card, patient_address,
    visit_status, assigned_doctor_id, assigned_doctor_name,
    department_id, department_name, patient_source, patient_level,
    patient_tags, first_visit_date, last_visit_date, total_consumption,
    remark, deleted_flag, create_user_id, create_time, update_user_id, update_time
)
SELECT
    customer_no, customer_name, customer_phone, customer_wechat, customer_email,
    gender, age, birthday, id_card, address,
    1 as visit_status, -- 默认为未到诊
    responsible_employee_id, responsible_employee_name,
    -- 根据负责员工获取部门信息
    (SELECT department_id FROM t_employee WHERE employee_id = t_customer.responsible_employee_id) as department_id,
    (SELECT d.department_name FROM t_employee e LEFT JOIN t_department d ON e.department_id = d.department_id WHERE e.employee_id = t_customer.responsible_employee_id) as department_name,
    customer_source, customer_level, customer_tags, first_visit_date, last_visit_date, total_consumption,
    remark, deleted_flag, create_user_id, create_time, update_user_id, update_time
FROM t_customer
WHERE deleted_flag = 0;

-- ----------------------------
-- 5. 创建示例数据
-- ----------------------------

-- 创建示例医生助理关系
INSERT IGNORE INTO t_doctor_assistant_relation (
    doctor_id, doctor_name, assistant_id, assistant_name, 
    department_id, department_name, relation_type, start_date, 
    relation_status, create_user_id, create_time
) VALUES
(1, '张医生', 2, '李助理', 1, '内科', 1, CURDATE(), 1, 1, NOW()),
(1, '张医生', 3, '王助理', 1, '内科', 1, CURDATE(), 1, 1, NOW()),
(4, '陈医生', 5, '赵助理', 2, '外科', 1, CURDATE(), 1, 1, NOW());

-- 创建示例诊断模板
INSERT IGNORE INTO t_diagnosis_template (
    template_name, template_description, department_id, department_name,
    disease_category, template_content, template_type, template_status,
    create_user_id, create_time
) VALUES
('内科常规诊断模板', '内科常规疾病诊断模板', 1, '内科', '常见病',
'{"chief_complaint":"","present_illness":"","past_history":"","physical_examination":"","preliminary_diagnosis":"","treatment_plan":""}',
1, 1, 1, NOW()),
('外科手术诊断模板', '外科手术相关诊断模板', 2, '外科', '手术',
'{"chief_complaint":"","present_illness":"","past_history":"","physical_examination":"","preliminary_diagnosis":"","treatment_plan":"","surgery_plan":""}',
2, 1, 1, NOW());

-- ----------------------------
-- 6. 为超级管理员分配所有到诊管理权限
-- ----------------------------

-- 为超级管理员(role_id=1)分配所有到诊管理权限
INSERT IGNORE INTO t_role_menu (role_id, menu_id, create_time, update_time) 
SELECT 1, menu_id, NOW(), NOW() FROM t_menu WHERE menu_id BETWEEN 6001 AND 6199 AND deleted_flag = 0;

-- 设置外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 待办管理系统数据库表结构
-- Author: 1024创新实验室-主任：卓大
-- Date: 2025-08-01 15:00:00
-- Description: 完善的待办工作管理功能，支持自动生成和手动创建待办任务

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- 1. 待办事项表
-- ----------------------------

-- 待办事项表
DROP TABLE IF EXISTS `t_todo_item`;
CREATE TABLE `t_todo_item` (
  `todo_id` bigint NOT NULL AUTO_INCREMENT COMMENT '待办ID',
  `todo_no` varchar(50) NOT NULL COMMENT '待办编号',
  `todo_type` varchar(50) NOT NULL COMMENT '待办类型：lead_follow-线索跟进，appointment_remind-预约提醒，visit_confirm-到诊确认，diagnosis_pending-待诊断，prescription_pending-待开单，follow_up_remind-回访提醒，treatment_follow-治疗跟进，satisfaction_survey-满意度调查',
  `todo_title` varchar(200) NOT NULL COMMENT '待办标题',
  `todo_content` text COMMENT '待办内容描述',
  `todo_status` tinyint NOT NULL DEFAULT '1' COMMENT '待办状态：1-待处理，2-处理中，3-已完成，4-已取消，5-已过期',
  `priority_level` tinyint NOT NULL DEFAULT '2' COMMENT '优先级：1-高，2-中，3-低',
  `urgency_level` tinyint NOT NULL DEFAULT '2' COMMENT '紧急程度：1-紧急，2-一般，3-不紧急',
  
  -- 业务关联信息
  `business_type` varchar(50) NOT NULL COMMENT '业务类型：lead-线索，appointment-预约，patient-患者，follow_up-回访',
  `business_id` bigint NOT NULL COMMENT '业务ID',
  `business_no` varchar(50) DEFAULT NULL COMMENT '业务编号',
  `customer_id` bigint DEFAULT NULL COMMENT '关联客户ID',
  `customer_name` varchar(50) DEFAULT NULL COMMENT '客户姓名',
  `customer_phone` varchar(20) DEFAULT NULL COMMENT '客户电话',
  
  -- 时间信息
  `due_time` datetime NOT NULL COMMENT '截止时间',
  `remind_time` datetime DEFAULT NULL COMMENT '提醒时间',
  `start_time` datetime DEFAULT NULL COMMENT '开始处理时间',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `estimated_duration` int DEFAULT NULL COMMENT '预估处理时长(分钟)',
  
  -- 分配和处理信息
  `assigned_employee_id` bigint NOT NULL COMMENT '分配员工ID',
  `assigned_employee_name` varchar(50) NOT NULL COMMENT '分配员工姓名',
  `assigned_department_id` bigint DEFAULT NULL COMMENT '分配部门ID',
  `assigned_department_name` varchar(50) DEFAULT NULL COMMENT '分配部门名称',
  `handler_employee_id` bigint DEFAULT NULL COMMENT '处理员工ID',
  `handler_employee_name` varchar(50) DEFAULT NULL COMMENT '处理员工姓名',
  
  -- 自动化信息
  `auto_generated` tinyint DEFAULT '0' COMMENT '是否自动生成：0-手动创建，1-自动生成',
  `generation_rule` varchar(100) DEFAULT NULL COMMENT '生成规则',
  `parent_todo_id` bigint DEFAULT NULL COMMENT '父待办ID（用于关联待办）',
  `repeat_type` varchar(20) DEFAULT NULL COMMENT '重复类型：daily-每日，weekly-每周，monthly-每月',
  `repeat_interval` int DEFAULT NULL COMMENT '重复间隔',
  `next_repeat_time` datetime DEFAULT NULL COMMENT '下次重复时间',
  
  -- 扩展信息
  `tags` varchar(500) DEFAULT NULL COMMENT '标签（JSON格式）',
  `extra_data` text COMMENT '扩展数据（JSON格式）',
  `completion_note` text COMMENT '完成备注',
  `cancel_reason` varchar(200) DEFAULT NULL COMMENT '取消原因',
  
  -- 系统字段
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`todo_id`),
  UNIQUE KEY `uk_todo_no` (`todo_no`),
  KEY `idx_todo_type` (`todo_type`),
  KEY `idx_todo_status` (`todo_status`),
  KEY `idx_priority_level` (`priority_level`),
  KEY `idx_business_type_id` (`business_type`, `business_id`),
  KEY `idx_assigned_employee` (`assigned_employee_id`),
  KEY `idx_handler_employee` (`handler_employee_id`),
  KEY `idx_due_time` (`due_time`),
  KEY `idx_remind_time` (`remind_time`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='待办事项表';

-- ----------------------------
-- 2. 待办处理记录表
-- ----------------------------

-- 待办处理记录表
DROP TABLE IF EXISTS `t_todo_operation`;
CREATE TABLE `t_todo_operation` (
  `operation_id` bigint NOT NULL AUTO_INCREMENT COMMENT '操作记录ID',
  `todo_id` bigint NOT NULL COMMENT '待办ID',
  `operation_type` tinyint NOT NULL COMMENT '操作类型：1-创建，2-分配，3-开始处理，4-暂停，5-完成，6-取消，7-更新，8-提醒',
  `operation_content` text COMMENT '操作内容',
  `old_status` tinyint DEFAULT NULL COMMENT '原状态',
  `new_status` tinyint DEFAULT NULL COMMENT '新状态',
  `old_assignee_id` bigint DEFAULT NULL COMMENT '原分配人ID',
  `new_assignee_id` bigint DEFAULT NULL COMMENT '新分配人ID',
  `operation_result` varchar(500) DEFAULT NULL COMMENT '操作结果',
  `operation_user_id` bigint NOT NULL COMMENT '操作人ID',
  `operation_user_name` varchar(50) NOT NULL COMMENT '操作人姓名',
  `operation_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  
  PRIMARY KEY (`operation_id`),
  KEY `idx_todo_id` (`todo_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_operation_user` (`operation_user_id`),
  KEY `idx_operation_time` (`operation_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='待办处理记录表';

-- ----------------------------
-- 3. 待办提醒表
-- ----------------------------

-- 待办提醒表
DROP TABLE IF EXISTS `t_todo_reminder`;
CREATE TABLE `t_todo_reminder` (
  `reminder_id` bigint NOT NULL AUTO_INCREMENT COMMENT '提醒ID',
  `todo_id` bigint NOT NULL COMMENT '待办ID',
  `reminder_type` tinyint NOT NULL DEFAULT '1' COMMENT '提醒方式：1-系统通知，2-短信，3-邮件，4-微信',
  `reminder_time` datetime NOT NULL COMMENT '提醒时间',
  `reminder_status` tinyint NOT NULL DEFAULT '1' COMMENT '提醒状态：1-待发送，2-已发送，3-发送失败，4-已取消',
  `reminder_content` text COMMENT '提醒内容',
  `target_user_id` bigint NOT NULL COMMENT '目标用户ID',
  `target_user_name` varchar(50) NOT NULL COMMENT '目标用户姓名',
  `send_time` datetime DEFAULT NULL COMMENT '发送时间',
  `send_result` varchar(500) DEFAULT NULL COMMENT '发送结果',
  `retry_count` int DEFAULT '0' COMMENT '重试次数',
  `max_retry_count` int DEFAULT '3' COMMENT '最大重试次数',
  
  PRIMARY KEY (`reminder_id`),
  KEY `idx_todo_id` (`todo_id`),
  KEY `idx_reminder_type` (`reminder_type`),
  KEY `idx_reminder_time` (`reminder_time`),
  KEY `idx_reminder_status` (`reminder_status`),
  KEY `idx_target_user` (`target_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='待办提醒表';

-- ----------------------------
-- 4. 待办生成规则表
-- ----------------------------

-- 待办生成规则表
DROP TABLE IF EXISTS `t_todo_generation_rule`;
CREATE TABLE `t_todo_generation_rule` (
  `rule_id` bigint NOT NULL AUTO_INCREMENT COMMENT '规则ID',
  `rule_name` varchar(100) NOT NULL COMMENT '规则名称',
  `rule_code` varchar(50) NOT NULL COMMENT '规则编码',
  `rule_description` text COMMENT '规则描述',
  `business_type` varchar(50) NOT NULL COMMENT '业务类型',
  `trigger_event` varchar(50) NOT NULL COMMENT '触发事件',
  `trigger_conditions` text COMMENT '触发条件（JSON格式）',
  `todo_template` text NOT NULL COMMENT '待办模板（JSON格式）',
  `rule_status` tinyint NOT NULL DEFAULT '1' COMMENT '规则状态：1-启用，0-禁用',
  `priority` int DEFAULT '0' COMMENT '规则优先级',
  `execution_count` int DEFAULT '0' COMMENT '执行次数',
  `last_execution_time` datetime DEFAULT NULL COMMENT '最后执行时间',
  
  -- 系统字段
  `deleted_flag` tinyint NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `create_user_id` bigint NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint DEFAULT NULL COMMENT '更新人ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`rule_id`),
  UNIQUE KEY `uk_rule_code` (`rule_code`),
  KEY `idx_business_type` (`business_type`),
  KEY `idx_trigger_event` (`trigger_event`),
  KEY `idx_rule_status` (`rule_status`),
  KEY `idx_priority` (`priority`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='待办生成规则表';

-- ----------------------------
-- 5. 待办统计表
-- ----------------------------

-- 待办统计表
DROP TABLE IF EXISTS `t_todo_statistics`;
CREATE TABLE `t_todo_statistics` (
  `stat_id` bigint NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `stat_date` date NOT NULL COMMENT '统计日期',
  `employee_id` bigint NOT NULL COMMENT '员工ID',
  `employee_name` varchar(50) NOT NULL COMMENT '员工姓名',
  `department_id` bigint DEFAULT NULL COMMENT '部门ID',
  `department_name` varchar(50) DEFAULT NULL COMMENT '部门名称',
  
  -- 待办数量统计
  `total_count` int DEFAULT '0' COMMENT '总待办数',
  `pending_count` int DEFAULT '0' COMMENT '待处理数',
  `processing_count` int DEFAULT '0' COMMENT '处理中数',
  `completed_count` int DEFAULT '0' COMMENT '已完成数',
  `cancelled_count` int DEFAULT '0' COMMENT '已取消数',
  `overdue_count` int DEFAULT '0' COMMENT '已逾期数',
  
  -- 优先级统计
  `high_priority_count` int DEFAULT '0' COMMENT '高优先级数',
  `medium_priority_count` int DEFAULT '0' COMMENT '中优先级数',
  `low_priority_count` int DEFAULT '0' COMMENT '低优先级数',
  
  -- 类型统计
  `lead_follow_count` int DEFAULT '0' COMMENT '线索跟进数',
  `appointment_remind_count` int DEFAULT '0' COMMENT '预约提醒数',
  `visit_confirm_count` int DEFAULT '0' COMMENT '到诊确认数',
  `diagnosis_pending_count` int DEFAULT '0' COMMENT '待诊断数',
  `follow_up_remind_count` int DEFAULT '0' COMMENT '回访提醒数',
  
  -- 效率统计
  `avg_completion_time` decimal(10,2) DEFAULT NULL COMMENT '平均完成时间(小时)',
  `completion_rate` decimal(5,2) DEFAULT NULL COMMENT '完成率(%)',
  `overdue_rate` decimal(5,2) DEFAULT NULL COMMENT '逾期率(%)',
  
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  PRIMARY KEY (`stat_id`),
  UNIQUE KEY `uk_date_employee` (`stat_date`, `employee_id`),
  KEY `idx_stat_date` (`stat_date`),
  KEY `idx_employee_id` (`employee_id`),
  KEY `idx_department_id` (`department_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='待办统计表';

-- ----------------------------
-- 6. 初始化待办生成规则数据
-- ----------------------------

-- 插入待办生成规则
INSERT INTO `t_todo_generation_rule` (`rule_name`, `rule_code`, `rule_description`, `business_type`, `trigger_event`, `trigger_conditions`, `todo_template`, `rule_status`, `priority`, `create_user_id`) VALUES
('线索跟进提醒', 'LEAD_FOLLOW_REMIND', '当线索设置下次跟进时间时，自动生成跟进待办', 'lead', 'follow_time_set', '{"conditions": [{"field": "next_follow_time", "operator": "not_null"}]}', '{"todo_type": "lead_follow", "title_template": "线索跟进: {customer_name}", "content_template": "客户电话: {customer_phone}, 计划跟进时间: {next_follow_time}", "priority_level": 2, "estimated_duration": 30}', 1, 1, 1),
('预约提醒', 'APPOINTMENT_REMIND', '预约日期当天自动生成预约提醒待办', 'appointment', 'appointment_confirmed', '{"conditions": [{"field": "appointment_status", "operator": "eq", "value": 2}]}', '{"todo_type": "appointment_remind", "title_template": "预约提醒: {customer_name}", "content_template": "预约项目: {project_name}, 预约时间: {appointment_date} {appointment_time}", "priority_level": 1, "estimated_duration": 15}', 1, 2, 1),
('到诊确认', 'VISIT_CONFIRM', '患者预约时间到达时自动生成到诊确认待办', 'patient', 'appointment_time_reached', '{"conditions": [{"field": "appointment_status", "operator": "eq", "value": 2}]}', '{"todo_type": "visit_confirm", "title_template": "到诊确认: {customer_name}", "content_template": "预约时间: {appointment_time}, 请确认患者是否到诊", "priority_level": 1, "estimated_duration": 10}', 1, 3, 1),
('回访提醒', 'FOLLOW_UP_REMIND', '回访计划时间到达时自动生成回访待办', 'follow_up', 'follow_up_time_reached', '{"conditions": [{"field": "next_follow_up_time", "operator": "lte", "value": "now"}]}', '{"todo_type": "follow_up_remind", "title_template": "患者回访: {customer_name}", "content_template": "回访类型: {follow_up_type}, 计划回访时间: {next_follow_up_time}", "priority_level": 2, "estimated_duration": 20}', 1, 4, 1);

SET FOREIGN_KEY_CHECKS = 1;

-- 医院营销系统完整菜单重构脚本
-- 保留所有原有功能模块，重新组织为二级菜单结构
-- Author: 1024创新实验室-主任：卓大
-- Date: 2025-07-23 14:00:00

-- 1. 删除所有现有的医院菜单和权限关联
DELETE FROM t_role_menu WHERE menu_id BETWEEN 5001 AND 5300;
DELETE FROM t_menu WHERE menu_id BETWEEN 5001 AND 5300;

-- 2. 创建新的二级菜单结构（五个主菜单）
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES

-- ==================== 线索管理 (主菜单) ====================
(5001, '线索管理', 1, 0, 10, '/lead', NULL, 1, NULL, NULL, 'UserAddOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5002, '线索列表', 2, 5001, 1, '/lead/list', NULL, 1, 'hospital:lead:query', NULL, 'UnorderedListOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5003, '线索跟进', 2, 5001, 2, '/lead/follow', NULL, 1, 'hospital:lead:follow:query', NULL, 'CommentOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),

-- ==================== 预约管理 (主菜单) ====================
(5010, '预约管理', 1, 0, 20, '/appointment', NULL, 1, NULL, NULL, 'CalendarOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5011, '预约列表', 2, 5010, 1, '/appointment/list', NULL, 1, 'hospital:appointment:query', NULL, 'UnorderedListOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5012, '医生排班', 2, 5010, 2, '/appointment/schedule', NULL, 1, 'hospital:doctorSchedule:query', NULL, 'ScheduleOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5013, '项目管理', 2, 5010, 3, '/appointment/project', NULL, 1, 'hospital:project:query', NULL, 'ProjectOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),

-- ==================== 客户管理 (主菜单) ====================
(5020, '客户管理', 1, 0, 30, '/customer', NULL, 1, NULL, NULL, 'TeamOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5021, '客户列表', 2, 5020, 1, '/customer/list', NULL, 1, 'hospital:customer:query', NULL, 'UnorderedListOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5022, '病历管理', 2, 5020, 2, '/customer/medical', NULL, 1, 'hospital:medicalRecord:query', NULL, 'FileTextOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),

-- ==================== 营销分析 (主菜单) ====================
(5030, '营销分析', 1, 0, 40, '/analytics', NULL, 1, NULL, NULL, 'DashboardOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5031, '仪表盘', 2, 5030, 1, '/analytics/dashboard', NULL, 1, 'hospital:dashboard:query', NULL, 'BarChartOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5032, 'ROI分析', 2, 5030, 2, '/analytics/roi', NULL, 1, 'hospital:marketing:roi', NULL, 'LineChartOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5033, '活动管理', 2, 5030, 3, '/analytics/campaigns', NULL, 1, 'hospital:marketing:campaigns', NULL, 'CalendarOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5034, '数据统计', 2, 5030, 4, '/analytics/statistics', NULL, 1, 'hospital:statistics:query', NULL, 'PieChartOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),

-- ==================== 系统管理 (主菜单) ====================
(5040, '系统管理', 1, 0, 50, '/system', NULL, 1, NULL, NULL, 'SettingOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5041, '财务管理', 2, 5040, 1, '/system/finance', NULL, 1, 'hospital:finance:query', NULL, 'DollarOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5042, '关怀管理', 2, 5040, 2, '/system/care', NULL, 1, 'hospital:care:query', NULL, 'HeartOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5043, '知识库', 2, 5040, 3, '/system/knowledge', NULL, 1, 'hospital:knowledge:query', NULL, 'BookOutlined', NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 3. 创建API权限点
INSERT INTO t_menu (menu_id, menu_name, menu_type, parent_id, sort, path, component, perms_type, api_perms, web_perms, icon, context_menu_id, frame_flag, frame_url, cache_flag, visible_flag, disabled_flag, deleted_flag, create_user_id, create_time, update_user_id, update_time) VALUES

-- 线索管理API权限
(5101, '线索查询', 3, 5002, 1, NULL, NULL, 2, 'hospital:lead:query', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5102, '线索新增', 3, 5002, 2, NULL, NULL, 2, 'hospital:lead:add', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5103, '线索编辑', 3, 5002, 3, NULL, NULL, 2, 'hospital:lead:edit', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5104, '线索删除', 3, 5002, 4, NULL, NULL, 2, 'hospital:lead:delete', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5105, '线索导出', 3, 5002, 5, NULL, NULL, 2, 'hospital:lead:export', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5106, '线索分配', 3, 5002, 6, NULL, NULL, 2, 'hospital:lead:assign', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 线索跟进API权限
(5111, '跟进查询', 3, 5003, 1, NULL, NULL, 2, 'hospital:lead:follow:query', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5112, '跟进新增', 3, 5003, 2, NULL, NULL, 2, 'hospital:lead:follow:add', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5113, '跟进编辑', 3, 5003, 3, NULL, NULL, 2, 'hospital:lead:follow:edit', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5114, '跟进删除', 3, 5003, 4, NULL, NULL, 2, 'hospital:lead:follow:delete', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 预约管理API权限
(5121, '预约查询', 3, 5011, 1, NULL, NULL, 2, 'hospital:appointment:query', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5122, '预约新增', 3, 5011, 2, NULL, NULL, 2, 'hospital:appointment:add', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5123, '预约编辑', 3, 5011, 3, NULL, NULL, 2, 'hospital:appointment:edit', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5124, '预约删除', 3, 5011, 4, NULL, NULL, 2, 'hospital:appointment:delete', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5125, '确认到诊', 3, 5011, 5, NULL, NULL, 2, 'hospital:appointment:arrive', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 医生排班API权限
(5131, '排班查询', 3, 5012, 1, NULL, NULL, 2, 'hospital:doctorSchedule:query', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5132, '排班新增', 3, 5012, 2, NULL, NULL, 2, 'hospital:doctorSchedule:add', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5133, '排班编辑', 3, 5012, 3, NULL, NULL, 2, 'hospital:doctorSchedule:edit', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5134, '排班删除', 3, 5012, 4, NULL, NULL, 2, 'hospital:doctorSchedule:delete', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 项目管理API权限
(5141, '项目查询', 3, 5013, 1, NULL, NULL, 2, 'hospital:project:query', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5142, '项目新增', 3, 5013, 2, NULL, NULL, 2, 'hospital:project:add', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5143, '项目编辑', 3, 5013, 3, NULL, NULL, 2, 'hospital:project:edit', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5144, '项目删除', 3, 5013, 4, NULL, NULL, 2, 'hospital:project:delete', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 客户管理API权限
(5151, '客户查询', 3, 5021, 1, NULL, NULL, 2, 'hospital:customer:query', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5152, '客户新增', 3, 5021, 2, NULL, NULL, 2, 'hospital:customer:add', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5153, '客户编辑', 3, 5021, 3, NULL, NULL, 2, 'hospital:customer:edit', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5154, '客户删除', 3, 5021, 4, NULL, NULL, 2, 'hospital:customer:delete', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5155, '客户转化', 3, 5021, 5, NULL, NULL, 2, 'hospital:customer:convert', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 病历管理API权限
(5161, '病历查询', 3, 5022, 1, NULL, NULL, 2, 'hospital:medicalRecord:query', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5162, '病历新增', 3, 5022, 2, NULL, NULL, 2, 'hospital:medicalRecord:add', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5163, '病历编辑', 3, 5022, 3, NULL, NULL, 2, 'hospital:medicalRecord:edit', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5164, '病历删除', 3, 5022, 4, NULL, NULL, 2, 'hospital:medicalRecord:delete', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 仪表盘API权限
(5171, '仪表盘查询', 3, 5031, 1, NULL, NULL, 2, 'hospital:dashboard:query', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5172, '仪表盘统计', 3, 5031, 2, NULL, NULL, 2, 'hospital:dashboard:statistics', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),

-- ROI分析API权限
(5181, 'ROI查询', 3, 5032, 1, NULL, NULL, 2, 'hospital:marketing:roi', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5182, 'ROI分析', 3, 5032, 2, NULL, NULL, 2, 'hospital:marketing:roi:analysis', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 活动管理API权限
(5191, '活动查询', 3, 5033, 1, NULL, NULL, 2, 'hospital:marketing:campaigns', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5192, '活动新增', 3, 5033, 2, NULL, NULL, 2, 'hospital:marketing:campaigns:add', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5193, '活动编辑', 3, 5033, 3, NULL, NULL, 2, 'hospital:marketing:campaigns:edit', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5194, '活动删除', 3, 5033, 4, NULL, NULL, 2, 'hospital:marketing:campaigns:delete', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 数据统计API权限
(5201, '统计查询', 3, 5034, 1, NULL, NULL, 2, 'hospital:statistics:query', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5202, '统计分析', 3, 5034, 2, NULL, NULL, 2, 'hospital:statistics:analysis', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 财务管理API权限
(5211, '财务查询', 3, 5041, 1, NULL, NULL, 2, 'hospital:finance:query', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5212, '财务管理', 3, 5041, 2, NULL, NULL, 2, 'hospital:finance:manage', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 关怀管理API权限
(5221, '关怀查询', 3, 5042, 1, NULL, NULL, 2, 'hospital:care:query', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5222, '关怀管理', 3, 5042, 2, NULL, NULL, 2, 'hospital:care:manage', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),

-- 知识库API权限
(5231, '知识库查询', 3, 5043, 1, NULL, NULL, 2, 'hospital:knowledge:query', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW()),
(5232, '知识库管理', 3, 5043, 2, NULL, NULL, 2, 'hospital:knowledge:manage', NULL, NULL, NULL, 0, NULL, 0, 1, 0, 0, 1, NOW(), 1, NOW());

-- 4. 为超级管理员分配所有权限
INSERT INTO t_role_menu (role_id, menu_id) VALUES
-- 主菜单
(1, 5001), (1, 5010), (1, 5020), (1, 5030), (1, 5040),
-- 二级菜单
(1, 5002), (1, 5003), (1, 5011), (1, 5012), (1, 5013), (1, 5021), (1, 5022), (1, 5031), (1, 5032), (1, 5033), (1, 5034), (1, 5041), (1, 5042), (1, 5043),
-- API权限
(1, 5101), (1, 5102), (1, 5103), (1, 5104), (1, 5105), (1, 5106),
(1, 5111), (1, 5112), (1, 5113), (1, 5114),
(1, 5121), (1, 5122), (1, 5123), (1, 5124), (1, 5125),
(1, 5131), (1, 5132), (1, 5133), (1, 5134),
(1, 5141), (1, 5142), (1, 5143), (1, 5144),
(1, 5151), (1, 5152), (1, 5153), (1, 5154), (1, 5155),
(1, 5161), (1, 5162), (1, 5163), (1, 5164),
(1, 5171), (1, 5172), (1, 5181), (1, 5182),
(1, 5191), (1, 5192), (1, 5193), (1, 5194),
(1, 5201), (1, 5202), (1, 5211), (1, 5212),
(1, 5221), (1, 5222), (1, 5231), (1, 5232);

-- 5. 验证菜单结构
SELECT
    m.menu_id,
    m.menu_name,
    m.menu_type,
    m.parent_id,
    m.path,
    m.icon,
    m.sort,
    CASE m.menu_type
        WHEN 1 THEN '目录'
        WHEN 2 THEN '菜单'
        WHEN 3 THEN '功能点'
    END as menu_type_name
FROM t_menu m
WHERE m.menu_id BETWEEN 5001 AND 5300
    AND m.deleted_flag = 0
ORDER BY m.parent_id, m.sort;

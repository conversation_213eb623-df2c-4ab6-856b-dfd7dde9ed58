-- 回访计划表
CREATE TABLE `t_follow_up_plan` (
  `plan_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '计划ID',
  `plan_name` varchar(200) NOT NULL COMMENT '计划名称',
  `plan_description` text COMMENT '计划描述',
  `plan_type` tinyint(4) NOT NULL COMMENT '计划类型：1-定期回访，2-节日回访，3-生日回访，4-满意度调查，5-产品推广',
  `target_customer_type` tinyint(4) NOT NULL COMMENT '目标客户类型：1-全部客户，2-新客户，3-老客户，4-VIP客户，5-指定客户',
  `customer_filter_conditions` json COMMENT '客户筛选条件（JSON格式）',
  `follow_up_method` tinyint(4) NOT NULL COMMENT '回访方式：1-电话回访，2-短信回访，3-微信回访，4-邮件回访，5-上门回访',
  `follow_up_content_template` text COMMENT '回访内容模板',
  `plan_start_date` date NOT NULL COMMENT '计划开始日期',
  `plan_end_date` date COMMENT '计划结束日期',
  `execution_frequency` tinyint(4) NOT NULL COMMENT '执行频率：1-一次性，2-每日，3-每周，4-每月，5-每季度，6-每年',
  `execution_time` time COMMENT '执行时间',
  `execution_days` varchar(50) COMMENT '执行日期（如：周一到周五为1,2,3,4,5）',
  `priority_level` tinyint(4) NOT NULL DEFAULT 3 COMMENT '优先级：1-低，2-中，3-高，4-紧急',
  `plan_status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '计划状态：1-待执行，2-执行中，3-已暂停，4-已完成，5-已取消',
  `auto_generate_records` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否自动生成回访记录：0-否，1-是',
  `responsible_user_id` bigint(20) NOT NULL COMMENT '负责人ID',
  `responsible_user_name` varchar(64) NOT NULL COMMENT '负责人姓名',
  `expected_customer_count` int(11) DEFAULT 0 COMMENT '预计客户数量',
  `actual_customer_count` int(11) DEFAULT 0 COMMENT '实际客户数量',
  `completed_count` int(11) DEFAULT 0 COMMENT '已完成数量',
  `success_rate` decimal(5,2) DEFAULT 0.00 COMMENT '成功率（%）',
  `last_execution_time` datetime COMMENT '最后执行时间',
  `next_execution_time` datetime COMMENT '下次执行时间',
  `remark` varchar(500) COMMENT '备注',
  `deleted_flag` tinyint(1) NOT NULL DEFAULT 0 COMMENT '删除标志：0-未删除，1-已删除',
  `create_user_id` bigint(20) NOT NULL COMMENT '创建人ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user_id` bigint(20) NOT NULL COMMENT '更新人ID',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`plan_id`),
  KEY `idx_plan_status` (`plan_status`),
  KEY `idx_responsible_user` (`responsible_user_id`),
  KEY `idx_plan_type` (`plan_type`),
  KEY `idx_execution_time` (`next_execution_time`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='回访计划表';

-- 回访计划执行记录表
CREATE TABLE `t_follow_up_plan_execution` (
  `execution_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '执行记录ID',
  `plan_id` bigint(20) NOT NULL COMMENT '计划ID',
  `execution_date` date NOT NULL COMMENT '执行日期',
  `execution_time` datetime NOT NULL COMMENT '执行时间',
  `target_customer_count` int(11) NOT NULL DEFAULT 0 COMMENT '目标客户数量',
  `generated_record_count` int(11) NOT NULL DEFAULT 0 COMMENT '生成记录数量',
  `execution_status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '执行状态：1-执行中，2-执行成功，3-执行失败',
  `execution_result` text COMMENT '执行结果描述',
  `error_message` text COMMENT '错误信息',
  `execution_duration` int(11) COMMENT '执行耗时（秒）',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`execution_id`),
  KEY `idx_plan_id` (`plan_id`),
  KEY `idx_execution_date` (`execution_date`),
  KEY `idx_execution_status` (`execution_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='回访计划执行记录表';

-- 为回访记录表添加计划ID字段
ALTER TABLE `t_follow_up_record` ADD COLUMN `plan_id` bigint(20) COMMENT '关联的计划ID' AFTER `follow_up_id`;
ALTER TABLE `t_follow_up_record` ADD INDEX `idx_plan_id` (`plan_id`);

package net.lab1024.sa.base.module.support.apiencrypt.service;

/**
 * 接口加密、解密 Service
 *
 * <AUTHOR>
 * @Date 2023/10/21 11:41:46
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright  <a href="https://1024lab.net">1024创新实验室</a>
 */

public interface ApiEncryptService {

    /**
     * 解密
     * @param data
     * @return
     */
    String decrypt(String data);

    /**
     * 加密
     *
     * @param data
     * @return
     */
    String encrypt(String data);

}

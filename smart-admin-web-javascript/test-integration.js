/**
 * 线索详情增强组件集成测试脚本
 * 用于验证组件是否正确集成到项目中
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 测试文件路径
const testFiles = [
  'src/views/business/hospital/lead/lead-list.vue',
  'src/views/business/hospital/lead/components/lead-detail-enhanced.vue',
  'src/views/business/hospital/lead/components/ai-analysis-panel.vue',
  'src/views/business/hospital/lead/lead-integration-demo.vue'
];

// 检查文件是否存在
function checkFileExists(filePath) {
  const fullPath = path.join(__dirname, filePath);
  return fs.existsSync(fullPath);
}

// 检查文件内容
function checkFileContent(filePath, searchStrings) {
  const fullPath = path.join(__dirname, filePath);
  if (!fs.existsSync(fullPath)) {
    return { exists: false, matches: [] };
  }
  
  const content = fs.readFileSync(fullPath, 'utf8');
  const matches = searchStrings.map(str => ({
    search: str,
    found: content.includes(str)
  }));
  
  return { exists: true, matches };
}

// 运行测试
function runTests() {
  console.log('🚀 开始线索详情增强组件集成测试...\n');
  
  // 1. 检查文件存在性
  console.log('📁 检查文件存在性:');
  testFiles.forEach(file => {
    const exists = checkFileExists(file);
    console.log(`  ${exists ? '✅' : '❌'} ${file}`);
  });
  
  console.log('\n📋 检查集成内容:');
  
  // 2. 检查线索列表页面集成
  const listPageChecks = checkFileContent('src/views/business/hospital/lead/lead-list.vue', [
    'import LeadDetailEnhanced',
    'leadDetailEnhanced',
    'showLeadDetailEnhanced',
    '详情(增强版)'
  ]);
  
  console.log('  线索列表页面 (lead-list.vue):');
  listPageChecks.matches.forEach(match => {
    console.log(`    ${match.found ? '✅' : '❌'} ${match.search}`);
  });
  
  // 3. 检查增强组件功能
  const enhancedComponentChecks = checkFileContent('src/views/business/hospital/lead/components/lead-detail-enhanced.vue', [
    'lead-detail-enhanced-drawer',
    'AIAnalysisPanel',
    'a-tabs',
    'showDrawer',
    'leadIdOrData'
  ]);
  
  console.log('  增强组件 (lead-detail-enhanced.vue):');
  enhancedComponentChecks.matches.forEach(match => {
    console.log(`    ${match.found ? '✅' : '❌'} ${match.search}`);
  });
  
  // 4. 检查AI分析组件
  const aiComponentChecks = checkFileContent('src/views/business/hospital/lead/components/ai-analysis-panel.vue', [
    'ai-analysis-panel',
    'RobotOutlined',
    'intention-score',
    'communication-suggestions',
    'AI员工销售分析'
  ]);
  
  console.log('  AI分析组件 (ai-analysis-panel.vue):');
  aiComponentChecks.matches.forEach(match => {
    console.log(`    ${match.found ? '✅' : '❌'} ${match.search}`);
  });
  
  // 5. 检查演示页面
  const demoPageChecks = checkFileContent('src/views/business/hospital/lead/lead-integration-demo.vue', [
    'integration-demo',
    'LeadDetailEnhanced',
    'showEnhancedDetail',
    '集成完成'
  ]);
  
  console.log('  演示页面 (lead-integration-demo.vue):');
  demoPageChecks.matches.forEach(match => {
    console.log(`    ${match.found ? '✅' : '❌'} ${match.search}`);
  });
  
  console.log('\n🎯 集成测试总结:');
  
  const allFilesExist = testFiles.every(file => checkFileExists(file));
  const allChecksPass = [
    ...listPageChecks.matches,
    ...enhancedComponentChecks.matches,
    ...aiComponentChecks.matches,
    ...demoPageChecks.matches
  ].every(check => check.found);
  
  if (allFilesExist && allChecksPass) {
    console.log('✅ 所有测试通过！增强组件已成功集成。');
    console.log('\n📝 下一步建议:');
    console.log('  1. 启动开发服务器: npm run dev');
    console.log('  2. 访问线索管理页面测试功能');
    console.log('  3. 点击客户姓名或"详情(增强版)"按钮查看效果');
    console.log('  4. 访问演示页面: /lead/integration-demo');
  } else {
    console.log('❌ 部分测试失败，请检查集成配置。');
  }
  
  console.log('\n🔗 相关链接:');
  console.log('  开发服务器: http://localhost:8082/');
  console.log('  线索管理: http://localhost:8082/#/business/hospital/lead');
  console.log('  集成演示: http://localhost:8082/#/business/hospital/lead/integration-demo');
}

// 执行测试
runTests();

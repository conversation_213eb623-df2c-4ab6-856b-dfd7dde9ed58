/**
 * 患者管理相关常量
 *
 * <AUTHOR>
 * @Date 2025-07-31 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */

// 到诊状态枚举
export const VISIT_STATUS_ENUM = {
  NOT_ARRIVED: {
    value: 1,
    desc: '未到诊',
    color: 'orange',
  },
  ARRIVED: {
    value: 2,
    desc: '已到诊',
    color: 'blue',
  },
  IN_TREATMENT: {
    value: 3,
    desc: '诊疗中',
    color: 'processing',
  },
  COMPLETED: {
    value: 4,
    desc: '已完成',
    color: 'success',
  },
  DISCHARGED: {
    value: 5,
    desc: '已离院',
    color: 'default',
  },
};

export const VISIT_STATUS_OPTIONS = [
  VISIT_STATUS_ENUM.NOT_ARRIVED,
  VISIT_STATUS_ENUM.ARRIVED,
  VISIT_STATUS_ENUM.IN_TREATMENT,
  VISIT_STATUS_ENUM.COMPLETED,
  VISIT_STATUS_ENUM.DISCHARGED,
];

// 患者等级枚举
export const PATIENT_LEVEL_ENUM = {
  NORMAL: {
    value: 1,
    desc: '普通患者',
    color: '#87d068',
  },
  VIP: {
    value: 2,
    desc: 'VIP患者',
    color: '#f50',
  },
  SVIP: {
    value: 3,
    desc: 'SVIP患者',
    color: '#108ee9',
  },
};

export const PATIENT_LEVEL_OPTIONS = [
  PATIENT_LEVEL_ENUM.NORMAL,
  PATIENT_LEVEL_ENUM.VIP,
  PATIENT_LEVEL_ENUM.SVIP,
];

// 诊断状态枚举
export const DIAGNOSIS_STATUS_ENUM = {
  DRAFT: {
    value: 1,
    desc: '草稿',
    color: 'default',
  },
  COMPLETED: {
    value: 2,
    desc: '已完成',
    color: 'success',
  },
  AUDITED: {
    value: 3,
    desc: '已审核',
    color: 'processing',
  },
};

export const DIAGNOSIS_STATUS_OPTIONS = [
  DIAGNOSIS_STATUS_ENUM.DRAFT,
  DIAGNOSIS_STATUS_ENUM.COMPLETED,
  DIAGNOSIS_STATUS_ENUM.AUDITED,
];

// 性别选项
export const GENDER_OPTIONS = [
  { value: 0, label: '未知' },
  { value: 1, label: '男' },
  { value: 2, label: '女' },
];

// 跟进状态枚举
export const FOLLOW_STATUS_ENUM = {
  PENDING: {
    value: 1,
    desc: '待跟进',
    color: 'orange',
  },
  COMPLETED: {
    value: 2,
    desc: '已完成',
    color: 'green',
  },
};

// 跟进类型枚举
export const FOLLOW_TYPE_ENUM = {
  TREATMENT_FOLLOW: {
    value: 1,
    desc: '治疗回访',
    color: 'blue',
  },
  REVISIT_REMINDER: {
    value: 2,
    desc: '复诊提醒',
    color: 'orange',
  },
  SATISFACTION_SURVEY: {
    value: 3,
    desc: '满意度调查',
    color: 'green',
  },
  POST_SURGERY_FOLLOW: {
    value: 4,
    desc: '术后跟进',
    color: 'red',
  },
  MEDICATION_FOLLOW: {
    value: 5,
    desc: '用药跟进',
    color: 'purple',
  },
};

// 操作类型枚举
export const OPERATION_TYPE_ENUM = {
  MEDICAL_OPERATION: {
    value: 1,
    desc: '医疗操作',
    color: 'blue',
  },
  SYSTEM_OPERATION: {
    value: 2,
    desc: '系统操作',
    color: 'green',
  },
  STATUS_CHANGE: {
    value: 3,
    desc: '状态变更',
    color: 'orange',
  },
  INFO_UPDATE: {
    value: 4,
    desc: '信息修改',
    color: 'purple',
  },
};

// AI评分等级
export const AI_SCORE_LEVEL = {
  EXCELLENT: {
    min: 90,
    max: 100,
    desc: '优秀',
    color: '#52c41a',
  },
  GOOD: {
    min: 80,
    max: 89,
    desc: '良好',
    color: '#1890ff',
  },
  AVERAGE: {
    min: 70,
    max: 79,
    desc: '一般',
    color: '#faad14',
  },
  POOR: {
    min: 60,
    max: 69,
    desc: '较差',
    color: '#ff7a45',
  },
  BAD: {
    min: 0,
    max: 59,
    desc: '差',
    color: '#ff4d4f',
  },
};

// 患者标签选项
export const PATIENT_TAG_OPTIONS = [
  { value: 'VIP患者', label: 'VIP患者' },
  { value: '复诊患者', label: '复诊患者' },
  { value: '手术患者', label: '手术患者' },
  { value: '慢性病患者', label: '慢性病患者' },
  { value: '急诊患者', label: '急诊患者' },
  { value: '住院患者', label: '住院患者' },
  { value: '门诊患者', label: '门诊患者' },
  { value: '康复患者', label: '康复患者' },
];

// 跟进方式枚举
export const FOLLOW_METHOD_ENUM = {
  PHONE: {
    value: 1,
    desc: '电话',
    color: 'blue',
  },
  WECHAT: {
    value: 2,
    desc: '微信',
    color: 'green',
  },
  SMS: {
    value: 3,
    desc: '短信',
    color: 'orange',
  },
  EMAIL: {
    value: 4,
    desc: '邮件',
    color: 'purple',
  },
  VISIT: {
    value: 5,
    desc: '上门',
    color: 'red',
  },
};

// 优先级枚举
export const PRIORITY_LEVEL_ENUM = {
  HIGH: {
    value: 1,
    desc: '高',
    color: 'red',
  },
  MEDIUM: {
    value: 2,
    desc: '中',
    color: 'orange',
  },
  LOW: {
    value: 3,
    desc: '低',
    color: 'green',
  },
};

// 满意度评分枚举
export const SATISFACTION_SCORE_ENUM = {
  VERY_DISSATISFIED: {
    value: 1,
    desc: '非常不满意',
    color: 'red',
  },
  DISSATISFIED: {
    value: 2,
    desc: '不满意',
    color: 'orange',
  },
  NEUTRAL: {
    value: 3,
    desc: '一般',
    color: 'yellow',
  },
  SATISFIED: {
    value: 4,
    desc: '满意',
    color: 'blue',
  },
  VERY_SATISFIED: {
    value: 5,
    desc: '非常满意',
    color: 'green',
  },
};

// 表格ID
export const PATIENT_TABLE_ID = 'PATIENT_TABLE_ID';
export const FOLLOW_TABLE_ID = 'FOLLOW_TABLE_ID';
export const OPERATION_TABLE_ID = 'OPERATION_TABLE_ID';

// 默认导出所有常量
const visitConst = {
  VISIT_STATUS_ENUM,
  VISIT_STATUS_OPTIONS,
  PATIENT_LEVEL_ENUM,
  PATIENT_LEVEL_OPTIONS,
  DIAGNOSIS_STATUS_ENUM,
  DIAGNOSIS_STATUS_OPTIONS,
  GENDER_OPTIONS,
  FOLLOW_STATUS_ENUM,
  FOLLOW_TYPE_ENUM,
  FOLLOW_METHOD_ENUM,
  PRIORITY_LEVEL_ENUM,
  SATISFACTION_SCORE_ENUM,
  OPERATION_TYPE_ENUM,
  AI_SCORE_LEVEL,
  PATIENT_TAG_OPTIONS,
  PATIENT_TABLE_ID,
  FOLLOW_TABLE_ID,
  OPERATION_TABLE_ID,
};

export default visitConst;

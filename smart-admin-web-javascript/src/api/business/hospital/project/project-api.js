import { postRequest, getRequest } from '/@/lib/axios';

export const projectApi = {
  /**
   * 获取项目列表
   */
  queryPage: (param) => {
    return postRequest('/project/queryPage', param);
  },

  /**
   * 获取所有项目
   */
  getAll: () => {
    return getRequest('/project/getAll');
  },

  /**
   * 根据ID获取项目详情
   */
  getById: (projectId) => {
    return getRequest(`/project/get/${projectId}`);
  },

  /**
   * 新增项目
   */
  add: (param) => {
    return postRequest('/project/add', param);
  },

  /**
   * 更新项目
   */
  update: (param) => {
    return postRequest('/project/update', param);
  },

  /**
   * 删除项目
   */
  delete: (projectId) => {
    return getRequest(`/project/delete/${projectId}`);
  },

  /**
   * 批量删除项目
   */
  batchDelete: (projectIdList) => {
    return postRequest('/project/batchDelete', projectIdList);
  },
};

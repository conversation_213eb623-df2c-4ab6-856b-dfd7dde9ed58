import { postRequest, getRequest } from '/@/lib/axios';

export const notificationApi = {
  /**
   * 获取通知列表
   */
  queryPage: (param) => {
    return postRequest('/notification/queryPage', param);
  },

  /**
   * 获取未读通知数量
   */
  getUnreadCount: () => {
    return getRequest('/notification/unreadCount');
  },

  /**
   * 标记通知为已读
   */
  markAsRead: (notificationId) => {
    return postRequest(`/notification/markAsRead/${notificationId}`);
  },

  /**
   * 批量标记为已读
   */
  batchMarkAsRead: (notificationIds) => {
    return postRequest('/notification/batchMarkAsRead', notificationIds);
  },

  /**
   * 全部标记为已读
   */
  markAllAsRead: () => {
    return postRequest('/notification/markAllAsRead');
  },

  /**
   * 删除通知
   */
  delete: (notificationId) => {
    return getRequest(`/notification/delete/${notificationId}`);
  },

  /**
   * 批量删除通知
   */
  batchDelete: (notificationIds) => {
    return postRequest('/notification/batchDelete', notificationIds);
  },

  /**
   * 发送通知
   */
  send: (param) => {
    return postRequest('/notification/send', param);
  },
};

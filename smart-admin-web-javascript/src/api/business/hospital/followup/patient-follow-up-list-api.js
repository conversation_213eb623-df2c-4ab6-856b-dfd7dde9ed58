/**
 * 患者回访列表API
 *
 * <AUTHOR>
 * @Date 2025-08-01 10:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */

import { postRequest, getRequest } from '/@/lib/axios'

export const patientFollowUpListApi = {
  /**
   * 分页查询患者回访列表
   * @param {Object} queryForm 查询条件
   * @returns {Promise}
   */
  queryPage: (queryForm) => {
    return postRequest('/api/hospital/followup/patient-list/queryPage', queryForm)
  },

  /**
   * 获取我的回访列表
   * @param {Number} followUpStatus 回访状态（可选）
   * @returns {Promise}
   */
  getMyFollowUpList: (followUpStatus) => {
    const params = followUpStatus ? { followUpStatus } : {}
    return getRequest('/api/hospital/followup/patient-list/my', params)
  },

  /**
   * 更新回访状态
   * @param {Number} followUpListId 回访列表ID
   * @param {Number} followUpStatus 回访状态
   * @returns {Promise}
   */
  updateStatus: (followUpListId, followUpStatus) => {
    return postRequest(`/api/hospital/followup/patient-list/updateStatus/${followUpListId}/${followUpStatus}`)
  },

  /**
   * 批量更新回访状态
   * @param {Array} followUpListIds 回访列表ID数组
   * @param {Number} followUpStatus 回访状态
   * @returns {Promise}
   */
  batchUpdateStatus: (followUpListIds, followUpStatus) => {
    return postRequest('/api/hospital/followup/patient-list/batchUpdateStatus', followUpListIds, {
      params: { followUpStatus }
    })
  }
}

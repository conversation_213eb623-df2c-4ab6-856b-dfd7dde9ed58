<template>
  <div class="leads-dashboard">
    <!-- 时间范围选择器 -->
    <a-row :gutter="16" class="time-range-section">
      <a-col :span="24">
        <a-card class="time-range-card">
          <div class="time-range-controls">
            <a-space>
              <span>时间范围：</span>
              <a-range-picker
                v-model:value="timeRange"
                :presets="timeRangePresets"
                @change="onTimeRangeChange"
                style="width: 300px"
              />
              <a-button type="primary" @click="refreshData">
                <template #icon>
                  <ReloadOutlined />
                </template>
                刷新数据
              </a-button>
            </a-space>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 个人工作统计 -->
    <a-row :gutter="16" class="personal-stats-section">
      <a-col :span="24">
        <a-card title="个人工作统计" class="personal-stats-card">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-statistic
                title="本月预约数量"
                :value="personalStats.monthlyAppointments"
                :value-style="{ color: '#1890ff' }"
              >
                <template #prefix>
                  <CalendarOutlined />
                </template>
              </a-statistic>
            </a-col>
            <a-col :span="6">
              <a-statistic
                title="本月到院数量"
                :value="personalStats.monthlyArrivals"
                :value-style="{ color: '#52c41a' }"
              >
                <template #prefix>
                  <CheckCircleOutlined />
                </template>
              </a-statistic>
            </a-col>
            <a-col :span="6">
              <a-statistic
                title="跟进中客户"
                :value="personalStats.followingCustomers"
                :value-style="{ color: '#faad14' }"
              >
                <template #prefix>
                  <PhoneOutlined />
                </template>
              </a-statistic>
            </a-col>
            <a-col :span="6">
              <a-statistic
                title="本月转化率"
                :value="personalStats.monthlyConversionRate"
                suffix="%"
                :value-style="{ color: '#722ed1' }"
              >
                <template #prefix>
                  <TrophyOutlined />
                </template>
              </a-statistic>
            </a-col>
          </a-row>
        </a-card>
      </a-col>
    </a-row>

    <!-- 线索统计卡片 -->
    <a-row :gutter="16" class="statistics-cards">
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic
            title="总线索数"
            :value="leadsStats.totalLeads"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix>
              <UserAddOutlined />
            </template>
          </a-statistic>
          <div class="stat-footer">
            今日新增: {{ leadsStats.todayLeads }}
          </div>
        </a-card>
      </a-col>
      
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic
            title="跟进中线索"
            :value="leadsStats.followingLeads"
            :value-style="{ color: '#52c41a' }"
          >
            <template #prefix>
              <PhoneOutlined />
            </template>
          </a-statistic>
          <div class="stat-footer">
            转化率: {{ leadsStats.conversionRate }}%
          </div>
        </a-card>
      </a-col>
      
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic
            title="已转化线索"
            :value="leadsStats.convertedLeads"
            :value-style="{ color: '#faad14' }"
          >
            <template #prefix>
              <CheckCircleOutlined />
            </template>
          </a-statistic>
          <div class="stat-footer">
            本月转化: {{ leadsStats.monthlyConverted }}
          </div>
        </a-card>
      </a-col>
      
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic
            title="待跟进线索"
            :value="leadsStats.pendingLeads"
            :value-style="{ color: '#f5222d' }"
          >
            <template #prefix>
              <ClockCircleOutlined />
            </template>
          </a-statistic>
          <div class="stat-footer">
            今日待跟进: {{ leadsStats.todayPending }}
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 图表区域 -->
    <a-row :gutter="16" class="chart-section">
      <a-col :span="12">
        <a-card title="线索来源分布" class="chart-card">
          <div ref="sourceChart" style="height: 300px;"></div>
        </a-card>
      </a-col>
      
      <a-col :span="12">
        <a-card title="线索状态分布" class="chart-card">
          <div ref="statusChart" style="height: 300px;"></div>
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="16" class="chart-section">
      <a-col :span="16">
        <a-card title="线索转化趋势" class="chart-card">
          <div ref="trendChart" style="height: 350px;"></div>
        </a-card>
      </a-col>

      <a-col :span="8">
        <a-card title="线索质量分布" class="chart-card">
          <div ref="qualityChart" style="height: 350px;"></div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 地理分布和人口统计 -->
    <a-row :gutter="16" class="chart-section">
      <a-col :span="12">
        <a-card title="地理分布统计" class="chart-card">
          <div ref="geographicChart" style="height: 350px;"></div>
        </a-card>
      </a-col>

      <a-col :span="12">
        <a-card title="人口统计分析" class="chart-card">
          <a-tabs v-model:activeKey="demographicActiveTab" @change="onDemographicTabChange">
            <a-tab-pane key="age" tab="年龄分布">
              <div ref="ageChart" style="height: 300px;"></div>
            </a-tab-pane>
            <a-tab-pane key="gender" tab="性别分布">
              <div ref="genderChart" style="height: 300px;"></div>
            </a-tab-pane>
            <a-tab-pane key="symptom" tab="症状分布">
              <div ref="symptomChart" style="height: 300px;"></div>
            </a-tab-pane>
          </a-tabs>
        </a-card>
      </a-col>
    </a-row>


  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import {
  UserAddOutlined,
  PhoneOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  CalendarOutlined,
  TrophyOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue';
import * as echarts from 'echarts';
import { dashboardApi } from '/@/api/business/hospital/dashboard-api';
import { smartSentry } from '/@/lib/smart-sentry';
import { message } from 'ant-design-vue';
import { defaultTimeRanges } from '/@/lib/default-time-ranges';
import dayjs from 'dayjs';

// 响应式数据
const leadsStats = ref({
  totalLeads: 0,
  todayLeads: 0,
  followingLeads: 0,
  convertedLeads: 0,
  pendingLeads: 0,
  todayPending: 0,
  conversionRate: 0,
  monthlyConverted: 0
});

const personalStats = ref({
  monthlyAppointments: 0,
  monthlyArrivals: 0,
  followingCustomers: 0,
  monthlyConversionRate: 0
});



// 时间范围相关
const timeRange = ref([dayjs().subtract(30, 'day'), dayjs()]);
const timeRangePresets = defaultTimeRanges;

// 人口统计标签页
const demographicActiveTab = ref('age');

// 图表引用
const sourceChart = ref(null);
const statusChart = ref(null);
const trendChart = ref(null);
const qualityChart = ref(null);
const geographicChart = ref(null);
const ageChart = ref(null);
const genderChart = ref(null);
const symptomChart = ref(null);

// 图表实例
let sourceChartInstance = null;
let statusChartInstance = null;
let trendChartInstance = null;
let qualityChartInstance = null;
let geographicChartInstance = null;
let ageChartInstance = null;
let genderChartInstance = null;
let symptomChartInstance = null;

// 初始化
onMounted(async () => {
  await loadData();
  await nextTick();
  await initCharts();

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize);
});

// 组件卸载时清理事件监听
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);

  // 销毁图表实例
  sourceChartInstance?.dispose();
  statusChartInstance?.dispose();
  trendChartInstance?.dispose();
  qualityChartInstance?.dispose();
  geographicChartInstance?.dispose();
  ageChartInstance?.dispose();
  genderChartInstance?.dispose();
  symptomChartInstance?.dispose();
});

// 加载数据
async function loadData() {
  try {
    // 加载线索统计数据
    await loadLeadsStatistics();

    // 加载个人工作统计
    await loadPersonalStats();

  } catch (error) {
    smartSentry.captureError(error);
    message.error('加载数据失败');
  }
}

// 加载线索统计
async function loadLeadsStatistics() {
  try {
    // 调用实际的API获取统计数据
    const res = await dashboardApi.getLeadBasicStats({
      startTime: timeRange.value[0]?.format('YYYY-MM-DD'),
      endTime: timeRange.value[1]?.format('YYYY-MM-DD')
    });
    if (res.ok && res.data) {
      leadsStats.value = {
        totalLeads: res.data.totalLeads || 0,
        todayLeads: res.data.todayLeads || 0,
        followingLeads: res.data.followingLeads || 0,
        convertedLeads: res.data.convertedLeads || 0,
        pendingLeads: res.data.pendingLeads || 0,
        todayPending: res.data.todayPending || 0,
        conversionRate: res.data.conversionRate || 0,
        monthlyConverted: res.data.monthlyConverted || 0
      };
    } else {
      throw new Error('API调用失败或返回数据无效');
    }
  } catch (error) {
    console.error('获取线索统计失败:', error);
    // 如果API调用失败，使用模拟数据
    leadsStats.value = {
      totalLeads: 156,
      todayLeads: 12,
      followingLeads: 67,
      convertedLeads: 89,
      pendingLeads: 67,
      todayPending: 8,
      conversionRate: 57.1,
      monthlyConverted: 22
    };
  }
}

// 加载个人工作统计
async function loadPersonalStats() {
  try {
    const res = await dashboardApi.getPersonalWorkStats({
      startTime: timeRange.value[0]?.format('YYYY-MM-DD'),
      endTime: timeRange.value[1]?.format('YYYY-MM-DD')
    });
    if (res.ok && res.data) {
      personalStats.value = {
        monthlyAppointments: res.data.monthlyAppointments || 0,
        monthlyArrivals: res.data.monthlyArrivals || 0,
        followingCustomers: res.data.followingCustomers || 0,
        monthlyConversionRate: res.data.monthlyConversionRate || 0
      };
    } else {
      throw new Error('API调用失败或返回数据无效');
    }
  } catch (error) {
    console.error('获取个人统计失败:', error);
    // 使用模拟数据
    personalStats.value = {
      monthlyAppointments: 28,
      monthlyArrivals: 22,
      followingCustomers: 15,
      monthlyConversionRate: 78.6
    };
  }
}



// 初始化图表
async function initCharts() {
  await initSourceChart();
  await initStatusChart();
  await initTrendChart();
  await initQualityChart();
  await initGeographicChart();
  await initDemographicCharts();
}

// 初始化来源分布图表
async function initSourceChart() {
  try {
    sourceChartInstance = echarts.init(sourceChart.value);

    // 调用API获取线索来源分布数据
    let chartData = [];

    try {
      const res = await dashboardApi.getLeadSourceAnalysis();
      if (res.ok && res.data && res.data.data) {
        chartData = res.data.data;
      } else {
        throw new Error('API调用失败或返回数据无效');
      }
    } catch (apiError) {
      console.error('获取线索来源分布失败:', apiError);
      // 使用模拟数据
      chartData = [
        { value: 45, name: '百度推广' },
        { value: 32, name: '微信广告' },
        { value: 28, name: '朋友推荐' },
        { value: 25, name: '官网咨询' },
        { value: 18, name: '电话咨询' },
        { value: 8, name: '其他渠道' }
      ];
    }

    const option = {
      tooltip: {
        trigger: 'item'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: '线索来源',
          type: 'pie',
          radius: '50%',
          data: chartData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };
    sourceChartInstance.setOption(option);
  } catch (error) {
    console.error('初始化来源分布图表失败:', error);
    // 如果出错，显示默认图表
    sourceChartInstance = echarts.init(sourceChart.value);
    sourceChartInstance.setOption({
      title: { text: '数据加载失败', left: 'center', top: 'center' }
    });
  }
}

// 工具函数
function getPriorityColor(priority) {
  const colors = {
    high: 'red',
    medium: 'orange',
    low: 'green'
  };
  return colors[priority] || 'default';
}

function getPriorityText(priority) {
  const texts = {
    high: '高优先级',
    medium: '中优先级',
    low: '低优先级'
  };
  return texts[priority] || '普通';
}

function getQualityText(quality) {
  const texts = {
    1: 'A级',
    2: 'B级',
    3: 'C级'
  };
  return texts[quality] || '未评级';
}

function formatTime(timeStr) {
  return timeStr ? timeStr.substring(11, 16) : '';
}

// 事件处理
function handleFollow(item) {
  message.info(`开始跟进客户: ${item.customerName}`);
  // 这里可以跳转到跟进页面或打开跟进弹窗
}

function handleContact(item) {
  message.info(`联系客户: ${item.customerName}`);
  // 这里可以跳转到客户详情页面
}

// 时间范围变化处理
function onTimeRangeChange(dates) {
  timeRange.value = dates;
  refreshData();
}

// 刷新数据
async function refreshData() {
  await loadData();
  await nextTick();
  await initCharts();
}

// 人口统计标签页切换
function onDemographicTabChange(activeKey) {
  demographicActiveTab.value = activeKey;
  nextTick(() => {
    if (activeKey === 'age' && ageChartInstance) {
      ageChartInstance.resize();
    } else if (activeKey === 'gender' && genderChartInstance) {
      genderChartInstance.resize();
    } else if (activeKey === 'symptom' && symptomChartInstance) {
      symptomChartInstance.resize();
    }
  });
}

// 窗口大小变化时重新调整图表大小
function handleResize() {
  nextTick(() => {
    sourceChartInstance?.resize();
    statusChartInstance?.resize();
    trendChartInstance?.resize();
    qualityChartInstance?.resize();
    geographicChartInstance?.resize();
    if (demographicActiveTab.value === 'age') {
      ageChartInstance?.resize();
    } else if (demographicActiveTab.value === 'gender') {
      genderChartInstance?.resize();
    } else if (demographicActiveTab.value === 'symptom') {
      symptomChartInstance?.resize();
    }
  });
}







// 初始化状态分布图表
async function initStatusChart() {
  try {
    statusChartInstance = echarts.init(statusChart.value);

    let chartData = [];

    try {
      const res = await dashboardApi.getLeadStatusDistribution();
      if (res.ok && res.data && res.data.data) {
        chartData = res.data.data;
      } else {
        throw new Error('API调用失败或返回数据无效');
      }
    } catch (apiError) {
      console.error('获取线索状态分布失败:', apiError);
      // 使用模拟数据
      chartData = [
        { value: 45, name: '新线索' },
        { value: 32, name: '跟进中' },
        { value: 28, name: '已预约' },
        { value: 25, name: '已到院' },
        { value: 18, name: '已转化' },
        { value: 8, name: '已关闭' }
      ];
    }

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: '线索状态',
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          data: chartData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };
    statusChartInstance.setOption(option);
  } catch (error) {
    console.error('初始化状态分布图表失败:', error);
  }
}

// 初始化趋势图表
async function initTrendChart() {
  try {
    trendChartInstance = echarts.init(trendChart.value);

    const res = await dashboardApi.getLeadTrendAnalysis({
      startTime: timeRange.value[0]?.format('YYYY-MM-DD'),
      endTime: timeRange.value[1]?.format('YYYY-MM-DD')
    });

    let xAxisData = [];
    let newLeadsData = [];
    let convertedData = [];

    if (res.ok && res.data) {
      xAxisData = res.data.dates || [];
      newLeadsData = res.data.newLeads || [];
      convertedData = res.data.converted || [];
    } else {
      // 使用模拟数据
      const days = 30;
      for (let i = days - 1; i >= 0; i--) {
        const date = dayjs().subtract(i, 'day').format('MM-DD');
        xAxisData.push(date);
        newLeadsData.push(Math.floor(Math.random() * 10) + 5);
        convertedData.push(Math.floor(Math.random() * 5) + 2);
      }
    }

    const option = {
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: ['新增线索', '转化线索']
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: xAxisData
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '新增线索',
          type: 'line',
          stack: 'Total',
          data: newLeadsData,
          smooth: true,
          itemStyle: { color: '#1890ff' }
        },
        {
          name: '转化线索',
          type: 'line',
          stack: 'Total',
          data: convertedData,
          smooth: true,
          itemStyle: { color: '#52c41a' }
        }
      ]
    };
    trendChartInstance.setOption(option);
  } catch (error) {
    console.error('初始化趋势图表失败:', error);
  }
}

// 初始化质量分布图表
async function initQualityChart() {
  try {
    qualityChartInstance = echarts.init(qualityChart.value);

    const res = await dashboardApi.getLeadQualityDistribution();
    let chartData = [];

    if (res.ok && res.data) {
      chartData = res.data.map(item => ({
        value: item.count,
        name: item.qualityName
      }));
    } else {
      // 使用模拟数据
      chartData = [
        { value: 45, name: 'A级' },
        { value: 67, name: 'B级' },
        { value: 44, name: 'C级' }
      ];
    }

    const option = {
      tooltip: {
        trigger: 'item'
      },
      series: [
        {
          name: '线索质量',
          type: 'pie',
          radius: '60%',
          data: chartData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          label: {
            show: true,
            formatter: '{b}: {c} ({d}%)'
          }
        }
      ]
    };
    qualityChartInstance.setOption(option);
  } catch (error) {
    console.error('初始化质量分布图表失败:', error);
  }
}

// 初始化地理分布图表
async function initGeographicChart() {
  try {
    geographicChartInstance = echarts.init(geographicChart.value);

    const res = await dashboardApi.getLeadGeographicDistribution();
    let chartData = [];

    if (res.ok && res.data) {
      chartData = res.data.map(item => ({
        value: item.count,
        name: item.regionName
      }));
    } else {
      // 使用模拟数据
      chartData = [
        { value: 45, name: '北京' },
        { value: 32, name: '上海' },
        { value: 28, name: '广州' },
        { value: 25, name: '深圳' },
        { value: 18, name: '杭州' },
        { value: 15, name: '成都' },
        { value: 12, name: '武汉' },
        { value: 8, name: '其他' }
      ];
    }

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      series: [
        {
          name: '地理分布',
          type: 'pie',
          radius: '55%',
          center: ['50%', '60%'],
          data: chartData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          labelLine: {
            show: false
          },
          label: {
            show: true,
            position: 'center'
          }
        }
      ]
    };
    geographicChartInstance.setOption(option);
  } catch (error) {
    console.error('初始化地理分布图表失败:', error);
  }
}

// 初始化人口统计图表
async function initDemographicCharts() {
  await initAgeChart();
  await initGenderChart();
  await initSymptomChart();
}

// 初始化年龄分布图表
async function initAgeChart() {
  try {
    ageChartInstance = echarts.init(ageChart.value);

    const res = await dashboardApi.getLeadDemographicStats();
    let ageData = [];

    if (res.ok && res.data && res.data.ageDistribution) {
      ageData = res.data.ageDistribution.map(item => ({
        value: item.count,
        name: item.ageRange
      }));
    } else {
      // 使用模拟数据
      ageData = [
        { value: 15, name: '18-25岁' },
        { value: 35, name: '26-35岁' },
        { value: 45, name: '36-45岁' },
        { value: 28, name: '46-55岁' },
        { value: 12, name: '56岁以上' }
      ];
    }

    const option = {
      tooltip: {
        trigger: 'item'
      },
      series: [
        {
          name: '年龄分布',
          type: 'pie',
          radius: ['40%', '70%'],
          data: ageData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };
    ageChartInstance.setOption(option);
  } catch (error) {
    console.error('初始化年龄分布图表失败:', error);
  }
}

// 初始化性别分布图表
async function initGenderChart() {
  try {
    genderChartInstance = echarts.init(genderChart.value);

    const res = await dashboardApi.getLeadDemographicStats();
    let genderData = [];

    if (res.ok && res.data && res.data.genderDistribution) {
      genderData = res.data.genderDistribution.map(item => ({
        value: item.count,
        name: item.genderName
      }));
    } else {
      // 使用模拟数据
      genderData = [
        { value: 78, name: '女性' },
        { value: 67, name: '男性' },
        { value: 11, name: '未知' }
      ];
    }

    const option = {
      tooltip: {
        trigger: 'item'
      },
      series: [
        {
          name: '性别分布',
          type: 'pie',
          radius: '60%',
          data: genderData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          label: {
            show: true,
            formatter: '{b}: {c} ({d}%)'
          }
        }
      ]
    };
    genderChartInstance.setOption(option);
  } catch (error) {
    console.error('初始化性别分布图表失败:', error);
  }
}

// 初始化症状分布图表
async function initSymptomChart() {
  try {
    symptomChartInstance = echarts.init(symptomChart.value);

    let symptomData = [];

    try {
      const res = await dashboardApi.getSymptomDistribution();
      if (res.ok && res.data && res.data.data) {
        symptomData = res.data.data;
      } else {
        throw new Error('API调用失败或返回数据无效');
      }
    } catch (apiError) {
      console.error('获取症状分布失败:', apiError);
      // 使用模拟数据
      symptomData = [
        { value: 25, name: '抑郁症' },
        { value: 18, name: '焦虑症' },
        { value: 15, name: '失眠症' },
        { value: 12, name: '强迫症' },
        { value: 10, name: '恐惧症' },
        { value: 8, name: '其他' }
      ];
    }

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      series: [
        {
          name: '症状分布',
          type: 'pie',
          radius: ['30%', '70%'],
          data: symptomData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          label: {
            show: true,
            formatter: '{b}: {d}%'
          }
        }
      ]
    };
    symptomChartInstance.setOption(option);
  } catch (error) {
    console.error('初始化症状分布图表失败:', error);
  }
}
</script>

<style scoped>
.leads-dashboard {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 64px);
}

.time-range-section {
  margin-bottom: 16px;
}

.time-range-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.time-range-controls {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.personal-stats-section {
  margin-bottom: 16px;
}

.personal-stats-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.statistics-cards {
  margin-bottom: 16px;
}

.statistics-cards .ant-col {
  margin-bottom: 16px;
}

.stat-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.chart-section {
  margin-bottom: 16px;
}

.chart-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 100%;
}

.chart-card .ant-card-body {
  padding: 16px;
}

.info-section {
  margin-bottom: 16px;
}

.info-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 100%;
}

.reminder-item {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.reminder-item:hover {
  background-color: #fafafa;
}

.reminder-item:last-child {
  border-bottom: none;
}

.reminder-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.reminder-info h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 500;
}

.reminder-info p {
  margin: 0;
  font-size: 12px;
  color: #666;
}

.reminder-actions {
  display: flex;
  gap: 8px;
}

.lead-item {
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.lead-item:hover {
  background-color: #fafafa;
}

.lead-item:last-child {
  border-bottom: none;
}

.lead-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.lead-info h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 500;
}

.lead-info p {
  margin: 0;
  font-size: 12px;
  color: #666;
}

.lead-actions {
  display: flex;
  gap: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .leads-dashboard {
    padding: 8px;
  }

  .statistics-cards .ant-col {
    margin-bottom: 8px;
  }

  .chart-section {
    margin-bottom: 8px;
  }

  .time-range-controls {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

/* 图表容器样式 */
.chart-container {
  width: 100%;
  height: 350px;
}

/* 统计卡片图标样式 */
.ant-statistic-content-prefix {
  margin-right: 8px;
}

/* 标签页样式优化 */
.ant-tabs-content-holder {
  padding-top: 16px;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
</style>

<style scoped>
.leads-dashboard {
  padding: 16px;
}

.statistics-cards {
  margin-bottom: 16px;
}

.stat-card {
  text-align: center;
}

.stat-footer {
  margin-top: 8px;
  color: #666;
  font-size: 12px;
}

.chart-section {
  margin-bottom: 16px;
}

.chart-card {
  height: 100%;
}

.reminder-section {
  margin-bottom: 16px;
}

.reminder-card {
  height: 400px;
}

.reminder-card .ant-card-body {
  height: calc(100% - 57px);
  overflow-y: auto;
}
</style>

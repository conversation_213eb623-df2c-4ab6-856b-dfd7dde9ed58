<template>
  <div class="revenue-dashboard">
    <!-- 收入统计卡片 -->
    <a-row :gutter="16" class="statistics-cards">
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic
            title="总收入"
            :value="revenueStats.totalRevenue"
            suffix="万元"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix>
              <DollarOutlined />
            </template>
          </a-statistic>
          <div class="stat-footer">
            今日收入: {{ revenueStats.todayRevenue }}万元
          </div>
        </a-card>
      </a-col>
      
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic
            title="本月收入"
            :value="revenueStats.monthlyRevenue"
            suffix="万元"
            :value-style="{ color: '#52c41a' }"
          >
            <template #prefix>
              <CalendarOutlined />
            </template>
          </a-statistic>
          <div class="stat-footer">
            环比增长: {{ revenueStats.monthlyGrowth }}%
          </div>
        </a-card>
      </a-col>
      
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic
            title="待收费用"
            :value="revenueStats.pendingRevenue"
            suffix="万元"
            :value-style="{ color: '#faad14' }"
          >
            <template #prefix>
              <ClockCircleOutlined />
            </template>
          </a-statistic>
          <div class="stat-footer">
            待收笔数: {{ revenueStats.pendingCount }}笔
          </div>
        </a-card>
      </a-col>
      
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic
            title="平均客单价"
            :value="revenueStats.averageOrderValue"
            suffix="元"
            :value-style="{ color: '#722ed1' }"
          >
            <template #prefix>
              <UserOutlined />
            </template>
          </a-statistic>
          <div class="stat-footer">
            较上月: {{ revenueStats.aovChange > 0 ? '+' : '' }}{{ revenueStats.aovChange }}%
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 图表区域 -->
    <a-row :gutter="16" class="chart-section">
      <a-col :span="16">
        <a-card title="收入趋势分析" class="chart-card">
          <div class="chart-controls">
            <a-radio-group v-model:value="trendPeriod" @change="updateTrendChart">
              <a-radio-button value="week">近7天</a-radio-button>
              <a-radio-button value="month">近30天</a-radio-button>
              <a-radio-button value="quarter">近3个月</a-radio-button>
            </a-radio-group>
          </div>
          <div ref="trendChart" style="height: 350px;"></div>
        </a-card>
      </a-col>
      
      <a-col :span="8">
        <a-card title="收费方式分布" class="chart-card">
          <div ref="paymentMethodChart" style="height: 350px;"></div>
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="16" class="chart-section">
      <a-col :span="12">
        <a-card title="科室收入排行" class="chart-card">
          <div ref="departmentChart" style="height: 300px;"></div>
        </a-card>
      </a-col>
      
      <a-col :span="12">
        <a-card title="项目收入分布" class="chart-card">
          <div ref="projectChart" style="height: 300px;"></div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 收费管理区域 -->
    <a-row :gutter="16" class="management-section">
      <a-col :span="16">
        <a-card title="今日收费记录" class="charge-records-card">
          <a-table
            :columns="chargeColumns"
            :data-source="todayChargeRecords"
            :pagination="{ pageSize: 10 }"
            size="small"
            :scroll="{ y: 300 }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'status'">
                <a-tag :color="getChargeStatusColor(record.status)">
                  {{ getChargeStatusText(record.status) }}
                </a-tag>
              </template>
              <template v-else-if="column.key === 'amount'">
                <span style="color: #52c41a; font-weight: bold;">
                  ¥{{ record.amount.toFixed(2) }}
                </span>
              </template>
              <template v-else-if="column.key === 'action'">
                <a-space>
                  <a @click="handleViewDetail(record)" v-if="record.status === 2">查看详情</a>
                  <a @click="handleConfirmCharge(record)" v-if="record.status === 1">确认收费</a>
                  <a @click="handleRefund(record)" v-if="record.status === 2">退费</a>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-card>
      </a-col>
      
      <a-col :span="8">
        <a-card title="收费统计" class="charge-stats-card">
          <div class="charge-stat-item">
            <div class="stat-label">今日收费笔数</div>
            <div class="stat-value">{{ chargeStats.todayCount }}笔</div>
          </div>
          <div class="charge-stat-item">
            <div class="stat-label">今日收费金额</div>
            <div class="stat-value">¥{{ chargeStats.todayAmount.toFixed(2) }}</div>
          </div>
          <div class="charge-stat-item">
            <div class="stat-label">平均收费时长</div>
            <div class="stat-value">{{ chargeStats.averageTime }}分钟</div>
          </div>
          <div class="charge-stat-item">
            <div class="stat-label">收费成功率</div>
            <div class="stat-value">{{ chargeStats.successRate }}%</div>
          </div>
          
          <a-divider />
          
          <div class="quick-actions">
            <h4>快捷操作</h4>
            <a-space direction="vertical" style="width: 100%;">
              <a-button type="primary" block @click="handleNewCharge">
                <PlusOutlined /> 新增收费
              </a-button>
              <a-button block @click="handleBatchCharge">
                <FileTextOutlined /> 批量收费
              </a-button>
              <a-button block @click="handleRevenueReport">
                <BarChartOutlined /> 收入报表
              </a-button>
            </a-space>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue';
import { 
  DollarOutlined, 
  CalendarOutlined, 
  ClockCircleOutlined, 
  UserOutlined,
  PlusOutlined,
  FileTextOutlined,
  BarChartOutlined
} from '@ant-design/icons-vue';
import * as echarts from 'echarts';
import { dashboardApi } from '/@/api/business/hospital/dashboard-api';
import { smartSentry } from '/@/lib/smart-sentry';
import { message } from 'ant-design-vue';

// 响应式数据
const revenueStats = ref({
  totalRevenue: 0,
  todayRevenue: 0,
  monthlyRevenue: 0,
  pendingRevenue: 0,
  averageOrderValue: 0,
  monthlyGrowth: 0,
  aovChange: 0,
  pendingCount: 0
});

const chargeStats = ref({
  todayCount: 0,
  todayAmount: 0,
  averageTime: 0,
  successRate: 0
});

const todayChargeRecords = ref([]);
const trendPeriod = ref('week');

// 图表引用
const trendChart = ref(null);
const paymentMethodChart = ref(null);
const departmentChart = ref(null);
const projectChart = ref(null);

// 图表实例
let trendChartInstance = null;
let paymentMethodChartInstance = null;
let departmentChartInstance = null;
let projectChartInstance = null;

// 表格列定义
const chargeColumns = [
  {
    title: '时间',
    dataIndex: 'chargeTime',
    key: 'chargeTime',
    width: 80
  },
  {
    title: '患者姓名',
    dataIndex: 'patientName',
    key: 'patientName',
    width: 100
  },
  {
    title: '收费项目',
    dataIndex: 'chargeName',
    key: 'chargeName',
    width: 120
  },
  {
    title: '金额',
    dataIndex: 'amount',
    key: 'amount',
    width: 80
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80
  },
  {
    title: '操作',
    key: 'action',
    width: 120
  }
];

// 初始化
onMounted(async () => {
  await loadData();
  await nextTick();
  initCharts();
});

// 加载数据
async function loadData() {
  try {
    await loadRevenueStats();
    await loadChargeStats();
    await loadTodayChargeRecords();
  } catch (error) {
    smartSentry.captureError(error);
    message.error('加载数据失败');
  }
}

// 加载收入统计
async function loadRevenueStats() {
  try {
    // 调用实际的API获取统计数据
    const res = await dashboardApi.getStatistics();
    if (res.ok && res.data) {
      revenueStats.value = {
        totalRevenue: res.data.totalRevenue ? parseFloat(res.data.totalRevenue) : 0,
        todayRevenue: res.data.todayRevenue ? parseFloat(res.data.todayRevenue) : 0,
        monthlyRevenue: res.data.totalRevenue ? parseFloat(res.data.totalRevenue) / 12 : 0, // 假设平均月收入
        pendingRevenue: res.data.todayRevenue ? parseFloat(res.data.todayRevenue) * 0.1 : 0, // 假设10%待收
        averageOrderValue: res.data.appointmentCount > 0 ? Math.floor(parseFloat(res.data.totalRevenue || 0) / res.data.appointmentCount) : 0,
        monthlyGrowth: 15.0, // 假设固定增长率
        aovChange: 8.0, // 假设固定变化率
        pendingCount: res.data.pendingAppointmentCount || 0
      };
    } else {
      throw new Error('API调用失败或返回数据无效');
    }
  } catch (error) {
    console.error('获取收入统计失败:', error);
    // 使用模拟数据
    revenueStats.value = {
      totalRevenue: 156.8,
      todayRevenue: 2.35,
      monthlyRevenue: 13.1,
      pendingRevenue: 0.45,
      averageOrderValue: 520,
      monthlyGrowth: 15.2,
      aovChange: 8.5,
      pendingCount: 3
    };
  }
}

// 加载收费统计
async function loadChargeStats() {
  // 模拟数据
  chargeStats.value = {
    todayCount: 45,
    todayAmount: 23500,
    averageTime: 3.5,
    successRate: 98.5
  };
}

// 加载今日收费记录
async function loadTodayChargeRecords() {
  // 模拟数据
  todayChargeRecords.value = [
    {
      id: 1,
      chargeTime: '09:30',
      patientName: '张三',
      chargeName: '挂号费',
      amount: 15,
      status: 2
    },
    {
      id: 2,
      chargeTime: '10:15',
      patientName: '李四',
      chargeName: '检查费',
      amount: 280,
      status: 2
    },
    {
      id: 3,
      chargeTime: '11:00',
      patientName: '王五',
      chargeName: '治疗费',
      amount: 450,
      status: 1
    }
  ];
}

// 初始化图表
function initCharts() {
  initTrendChart();
  initPaymentMethodChart();
  initDepartmentChart();
  initProjectChart();
}

// 初始化趋势图表
function initTrendChart() {
  trendChartInstance = echarts.init(trendChart.value);
  updateTrendChart();
}

// 更新趋势图表
function updateTrendChart() {
  if (!trendChartInstance) return;
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['收入金额', '收费笔数']
    },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    },
    yAxis: [
      {
        type: 'value',
        name: '金额(万元)',
        position: 'left'
      },
      {
        type: 'value',
        name: '笔数',
        position: 'right'
      }
    ],
    series: [
      {
        name: '收入金额',
        type: 'line',
        yAxisIndex: 0,
        data: [3.2, 4.5, 3.8, 5.2, 4.8, 3.5, 2.8],
        smooth: true,
        itemStyle: {
          color: '#1890ff'
        }
      },
      {
        name: '收费笔数',
        type: 'bar',
        yAxisIndex: 1,
        data: [45, 62, 58, 78, 65, 48, 35],
        itemStyle: {
          color: '#52c41a'
        }
      }
    ]
  };
  trendChartInstance.setOption(option);
}

// 工具函数
function getChargeStatusColor(status) {
  const colors = {
    1: 'orange',    // 待收费
    2: 'green',     // 已收费
    3: 'red'        // 已退费
  };
  return colors[status] || 'default';
}

function getChargeStatusText(status) {
  const texts = {
    1: '待收费',
    2: '已收费',
    3: '已退费'
  };
  return texts[status] || '未知';
}

// 事件处理
function handleViewDetail(record) {
  message.info(`查看收费详情: ${record.patientName}`);
  // 这里打开收费详情弹窗
}

function handleConfirmCharge(record) {
  message.success(`确认收费: ${record.patientName}`);
  // 这里调用确认收费的API
}

function handleRefund(record) {
  message.warning(`申请退费: ${record.patientName}`);
  // 这里打开退费申请弹窗
}

function handleNewCharge() {
  message.info('新增收费');
  // 这里跳转到收费页面
}

function handleBatchCharge() {
  message.info('批量收费');
  // 这里打开批量收费弹窗
}

function handleRevenueReport() {
  message.info('收入报表');
  // 这里跳转到报表页面
}

// 其他图表初始化函数（简化版）
function initPaymentMethodChart() {
  paymentMethodChartInstance = echarts.init(paymentMethodChart.value);
  // 支付方式分布图表配置...
}

function initDepartmentChart() {
  departmentChartInstance = echarts.init(departmentChart.value);
  // 科室收入图表配置...
}

function initProjectChart() {
  projectChartInstance = echarts.init(projectChart.value);
  // 项目收入图表配置...
}
</script>

<style scoped>
.revenue-dashboard {
  padding: 16px;
}

.statistics-cards {
  margin-bottom: 16px;
}

.stat-card {
  text-align: center;
}

.stat-footer {
  margin-top: 8px;
  color: #666;
  font-size: 12px;
}

.chart-section {
  margin-bottom: 16px;
}

.chart-card {
  height: 100%;
}

.chart-controls {
  margin-bottom: 16px;
  text-align: right;
}

.management-section {
  margin-bottom: 16px;
}

.charge-records-card {
  height: 450px;
}

.charge-stats-card {
  height: 450px;
}

.charge-stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

.stat-value {
  color: #1890ff;
  font-size: 16px;
  font-weight: bold;
}

.quick-actions {
  margin-top: 16px;
}

.quick-actions h4 {
  margin-bottom: 12px;
  color: #333;
}
</style>

<template>
  <a-drawer
    v-model:visible="drawerVisible"
    title="重新分配待办"
    width="500"
    :closable="true"
    :mask-closable="false"
  >
    <div v-if="loading" class="loading-container">
      <a-spin size="large" />
    </div>
    
    <div v-else-if="todoDetail" class="reassign-content">
      <!-- 待办信息 -->
      <a-descriptions title="待办信息" :column="1" bordered>
        <a-descriptions-item label="待办标题">
          {{ todoDetail.todoTitle }}
        </a-descriptions-item>
        <a-descriptions-item label="待办类型">
          <a-tag>{{ todoDetail.todoTypeDesc }}</a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="优先级">
          <a-tag :color="todoDetail.priorityColor">{{ todoDetail.priorityLevelDesc }}</a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="当前分配人">
          <a-tag color="blue">{{ todoDetail.assignedEmployeeName }}</a-tag>
        </a-descriptions-item>
      </a-descriptions>

      <!-- 重新分配表单 -->
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
        class="reassign-form"
      >
        <a-form-item label="新分配人" name="newAssignedEmployeeId">
          <a-select
            v-model:value="formData.newAssignedEmployeeId"
            placeholder="请选择新的分配人"
            show-search
            :filter-option="filterEmployee"
          >
            <a-select-option
              v-for="employee in availableEmployees"
              :key="employee.employeeId"
              :value="employee.employeeId"
            >
              <div class="employee-option">
                <div class="employee-name">{{ employee.actualName }}</div>
                <div class="employee-info">{{ employee.phone }} | {{ employee.departmentName }}</div>
              </div>
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="分配原因" name="reassignReason">
          <a-textarea
            v-model:value="formData.reassignReason"
            placeholder="请输入重新分配的原因"
            :rows="3"
            :max-length="200"
            show-count
          />
        </a-form-item>

        <a-form-item label="是否通知原分配人" name="notifyOriginal">
          <a-switch v-model:checked="formData.notifyOriginal" />
          <span class="switch-label">通知原分配人此次变更</span>
        </a-form-item>

        <a-form-item label="是否通知新分配人" name="notifyNew">
          <a-switch v-model:checked="formData.notifyNew" />
          <span class="switch-label">通知新分配人接收待办</span>
        </a-form-item>
      </a-form>

      <!-- 分配历史 -->
      <a-divider>分配历史</a-divider>
      <div v-if="assignmentHistory.length > 0" class="assignment-history">
        <a-timeline>
          <a-timeline-item
            v-for="(item, index) in assignmentHistory"
            :key="index"
            :color="index === 0 ? 'blue' : 'gray'"
          >
            <div class="history-item">
              <div class="history-title">
                {{ item.operationType === 'create' ? '创建待办' : '重新分配' }}
              </div>
              <div class="history-detail">
                <span>分配给: {{ item.assignedEmployeeName }}</span>
                <span class="history-time">{{ dayjs(item.operationTime).format('YYYY-MM-DD HH:mm') }}</span>
              </div>
              <div v-if="item.operationReason" class="history-reason">
                原因: {{ item.operationReason }}
              </div>
            </div>
          </a-timeline-item>
        </a-timeline>
      </div>
      <div v-else class="no-history">
        <a-empty description="暂无分配历史" size="small" />
      </div>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <a-space>
          <a-button @click="onCancel">取消</a-button>
          <a-button type="primary" :loading="submitLoading" @click="onSubmit">
            确认分配
          </a-button>
        </a-space>
      </div>
    </template>
  </a-drawer>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { todoApi } from '@/api/business/hospital/todo-api'
import { employeeApi } from '@/api/system/employee-api'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  todoId: {
    type: [String, Number],
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'refresh'])

// 数据状态
const drawerVisible = ref(false)
const formRef = ref()
const loading = ref(false)
const submitLoading = ref(false)
const todoDetail = ref(null)
const employeeList = ref([])
const assignmentHistory = ref([])

// 表单数据
const formData = reactive({
  newAssignedEmployeeId: undefined,
  reassignReason: '',
  notifyOriginal: true,
  notifyNew: true
})

// 表单验证规则
const formRules = {
  newAssignedEmployeeId: [
    { required: true, message: '请选择新的分配人', trigger: 'change' }
  ],
  reassignReason: [
    { required: true, message: '请输入重新分配的原因', trigger: 'blur' },
    { max: 200, message: '原因长度不能超过200个字符', trigger: 'blur' }
  ]
}

// 计算属性
const availableEmployees = computed(() => {
  if (!todoDetail.value) return employeeList.value
  // 排除当前分配人
  return employeeList.value.filter(emp => emp.employeeId !== todoDetail.value.assignedEmployeeId)
})

// 员工过滤
const filterEmployee = (input, option) => {
  const employee = employeeList.value.find(emp => emp.employeeId === option.value)
  if (!employee) return false
  
  const searchText = input.toLowerCase()
  return employee.actualName.toLowerCase().includes(searchText) ||
         employee.phone.includes(searchText) ||
         (employee.departmentName && employee.departmentName.toLowerCase().includes(searchText))
}

// 加载员工列表
const loadEmployeeList = async () => {
  try {
    const { data } = await employeeApi.queryAll()
    employeeList.value = data || []
  } catch (error) {
    console.error('加载员工列表失败:', error)
  }
}

// 加载待办详情
const loadTodoDetail = async () => {
  if (!props.todoId) return
  
  try {
    loading.value = true
    const { data } = await todoApi.getDetail(props.todoId)
    todoDetail.value = data
    
    // 模拟加载分配历史（实际项目中应该有专门的API）
    assignmentHistory.value = [
      {
        operationType: 'create',
        assignedEmployeeName: data.assignedEmployeeName,
        operationTime: data.createTime,
        operationReason: '创建待办'
      }
    ]
  } catch (error) {
    console.error('加载待办详情失败:', error)
    todoDetail.value = null
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(formData, {
    newAssignedEmployeeId: undefined,
    reassignReason: '',
    notifyOriginal: true,
    notifyNew: true
  })
}

// 提交表单
const onSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    submitLoading.value = true
    
    await todoApi.operation({
      todoId: props.todoId,
      operationType: 'reassign',
      newAssignedEmployeeId: formData.newAssignedEmployeeId,
      reassignReason: formData.reassignReason,
      notifyOriginal: formData.notifyOriginal,
      notifyNew: formData.notifyNew
    })
    
    message.success('重新分配成功')
    emit('refresh')
    onCancel()
  } catch (error) {
    console.error('重新分配失败:', error)
  } finally {
    submitLoading.value = false
  }
}

// 取消操作
const onCancel = () => {
  drawerVisible.value = false
}

// 监听器
watch(() => props.visible, (newVal) => {
  drawerVisible.value = newVal
  if (newVal && props.todoId) {
    loadTodoDetail()
    resetForm()
  }
})

watch(drawerVisible, (newVal) => {
  emit('update:visible', newVal)
  if (!newVal) {
    resetForm()
  }
})

// 初始化
onMounted(() => {
  loadEmployeeList()
})
</script>

<style scoped>
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.reassign-content {
  padding-bottom: 60px;
}

.reassign-form {
  margin-top: 16px;
}

.switch-label {
  margin-left: 8px;
  color: #666;
}

.employee-option {
  padding: 4px 0;
}

.employee-name {
  font-weight: 500;
  color: #333;
}

.employee-info {
  font-size: 12px;
  color: #999;
  margin-top: 2px;
}

.assignment-history {
  max-height: 300px;
  overflow-y: auto;
}

.history-item {
  padding: 4px 0;
}

.history-title {
  font-weight: 500;
  color: #333;
}

.history-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 4px;
  font-size: 12px;
  color: #666;
}

.history-time {
  color: #999;
}

.history-reason {
  margin-top: 4px;
  font-size: 12px;
  color: #999;
  font-style: italic;
}

.no-history {
  text-align: center;
  padding: 20px 0;
}

.drawer-footer {
  text-align: right;
  padding: 16px 0;
  border-top: 1px solid #f0f0f0;
}
</style>

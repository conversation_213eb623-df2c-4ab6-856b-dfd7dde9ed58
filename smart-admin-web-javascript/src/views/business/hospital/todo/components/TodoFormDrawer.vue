<template>
  <a-drawer
    v-model:visible="drawerVisible"
    :title="isEdit ? '编辑待办' : '新增待办'"
    width="600"
    :closable="true"
    :mask-closable="false"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      layout="vertical"
      @finish="onSubmit"
    >
      <a-form-item label="待办标题" name="todoTitle">
        <a-input v-model:value="formData.todoTitle" placeholder="请输入待办标题" />
      </a-form-item>

      <a-form-item label="待办类型" name="todoType">
        <a-select v-model:value="formData.todoType" placeholder="请选择待办类型">
          <a-select-option v-for="(desc, type) in TodoTypeDescMap" :key="type" :value="type">
            {{ desc }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="优先级" name="priorityLevel">
        <a-select v-model:value="formData.priorityLevel" placeholder="请选择优先级">
          <a-select-option v-for="(desc, priority) in TodoPriorityDescMap" :key="priority" :value="parseInt(priority)">
            <a-tag :color="TodoPriorityColorMap[priority]" size="small">{{ desc }}</a-tag>
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="分配人" name="assignedEmployeeId">
        <a-select v-model:value="formData.assignedEmployeeId" placeholder="请选择分配人" show-search :filter-option="filterEmployee">
          <a-select-option v-for="employee in employeeList" :key="employee.employeeId" :value="employee.employeeId">
            {{ employee.actualName }} ({{ employee.phone }})
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="截止时间" name="dueTime">
        <a-date-picker
          v-model:value="formData.dueTime"
          show-time
          format="YYYY-MM-DD HH:mm:ss"
          placeholder="请选择截止时间"
          style="width: 100%"
        />
      </a-form-item>

      <a-form-item label="待办内容" name="todoContent">
        <a-textarea
          v-model:value="formData.todoContent"
          placeholder="请输入待办内容"
          :rows="4"
          :max-length="500"
          show-count
        />
      </a-form-item>

      <!-- 业务关联信息 -->
      <a-divider>业务关联信息（可选）</a-divider>

      <a-form-item label="客户姓名" name="customerName">
        <a-input v-model:value="formData.customerName" placeholder="请输入客户姓名" />
      </a-form-item>

      <a-form-item label="客户电话" name="customerPhone">
        <a-input v-model:value="formData.customerPhone" placeholder="请输入客户电话" />
      </a-form-item>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="线索ID" name="leadId">
            <a-input-number
              v-model:value="formData.leadId"
              placeholder="请输入线索ID"
              style="width: 100%"
              :min="1"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="预约ID" name="appointmentId">
            <a-input-number
              v-model:value="formData.appointmentId"
              placeholder="请输入预约ID"
              style="width: 100%"
              :min="1"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="12">
          <a-form-item label="患者ID" name="patientId">
            <a-input-number
              v-model:value="formData.patientId"
              placeholder="请输入患者ID"
              style="width: 100%"
              :min="1"
            />
          </a-form-item>
        </a-col>
        <a-col :span="12">
          <a-form-item label="回访ID" name="followUpId">
            <a-input-number
              v-model:value="formData.followUpId"
              placeholder="请输入回访ID"
              style="width: 100%"
              :min="1"
            />
          </a-form-item>
        </a-col>
      </a-row>

      <a-form-item label="备注" name="remark">
        <a-textarea
          v-model:value="formData.remark"
          placeholder="请输入备注信息"
          :rows="3"
          :max-length="200"
          show-count
        />
      </a-form-item>
    </a-form>

    <template #footer>
      <div class="drawer-footer">
        <a-space>
          <a-button @click="onCancel">取消</a-button>
          <a-button type="primary" :loading="submitLoading" @click="onSubmit">
            {{ isEdit ? '更新' : '创建' }}
          </a-button>
        </a-space>
      </div>
    </template>
  </a-drawer>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import { todoApi, TodoTypeDescMap, TodoPriorityDescMap, TodoPriorityColorMap } from '@/api/business/hospital/todo-api'
import { employeeApi } from '@/api/system/employee-api'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  todoId: {
    type: [String, Number],
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'refresh'])

// 数据状态
const drawerVisible = ref(false)
const formRef = ref()
const submitLoading = ref(false)
const employeeList = ref([])

// 表单数据
const formData = reactive({
  todoTitle: '',
  todoType: undefined,
  priorityLevel: 2, // 默认中等优先级
  assignedEmployeeId: undefined,
  dueTime: undefined,
  todoContent: '',
  customerName: '',
  customerPhone: '',
  leadId: undefined,
  appointmentId: undefined,
  patientId: undefined,
  followUpId: undefined,
  remark: ''
})

// 表单验证规则
const formRules = {
  todoTitle: [
    { required: true, message: '请输入待办标题', trigger: 'blur' },
    { max: 100, message: '标题长度不能超过100个字符', trigger: 'blur' }
  ],
  todoType: [
    { required: true, message: '请选择待办类型', trigger: 'change' }
  ],
  priorityLevel: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ],
  assignedEmployeeId: [
    { required: true, message: '请选择分配人', trigger: 'change' }
  ],
  customerPhone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

// 计算属性
const isEdit = computed(() => !!props.todoId)

// 员工过滤
const filterEmployee = (input, option) => {
  return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

// 加载员工列表
const loadEmployeeList = async () => {
  try {
    const { data } = await employeeApi.queryAll()
    employeeList.value = data || []
  } catch (error) {
    console.error('加载员工列表失败:', error)
  }
}

// 加载待办详情
const loadTodoDetail = async () => {
  if (!props.todoId) return
  
  try {
    const { data } = await todoApi.getDetail(props.todoId)
    if (data) {
      Object.assign(formData, {
        todoTitle: data.todoTitle,
        todoType: data.todoType,
        priorityLevel: data.priorityLevel,
        assignedEmployeeId: data.assignedEmployeeId,
        dueTime: data.dueTime ? dayjs(data.dueTime) : undefined,
        todoContent: data.todoContent,
        customerName: data.customerName,
        customerPhone: data.customerPhone,
        leadId: data.leadId,
        appointmentId: data.appointmentId,
        patientId: data.patientId,
        followUpId: data.followUpId,
        remark: data.remark
      })
    }
  } catch (error) {
    console.error('加载待办详情失败:', error)
  }
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(formData, {
    todoTitle: '',
    todoType: undefined,
    priorityLevel: 2,
    assignedEmployeeId: undefined,
    dueTime: undefined,
    todoContent: '',
    customerName: '',
    customerPhone: '',
    leadId: undefined,
    appointmentId: undefined,
    patientId: undefined,
    followUpId: undefined,
    remark: ''
  })
}

// 提交表单
const onSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    submitLoading.value = true
    
    const params = {
      ...formData,
      dueTime: formData.dueTime ? dayjs(formData.dueTime).format('YYYY-MM-DD HH:mm:ss') : undefined
    }
    
    if (isEdit.value) {
      params.todoId = props.todoId
      await todoApi.update(params)
      message.success('更新待办成功')
    } else {
      await todoApi.add(params)
      message.success('创建待办成功')
    }
    
    emit('refresh')
    onCancel()
  } catch (error) {
    console.error('提交表单失败:', error)
  } finally {
    submitLoading.value = false
  }
}

// 取消操作
const onCancel = () => {
  drawerVisible.value = false
}

// 监听器
watch(() => props.visible, (newVal) => {
  drawerVisible.value = newVal
  if (newVal) {
    if (props.todoId) {
      loadTodoDetail()
    } else {
      resetForm()
    }
  }
})

watch(drawerVisible, (newVal) => {
  emit('update:visible', newVal)
  if (!newVal) {
    resetForm()
  }
})

// 初始化
onMounted(() => {
  loadEmployeeList()
})
</script>

<style scoped>
.drawer-footer {
  text-align: right;
  padding: 16px 0;
  border-top: 1px solid #f0f0f0;
}
</style>

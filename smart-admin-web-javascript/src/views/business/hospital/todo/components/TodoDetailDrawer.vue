<template>
  <a-drawer
    v-model:visible="drawerVisible"
    title="待办详情"
    width="600"
    :closable="true"
    :mask-closable="false"
  >
    <div v-if="loading" class="loading-container">
      <a-spin size="large" />
    </div>
    
    <div v-else-if="todoDetail" class="todo-detail">
      <!-- 基本信息 -->
      <a-descriptions title="基本信息" :column="1" bordered>
        <a-descriptions-item label="待办标题">
          {{ todoDetail.todoTitle }}
        </a-descriptions-item>
        <a-descriptions-item label="待办类型">
          <a-tag>{{ todoDetail.todoTypeDesc }}</a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="优先级">
          <a-tag :color="todoDetail.priorityColor">{{ todoDetail.priorityLevelDesc }}</a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-tag :color="getStatusColor(todoDetail.todoStatus)">
            {{ todoDetail.todoStatusDesc }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="待办内容">
          <div class="todo-content">{{ todoDetail.todoContent || '无内容' }}</div>
        </a-descriptions-item>
      </a-descriptions>

      <!-- 时间信息 -->
      <a-descriptions title="时间信息" :column="1" bordered class="section">
        <a-descriptions-item label="创建时间">
          {{ dayjs(todoDetail.createTime).format('YYYY-MM-DD HH:mm:ss') }}
        </a-descriptions-item>
        <a-descriptions-item label="截止时间">
          {{ todoDetail.dueTime ? dayjs(todoDetail.dueTime).format('YYYY-MM-DD HH:mm:ss') : '无截止时间' }}
        </a-descriptions-item>
        <a-descriptions-item label="开始时间">
          {{ todoDetail.startTime ? dayjs(todoDetail.startTime).format('YYYY-MM-DD HH:mm:ss') : '未开始' }}
        </a-descriptions-item>
        <a-descriptions-item label="完成时间">
          {{ todoDetail.completeTime ? dayjs(todoDetail.completeTime).format('YYYY-MM-DD HH:mm:ss') : '未完成' }}
        </a-descriptions-item>
        <a-descriptions-item v-if="todoDetail.isOverdue" label="逾期状态">
          <span class="overdue-text">逾期 {{ todoDetail.overdueDays }} 天</span>
        </a-descriptions-item>
        <a-descriptions-item v-else-if="todoDetail.remainingHours !== null" label="剩余时间">
          <span :class="todoDetail.remainingHours <= 24 ? 'urgent-text' : 'normal-text'">
            {{ todoDetail.remainingHours }} 小时
          </span>
        </a-descriptions-item>
      </a-descriptions>

      <!-- 分配信息 -->
      <a-descriptions title="分配信息" :column="1" bordered class="section">
        <a-descriptions-item label="创建人">
          {{ todoDetail.createUserName }}
        </a-descriptions-item>
        <a-descriptions-item label="分配人">
          {{ todoDetail.assignedEmployeeName }}
        </a-descriptions-item>
        <a-descriptions-item v-if="todoDetail.processingDuration" label="处理时长">
          {{ todoDetail.processingDuration }} 分钟
        </a-descriptions-item>
      </a-descriptions>

      <!-- 业务关联信息 -->
      <a-descriptions v-if="hasBusinessInfo" title="业务关联" :column="1" bordered class="section">
        <a-descriptions-item v-if="todoDetail.customerName" label="客户姓名">
          {{ todoDetail.customerName }}
        </a-descriptions-item>
        <a-descriptions-item v-if="todoDetail.customerPhone" label="客户电话">
          {{ todoDetail.customerPhone }}
        </a-descriptions-item>
        <a-descriptions-item v-if="todoDetail.leadId" label="线索ID">
          <a-button type="link" size="small" @click="goToLead">{{ todoDetail.leadId }}</a-button>
        </a-descriptions-item>
        <a-descriptions-item v-if="todoDetail.appointmentId" label="预约ID">
          <a-button type="link" size="small" @click="goToAppointment">{{ todoDetail.appointmentId }}</a-button>
        </a-descriptions-item>
        <a-descriptions-item v-if="todoDetail.patientId" label="患者ID">
          <a-button type="link" size="small" @click="goToPatient">{{ todoDetail.patientId }}</a-button>
        </a-descriptions-item>
        <a-descriptions-item v-if="todoDetail.followUpId" label="回访ID">
          <a-button type="link" size="small" @click="goToFollowUp">{{ todoDetail.followUpId }}</a-button>
        </a-descriptions-item>
      </a-descriptions>

      <!-- 备注信息 -->
      <a-descriptions v-if="todoDetail.remark" title="备注信息" :column="1" bordered class="section">
        <a-descriptions-item label="备注">
          <div class="remark-content">{{ todoDetail.remark }}</div>
        </a-descriptions-item>
      </a-descriptions>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <a-space>
          <a-button v-if="todoDetail.availableActions && todoDetail.availableActions.includes('start')" type="primary" @click="onStart">
            <template #icon><PlayCircleOutlined /></template>
            开始处理
          </a-button>
          <a-button v-if="todoDetail.availableActions && todoDetail.availableActions.includes('pause')" @click="onPause">
            <template #icon><PauseCircleOutlined /></template>
            暂停处理
          </a-button>
          <a-button v-if="todoDetail.availableActions && todoDetail.availableActions.includes('complete')" type="primary" @click="onComplete">
            <template #icon><CheckCircleOutlined /></template>
            完成
          </a-button>
          <a-button v-if="todoDetail.availableActions && todoDetail.availableActions.includes('cancel')" danger @click="onCancel">
            <template #icon><CloseCircleOutlined /></template>
            取消
          </a-button>
          <a-button v-if="todoDetail.availableActions && todoDetail.availableActions.includes('reassign')" @click="onReassign">
            <template #icon><UserSwitchOutlined /></template>
            重新分配
          </a-button>
          <a-button @click="onEdit">
            <template #icon><EditOutlined /></template>
            编辑
          </a-button>
        </a-space>
      </div>
    </div>

    <div v-else class="empty-container">
      <a-empty description="未找到待办信息" />
    </div>
  </a-drawer>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  UserSwitchOutlined,
  EditOutlined
} from '@ant-design/icons-vue'
import { todoApi } from '@/api/business/hospital/todo-api'

const router = useRouter()

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  todoId: {
    type: [String, Number],
    default: null
  }
})

// Emits
const emit = defineEmits(['update:visible', 'refresh', 'edit', 'reassign'])

// 数据状态
const drawerVisible = ref(false)
const loading = ref(false)
const todoDetail = ref(null)

// 计算属性
const hasBusinessInfo = computed(() => {
  if (!todoDetail.value) return false
  return todoDetail.value.customerName || 
         todoDetail.value.leadId || 
         todoDetail.value.appointmentId || 
         todoDetail.value.patientId || 
         todoDetail.value.followUpId
})

// 状态颜色映射
const getStatusColor = (status) => {
  const colorMap = {
    1: 'blue',      // 待处理
    2: 'orange',    // 处理中
    3: 'green',     // 已完成
    4: 'red',       // 已取消
    5: 'volcano'    // 已逾期
  }
  return colorMap[status] || 'default'
}

// 加载待办详情
const loadTodoDetail = async () => {
  if (!props.todoId) return
  
  try {
    loading.value = true
    const { data } = await todoApi.getDetail(props.todoId)
    todoDetail.value = data
  } catch (error) {
    console.error('加载待办详情失败:', error)
    todoDetail.value = null
  } finally {
    loading.value = false
  }
}

// 操作方法
const onStart = async () => {
  try {
    await todoApi.operation({
      todoId: props.todoId,
      operationType: 'start'
    })
    message.success('开始处理成功')
    emit('refresh')
    loadTodoDetail()
  } catch (error) {
    console.error('开始处理失败:', error)
  }
}

const onPause = async () => {
  try {
    await todoApi.operation({
      todoId: props.todoId,
      operationType: 'pause'
    })
    message.success('暂停处理成功')
    emit('refresh')
    loadTodoDetail()
  } catch (error) {
    console.error('暂停处理失败:', error)
  }
}

const onComplete = () => {
  Modal.confirm({
    title: '确认完成',
    content: `确定要完成待办"${todoDetail.value.todoTitle}"吗？`,
    onOk: async () => {
      try {
        await todoApi.operation({
          todoId: props.todoId,
          operationType: 'complete'
        })
        message.success('完成待办成功')
        emit('refresh')
        loadTodoDetail()
      } catch (error) {
        console.error('完成待办失败:', error)
      }
    }
  })
}

const onCancel = () => {
  Modal.confirm({
    title: '确认取消',
    content: `确定要取消待办"${todoDetail.value.todoTitle}"吗？`,
    onOk: async () => {
      try {
        await todoApi.operation({
          todoId: props.todoId,
          operationType: 'cancel'
        })
        message.success('取消待办成功')
        emit('refresh')
        loadTodoDetail()
      } catch (error) {
        console.error('取消待办失败:', error)
      }
    }
  })
}

const onEdit = () => {
  emit('edit', props.todoId)
}

const onReassign = () => {
  emit('reassign', props.todoId)
}

// 业务跳转方法
const goToLead = () => {
  router.push(`/lead/360-view/${todoDetail.value.leadId}`)
}

const goToAppointment = () => {
  router.push('/appointment/list')
}

const goToPatient = () => {
  router.push('/visit/patient/list')
}

const goToFollowUp = () => {
  router.push('/followup/record')
}

// 监听器
watch(() => props.visible, (newVal) => {
  drawerVisible.value = newVal
  if (newVal && props.todoId) {
    loadTodoDetail()
  }
})

watch(drawerVisible, (newVal) => {
  emit('update:visible', newVal)
})
</script>

<style scoped>
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.todo-detail {
  padding-bottom: 60px;
}

.section {
  margin-top: 16px;
}

.todo-content,
.remark-content {
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 200px;
  overflow-y: auto;
}

.overdue-text {
  color: #ff4d4f;
  font-weight: bold;
}

.urgent-text {
  color: #fa8c16;
  font-weight: bold;
}

.normal-text {
  color: #52c41a;
}

.action-buttons {
  position: fixed;
  bottom: 0;
  right: 0;
  left: 0;
  padding: 16px;
  background: #fff;
  border-top: 1px solid #f0f0f0;
  text-align: center;
  z-index: 1000;
}
</style>

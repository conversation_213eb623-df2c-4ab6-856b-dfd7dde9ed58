<template>
  <div class="todo-container">
    <!-- 搜索区域 -->
    <a-card class="search-card">
      <a-form layout="inline" :model="searchForm" @finish="handleSearch">
        <a-form-item label="待办类型" name="todoType">
          <a-select
            v-model:value="searchForm.todoType"
            placeholder="请选择待办类型"
            style="width: 150px"
            allow-clear
          >
            <a-select-option value="lead_follow">线索跟进</a-select-option>
            <a-select-option value="appointment_remind">预约提醒</a-select-option>
            <a-select-option value="customer_follow">客户跟进</a-select-option>
            <a-select-option value="treatment_follow">治疗回访</a-select-option>
            <a-select-option value="approval">审批事项</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="优先级" name="priorityLevel">
          <a-select
            v-model:value="searchForm.priorityLevel"
            placeholder="请选择优先级"
            style="width: 120px"
            allow-clear
          >
            <a-select-option :value="1">高</a-select-option>
            <a-select-option :value="2">中</a-select-option>
            <a-select-option :value="3">低</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="状态" name="todoStatus">
          <a-select
            v-model:value="searchForm.todoStatus"
            placeholder="请选择状态"
            style="width: 120px"
            allow-clear
          >
            <a-select-option :value="1">待处理</a-select-option>
            <a-select-option :value="2">处理中</a-select-option>
            <a-select-option :value="3">已完成</a-select-option>
            <a-select-option :value="4">已取消</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="截止时间" name="dueTimeRange">
          <a-range-picker
            v-model:value="searchForm.dueTimeRange"
            style="width: 240px"
            format="YYYY-MM-DD"
          />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" html-type="submit" :loading="loading">
            <template #icon>
              <SearchOutlined />
            </template>
            搜索
          </a-button>
          <a-button style="margin-left: 8px" @click="handleReset">
            <template #icon>
              <ReloadOutlined />
            </template>
            重置
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 操作按钮 -->
    <a-card class="action-card">
      <a-space>
        <a-button type="primary" @click="handleAdd">
          <template #icon>
            <PlusOutlined />
          </template>
          新增待办
        </a-button>
        <a-button @click="handleBatchComplete" :disabled="!selectedRowKeys.length">
          <template #icon>
            <CheckOutlined />
          </template>
          批量完成
        </a-button>
        <a-button @click="handleBatchDelete" :disabled="!selectedRowKeys.length">
          <template #icon>
            <DeleteOutlined />
          </template>
          批量删除
        </a-button>
      </a-space>
    </a-card>

    <!-- 数据表格 -->
    <a-card>
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        :row-selection="rowSelection"
        @change="handleTableChange"
        row-key="todoId"
      >
        <!-- 待办类型 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'todoType'">
            <a-tag :color="getTodoTypeColor(record.todoType)">
              {{ getTodoTypeText(record.todoType) }}
            </a-tag>
          </template>

          <!-- 优先级 -->
          <template v-else-if="column.key === 'priorityLevel'">
            <a-tag :color="getPriorityColor(record.priorityLevel)">
              {{ getPriorityText(record.priorityLevel) }}
            </a-tag>
          </template>

          <!-- 状态 -->
          <template v-else-if="column.key === 'todoStatus'">
            <a-tag :color="getStatusColor(record.todoStatus)">
              {{ getStatusText(record.todoStatus) }}
            </a-tag>
          </template>

          <!-- 操作 -->
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleView(record)">
                查看
              </a-button>
              <a-button type="link" size="small" @click="handleEdit(record)" v-if="record.todoStatus === 1">
                编辑
              </a-button>
              <a-button type="link" size="small" @click="handleComplete(record)" v-if="record.todoStatus === 1">
                完成
              </a-button>
              <a-button type="link" size="small" @click="handleDelete(record)" v-if="record.todoStatus === 1">
                删除
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 新增/编辑弹窗 -->
    <todo-form-modal
      ref="todoFormModal"
      @refresh="loadData"
    />

    <!-- 查看详情弹窗 -->
    <todo-detail-modal
      ref="todoDetailModal"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  CheckOutlined,
  DeleteOutlined,
} from '@ant-design/icons-vue';
import { todoApi } from '/@/api/business/hospital/todo-api';
import TodoFormModal from './components/TodoFormDrawer.vue';
import TodoDetailModal from './components/TodoDetailDrawer.vue';

// 搜索表单
const searchForm = reactive({
  todoType: undefined,
  priorityLevel: undefined,
  todoStatus: undefined,
  dueTimeRange: [],
});

// 表格数据
const dataSource = ref([]);
const loading = ref(false);
const selectedRowKeys = ref([]);

// 分页
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`,
});

// 表格列定义
const columns = [
  {
    title: '待办标题',
    dataIndex: 'todoTitle',
    key: 'todoTitle',
    width: 200,
    ellipsis: true,
  },
  {
    title: '待办类型',
    dataIndex: 'todoType',
    key: 'todoType',
    width: 120,
  },
  {
    title: '优先级',
    dataIndex: 'priorityLevel',
    key: 'priorityLevel',
    width: 80,
  },
  {
    title: '状态',
    dataIndex: 'todoStatus',
    key: 'todoStatus',
    width: 100,
  },
  {
    title: '分配人',
    dataIndex: 'assignedEmployeeName',
    key: 'assignedEmployeeName',
    width: 100,
  },
  {
    title: '截止时间',
    dataIndex: 'dueTime',
    key: 'dueTime',
    width: 150,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 150,
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right',
  },
];

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys) => {
    selectedRowKeys.value = keys;
  },
};

// 弹窗引用
const todoFormModal = ref();
const todoDetailModal = ref();

// 加载数据
const loadData = async () => {
  try {
    loading.value = true;
    const params = {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      ...searchForm,
      startDate: searchForm.dueTimeRange?.[0]?.format('YYYY-MM-DD'),
      endDate: searchForm.dueTimeRange?.[1]?.format('YYYY-MM-DD'),
    };
    
    const res = await todoApi.getPage(params);
    if (res.ok) {
      dataSource.value = res.data.list;
      pagination.total = res.data.total;
    }
  } catch (error) {
    console.error('加载数据失败:', error);
    message.error('加载数据失败');
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  loadData();
};

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    todoType: undefined,
    priorityLevel: undefined,
    todoStatus: undefined,
    dueTimeRange: [],
  });
  pagination.current = 1;
  loadData();
};

// 表格变化
const handleTableChange = (pag) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  loadData();
};

// 新增
const handleAdd = () => {
  todoFormModal.value.showModal();
};

// 编辑
const handleEdit = (record) => {
  todoFormModal.value.showModal(record);
};

// 查看
const handleView = (record) => {
  todoDetailModal.value.showModal(record);
};

// 完成
const handleComplete = async (record) => {
  try {
    const res = await todoApi.complete(record.todoId);
    if (res.ok) {
      message.success('操作成功');
      loadData();
    }
  } catch (error) {
    console.error('操作失败:', error);
    message.error('操作失败');
  }
};

// 删除
const handleDelete = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除待办事项"${record.todoTitle}"吗？`,
    onOk: async () => {
      try {
        const res = await todoApi.delete(record.todoId);
        if (res.ok) {
          message.success('删除成功');
          loadData();
        }
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
};

// 批量完成
const handleBatchComplete = async () => {
  Modal.confirm({
    title: '确认批量完成',
    content: `确定要完成选中的 ${selectedRowKeys.value.length} 个待办事项吗？`,
    onOk: async () => {
      try {
        const res = await todoApi.batchComplete(selectedRowKeys.value);
        if (res.ok) {
          message.success('批量完成成功');
          selectedRowKeys.value = [];
          loadData();
        }
      } catch (error) {
        console.error('批量完成失败:', error);
        message.error('批量完成失败');
      }
    },
  });
};

// 批量删除
const handleBatchDelete = () => {
  Modal.confirm({
    title: '确认批量删除',
    content: `确定要删除选中的 ${selectedRowKeys.value.length} 个待办事项吗？`,
    onOk: async () => {
      try {
        const res = await todoApi.batchDelete(selectedRowKeys.value);
        if (res.ok) {
          message.success('批量删除成功');
          selectedRowKeys.value = [];
          loadData();
        }
      } catch (error) {
        console.error('批量删除失败:', error);
        message.error('批量删除失败');
      }
    },
  });
};

// 获取待办类型颜色
const getTodoTypeColor = (type) => {
  const colorMap = {
    lead_follow: 'blue',
    appointment_remind: 'green',
    customer_follow: 'orange',
    treatment_follow: 'purple',
    approval: 'red',
  };
  return colorMap[type] || 'default';
};

// 获取待办类型文本
const getTodoTypeText = (type) => {
  const textMap = {
    lead_follow: '线索跟进',
    appointment_remind: '预约提醒',
    customer_follow: '客户跟进',
    treatment_follow: '治疗回访',
    approval: '审批事项',
  };
  return textMap[type] || type;
};

// 获取优先级颜色
const getPriorityColor = (priority) => {
  switch (priority) {
    case 1: return 'red';
    case 2: return 'orange';
    case 3: return 'blue';
    default: return 'green';
  }
};

// 获取优先级文本
const getPriorityText = (priority) => {
  switch (priority) {
    case 1: return '高';
    case 2: return '中';
    case 3: return '低';
    default: return '普通';
  }
};

// 获取状态颜色
const getStatusColor = (status) => {
  switch (status) {
    case 1: return 'blue';
    case 2: return 'orange';
    case 3: return 'green';
    case 4: return 'red';
    default: return 'default';
  }
};

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 1: return '待处理';
    case 2: return '处理中';
    case 3: return '已完成';
    case 4: return '已取消';
    default: return '未知';
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadData();
});
</script>

<style scoped>
.todo-container {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
}

.search-card {
  margin-bottom: 16px;
}

.action-card {
  margin-bottom: 16px;
}
</style> 
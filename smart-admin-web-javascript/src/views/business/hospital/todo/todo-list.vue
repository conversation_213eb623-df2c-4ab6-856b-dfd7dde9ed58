<template>
  <div class="todo-list-container">
    <!-- 搜索表单 -->
    <a-card size="small" class="search-card">
      <a-form ref="searchFormRef" :model="searchForm" layout="inline">
        <a-form-item label="待办标题" name="todoTitle">
          <a-input v-model:value="searchForm.todoTitle" placeholder="请输入待办标题" allow-clear />
        </a-form-item>
        
        <a-form-item label="待办类型" name="todoType">
          <a-select v-model:value="searchForm.todoType" placeholder="请选择待办类型" allow-clear style="width: 150px">
            <a-select-option v-for="(desc, type) in TodoTypeDescMap" :key="type" :value="type">
              {{ desc }}
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="状态" name="todoStatus">
          <a-select v-model:value="searchForm.todoStatus" placeholder="请选择状态" allow-clear style="width: 120px">
            <a-select-option v-for="(desc, status) in TodoStatusDescMap" :key="status" :value="parseInt(status)">
              {{ desc }}
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="优先级" name="priorityLevel">
          <a-select v-model:value="searchForm.priorityLevel" placeholder="请选择优先级" allow-clear style="width: 100px">
            <a-select-option v-for="(desc, priority) in TodoPriorityDescMap" :key="priority" :value="parseInt(priority)">
              {{ desc }}
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="分配人" name="assignedEmployeeId">
          <a-select v-model:value="searchForm.assignedEmployeeId" placeholder="请选择分配人" allow-clear style="width: 150px">
            <a-select-option v-for="employee in employeeList" :key="employee.employeeId" :value="employee.employeeId">
              {{ employee.actualName }}
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="创建时间" name="createTimeRange">
          <a-range-picker v-model:value="searchForm.createTimeRange" />
        </a-form-item>
        
        <a-form-item>
          <a-space>
            <a-button type="primary" @click="onSearch">
              <template #icon><SearchOutlined /></template>
              查询
            </a-button>
            <a-button @click="onReset">
              <template #icon><ReloadOutlined /></template>
              重置
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 操作栏 -->
    <a-card size="small" class="action-card">
      <a-row justify="space-between">
        <a-col>
          <a-space>
            <a-button type="primary" @click="onAdd">
              <template #icon><PlusOutlined /></template>
              新增待办
            </a-button>
            <a-button :disabled="!hasSelected" @click="onBatchComplete">
              <template #icon><CheckOutlined /></template>
              批量完成
            </a-button>
            <a-button :disabled="!hasSelected" @click="onBatchCancel">
              <template #icon><CloseOutlined /></template>
              批量取消
            </a-button>
          </a-space>
        </a-col>
        <a-col>
          <a-space>
            <a-button @click="onRefresh">
              <template #icon><ReloadOutlined /></template>
              刷新
            </a-button>
          </a-space>
        </a-col>
      </a-row>
    </a-card>

    <!-- 数据表格 -->
    <a-card size="small">
      <a-table
        :columns="columns"
        :data-source="tableData"
        :loading="tableLoading"
        :pagination="pagination"
        :row-selection="rowSelection"
        row-key="todoId"
        size="small"
        @change="onTableChange"
      >
        <!-- 待办类型 -->
        <template #todoType="{ record }">
          <a-tag>{{ record.todoTypeDesc }}</a-tag>
        </template>

        <!-- 优先级 -->
        <template #priorityLevel="{ record }">
          <a-tag :color="record.priorityColor">{{ record.priorityLevelDesc }}</a-tag>
        </template>

        <!-- 状态 -->
        <template #todoStatus="{ record }">
          <a-tag :color="getStatusColor(record.todoStatus)">
            {{ record.todoStatusDesc }}
          </a-tag>
        </template>

        <!-- 逾期状态 -->
        <template #overdueStatus="{ record }">
          <span v-if="record.isOverdue" class="overdue-text">
            逾期{{ record.overdueDays }}天
          </span>
          <span v-else-if="record.remainingHours !== null && record.remainingHours <= 24" class="urgent-text">
            剩余{{ record.remainingHours }}小时
          </span>
          <span v-else class="normal-text">正常</span>
        </template>

        <!-- 操作 -->
        <template #action="{ record }">
          <a-space size="small">
            <a-button type="link" size="small" @click="onView(record)">查看</a-button>
            <a-button v-if="record.availableActions && record.availableActions.includes('start')" type="link" size="small" @click="onStart(record)">
              开始
            </a-button>
            <a-button v-if="record.availableActions && record.availableActions.includes('pause')" type="link" size="small" @click="onPause(record)">
              暂停
            </a-button>
            <a-button v-if="record.availableActions && record.availableActions.includes('complete')" type="link" size="small" @click="onComplete(record)">
              完成
            </a-button>
            <a-button v-if="record.availableActions && record.availableActions.includes('cancel')" type="link" size="small" danger @click="onCancel(record)">
              取消
            </a-button>
            <a-dropdown>
              <template #overlay>
                <a-menu>
                  <a-menu-item key="edit" @click="onEdit(record)">
                    <EditOutlined />
                    编辑
                  </a-menu-item>
                  <a-menu-item key="reassign" @click="onReassign(record)">
                    <UserSwitchOutlined />
                    重新分配
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item key="delete" danger @click="onDelete(record)">
                    <DeleteOutlined />
                    删除
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button type="link" size="small">
                更多 <DownOutlined />
              </a-button>
            </a-dropdown>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 待办详情抽屉 -->
    <TodoDetailDrawer
      v-model:visible="detailDrawerVisible"
      :todo-id="currentTodoId"
      @refresh="onRefresh"
    />

    <!-- 待办表单抽屉 -->
    <TodoFormDrawer
      v-model:visible="formDrawerVisible"
      :todo-id="currentTodoId"
      @refresh="onRefresh"
    />

    <!-- 重新分配抽屉 -->
    <TodoReassignDrawer
      v-model:visible="reassignDrawerVisible"
      :todo-id="currentTodoId"
      @refresh="onRefresh"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  CheckOutlined,
  CloseOutlined,
  EditOutlined,
  DeleteOutlined,
  UserSwitchOutlined,
  DownOutlined
} from '@ant-design/icons-vue'
import { todoApi, TodoTypeDescMap, TodoStatusDescMap, TodoPriorityDescMap } from '@/api/business/hospital/todo-api'
import { employeeApi } from '@/api/system/employee-api'
import TodoDetailDrawer from './components/TodoDetailDrawer.vue'
import TodoFormDrawer from './components/TodoFormDrawer.vue'
import TodoReassignDrawer from './components/TodoReassignDrawer.vue'

const route = useRoute()

// 搜索表单
const searchFormRef = ref()
const searchForm = reactive({
  todoTitle: '',
  todoType: undefined,
  todoStatus: undefined,
  priorityLevel: undefined,
  assignedEmployeeId: undefined,
  createTimeRange: undefined
})

// 表格数据
const tableData = ref([])
const tableLoading = ref(false)
const selectedRowKeys = ref([])
const employeeList = ref([])

// 分页
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

// 抽屉状态
const detailDrawerVisible = ref(false)
const formDrawerVisible = ref(false)
const reassignDrawerVisible = ref(false)
const currentTodoId = ref(null)

// 表格列配置
const columns = [
  {
    title: '待办标题',
    dataIndex: 'todoTitle',
    width: 200,
    ellipsis: true
  },
  {
    title: '类型',
    dataIndex: 'todoType',
    width: 100,
    slots: { customRender: 'todoType' }
  },
  {
    title: '优先级',
    dataIndex: 'priorityLevel',
    width: 80,
    slots: { customRender: 'priorityLevel' }
  },
  {
    title: '状态',
    dataIndex: 'todoStatus',
    width: 80,
    slots: { customRender: 'todoStatus' }
  },
  {
    title: '分配人',
    dataIndex: 'assignedEmployeeName',
    width: 100
  },
  {
    title: '客户',
    dataIndex: 'customerName',
    width: 100,
    ellipsis: true
  },
  {
    title: '截止时间',
    dataIndex: 'dueTime',
    width: 150,
    customRender: ({ text }) => text ? dayjs(text).format('YYYY-MM-DD HH:mm') : '-'
  },
  {
    title: '逾期状态',
    dataIndex: 'overdueStatus',
    width: 100,
    slots: { customRender: 'overdueStatus' }
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 150,
    customRender: ({ text }) => dayjs(text).format('YYYY-MM-DD HH:mm')
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right',
    slots: { customRender: 'action' }
  }
]

// 行选择
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys) => {
    selectedRowKeys.value = keys
  }
}

// 计算属性
const hasSelected = computed(() => selectedRowKeys.value.length > 0)

// 状态颜色映射
const getStatusColor = (status) => {
  const colorMap = {
    1: 'blue',      // 待处理
    2: 'orange',    // 处理中
    3: 'green',     // 已完成
    4: 'red',       // 已取消
    5: 'volcano'    // 已逾期
  }
  return colorMap[status] || 'default'
}

// 加载员工列表
const loadEmployeeList = async () => {
  try {
    const { data } = await employeeApi.queryAll()
    employeeList.value = data || []
  } catch (error) {
    console.error('加载员工列表失败:', error)
  }
}

// 查询数据
const queryData = async () => {
  try {
    tableLoading.value = true
    
    const params = {
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      ...searchForm
    }
    
    // 处理时间范围
    if (searchForm.createTimeRange && searchForm.createTimeRange.length === 2) {
      params.createTimeBegin = dayjs(searchForm.createTimeRange[0]).format('YYYY-MM-DD 00:00:00')
      params.createTimeEnd = dayjs(searchForm.createTimeRange[1]).format('YYYY-MM-DD 23:59:59')
    }
    
    const { data } = await todoApi.queryPage(params)
    
    tableData.value = data.list || []
    pagination.total = data.total || 0
  } catch (error) {
    console.error('查询待办列表失败:', error)
    tableData.value = []
    pagination.total = 0
  } finally {
    tableLoading.value = false
  }
}

// 事件处理
const onSearch = () => {
  pagination.current = 1
  queryData()
}

const onReset = () => {
  searchFormRef.value?.resetFields()
  pagination.current = 1
  queryData()
}

const onRefresh = () => {
  queryData()
}

const onTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  queryData()
}

const onAdd = () => {
  currentTodoId.value = null
  formDrawerVisible.value = true
}

const onView = (record) => {
  currentTodoId.value = record.todoId
  detailDrawerVisible.value = true
}

const onEdit = (record) => {
  currentTodoId.value = record.todoId
  formDrawerVisible.value = true
}

const onReassign = (record) => {
  currentTodoId.value = record.todoId
  reassignDrawerVisible.value = true
}

const onStart = async (record) => {
  try {
    await todoApi.operation({
      todoId: record.todoId,
      operationType: 'start'
    })
    message.success('开始处理成功')
    onRefresh()
  } catch (error) {
    console.error('开始处理失败:', error)
  }
}

const onPause = async (record) => {
  try {
    await todoApi.operation({
      todoId: record.todoId,
      operationType: 'pause'
    })
    message.success('暂停处理成功')
    onRefresh()
  } catch (error) {
    console.error('暂停处理失败:', error)
  }
}

const onComplete = (record) => {
  Modal.confirm({
    title: '确认完成',
    content: `确定要完成待办"${record.todoTitle}"吗？`,
    onOk: async () => {
      try {
        await todoApi.operation({
          todoId: record.todoId,
          operationType: 'complete'
        })
        message.success('完成待办成功')
        onRefresh()
      } catch (error) {
        console.error('完成待办失败:', error)
      }
    }
  })
}

const onCancel = (record) => {
  Modal.confirm({
    title: '确认取消',
    content: `确定要取消待办"${record.todoTitle}"吗？`,
    onOk: async () => {
      try {
        await todoApi.operation({
          todoId: record.todoId,
          operationType: 'cancel'
        })
        message.success('取消待办成功')
        onRefresh()
      } catch (error) {
        console.error('取消待办失败:', error)
      }
    }
  })
}

const onDelete = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除待办"${record.todoTitle}"吗？此操作不可恢复。`,
    onOk: async () => {
      try {
        await todoApi.delete(record.todoId)
        message.success('删除待办成功')
        onRefresh()
      } catch (error) {
        console.error('删除待办失败:', error)
      }
    }
  })
}

const onBatchComplete = () => {
  Modal.confirm({
    title: '批量完成',
    content: `确定要完成选中的 ${selectedRowKeys.value.length} 个待办吗？`,
    onOk: async () => {
      try {
        await todoApi.batchUpdateStatus({
          todoIds: selectedRowKeys.value,
          todoStatus: 3 // 已完成
        })
        message.success('批量完成成功')
        selectedRowKeys.value = []
        onRefresh()
      } catch (error) {
        console.error('批量完成失败:', error)
      }
    }
  })
}

const onBatchCancel = () => {
  Modal.confirm({
    title: '批量取消',
    content: `确定要取消选中的 ${selectedRowKeys.value.length} 个待办吗？`,
    onOk: async () => {
      try {
        await todoApi.batchUpdateStatus({
          todoIds: selectedRowKeys.value,
          todoStatus: 4 // 已取消
        })
        message.success('批量取消成功')
        selectedRowKeys.value = []
        onRefresh()
      } catch (error) {
        console.error('批量取消失败:', error)
      }
    }
  })
}

// 监听路由参数变化
watch(() => route.query, (newQuery) => {
  if (newQuery.filter) {
    // 根据过滤条件设置搜索表单
    switch (newQuery.filter) {
      case 'today':
        searchForm.createTimeRange = [dayjs().startOf('day'), dayjs().endOf('day')]
        break
      case 'overdue':
        searchForm.todoStatus = 5 // 已逾期
        break
      case 'high-priority':
        searchForm.priorityLevel = 1 // 高优先级
        break
      case 'processing':
        searchForm.todoStatus = 2 // 处理中
        break
    }
    onSearch()
  }
}, { immediate: true })

// 初始化
onMounted(() => {
  loadEmployeeList()
  if (!route.query.filter) {
    queryData()
  }
})
</script>

<style scoped>
.todo-list-container {
  padding: 16px;
}

.search-card,
.action-card {
  margin-bottom: 16px;
}

.overdue-text {
  color: #ff4d4f;
  font-weight: bold;
}

.urgent-text {
  color: #fa8c16;
  font-weight: bold;
}

.normal-text {
  color: #52c41a;
}
</style>

<template>
  <div class="patient-detail-demo">
    <a-card title="患者详情组件演示" class="demo-card">
      <div class="demo-description">
        <a-alert
          message="患者详情组件对比演示"
          description="本页面展示了传统患者详情组件与增强版患者详情组件的功能对比。增强版组件基于悟空CRM设计理念，提供更丰富的功能和更好的用户体验。"
          type="info"
          show-icon
          style="margin-bottom: 24px;"
        />
      </div>

      <div class="demo-buttons">
        <a-space size="large">
          <a-card title="传统版本" class="version-card">
            <div class="version-description">
              <p>• 基础患者信息展示</p>
              <p>• 简单的卡片布局</p>
              <p>• 基本的数据展示功能</p>
            </div>
            <a-button type="primary" @click="showTraditionalDetail" block>
              查看传统版详情
            </a-button>
          </a-card>

          <a-card title="增强版本" class="version-card enhanced">
            <div class="version-description">
              <p>• 🎯 AI智能评分系统</p>
              <p>• 📊 动态活动时间线</p>
              <p>• 🏥 医疗任务管理</p>
              <p>• 📞 回访管理功能</p>
              <p>• 👥 联系人管理</p>
              <p>• 📎 附件管理</p>
              <p>• 📈 患者统计摘要</p>
            </div>
            <a-button type="primary" @click="showEnhancedDetail" block>
              查看增强版详情
            </a-button>
          </a-card>
        </a-space>
      </div>

      <div class="demo-features">
        <a-divider>功能对比</a-divider>
        <a-table :columns="comparisonColumns" :data-source="comparisonData" :pagination="false" size="small">
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'traditional'">
              <a-tag :color="record.traditional ? 'green' : 'red'">
                {{ record.traditional ? '✓ 支持' : '✗ 不支持' }}
              </a-tag>
            </template>
            <template v-if="column.key === 'enhanced'">
              <a-tag :color="record.enhanced ? 'green' : 'orange'">
                {{ record.enhanced ? '✓ 支持' : '○ 计划中' }}
              </a-tag>
            </template>
          </template>
        </a-table>
      </div>

      <div class="demo-sample-data">
        <a-divider>演示数据</a-divider>
        <a-descriptions title="当前演示患者信息" bordered size="small">
          <a-descriptions-item label="患者姓名">{{ samplePatient.patientName }}</a-descriptions-item>
          <a-descriptions-item label="患者编号">{{ samplePatient.patientNo }}</a-descriptions-item>
          <a-descriptions-item label="联系电话">{{ samplePatient.patientPhone }}</a-descriptions-item>
          <a-descriptions-item label="性别年龄">{{ samplePatient.genderName }} / {{ samplePatient.age }}岁</a-descriptions-item>
          <a-descriptions-item label="到诊状态">{{ samplePatient.visitStatusName }}</a-descriptions-item>
          <a-descriptions-item label="患者等级">{{ samplePatient.patientLevelName }}</a-descriptions-item>
        </a-descriptions>
      </div>
    </a-card>

    <!-- 传统版患者详情 -->
    <PatientDetailModal ref="patientDetailModal" />

    <!-- 增强版患者详情 -->
    <PatientDetailEnhanced ref="patientDetailEnhanced" />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { message } from 'ant-design-vue';
import PatientDetailModal from './components/patient-detail-modal.vue';
import PatientDetailEnhanced from './components/patient-detail-enhanced.vue';

// 组件引用
const patientDetailModal = ref();
const patientDetailEnhanced = ref();

// 演示患者数据
const samplePatient = reactive({
  patientId: 1,
  patientNo: 'P202401001',
  patientName: '张三',
  patientPhone: '13800138001',
  genderName: '男',
  age: 45,
  visitStatusName: '已到诊',
  patientLevelName: 'VIP患者',
});

// 功能对比表格列定义
const comparisonColumns = [
  { title: '功能模块', dataIndex: 'feature', key: 'feature', width: 200 },
  { title: '传统版本', key: 'traditional', width: 120 },
  { title: '增强版本', key: 'enhanced', width: 120 },
  { title: '说明', dataIndex: 'description', key: 'description' },
];

// 功能对比数据
const comparisonData = [
  {
    feature: '患者基本信息',
    traditional: true,
    enhanced: true,
    description: '增强版提供AI评分和更丰富的状态标签'
  },
  {
    feature: '活动时间线',
    traditional: false,
    enhanced: true,
    description: '动态展示患者就诊历程和医疗活动'
  },
  {
    feature: '医疗任务管理',
    traditional: false,
    enhanced: true,
    description: '管理检查单、化验单、处方单等医疗任务'
  },
  {
    feature: '回访管理',
    traditional: false,
    enhanced: true,
    description: '完整的回访计划创建和执行管理'
  },
  {
    feature: '联系人管理',
    traditional: false,
    enhanced: true,
    description: '管理患者的紧急联系人和家属信息'
  },
  {
    feature: '附件管理',
    traditional: false,
    enhanced: true,
    description: '上传、预览、下载医疗相关文件'
  },
  {
    feature: '统计摘要',
    traditional: false,
    enhanced: true,
    description: '图表化展示患者关键统计数据'
  },
  {
    feature: '操作记录',
    traditional: false,
    enhanced: true,
    description: '完整的医疗操作和系统操作历史'
  }
];

// 显示传统版详情
function showTraditionalDetail() {
  patientDetailModal.value.showModal(samplePatient.patientId);
  message.info('正在打开传统版患者详情');
}

// 显示增强版详情
function showEnhancedDetail() {
  patientDetailEnhanced.value.showModal(samplePatient.patientId);
  message.info('正在打开增强版患者详情');
}
</script>

<style scoped>
.patient-detail-demo {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.demo-card {
  max-width: 1200px;
  margin: 0 auto;
}

.demo-buttons {
  margin: 24px 0;
}

.version-card {
  width: 300px;
  text-align: center;
}

.version-card.enhanced {
  border: 2px solid #1890ff;
}

.version-description {
  margin-bottom: 16px;
  text-align: left;
}

.version-description p {
  margin: 8px 0;
  color: #666;
}

.demo-features {
  margin: 32px 0;
}

.demo-sample-data {
  margin: 32px 0;
}
</style>

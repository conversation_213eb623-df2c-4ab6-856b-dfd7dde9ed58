<template>
  <a-drawer
    title="患者详情"
    :width="1200"
    :open="visible"
    placement="right"
    @close="onClose"
    class="patient-detail-enhanced"
  >
    <a-spin :spinning="loading">
      <div v-if="patientData" class="patient-detail-container">
        <!-- 左侧：患者基本信息和快速操作 -->
        <div class="patient-info-section">
          <!-- 患者基本信息卡片 -->
          <a-card class="patient-basic-card">
            <div class="patient-header">
              <div class="patient-avatar">
                <a-avatar :size="64" :style="{ backgroundColor: getPatientLevelColor(patientData.patientLevel) }">
                  {{ patientData.patientName?.charAt(0) }}
                </a-avatar>
              </div>
              <div class="patient-info">
                <div class="patient-name">{{ patientData.patientName }}</div>
                <div class="patient-tags">
                  <a-tag :color="getVisitStatusColor(patientData.visitStatus)">
                    {{ getVisitStatusName(patientData.visitStatus) }}
                  </a-tag>
                  <a-tag :color="getPatientLevelColor(patientData.patientLevel)">
                    {{ getPatientLevelName(patientData.patientLevel) }}
                  </a-tag>
                  <a-tag v-if="patientData.diagnosisStatus" :color="getDiagnosisStatusColor(patientData.diagnosisStatus)">
                    {{ getDiagnosisStatusName(patientData.diagnosisStatus) }}
                  </a-tag>
                  <a-tag v-if="patientData.patientTags" color="cyan">
                    {{ patientData.patientTags }}
                  </a-tag>
                </div>
                <div class="patient-ai-score">
                  <span class="ai-label">AI患者评分：</span>
                  <a-rate :value="aiScoreStars" disabled allow-half />
                  <span class="score-text" :style="{ color: aiScoreColor }">{{ aiScore }}分</span>
                  <span class="score-level">({{ aiScoreLevel }})</span>
                </div>
              </div>
            </div>
            
            <!-- 患者关键信息 -->
            <div class="patient-key-info">
              <div class="info-row">
                <span class="info-label">患者编号：</span>
                <span class="info-value">{{ patientData.patientNo }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">性别年龄：</span>
                <span class="info-value">
                  <DictLabel :dict-code="DICT_CODE_ENUM.GENDER" :data-value="patientData.gender" />
                  {{ patientData.age }}岁
                </span>
              </div>
              <div class="info-row">
                <span class="info-label">联系电话：</span>
                <span class="info-value">{{ patientData.patientPhone }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">负责医生：</span>
                <span class="info-value">{{ patientData.assignedDoctorName || '未分配' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">医疗助理：</span>
                <span class="info-value">{{ patientData.assignedAssistantName || '未分配' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">就诊日期：</span>
                <span class="info-value">{{ formatDate(patientData.visitDate) }}</span>
              </div>
            </div>
          </a-card>

          <!-- 快速操作按钮 -->
          <a-card title="快速操作" class="quick-actions-card">
            <div class="action-buttons">
              <a-button type="primary" @click="createMedicalTask">
                <template #icon><PlusOutlined /></template>
                创建医疗任务
              </a-button>
              <a-button @click="createFollowUpPlan">
                <template #icon><CalendarOutlined /></template>
                创建回访计划
              </a-button>
              <a-button @click="addContact">
                <template #icon><UserAddOutlined /></template>
                添加联系人
              </a-button>
              <a-button @click="viewDiagnosis">
                <template #icon><FileTextOutlined /></template>
                查看诊断记录
              </a-button>
              <a-button @click="editPatient">
                <template #icon><EditOutlined /></template>
                编辑患者信息
              </a-button>
            </div>
          </a-card>

          <!-- 患者统计摘要 -->
          <a-card class="patient-summary-card">
            <template #title>
              <span>患者摘要</span>
              <a-button type="text" size="small" @click="refreshPatientStats" style="float: right;">
                <template #icon><ReloadOutlined /></template>
              </a-button>
            </template>
            <div class="summary-stats">
              <div class="stat-item">
                <div class="stat-icon">
                  <CalendarOutlined style="color: #1890ff;" />
                </div>
                <div class="stat-content">
                  <div class="stat-label">就诊次数</div>
                  <div class="stat-value">{{ patientData.visitCount || 0 }} 次</div>
                </div>
              </div>
              <div class="stat-item">
                <div class="stat-icon">
                  <ClockCircleOutlined style="color: #fa8c16;" />
                </div>
                <div class="stat-content">
                  <div class="stat-label">未回访时长</div>
                  <div class="stat-value">{{ patientData.unFollowDays || 0 }} 天</div>
                </div>
              </div>
              <div class="stat-item">
                <div class="stat-icon">
                  <UserOutlined style="color: #52c41a;" />
                </div>
                <div class="stat-content">
                  <div class="stat-label">医疗费用</div>
                  <div class="stat-value">¥{{ formatCurrency(patientData.totalConsumption) }}</div>
                </div>
              </div>
              <div class="stat-item">
                <div class="stat-icon">
                  <HeartOutlined style="color: #eb2f96;" />
                </div>
                <div class="stat-content">
                  <div class="stat-label">满意度评分</div>
                  <div class="stat-value">
                    <a-rate :value="patientData.satisfactionScore || 0" disabled allow-half style="font-size: 12px;" />
                    <span style="margin-left: 4px;">{{ patientData.satisfactionScore || 0 }} 分</span>
                  </div>
                </div>
              </div>
              <div class="stat-item">
                <div class="stat-icon">
                  <MedicineBoxOutlined style="color: #722ed1;" />
                </div>
                <div class="stat-content">
                  <div class="stat-label">待处理任务</div>
                  <div class="stat-value">{{ pendingTasksCount }} 项</div>
                </div>
              </div>
              <div class="stat-item">
                <div class="stat-icon">
                  <FileTextOutlined style="color: #13c2c2;" />
                </div>
                <div class="stat-content">
                  <div class="stat-label">回访记录</div>
                  <div class="stat-value">{{ followUpList.length }} 条</div>
                </div>
              </div>
            </div>
          </a-card>
        </div>

        <!-- 右侧：功能标签页 -->
        <div class="patient-tabs-section">
          <a-tabs v-model:activeKey="activeTabKey" type="card" class="patient-tabs">
            <!-- 活动时间线 -->
            <a-tab-pane key="timeline" tab="活动时间线">
              <div class="tab-content">
                <div class="timeline-header">
                  <a-button type="primary" size="small" @click="refreshTimeline">
                    <template #icon><ReloadOutlined /></template>
                    刷新时间线
                  </a-button>
                </div>
                <a-timeline v-if="timelineData.length > 0">
                  <a-timeline-item
                    v-for="item in timelineData"
                    :key="item.id"
                    :color="item.color"
                  >
                    <template #dot>
                      <component :is="item.icon" style="font-size: 16px;" />
                    </template>
                    <div class="timeline-item-content">
                      <div class="timeline-title">
                        <span class="timeline-type">{{ item.type }}</span>
                        <span class="timeline-time">{{ formatDateTime(item.time) }}</span>
                      </div>
                      <div class="timeline-content">{{ item.content }}</div>
                      <div class="timeline-operator" v-if="item.operator">
                        操作人：{{ item.operator }}
                      </div>
                    </div>
                  </a-timeline-item>
                </a-timeline>
                <a-empty v-else description="暂无活动记录" />
              </div>
            </a-tab-pane>

            <!-- 详细资料 -->
            <a-tab-pane key="details" tab="详细资料">
              <div class="tab-content">
                <a-descriptions :column="2" bordered>
                  <a-descriptions-item label="患者编号">{{ patientData.patientNo }}</a-descriptions-item>
                  <a-descriptions-item label="患者姓名">{{ patientData.patientName }}</a-descriptions-item>
                  <a-descriptions-item label="患者电话">{{ patientData.patientPhone }}</a-descriptions-item>
                  <a-descriptions-item label="患者微信">{{ patientData.patientWechat }}</a-descriptions-item>
                  <a-descriptions-item label="性别">{{ patientData.genderName }}</a-descriptions-item>
                  <a-descriptions-item label="年龄">{{ patientData.age }}</a-descriptions-item>
                  <a-descriptions-item label="出生日期">{{ patientData.birthday }}</a-descriptions-item>
                  <a-descriptions-item label="身份证号">{{ patientData.idCard }}</a-descriptions-item>
                  <a-descriptions-item label="患者地址" :span="2">{{ patientData.patientAddress }}</a-descriptions-item>
                  <a-descriptions-item label="紧急联系人">{{ patientData.emergencyContact }}</a-descriptions-item>
                  <a-descriptions-item label="联系人电话">{{ patientData.emergencyPhone }}</a-descriptions-item>
                  <a-descriptions-item label="患者标签" :span="2">{{ patientData.patientTags }}</a-descriptions-item>
                  <a-descriptions-item label="备注信息" :span="2">{{ patientData.remark }}</a-descriptions-item>
                </a-descriptions>
              </div>
            </a-tab-pane>

            <!-- 回访管理 -->
            <a-tab-pane key="followup" tab="回访管理">
              <div class="tab-content">
                <div class="tab-header">
                  <a-space>
                    <a-button type="primary" @click="createFollowUp">
                      <template #icon><PlusOutlined /></template>
                      新建回访
                    </a-button>
                    <a-button @click="createFollowUpPlan">
                      <template #icon><CalendarOutlined /></template>
                      创建回访计划
                    </a-button>
                    <a-select v-model:value="followUpFilter" placeholder="状态筛选" style="width: 120px" allowClear>
                      <a-select-option value="1">待跟进</a-select-option>
                      <a-select-option value="2">跟进中</a-select-option>
                      <a-select-option value="3">已完成</a-select-option>
                      <a-select-option value="4">已取消</a-select-option>
                    </a-select>
                  </a-space>
                </div>
                <a-table
                  :columns="followUpColumns"
                  :data-source="filteredFollowUpList"
                  :pagination="false"
                  size="small"
                >
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'followType'">
                      <a-tag :color="getFollowTypeColor(record.followType)">
                        {{ getFollowTypeName(record.followType) }}
                      </a-tag>
                    </template>
                    <template v-if="column.key === 'followMethod'">
                      <a-tag :color="getFollowMethodColor(record.followMethod)">
                        {{ getFollowMethodName(record.followMethod) }}
                      </a-tag>
                    </template>
                    <template v-if="column.key === 'priorityLevel'">
                      <a-tag :color="getPriorityLevelColor(record.priorityLevel)">
                        {{ getPriorityLevelName(record.priorityLevel) }}
                      </a-tag>
                    </template>
                    <template v-if="column.key === 'plannedFollowTime'">
                      {{ formatDateTime(record.plannedFollowTime) }}
                    </template>
                    <template v-if="column.key === 'followStatus'">
                      <a-tag :color="getFollowStatusColor(record.followStatus)">
                        {{ getFollowStatusName(record.followStatus) }}
                      </a-tag>
                    </template>
                    <template v-if="column.key === 'actions'">
                      <a-space>
                        <a @click="viewFollowUp(record)">查看</a>
                        <a @click="editFollowUp(record)" v-if="record.followStatus === 1">编辑</a>
                        <a @click="executeFollowUp(record)" v-if="record.followStatus === 1">执行</a>
                        <a @click="completeFollowUp(record)" v-if="record.followStatus === 2">完成</a>
                      </a-space>
                    </template>
                  </template>
                </a-table>
              </div>
            </a-tab-pane>

            <!-- 医疗任务管理 -->
            <a-tab-pane key="medical-tasks" tab="医疗任务">
              <div class="tab-content">
                <div class="tab-header">
                  <a-space>
                    <a-button type="primary" @click="createMedicalTask">
                      <template #icon><PlusOutlined /></template>
                      创建医疗任务
                    </a-button>
                    <a-select v-model:value="taskFilter" placeholder="任务类型筛选" style="width: 150px" allowClear>
                      <a-select-option value="1">检查单</a-select-option>
                      <a-select-option value="2">化验单</a-select-option>
                      <a-select-option value="3">处方单</a-select-option>
                      <a-select-option value="4">手术单</a-select-option>
                    </a-select>
                  </a-space>
                </div>
                <a-table
                  :columns="medicalTaskColumns"
                  :data-source="filteredMedicalTasks"
                  :pagination="false"
                  size="small"
                >
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'taskType'">
                      <a-tag :color="getMedicalTaskTypeColor(record.taskType)">
                        {{ getMedicalTaskTypeName(record.taskType) }}
                      </a-tag>
                    </template>
                    <template v-if="column.key === 'taskStatus'">
                      <a-tag :color="getMedicalTaskStatusColor(record.taskStatus)">
                        {{ getMedicalTaskStatusName(record.taskStatus) }}
                      </a-tag>
                    </template>
                    <template v-if="column.key === 'actions'">
                      <a-space>
                        <a @click="viewMedicalTask(record)">查看</a>
                        <a @click="editMedicalTask(record)" v-if="record.taskStatus === 1">编辑</a>
                        <a @click="executeMedicalTask(record)" v-if="record.taskStatus === 2">执行</a>
                      </a-space>
                    </template>
                  </template>
                </a-table>
              </div>
            </a-tab-pane>

            <!-- 联系人管理 -->
            <a-tab-pane key="contacts" tab="联系人">
              <div class="tab-content">
                <div class="tab-header">
                  <a-button type="primary" @click="addContactPerson">
                    <template #icon><PlusOutlined /></template>
                    添加联系人
                  </a-button>
                </div>
                <a-list :data-source="contactList" item-layout="horizontal" v-if="contactList.length > 0">
                  <template #renderItem="{ item }">
                    <a-list-item>
                      <a-list-item-meta>
                        <template #avatar>
                          <a-avatar :style="{ backgroundColor: getContactAvatarColor(item.relationship) }">
                            {{ item.name?.charAt(0) }}
                          </a-avatar>
                        </template>
                        <template #title>
                          <span>{{ item.name }}</span>
                          <a-tag :color="getRelationshipColor(item.relationship)" style="margin-left: 8px;">
                            {{ item.relationship }}
                          </a-tag>
                        </template>
                        <template #description>
                          <div>
                            <div>电话：{{ item.phone }}</div>
                            <div v-if="item.wechat">微信：{{ item.wechat }}</div>
                            <div v-if="item.address">地址：{{ item.address }}</div>
                          </div>
                        </template>
                      </a-list-item-meta>
                      <template #actions>
                        <a @click="editContact(item)">编辑</a>
                        <a @click="deleteContact(item)" style="color: #ff4d4f;">删除</a>
                      </template>
                    </a-list-item>
                  </template>
                </a-list>
                <a-empty v-else description="暂无联系人信息" />
              </div>
            </a-tab-pane>

            <!-- 附件管理 -->
            <a-tab-pane key="attachments" tab="附件">
              <div class="tab-content">
                <div class="tab-header">
                  <a-space>
                    <a-upload
                      :before-upload="beforeUpload"
                      :custom-request="handleUpload"
                      :show-upload-list="false"
                      multiple
                    >
                      <a-button type="primary">
                        <template #icon><UploadOutlined /></template>
                        上传附件
                      </a-button>
                    </a-upload>
                    <a-select v-model:value="attachmentFilter" placeholder="文件类型筛选" style="width: 150px" allowClear>
                      <a-select-option value="image">图片</a-select-option>
                      <a-select-option value="document">文档</a-select-option>
                      <a-select-option value="video">视频</a-select-option>
                      <a-select-option value="other">其他</a-select-option>
                    </a-select>
                  </a-space>
                </div>
                <a-list :data-source="filteredAttachmentList" item-layout="horizontal" v-if="attachmentList.length > 0">
                  <template #renderItem="{ item }">
                    <a-list-item>
                      <a-list-item-meta>
                        <template #avatar>
                          <component :is="getFileIcon(item.fileType)" :style="{ fontSize: '24px', color: getFileTypeColor(item.fileType) }" />
                        </template>
                        <template #title>
                          <span>{{ item.fileName }}</span>
                          <a-tag :color="getFileTypeColor(item.fileType)" style="margin-left: 8px;">
                            {{ item.fileType }}
                          </a-tag>
                        </template>
                        <template #description>
                          <div>
                            <div>大小：{{ formatFileSize(item.fileSize) }}</div>
                            <div>上传时间：{{ formatDateTime(item.uploadTime) }}</div>
                            <div v-if="item.uploadUserName">上传人：{{ item.uploadUserName }}</div>
                          </div>
                        </template>
                      </a-list-item-meta>
                      <template #actions>
                        <a @click="previewFile(item)">预览</a>
                        <a @click="downloadFile(item)">下载</a>
                        <a @click="deleteFile(item)" style="color: #ff4d4f;">删除</a>
                      </template>
                    </a-list-item>
                  </template>
                </a-list>
                <a-empty v-else description="暂无附件" />
              </div>
            </a-tab-pane>

            <!-- 操作记录 -->
            <a-tab-pane key="operations" tab="操作记录">
              <div class="tab-content">
                <a-table 
                  :columns="operationColumns" 
                  :data-source="operationList" 
                  :pagination="false"
                  size="small"
                >
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'operationType'">
                      <a-tag>{{ record.operationTypeName }}</a-tag>
                    </template>
                  </template>
                </a-table>
              </div>
            </a-tab-pane>
          </a-tabs>
        </div>
      </div>
    </a-spin>
  </a-drawer>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';
import { message } from 'ant-design-vue';
import {
  PlusOutlined,
  CalendarOutlined,
  UserAddOutlined,
  FileTextOutlined,
  EditOutlined,
  ClockCircleOutlined,
  UserOutlined,
  MedicineBoxOutlined,
  UploadOutlined,
  FileOutlined,
  ReloadOutlined,
  ExperimentOutlined,
  HeartOutlined,
  SettingOutlined
} from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import { visitPatientApi } from '/@/api/business/hospital/visit/visit-patient-api';
import { visitFollowApi } from '/@/api/business/hospital/visit/visit-follow-api';
import { smartSentry } from '/@/lib/smart-sentry';
import { DICT_CODE_ENUM } from '/@/constants/support/dict-const';
import {
  VISIT_STATUS_ENUM,
  PATIENT_LEVEL_ENUM,
  DIAGNOSIS_STATUS_ENUM,
  FOLLOW_STATUS_ENUM,
  FOLLOW_TYPE_ENUM,
  OPERATION_TYPE_ENUM,
  AI_SCORE_LEVEL,
  GENDER_OPTIONS
} from '/@/constants/business/hospital/visit-const';
import DictLabel from '/@/components/support/dict-label/index.vue';

// ----------------------- 以下是声明的变量 -----------------------
const visible = ref(false);
const loading = ref(false);
const patientData = ref(null);
const activeTabKey = ref('timeline');

// ----------------------- AI评分计算 -----------------------
const aiScore = computed(() => {
  if (!patientData.value) return 0;

  let score = 60; // 基础分数

  // 根据患者等级调整分数
  if (patientData.value.patientLevel === 3) score += 20; // SVIP
  else if (patientData.value.patientLevel === 2) score += 10; // VIP

  // 根据到诊状态调整分数
  if (patientData.value.visitStatus === 4) score += 15; // 已完成
  else if (patientData.value.visitStatus === 3) score += 10; // 诊疗中
  else if (patientData.value.visitStatus === 2) score += 5; // 已到诊

  // 根据诊断状态调整分数
  if (patientData.value.diagnosisStatus === 3) score += 10; // 已审核
  else if (patientData.value.diagnosisStatus === 2) score += 5; // 已完成

  // 根据总消费金额调整分数
  if (patientData.value.totalConsumption > 10000) score += 10;
  else if (patientData.value.totalConsumption > 5000) score += 5;

  return Math.max(0, Math.min(100, score));
});

const aiScoreStars = computed(() => {
  return aiScore.value / 20; // 100分对应5星
});

const aiScoreColor = computed(() => {
  const score = aiScore.value;
  for (const level of Object.values(AI_SCORE_LEVEL)) {
    if (score >= level.min && score <= level.max) {
      return level.color;
    }
  }
  return '#666';
});

const aiScoreLevel = computed(() => {
  const score = aiScore.value;
  for (const level of Object.values(AI_SCORE_LEVEL)) {
    if (score >= level.min && score <= level.max) {
      return level.desc;
    }
  }
  return '未知';
});

// ----------------------- 医疗任务筛选 -----------------------
const filteredMedicalTasks = computed(() => {
  if (!taskFilter.value) {
    return medicalTaskList.value;
  }
  return medicalTaskList.value.filter(task => task.taskType === parseInt(taskFilter.value));
});

// ----------------------- 回访记录筛选 -----------------------
const filteredFollowUpList = computed(() => {
  if (!followUpFilter.value) {
    return followUpList.value;
  }
  return followUpList.value.filter(follow => follow.followStatus === parseInt(followUpFilter.value));
});

// ----------------------- 附件筛选 -----------------------
const filteredAttachmentList = computed(() => {
  if (!attachmentFilter.value) {
    return attachmentList.value;
  }
  return attachmentList.value.filter(attachment => {
    const fileType = attachment.fileType?.toLowerCase();
    switch (attachmentFilter.value) {
      case 'image':
        return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(fileType);
      case 'document':
        return ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'].includes(fileType);
      case 'video':
        return ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'].includes(fileType);
      case 'other':
        return !['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'].includes(fileType);
      default:
        return true;
    }
  });
});

// ----------------------- 统计数据计算 -----------------------
const pendingTasksCount = computed(() => {
  const pendingFollowUps = followUpList.value.filter(item => item.followStatus === 1).length;
  const pendingMedicalTasks = medicalTaskList.value.filter(item => item.taskStatus === 1).length;
  return pendingFollowUps + pendingMedicalTasks;
});

// 模拟数据
const followUpList = ref([]);
const contactList = ref([]);
const attachmentList = ref([]);
const operationList = ref([]);
const timelineData = ref([]);
const medicalTaskList = ref([]);
const taskFilter = ref(null);
const followUpFilter = ref(null);
const attachmentFilter = ref(null);

// 表格列定义
const followUpColumns = [
  { title: '跟进类型', dataIndex: 'followType', key: 'followType', width: 100 },
  { title: '跟进方式', dataIndex: 'followMethod', key: 'followMethod', width: 80 },
  { title: '跟进内容', dataIndex: 'followContent', key: 'followContent', ellipsis: true },
  { title: '优先级', dataIndex: 'priorityLevel', key: 'priorityLevel', width: 80 },
  { title: '跟进人', dataIndex: 'assignedUserName', key: 'assignedUserName', width: 100 },
  { title: '计划时间', dataIndex: 'plannedFollowTime', key: 'plannedFollowTime', width: 150 },
  { title: '跟进状态', dataIndex: 'followStatus', key: 'followStatus', width: 100 },
  { title: '操作', key: 'actions', width: 150 },
];

const operationColumns = [
  { title: '操作类型', dataIndex: 'operationType', key: 'operationType' },
  { title: '操作内容', dataIndex: 'operationContent', key: 'operationContent' },
  { title: '操作人', dataIndex: 'operatorName', key: 'operatorName' },
  { title: '操作时间', dataIndex: 'operationTime', key: 'operationTime' },
];

const medicalTaskColumns = [
  { title: '任务类型', dataIndex: 'taskType', key: 'taskType', width: 100 },
  { title: '任务名称', dataIndex: 'taskName', key: 'taskName' },
  { title: '创建医生', dataIndex: 'doctorName', key: 'doctorName', width: 100 },
  { title: '任务状态', dataIndex: 'taskStatus', key: 'taskStatus', width: 100 },
  { title: '创建时间', dataIndex: 'createTime', key: 'createTime', width: 150 },
  { title: '操作', key: 'actions', width: 150 },
];

// ----------------------- 显示弹窗 -----------------------
async function showModal(patientId) {
  visible.value = true;
  loading.value = true;

  try {
    console.log('🔍 [DEBUG] 加载患者详情，患者ID:', patientId);
    let res = await visitPatientApi.getDetail(patientId);
    console.log('📊 [DEBUG] 患者详情API响应:', res);

    if (res.ok && res.data) {
      patientData.value = res.data;
      console.log('✅ [DEBUG] 患者数据加载成功:', patientData.value);

      // 加载相关数据
      await loadRelatedData(patientId);
    } else {
      console.error('❌ [DEBUG] 患者详情加载失败:', res);
    }
  } catch (e) {
    console.error('❌ [DEBUG] 患者详情加载异常:', e);
    smartSentry.captureError(e);
  } finally {
    loading.value = false;
  }
}

// ----------------------- 加载相关数据 -----------------------
async function loadRelatedData(patientId) {
  try {
    console.log('🔍 [DEBUG] 开始加载患者相关数据，患者ID:', patientId);

    // 加载跟进记录
    await loadFollowUpList(patientId);

    // 加载操作记录
    await loadOperationList(patientId);

    // 加载时间线数据
    await loadTimelineData(patientId);

    // 加载医疗任务数据
    await loadMedicalTasks(patientId);

    // 加载联系人数据
    await loadContactList(patientId);

    // 加载附件数据
    await loadAttachmentList(patientId);

    console.log('✅ [DEBUG] 患者相关数据加载完成');
  } catch (e) {
    console.error('❌ [DEBUG] 加载患者相关数据失败:', e);
  }
}

// 加载跟进记录
async function loadFollowUpList(patientId) {
  try {
    // 使用visitFollowApi.getByPatientId加载跟进记录
    const res = await visitFollowApi.getByPatientId(patientId);

    if (res.ok && res.data) {
      followUpList.value = res.data || [];
      console.log('✅ [DEBUG] 跟进记录加载成功:', followUpList.value.length, '条');
    } else {
      console.log('⚠️ [DEBUG] 跟进记录API返回空数据或失败:', res);
      followUpList.value = [];
    }
  } catch (e) {
    console.error('❌ [DEBUG] 加载跟进记录失败:', e);
    followUpList.value = [];
  }
}

// 加载操作记录
async function loadOperationList(patientId) {
  try {
    // 模拟操作记录数据，后续可以实现真实API
    operationList.value = [
      {
        id: 1,
        operationType: '医疗操作',
        operationContent: '创建患者档案',
        operatorName: '系统管理员',
        operationTime: dayjs().subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss')
      },
      {
        id: 2,
        operationType: '状态变更',
        operationContent: '更新到诊状态为已到诊',
        operatorName: '前台接待',
        operationTime: dayjs().subtract(2, 'hour').format('YYYY-MM-DD HH:mm:ss')
      }
    ];
    console.log('✅ [DEBUG] 操作记录加载成功:', operationList.value.length, '条');
  } catch (e) {
    console.error('❌ [DEBUG] 加载操作记录失败:', e);
    operationList.value = [];
  }
}

// 加载时间线数据
async function loadTimelineData(patientId) {
  try {
    const timeline = [];

    // 添加患者基础信息到时间线
    if (patientData.value) {
      timeline.push({
        id: 'patient-register',
        type: '患者登记',
        content: `患者完成基本信息登记，分配到${patientData.value.departmentName || '相关科室'}`,
        time: patientData.value.createTime || patientData.value.visitDate,
        operator: '系统',
        color: 'blue',
        icon: 'UserOutlined'
      });

      if (patientData.value.assignedDoctorName) {
        timeline.push({
          id: 'doctor-assign',
          type: '医生分配',
          content: `分配主治医生：${patientData.value.assignedDoctorName}`,
          time: patientData.value.assignedTime || patientData.value.visitDate,
          operator: '系统',
          color: 'green',
          icon: 'UserOutlined'
        });
      }

      timeline.push({
        id: 'treatment-start',
        type: '开始诊疗',
        content: `患者开始接受诊疗，当前状态：${getVisitStatusName(patientData.value.visitStatus)}`,
        time: patientData.value.visitDate,
        operator: patientData.value.assignedDoctorName || '医生',
        color: 'orange',
        icon: 'MedicineBoxOutlined'
      });
    }

    // 添加跟进记录到时间线
    followUpList.value.forEach(follow => {
      timeline.push({
        id: `follow-${follow.followId}`,
        type: '跟进记录',
        content: follow.followContent || '进行了跟进',
        time: follow.actualFollowTime || follow.plannedFollowTime,
        operator: follow.assignedUserName || '医疗助理',
        color: 'cyan',
        icon: 'ClockCircleOutlined'
      });
    });

    // 按时间排序
    timeline.sort((a, b) => new Date(b.time) - new Date(a.time));

    timelineData.value = timeline;
    console.log('✅ [DEBUG] 时间线数据加载成功:', timeline.length, '条');
  } catch (e) {
    console.error('❌ [DEBUG] 加载时间线数据失败:', e);
    timelineData.value = [];
  }
}

// 加载医疗任务数据
async function loadMedicalTasks(patientId) {
  try {
    // 模拟医疗任务数据
    const tasks = [
      {
        id: 1,
        taskType: 1, // 检查单
        taskName: '血常规检查',
        doctorName: patientData.value?.assignedDoctorName || '主治医生',
        taskStatus: 2, // 已审核
        createTime: dayjs().subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss'),
        remark: '常规血液检查'
      },
      {
        id: 2,
        taskType: 2, // 化验单
        taskName: '肝功能化验',
        doctorName: patientData.value?.assignedDoctorName || '主治医生',
        taskStatus: 3, // 已执行
        createTime: dayjs().subtract(2, 'day').format('YYYY-MM-DD HH:mm:ss'),
        remark: '肝功能指标检测'
      },
      {
        id: 3,
        taskType: 3, // 处方单
        taskName: '消炎药处方',
        doctorName: patientData.value?.assignedDoctorName || '主治医生',
        taskStatus: 1, // 待审核
        createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        remark: '抗炎治疗药物'
      }
    ];

    medicalTaskList.value = tasks;
    console.log('✅ [DEBUG] 医疗任务数据加载成功:', tasks.length, '条');
  } catch (e) {
    console.error('❌ [DEBUG] 加载医疗任务数据失败:', e);
    medicalTaskList.value = [];
  }
}

// 加载联系人数据
async function loadContactList(patientId) {
  try {
    // 模拟联系人数据
    const contacts = [
      {
        id: 1,
        name: '张三',
        relationship: '配偶',
        phone: '13800138001',
        wechat: 'zhangsan123',
        address: '北京市朝阳区xxx街道',
      },
      {
        id: 2,
        name: '李四',
        relationship: '子女',
        phone: '13800138002',
        wechat: 'lisi456',
        address: '北京市海淀区xxx小区',
      },
      {
        id: 3,
        name: '王五',
        relationship: '朋友',
        phone: '13800138003',
        address: '北京市西城区xxx路',
      }
    ];

    contactList.value = contacts;
    console.log('✅ [DEBUG] 联系人数据加载成功:', contacts.length, '条');
  } catch (e) {
    console.error('❌ [DEBUG] 加载联系人数据失败:', e);
    contactList.value = [];
  }
}

// 加载附件数据
async function loadAttachmentList(patientId) {
  try {
    // 模拟附件数据
    const attachments = [
      {
        id: 1,
        fileName: '血常规检查报告.pdf',
        fileType: 'pdf',
        fileSize: 1024 * 1024 * 2.5, // 2.5MB
        uploadTime: dayjs().subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss'),
        uploadUserName: '医生A',
      },
      {
        id: 2,
        fileName: '患者照片.jpg',
        fileType: 'jpg',
        fileSize: 1024 * 512, // 512KB
        uploadTime: dayjs().subtract(2, 'day').format('YYYY-MM-DD HH:mm:ss'),
        uploadUserName: '护士B',
      },
      {
        id: 3,
        fileName: '诊断记录.docx',
        fileType: 'docx',
        fileSize: 1024 * 256, // 256KB
        uploadTime: dayjs().subtract(3, 'day').format('YYYY-MM-DD HH:mm:ss'),
        uploadUserName: '医生C',
      }
    ];

    attachmentList.value = attachments;
    console.log('✅ [DEBUG] 附件数据加载成功:', attachments.length, '条');
  } catch (e) {
    console.error('❌ [DEBUG] 加载附件数据失败:', e);
    attachmentList.value = [];
  }
}

// ----------------------- 关闭弹窗 -----------------------
function onClose() {
  visible.value = false;
  patientData.value = null;
  activeTabKey.value = 'timeline';
}

// ----------------------- 快速操作方法 -----------------------
function createMedicalTask() {
  // TODO: 实现创建医疗任务弹窗
  console.log('创建医疗任务');
}

// ----------------------- 医疗任务相关方法 -----------------------
function getMedicalTaskTypeColor(taskType) {
  const colorMap = {
    1: 'blue',    // 检查单
    2: 'orange',  // 化验单
    3: 'green',   // 处方单
    4: 'red',     // 手术单
  };
  return colorMap[taskType] || 'default';
}

function getMedicalTaskTypeName(taskType) {
  const nameMap = {
    1: '检查单',
    2: '化验单',
    3: '处方单',
    4: '手术单',
  };
  return nameMap[taskType] || '未知';
}

function getMedicalTaskStatusColor(taskStatus) {
  const colorMap = {
    1: 'orange',   // 待审核
    2: 'blue',     // 已审核
    3: 'green',    // 已执行
    4: 'red',      // 已取消
  };
  return colorMap[taskStatus] || 'default';
}

function getMedicalTaskStatusName(taskStatus) {
  const nameMap = {
    1: '待审核',
    2: '已审核',
    3: '已执行',
    4: '已取消',
  };
  return nameMap[taskStatus] || '未知';
}

function viewMedicalTask(record) {
  console.log('查看医疗任务:', record);
  // TODO: 实现查看医疗任务详情
}

function editMedicalTask(record) {
  console.log('编辑医疗任务:', record);
  // TODO: 实现编辑医疗任务
}

function executeMedicalTask(record) {
  console.log('执行医疗任务:', record);
  // TODO: 实现执行医疗任务
}

// ----------------------- 时间线相关方法 -----------------------
function refreshTimeline() {
  if (patientData.value) {
    loadTimelineData(patientData.value.patientId);
  }
}

function createFollowUpPlan() {
  if (!patientData.value) {
    message.warning('请先选择患者');
    return;
  }

  // 调用API创建回访计划
  visitFollowApi.createPlan(patientData.value.patientId).then(res => {
    if (res.ok) {
      message.success('回访计划创建成功');
      // 重新加载回访记录
      loadFollowUpList(patientData.value.patientId);
    } else {
      message.error(res.mesg || '创建回访计划失败');
    }
  }).catch(e => {
    console.error('创建回访计划失败:', e);
    message.error('创建回访计划失败');
  });
}

function addContact() {
  // TODO: 实现添加联系人
  console.log('添加联系人');
}

function viewDiagnosis() {
  // TODO: 实现查看诊断记录
  console.log('查看诊断记录');
}

function editPatient() {
  // TODO: 实现编辑患者信息
  console.log('编辑患者信息');
}

// ----------------------- 标签页操作方法 -----------------------
function createFollowUp() {
  // TODO: 实现新建回访弹窗
  console.log('新建回访');
}

// ----------------------- 回访管理相关方法 -----------------------
function getFollowTypeColor(followType) {
  const typeMap = {
    1: 'blue',    // 治疗回访
    2: 'orange',  // 复诊提醒
    3: 'green',   // 满意度调查
    4: 'red',     // 术后跟进
    5: 'purple',  // 用药跟进
  };
  return typeMap[followType] || 'default';
}

function getFollowTypeName(followType) {
  const typeMap = {
    1: '治疗回访',
    2: '复诊提醒',
    3: '满意度调查',
    4: '术后跟进',
    5: '用药跟进',
  };
  return typeMap[followType] || '未知';
}

function getFollowMethodColor(followMethod) {
  const methodMap = {
    1: 'blue',    // 电话
    2: 'green',   // 微信
    3: 'orange',  // 短信
    4: 'purple',  // 邮件
    5: 'red',     // 上门
  };
  return methodMap[followMethod] || 'default';
}

function getFollowMethodName(followMethod) {
  const methodMap = {
    1: '电话',
    2: '微信',
    3: '短信',
    4: '邮件',
    5: '上门',
  };
  return methodMap[followMethod] || '未知';
}

function getPriorityLevelColor(priorityLevel) {
  const priorityMap = {
    1: 'red',     // 高
    2: 'orange',  // 中
    3: 'green',   // 低
  };
  return priorityMap[priorityLevel] || 'default';
}

function getPriorityLevelName(priorityLevel) {
  const priorityMap = {
    1: '高',
    2: '中',
    3: '低',
  };
  return priorityMap[priorityLevel] || '未知';
}

function getFollowStatusColor(followStatus) {
  const statusMap = {
    1: 'orange',  // 待跟进
    2: 'blue',    // 跟进中
    3: 'green',   // 已完成
    4: 'red',     // 已取消
    5: 'volcano', // 跟进失败
  };
  return statusMap[followStatus] || 'default';
}

function getFollowStatusName(followStatus) {
  const statusMap = {
    1: '待跟进',
    2: '跟进中',
    3: '已完成',
    4: '已取消',
    5: '跟进失败',
  };
  return statusMap[followStatus] || '未知';
}

function viewFollowUp(record) {
  console.log('查看回访记录:', record);
  // TODO: 实现查看回访记录详情
}

function editFollowUp(record) {
  console.log('编辑回访记录:', record);
  // TODO: 实现编辑回访记录
}

function executeFollowUp(record) {
  console.log('执行回访:', record);
  // TODO: 实现执行回访
}

function completeFollowUp(record) {
  console.log('完成回访:', record);
  // TODO: 实现完成回访
}

// ----------------------- 联系人管理相关方法 -----------------------
function getContactAvatarColor(relationship) {
  const colorMap = {
    '配偶': '#1890ff',
    '子女': '#52c41a',
    '父母': '#fa8c16',
    '兄弟姐妹': '#722ed1',
    '朋友': '#13c2c2',
    '同事': '#eb2f96',
    '其他': '#666666',
  };
  return colorMap[relationship] || '#1890ff';
}

function getRelationshipColor(relationship) {
  const colorMap = {
    '配偶': 'blue',
    '子女': 'green',
    '父母': 'orange',
    '兄弟姐妹': 'purple',
    '朋友': 'cyan',
    '同事': 'magenta',
    '其他': 'default',
  };
  return colorMap[relationship] || 'default';
}

function editContact(item) {
  console.log('编辑联系人:', item);
  // TODO: 实现编辑联系人
}

function deleteContact(item) {
  console.log('删除联系人:', item);
  // TODO: 实现删除联系人
}

// ----------------------- 附件管理相关方法 -----------------------
function getFileIcon(fileType) {
  const type = fileType?.toLowerCase();
  if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(type)) {
    return 'FileImageOutlined';
  } else if (['pdf'].includes(type)) {
    return 'FilePdfOutlined';
  } else if (['doc', 'docx'].includes(type)) {
    return 'FileWordOutlined';
  } else if (['xls', 'xlsx'].includes(type)) {
    return 'FileExcelOutlined';
  } else if (['ppt', 'pptx'].includes(type)) {
    return 'FilePptOutlined';
  } else if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'].includes(type)) {
    return 'VideoCameraOutlined';
  } else {
    return 'FileOutlined';
  }
}

function getFileTypeColor(fileType) {
  const type = fileType?.toLowerCase();
  if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(type)) {
    return '#52c41a';
  } else if (['pdf'].includes(type)) {
    return '#ff4d4f';
  } else if (['doc', 'docx'].includes(type)) {
    return '#1890ff';
  } else if (['xls', 'xlsx'].includes(type)) {
    return '#52c41a';
  } else if (['ppt', 'pptx'].includes(type)) {
    return '#fa8c16';
  } else if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'].includes(type)) {
    return '#722ed1';
  } else {
    return '#666666';
  }
}

function formatFileSize(bytes) {
  if (!bytes) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function beforeUpload(file) {
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('文件大小不能超过10MB!');
  }
  return isLt10M;
}

function handleUpload(options) {
  console.log('上传文件:', options);
  // TODO: 实现文件上传
  message.success('文件上传成功');
}

function previewFile(item) {
  console.log('预览文件:', item);
  // TODO: 实现文件预览
}

function downloadFile(item) {
  console.log('下载文件:', item);
  // TODO: 实现文件下载
}

function deleteFile(item) {
  console.log('删除文件:', item);
  // TODO: 实现删除文件
}

// ----------------------- 统计摘要相关方法 -----------------------
function formatCurrency(amount) {
  if (!amount) return '0.00';
  return Number(amount).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
}

function refreshPatientStats() {
  if (patientData.value) {
    // 重新加载患者数据和相关统计
    loadPatientDetail(patientData.value.patientId);
    message.success('统计数据已刷新');
  }
}

function addContactPerson() {
  // TODO: 实现添加联系人
  console.log('添加联系人');
}

// ----------------------- 状态颜色和名称方法 -----------------------
function getVisitStatusColor(status) {
  for (const statusEnum of Object.values(VISIT_STATUS_ENUM)) {
    if (statusEnum.value === status) {
      return statusEnum.color;
    }
  }
  return 'default';
}

function getVisitStatusName(status) {
  for (const statusEnum of Object.values(VISIT_STATUS_ENUM)) {
    if (statusEnum.value === status) {
      return statusEnum.desc;
    }
  }
  return '未知';
}

function getPatientLevelColor(level) {
  for (const levelEnum of Object.values(PATIENT_LEVEL_ENUM)) {
    if (levelEnum.value === level) {
      return levelEnum.color;
    }
  }
  return PATIENT_LEVEL_ENUM.NORMAL.color;
}

function getPatientLevelName(level) {
  for (const levelEnum of Object.values(PATIENT_LEVEL_ENUM)) {
    if (levelEnum.value === level) {
      return levelEnum.desc;
    }
  }
  return PATIENT_LEVEL_ENUM.NORMAL.desc;
}

function getDiagnosisStatusColor(status) {
  for (const statusEnum of Object.values(DIAGNOSIS_STATUS_ENUM)) {
    if (statusEnum.value === status) {
      return statusEnum.color;
    }
  }
  return 'default';
}

function getDiagnosisStatusName(status) {
  for (const statusEnum of Object.values(DIAGNOSIS_STATUS_ENUM)) {
    if (statusEnum.value === status) {
      return statusEnum.desc;
    }
  }
  return '未知';
}

// ----------------------- 日期格式化方法 -----------------------
function formatDate(date) {
  if (!date) return '-';
  return dayjs(date).format('YYYY-MM-DD');
}

function formatDateTime(date) {
  if (!date) return '-';
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
}

// ----------------------- 暴露方法 -----------------------
defineExpose({
  showModal,
});
</script>

<style scoped>
.patient-detail-enhanced {
  .patient-detail-container {
    display: flex;
    gap: 24px;
    height: 100%;
  }

  .patient-info-section {
    width: 350px;
    flex-shrink: 0;
  }

  .patient-tabs-section {
    flex: 1;
    min-width: 0;
  }

  /* 患者基本信息卡片样式 */
  .patient-basic-card {
    margin-bottom: 16px;

    .patient-header {
      display: flex;
      align-items: center;
      margin-bottom: 16px;

      .patient-avatar {
        margin-right: 16px;
      }

      .patient-info {
        flex: 1;

        .patient-name {
          font-size: 20px;
          font-weight: 600;
          color: #262626;
          margin-bottom: 8px;
        }

        .patient-tags {
          margin-bottom: 8px;

          .ant-tag {
            margin-right: 8px;
          }
        }

        .patient-ai-score {
          display: flex;
          align-items: center;

          .ai-label {
            margin-right: 8px;
            color: #666;
          }

          .score-text {
            margin-left: 8px;
            color: #666;
          }
        }
      }
    }

    .patient-key-info {
      .info-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;

        .info-label {
          color: #666;
          font-weight: 500;
        }

        .info-value {
          color: #333;
        }
      }
    }
  }

  /* 快速操作卡片样式 */
  .quick-actions-card {
    margin-bottom: 16px;

    .action-buttons {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .ant-btn {
        justify-content: flex-start;
      }
    }
  }

  /* 患者摘要卡片样式 */
  .patient-summary-card {
    .summary-stats {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .stat-item {
        display: flex;
        align-items: center;
        padding: 12px;
        border-radius: 8px;
        background: #fafafa;
        transition: all 0.3s ease;

        &:hover {
          background: #f0f0f0;
          transform: translateY(-1px);
        }

        .stat-icon {
          margin-right: 12px;
          font-size: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 32px;
          height: 32px;
          border-radius: 50%;
          background: rgba(24, 144, 255, 0.1);
        }

        .stat-content {
          flex: 1;

          .stat-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
          }

          .stat-value {
            font-size: 14px;
            font-weight: 600;
            color: #333;
          }
        }
      }
    }
  }

  /* 标签页样式 */
  .patient-tabs {
    height: 100%;

    .ant-tabs-content-holder {
      height: calc(100vh - 200px);
      overflow-y: auto;
    }

    .tab-content {
      padding: 16px;

      .tab-header {
        margin-bottom: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
  }

  /* 时间线样式 */
  .ant-timeline {
    .ant-timeline-item-content {
      p {
        margin-bottom: 4px;

        &:first-child {
          font-weight: 500;
          color: #333;
        }

        &:last-child {
          color: #666;
          font-size: 12px;
        }
      }
    }
  }

  /* 表格样式 */
  .ant-table-small {
    .ant-table-tbody > tr > td {
      padding: 8px;
    }
  }

  /* 列表样式 */
  .ant-list-item {
    padding: 12px 0;
  }

  /* 时间线样式 */
  .timeline-header {
    margin-bottom: 16px;
    text-align: right;
  }

  .timeline-item-content {
    .timeline-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 4px;

      .timeline-type {
        font-weight: 600;
        color: #1890ff;
      }

      .timeline-time {
        font-size: 12px;
        color: #999;
      }
    }

    .timeline-content {
      color: #666;
      margin-bottom: 4px;
      line-height: 1.5;
    }

    .timeline-operator {
      font-size: 12px;
      color: #999;
    }
  }

  /* 标签页内容样式 */
  .tab-content {
    padding: 16px 0;

    .tab-header {
      margin-bottom: 16px;
    }
  }
}
</style>

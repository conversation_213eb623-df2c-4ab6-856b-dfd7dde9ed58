<template>
  <a-card class="patient-follow-up-container">
    <!-- 筛选选项卡 -->
    <div class="filter-tabs">
      <a-tabs v-model:activeKey="activeFilterTab" @change="onFilterTabChange" type="card">
        <a-tab-pane key="my" tab="我的回访" />
        <a-tab-pane key="subordinate" tab="下属回访" />
        <a-tab-pane key="all" tab="全部回访" />
        <a-tab-pane key="pending" tab="待回访" />
        <a-tab-pane key="overdue" tab="逾期回访" />
        <a-tab-pane key="completed" tab="已完成" />
      </a-tabs>
    </div>

    <!-- 查询表单 -->
    <div class="header">
      <div class="query-operate">
        <a-form layout="inline" :model="queryForm">
          <a-form-item label="患者姓名">
            <a-input v-model:value="queryForm.patientName" placeholder="请输入患者姓名" style="width: 150px" />
          </a-form-item>
          <a-form-item label="患者电话">
            <a-input v-model:value="queryForm.patientPhone" placeholder="请输入患者电话" style="width: 150px" />
          </a-form-item>
          <a-form-item label="分配助理">
            <employee-select v-model:value="queryForm.assignedAssistantId" placeholder="请选择助理" allow-clear style="width: 150px" />
          </a-form-item>
          <a-form-item label="分配医生">
            <employee-select v-model:value="queryForm.assignedDoctorId" placeholder="请选择医生" allow-clear style="width: 150px" />
          </a-form-item>
          <a-form-item label="回访状态">
            <a-select v-model:value="queryForm.followUpStatus" placeholder="请选择回访状态" style="width: 150px" allow-clear>
              <a-select-option :value="1">待回访</a-select-option>
              <a-select-option :value="2">回访中</a-select-option>
              <a-select-option :value="3">已回访</a-select-option>
              <a-select-option :value="4">暂停回访</a-select-option>
              <a-select-option :value="5">结束回访</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="回访优先级">
            <a-select v-model:value="queryForm.followUpPriority" placeholder="请选择优先级" style="width: 120px" allow-clear>
              <a-select-option :value="1">高</a-select-option>
              <a-select-option :value="2">中</a-select-option>
              <a-select-option :value="3">低</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="queryData">
              <template #icon>
                <SearchOutlined />
              </template>
              查询
            </a-button>
          </a-form-item>
          <a-form-item>
            <a-button @click="resetQuery">
              <template #icon>
                <ReloadOutlined />
              </template>
              重置
            </a-button>
          </a-form-item>
        </a-form>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card size="small">
            <a-statistic title="待回访" :value="stats.pending" :value-style="{ color: '#1890ff' }" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic title="回访中" :value="stats.inProgress" :value-style="{ color: '#52c41a' }" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic title="已完成" :value="stats.completed" :value-style="{ color: '#722ed1' }" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card size="small">
            <a-statistic title="逾期回访" :value="stats.overdue" :value-style="{ color: '#f5222d' }" />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 表格 -->
    <div class="table-container">
      <a-table
        :columns="columns"
        :data-source="tableData"
        :loading="tableLoading"
        :pagination="false"
        size="small"
        :scroll="{ x: 1500 }"
        row-key="followUpListId"
      >
        <!-- 患者信息 -->
        <template #patientInfo="{ record }">
          <div>
            <div style="font-weight: 500;">{{ record.patientName }}</div>
            <div style="color: #666; font-size: 12px;">{{ record.patientPhone }}</div>
            <div style="color: #666; font-size: 12px;" v-if="record.age">{{ record.genderDesc }} / {{ record.age }}岁</div>
          </div>
        </template>

        <!-- 分配信息 -->
        <template #assignmentInfo="{ record }">
          <div>
            <div style="font-weight: 500;">助理：{{ record.assignedAssistantName }}</div>
            <div style="color: #666; font-size: 12px;" v-if="record.assignedDoctorName">医生：{{ record.assignedDoctorName }}</div>
            <div style="color: #666; font-size: 12px;" v-if="record.departmentName">科室：{{ record.departmentName }}</div>
          </div>
        </template>

        <!-- 诊断状态 -->
        <template #diagnosisStatus="{ record }">
          <a-tag :color="getDiagnosisStatusColor(record.diagnosisStatus)">
            {{ record.diagnosisStatusDesc }}
          </a-tag>
        </template>

        <!-- 回访状态 -->
        <template #followUpStatus="{ record }">
          <a-tag :color="getFollowUpStatusColor(record.followUpStatus)">
            {{ record.followUpStatusDesc }}
          </a-tag>
        </template>

        <!-- 回访优先级 -->
        <template #followUpPriority="{ record }">
          <a-tag :color="getPriorityColor(record.followUpPriority)">
            {{ record.followUpPriorityDesc }}
          </a-tag>
        </template>

        <!-- 下次回访时间 -->
        <template #nextFollowUpTime="{ record }">
          <div v-if="record.nextFollowUpTime">
            <div :style="{ color: record.isOverdue ? '#f5222d' : '#333' }">
              {{ formatDateTime(record.nextFollowUpTime) }}
            </div>
            <div style="font-size: 12px; color: #666;" v-if="record.daysToNextFollowUp !== null">
              {{ record.isOverdue ? '逾期' : '还有' }}{{ Math.abs(record.daysToNextFollowUp) }}天
            </div>
          </div>
          <span v-else style="color: #ccc;">未设置</span>
        </template>

        <!-- 操作列 -->
        <template #action="{ record }">
          <a-space>
            <a-button type="link" size="small" @click="handleFollowUp(record)">
              回访
            </a-button>
            <a-button type="link" size="small" @click="handleViewDetail(record)">
              详情
            </a-button>
            <a-dropdown>
              <template #overlay>
                <a-menu>
                  <a-menu-item key="1" @click="handleUpdateStatus(record, 1)">
                    <a>设为待回访</a>
                  </a-menu-item>
                  <a-menu-item key="2" @click="handleUpdateStatus(record, 2)">
                    <a>设为回访中</a>
                  </a-menu-item>
                  <a-menu-item key="3" @click="handleUpdateStatus(record, 3)">
                    <a>设为已回访</a>
                  </a-menu-item>
                  <a-menu-item key="4" @click="handleUpdateStatus(record, 4)">
                    <a>暂停回访</a>
                  </a-menu-item>
                  <a-menu-item key="5" @click="handleUpdateStatus(record, 5)">
                    <a>结束回访</a>
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button type="link" size="small">
                更多 <DownOutlined />
              </a-button>
            </a-dropdown>
          </a-space>
        </template>
      </a-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <a-pagination
        v-model:current="queryForm.pageNum"
        v-model:page-size="queryForm.pageSize"
        :total="total"
        show-size-changer
        show-quick-jumper
        :show-total="(total) => `共 ${total} 条`"
        @change="queryData"
        @show-size-change="queryData"
      />
    </div>
  </a-card>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { SearchOutlined, ReloadOutlined, DownOutlined } from '@ant-design/icons-vue'
import { patientFollowUpListApi } from '/@/api/business/hospital/followup/patient-follow-up-list-api'
import EmployeeSelect from '/@/components/system/employee-select/index.vue'
import dayjs from 'dayjs'

// 响应式数据
const activeFilterTab = ref('my')
const tableLoading = ref(false)
const tableData = ref([])
const total = ref(0)

// 查询表单
const queryForm = reactive({
  pageNum: 1,
  pageSize: 10,
  patientName: '',
  patientPhone: '',
  assignedAssistantId: null,
  assignedDoctorId: null,
  followUpStatus: null,
  followUpPriority: null,
  filterScope: 'my'
})

// 统计数据
const stats = reactive({
  pending: 0,
  inProgress: 0,
  completed: 0,
  overdue: 0
})

// 表格列定义
const columns = [
  {
    title: '患者信息',
    dataIndex: 'patientInfo',
    key: 'patientInfo',
    width: 180,
    slots: { customRender: 'patientInfo' }
  },
  {
    title: '分配信息',
    dataIndex: 'assignmentInfo',
    key: 'assignmentInfo',
    width: 200,
    slots: { customRender: 'assignmentInfo' }
  },
  {
    title: '到诊日期',
    dataIndex: 'visitDate',
    key: 'visitDate',
    width: 100
  },
  {
    title: '诊断状态',
    dataIndex: 'diagnosisStatus',
    key: 'diagnosisStatus',
    width: 100,
    slots: { customRender: 'diagnosisStatus' }
  },
  {
    title: '回访状态',
    dataIndex: 'followUpStatus',
    key: 'followUpStatus',
    width: 100,
    slots: { customRender: 'followUpStatus' }
  },
  {
    title: '优先级',
    dataIndex: 'followUpPriority',
    key: 'followUpPriority',
    width: 80,
    slots: { customRender: 'followUpPriority' }
  },
  {
    title: '回访次数',
    dataIndex: 'followUpCount',
    key: 'followUpCount',
    width: 80
  },
  {
    title: '下次回访时间',
    dataIndex: 'nextFollowUpTime',
    key: 'nextFollowUpTime',
    width: 150,
    slots: { customRender: 'nextFollowUpTime' }
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right',
    slots: { customRender: 'action' }
  }
]

// 方法定义
const queryData = async () => {
  try {
    tableLoading.value = true
    const response = await patientFollowUpListApi.queryPage(queryForm)
    if (response.ok) {
      tableData.value = response.data.list
      total.value = response.data.total
    } else {
      message.error(response.msg || '查询失败')
    }
  } catch (error) {
    console.error('查询患者回访列表失败:', error)
    message.error('查询失败')
  } finally {
    tableLoading.value = false
  }
}

const resetQuery = () => {
  Object.assign(queryForm, {
    pageNum: 1,
    pageSize: 10,
    patientName: '',
    patientPhone: '',
    assignedAssistantId: null,
    assignedDoctorId: null,
    followUpStatus: null,
    followUpPriority: null,
    filterScope: activeFilterTab.value
  })
  queryData()
}

const onFilterTabChange = (key) => {
  activeFilterTab.value = key
  queryForm.filterScope = key
  queryForm.pageNum = 1
  
  // 根据选项卡设置默认筛选条件
  switch (key) {
    case 'pending':
      queryForm.followUpStatus = 1
      break
    case 'overdue':
      // TODO: 添加逾期筛选逻辑
      break
    case 'completed':
      queryForm.followUpStatus = 3
      break
    default:
      queryForm.followUpStatus = null
      break
  }
  
  queryData()
}

// 状态颜色映射
const getDiagnosisStatusColor = (status) => {
  const colorMap = {
    1: 'orange',
    2: 'blue',
    3: 'green',
    4: 'purple',
    5: 'cyan',
    6: 'gold',
    7: 'success'
  }
  return colorMap[status] || 'default'
}

const getFollowUpStatusColor = (status) => {
  const colorMap = {
    1: 'orange',
    2: 'blue',
    3: 'green',
    4: 'red',
    5: 'default'
  }
  return colorMap[status] || 'default'
}

const getPriorityColor = (priority) => {
  const colorMap = {
    1: 'red',
    2: 'orange',
    3: 'green'
  }
  return colorMap[priority] || 'default'
}

// 日期格式化函数
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss')
}

// 操作方法
const handleFollowUp = (record) => {
  // TODO: 实现回访功能
  message.info('回访功能开发中...')
}

const handleViewDetail = (record) => {
  // TODO: 实现查看详情功能
  message.info('详情功能开发中...')
}

const handleUpdateStatus = async (record, status) => {
  try {
    const response = await patientFollowUpListApi.updateStatus(record.followUpListId, status)
    if (response.ok) {
      message.success('状态更新成功')
      queryData()
    } else {
      message.error(response.msg || '状态更新失败')
    }
  } catch (error) {
    console.error('更新状态失败:', error)
    message.error('状态更新失败')
  }
}

// 生命周期
onMounted(() => {
  queryData()
})
</script>

<style scoped>
.patient-follow-up-container {
  margin: 16px;
}

.filter-tabs {
  margin-bottom: 16px;
}

.header {
  margin-bottom: 16px;
}

.query-operate {
  margin-bottom: 16px;
}

.stats-cards {
  margin-bottom: 16px;
}

.table-container {
  margin-bottom: 16px;
}

.pagination-container {
  text-align: right;
}
</style>

<template>
  <div class="customer-360-view">
    <a-spin :spinning="loading">
      <!-- 客户选择器 -->
      <a-card size="small" style="margin-bottom: 16px">
        <a-row :gutter="16" align="middle">
          <a-col :span="6">
            <a-input-search
              v-model:value="searchKeyword"
              placeholder="输入客户姓名或电话搜索"
              @search="searchCustomer"
              enter-button="搜索"
            />
          </a-col>
          <a-col :span="18">
            <a-select
              v-model:value="selectedCustomerId"
              placeholder="请选择客户"
              show-search
              :filter-option="false"
              :not-found-content="customerSearching ? undefined : null"
              style="width: 100%"
              @search="handleCustomerSearch"
              @change="handleCustomerChange"
            >
              <template v-if="customerSearching" #notFoundContent>
                <a-spin size="small" />
              </template>
              <a-select-option v-for="customer in customerOptions" :key="customer.customerId" :value="customer.customerId">
                {{ customer.customerName }} - {{ $smartUtil.desensitizePhone(customer.customerPhone) }}
              </a-select-option>
            </a-select>
          </a-col>
        </a-row>
      </a-card>

      <div v-if="customerData">
        <!-- 客户基础信息卡片 -->
        <a-row :gutter="24" style="margin-bottom: 24px">
          <a-col :span="24">
            <a-card title="客户基础信息" size="small">
              <a-row :gutter="16">
                <a-col :span="4">
                  <div class="info-item">
                    <div class="label">客户姓名</div>
                    <div class="value">{{ customerData.customerName }}</div>
                  </div>
                </a-col>
                <a-col :span="4">
                  <div class="info-item">
                    <div class="label">客户电话</div>
                    <div class="value">{{ $smartUtil.desensitizePhone(customerData.customerPhone) }}</div>
                  </div>
                </a-col>
                <a-col :span="4">
                  <div class="info-item">
                    <div class="label">性别年龄</div>
                    <div class="value">{{ customerData.genderName }} / {{ customerData.age }}岁</div>
                  </div>
                </a-col>
                <a-col :span="4">
                  <div class="info-item">
                    <div class="label">客户状态</div>
                    <div class="value">
                      <a-tag :color="getCustomerStatusColor(customerData.customerStatus)">
                        {{ customerData.customerStatusName }}
                      </a-tag>
                    </div>
                  </div>
                </a-col>
                <a-col :span="4">
                  <div class="info-item">
                    <div class="label">治疗状态</div>
                    <div class="value">
                      <a-tag :color="getTreatmentStatusColor(customerData.treatmentStatus)">
                        {{ customerData.treatmentStatusName || '未设置' }}
                      </a-tag>
                    </div>
                  </div>
                </a-col>
                <a-col :span="4">
                  <div class="info-item">
                    <div class="label">负责人员</div>
                    <div class="value">{{ customerData.responsibleEmployeeName || '未分配' }}</div>
                  </div>
                </a-col>
              </a-row>
            </a-card>
          </a-col>
        </a-row>

        <!-- 回访统计卡片 -->
        <a-row :gutter="24" style="margin-bottom: 24px">
          <a-col :span="6">
            <a-card size="small">
              <a-statistic title="总回访次数" :value="followUpStats.totalCount" />
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small">
              <a-statistic title="待回访" :value="followUpStats.pendingCount" value-style="color: #faad14" />
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small">
              <a-statistic title="已完成" :value="followUpStats.completedCount" value-style="color: #52c41a" />
            </a-card>
          </a-col>
          <a-col :span="6">
            <a-card size="small">
              <a-statistic title="平均满意度" :value="followUpStats.avgSatisfaction" :precision="1" suffix="分" />
            </a-card>
          </a-col>
        </a-row>

        <!-- 主要内容区域 -->
        <a-row :gutter="24">
          <!-- 左侧：回访计划和记录 -->
          <a-col :span="16">
            <a-card title="回访管理" size="small">
              <template #extra>
                <a-space>
                  <a-button type="primary" size="small" @click="showAddPlanModal" v-privilege="'hospital:followup:plan:add'">
                    新建计划
                  </a-button>
                  <a-button type="primary" size="small" @click="showAddRecordModal" v-privilege="'hospital:followup:record:add'">
                    新建回访
                  </a-button>
                </a-space>
              </template>

              <a-tabs v-model:activeKey="activeTab">
                <!-- 回访计划 -->
                <a-tab-pane key="plans" tab="回访计划">
                  <a-table
                    :columns="planColumns"
                    :data-source="followUpPlans"
                    :loading="planLoading"
                    :pagination="false"
                    size="small"
                    :scroll="{ y: 400 }"
                  >
                    <template #bodyCell="{ column, record }">
                      <template v-if="column.dataIndex === 'planStatus'">
                        <a-tag :color="getPlanStatusColor(record.planStatus)">
                          {{ getPlanStatusText(record.planStatus) }}
                        </a-tag>
                      </template>
                      <template v-if="column.dataIndex === 'progress'">
                        <a-progress 
                          :percent="getProgressPercent(record)" 
                          size="small" 
                          :status="record.planStatus === 3 ? 'success' : 'active'"
                        />
                      </template>
                      <template v-if="column.dataIndex === 'operate'">
                        <a-space>
                          <a-button type="link" size="small" @click="editPlan(record)">编辑</a-button>
                          <a-button type="link" size="small" @click="togglePlanStatus(record)">
                            {{ record.planStatus === 1 ? '暂停' : '启用' }}
                          </a-button>
                        </a-space>
                      </template>
                    </template>
                  </a-table>
                </a-tab-pane>

                <!-- 回访记录 -->
                <a-tab-pane key="records" tab="回访记录">
                  <a-table
                    :columns="recordColumns"
                    :data-source="followUpRecords"
                    :loading="recordLoading"
                    :pagination="false"
                    size="small"
                    :scroll="{ y: 400 }"
                  >
                    <template #bodyCell="{ column, record }">
                      <template v-if="column.dataIndex === 'followUpStatus'">
                        <a-tag :color="getFollowUpStatusColor(record.followUpStatus)">
                          {{ record.followUpStatusName }}
                        </a-tag>
                      </template>
                      <template v-if="column.dataIndex === 'satisfactionScore'">
                        <a-rate v-if="record.satisfactionScore" :value="record.satisfactionScore" disabled size="small" />
                        <span v-else style="color: #ccc">未评分</span>
                      </template>
                      <template v-if="column.dataIndex === 'operate'">
                        <a-space>
                          <a-button type="link" size="small" @click="viewRecord(record)">详情</a-button>
                          <a-button 
                            type="link" 
                            size="small" 
                            @click="completeFollowUp(record)"
                            v-if="record.followUpStatus === 1"
                          >
                            完成
                          </a-button>
                        </a-space>
                      </template>
                    </template>
                  </a-table>
                </a-tab-pane>
              </a-tabs>
            </a-card>
          </a-col>

          <!-- 右侧：状态时间线和快速操作 -->
          <a-col :span="8">
            <!-- 治疗状态管理 -->
            <a-card title="治疗状态管理" size="small" style="margin-bottom: 16px">
              <template #extra>
                <a-button type="link" size="small" @click="showStatusHistoryModal">历史</a-button>
              </template>
              <a-form layout="vertical">
                <a-form-item label="当前治疗状态">
                  <a-select 
                    v-model:value="currentTreatmentStatus" 
                    placeholder="请选择治疗状态"
                    @change="updateTreatmentStatus"
                  >
                    <a-select-option :value="1">用药中</a-select-option>
                    <a-select-option :value="2">住院中</a-select-option>
                    <a-select-option :value="3">疗程中</a-select-option>
                    <a-select-option :value="4">纠纷</a-select-option>
                    <a-select-option :value="5">已结束治疗</a-select-option>
                    <a-select-option :value="6">失联</a-select-option>
                  </a-select>
                </a-form-item>
              </a-form>
            </a-card>

            <!-- 快速回访 -->
            <a-card title="快速回访" size="small">
              <a-space direction="vertical" style="width: 100%">
                <a-button block @click="quickFollowUp('用药跟进')">用药跟进</a-button>
                <a-button block @click="quickFollowUp('住院跟进')">住院跟进</a-button>
                <a-button block @click="quickFollowUp('疗程跟进')">疗程跟进</a-button>
                <a-button block @click="quickFollowUp('复诊提醒')">复诊提醒</a-button>
                <a-button block @click="quickFollowUp('康复指导')">康复指导</a-button>
              </a-space>
            </a-card>
          </a-col>
        </a-row>
      </div>

      <div v-else class="empty-state">
        <a-empty description="请选择客户查看360视图" />
      </div>
    </a-spin>

    <!-- 弹窗组件 -->
    <FollowUpPlanFormModal ref="planFormModalRef" @refresh="loadFollowUpPlans" />
    <FollowUpRecordFormModal ref="recordFormModalRef" @refresh="loadFollowUpRecords" />
    <FollowUpRecordDetailModal ref="recordDetailModalRef" />
    <!-- <TreatmentStatusHistoryModal ref="statusHistoryModalRef" /> -->
  </div>
</template>

<script setup>
import { reactive, ref, onMounted, watch } from 'vue';
import { message } from 'ant-design-vue';
import { smartSentry } from '/@/lib/smart-sentry';
import { customerApi } from '/@/api/business/hospital/customer-api';
import { followUpPlanApi } from '/@/api/business/hospital/follow-up-plan-api';
import { followUpRecordApi } from '/@/api/business/hospital/follow-up-record-api';
import FollowUpPlanFormModal from '../plan/components/FollowUpPlanFormModal.vue';
import FollowUpRecordFormModal from '../record/components/FollowUpRecordFormModal.vue';
import FollowUpRecordDetailModal from '../record/components/FollowUpRecordDetailModal.vue';
// import TreatmentStatusHistoryModal from '../status/components/TreatmentStatusHistoryModal.vue';

// 响应式数据
const loading = ref(false);
const searchKeyword = ref('');
const selectedCustomerId = ref(null);
const customerData = ref(null);
const customerOptions = ref([]);
const customerSearching = ref(false);
const activeTab = ref('records');
const currentTreatmentStatus = ref(null);

// 回访统计
const followUpStats = ref({
  totalCount: 0,
  pendingCount: 0,
  completedCount: 0,
  avgSatisfaction: 0,
});

// 回访计划和记录
const followUpPlans = ref([]);
const followUpRecords = ref([]);
const planLoading = ref(false);
const recordLoading = ref(false);

// 弹窗引用
const planFormModalRef = ref();
const recordFormModalRef = ref();
const recordDetailModalRef = ref();
const statusHistoryModalRef = ref();

// 表格列定义
const planColumns = [
  { title: '计划名称', dataIndex: 'planName', width: 120 },
  { title: '状态', dataIndex: 'planStatus', width: 80 },
  { title: '进度', dataIndex: 'progress', width: 100 },
  { title: '操作', dataIndex: 'operate', width: 100 },
];

const recordColumns = [
  { title: '回访类型', dataIndex: 'followUpTypeName', width: 100 },
  { title: '状态', dataIndex: 'followUpStatus', width: 80 },
  { title: '回访时间', dataIndex: 'scheduledTime', width: 120 },
  { title: '满意度', dataIndex: 'satisfactionScore', width: 100 },
  { title: '操作', dataIndex: 'operate', width: 100 },
];

// 搜索客户
const searchCustomer = async () => {
  if (!searchKeyword.value.trim()) {
    message.warning('请输入搜索关键词');
    return;
  }
  await handleCustomerSearch(searchKeyword.value);
};

// 处理客户搜索
const handleCustomerSearch = async (value) => {
  if (!value) {
    customerOptions.value = [];
    return;
  }
  
  customerSearching.value = true;
  try {
    const res = await customerApi.search({ keyword: value });
    customerOptions.value = res.data || [];
  } catch (error) {
    smartSentry.captureError(error);
  } finally {
    customerSearching.value = false;
  }
};

// 处理客户选择
const handleCustomerChange = (customerId) => {
  if (customerId) {
    loadCustomerData(customerId);
  } else {
    customerData.value = null;
  }
};

// 加载客户数据
const loadCustomerData = async (customerId) => {
  loading.value = true;
  try {
    const res = await customerApi.getDetail(customerId);
    customerData.value = res.data;
    currentTreatmentStatus.value = res.data.treatmentStatus;
    
    // 加载相关数据
    await Promise.all([
      loadFollowUpStats(customerId),
      loadFollowUpPlans(customerId),
      loadFollowUpRecords(customerId),
    ]);
  } catch (error) {
    smartSentry.captureError(error);
  } finally {
    loading.value = false;
  }
};

// 加载回访统计
const loadFollowUpStats = async (customerId) => {
  try {
    const res = await followUpRecordApi.getStats(customerId);
    followUpStats.value = res.data;
  } catch (error) {
    smartSentry.captureError(error);
  }
};

// 加载回访计划
const loadFollowUpPlans = async (customerId = selectedCustomerId.value) => {
  if (!customerId) return;
  
  planLoading.value = true;
  try {
    const res = await followUpPlanApi.getByCustomerId(customerId);
    followUpPlans.value = res.data || [];
  } catch (error) {
    smartSentry.captureError(error);
  } finally {
    planLoading.value = false;
  }
};

// 加载回访记录
const loadFollowUpRecords = async (customerId = selectedCustomerId.value) => {
  if (!customerId) return;
  
  recordLoading.value = true;
  try {
    const res = await followUpRecordApi.getByCustomerId(customerId);
    followUpRecords.value = res.data || [];
  } catch (error) {
    smartSentry.captureError(error);
  } finally {
    recordLoading.value = false;
  }
};

// 更新治疗状态
const updateTreatmentStatus = async (status) => {
  try {
    await customerApi.updateTreatmentStatus(selectedCustomerId.value, status);
    message.success('治疗状态更新成功');
    loadCustomerData(selectedCustomerId.value);
  } catch (error) {
    smartSentry.captureError(error);
  }
};

// 快速回访
const quickFollowUp = (type) => {
  recordFormModalRef.value.showModal({
    customerId: selectedCustomerId.value,
    customerName: customerData.value.customerName,
    customerPhone: customerData.value.customerPhone,
    followUpType: getFollowUpTypeByName(type),
  });
};

// 辅助函数
const getCustomerStatusColor = (status) => {
  const colors = { 1: 'blue', 2: 'orange', 3: 'green', 4: 'red' };
  return colors[status] || 'default';
};

const getTreatmentStatusColor = (status) => {
  const colors = { 1: 'blue', 2: 'orange', 3: 'purple', 4: 'red', 5: 'green', 6: 'gray' };
  return colors[status] || 'default';
};

const getPlanStatusColor = (status) => {
  const colors = { 1: 'green', 2: 'orange', 3: 'gray' };
  return colors[status] || 'default';
};

const getPlanStatusText = (status) => {
  const texts = { 1: '启用', 2: '暂停', 3: '已结束' };
  return texts[status] || '';
};

const getFollowUpStatusColor = (status) => {
  const colors = { 1: 'orange', 2: 'green', 3: 'red', 4: 'gray' };
  return colors[status] || 'default';
};

const getProgressPercent = (record) => {
  if (!record.totalCount || record.totalCount === 0) return 0;
  return Math.round((record.completedCount || 0) / record.totalCount * 100);
};

const getFollowUpTypeByName = (name) => {
  const typeMap = { '用药跟进': 1, '住院跟进': 2, '疗程跟进': 3, '复诊提醒': 5, '康复指导': 6 };
  return typeMap[name] || 1;
};

// 弹窗操作
const showAddPlanModal = () => {
  planFormModalRef.value.showModal({ customerId: selectedCustomerId.value });
};

const showAddRecordModal = () => {
  recordFormModalRef.value.showModal({ customerId: selectedCustomerId.value });
};

const showStatusHistoryModal = () => {
  statusHistoryModalRef.value.showModal(selectedCustomerId.value);
};

const editPlan = (record) => {
  planFormModalRef.value.showModal(record);
};

const viewRecord = (record) => {
  recordDetailModalRef.value.showModal(record);
};

const togglePlanStatus = async (record) => {
  const newStatus = record.planStatus === 1 ? 2 : 1;
  try {
    await followUpPlanApi.updateStatus(record.planId, newStatus);
    message.success('状态更新成功');
    loadFollowUpPlans();
  } catch (error) {
    smartSentry.captureError(error);
  }
};

const completeFollowUp = async (record) => {
  try {
    await followUpRecordApi.complete(record.followUpId);
    message.success('回访完成');
    loadFollowUpRecords();
    loadFollowUpStats(selectedCustomerId.value);
  } catch (error) {
    smartSentry.captureError(error);
  }
};

// 组件挂载
onMounted(() => {
  // 初始化时可以加载一些默认数据
});
</script>

<style scoped>
.customer-360-view {
  margin: -24px;
}

.info-item {
  margin-bottom: 16px;
}

.info-item .label {
  color: #666;
  font-size: 12px;
  margin-bottom: 4px;
}

.info-item .value {
  font-weight: 500;
  font-size: 14px;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
}
</style>

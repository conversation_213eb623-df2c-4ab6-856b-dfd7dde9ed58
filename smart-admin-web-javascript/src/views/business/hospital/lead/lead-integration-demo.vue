<template>
  <div class="integration-demo">
    <a-card title="线索详情组件集成演示" class="demo-card">
      <div class="demo-header">
        <a-alert
          message="集成完成"
          description="新的增强版线索详情组件已成功集成到线索列表页面中。点击下方按钮体验不同版本的详情组件。"
          type="success"
          show-icon
          style="margin-bottom: 24px;"
        />
        
        <div class="version-comparison">
          <a-row :gutter="24">
            <a-col :span="12">
              <a-card size="small" title="原版组件" class="version-card">
                <div class="version-features">
                  <p>✓ 基础信息展示</p>
                  <p>✓ 跟进记录列表</p>
                  <p>✓ 简单操作功能</p>
                  <p>✓ 900px宽度</p>
                </div>
                <a-button type="default" block @click="showOriginalDetail">
                  查看原版详情
                </a-button>
              </a-card>
            </a-col>
            <a-col :span="12">
              <a-card size="small" title="增强版组件" class="version-card enhanced">
                <div class="version-features">
                  <p>✨ 现代化UI设计</p>
                  <p>✨ 时间轴式跟进记录</p>
                  <p>✨ 多标签页内容组织</p>
                  <p>✨ AI智能分析</p>
                  <p>✨ 响应式布局</p>
                  <p>✨ 1000px宽度</p>
                  <p>✨ 快捷操作按钮</p>
                  <p>✨ 附件管理</p>
                </div>
                <a-button type="primary" block @click="showEnhancedDetail">
                  查看增强版详情
                </a-button>
              </a-card>
            </a-col>
          </a-row>
        </div>
      </div>

      <div class="demo-content">
        <a-divider>集成说明</a-divider>
        
        <a-steps :current="3" size="small">
          <a-step title="组件开发" description="完成增强版组件开发" />
          <a-step title="功能测试" description="验证各项功能正常" />
          <a-step title="集成到列表" description="添加到线索列表页面" />
          <a-step title="用户体验优化" description="收集反馈持续改进" />
        </a-steps>

        <div class="integration-details">
          <h3>集成详情</h3>
          <a-descriptions :column="2" size="small" bordered>
            <a-descriptions-item label="集成位置">线索列表页面 (lead-list.vue)</a-descriptions-item>
            <a-descriptions-item label="触发方式">点击客户姓名或"详情(增强版)"按钮</a-descriptions-item>
            <a-descriptions-item label="数据传递">直接传递record对象</a-descriptions-item>
            <a-descriptions-item label="事件处理">支持refresh事件刷新列表</a-descriptions-item>
            <a-descriptions-item label="权限控制">继承原有权限设置</a-descriptions-item>
            <a-descriptions-item label="兼容性">保留原版组件，平滑过渡</a-descriptions-item>
          </a-descriptions>
        </div>

        <div class="feature-highlights">
          <h3>主要改进</h3>
          <a-row :gutter="16">
            <a-col :span="8">
              <a-card size="small" class="feature-card">
                <template #title>
                  <EyeOutlined style="color: #1890ff;" />
                  视觉体验
                </template>
                <ul>
                  <li>更宽的展示区域</li>
                  <li>现代化卡片设计</li>
                  <li>图标化信息展示</li>
                  <li>彩色状态标签</li>
                </ul>
              </a-card>
            </a-col>
            <a-col :span="8">
              <a-card size="small" class="feature-card">
                <template #title>
                  <ThunderboltOutlined style="color: #52c41a;" />
                  功能增强
                </template>
                <ul>
                  <li>AI智能分析</li>
                  <li>快速跟进输入</li>
                  <li>多标签页组织</li>
                  <li>附件管理</li>
                </ul>
              </a-card>
            </a-col>
            <a-col :span="8">
              <a-card size="small" class="feature-card">
                <template #title>
                  <MobileOutlined style="color: #fa8c16;" />
                  响应式设计
                </template>
                <ul>
                  <li>多设备适配</li>
                  <li>触摸友好</li>
                  <li>弹性布局</li>
                  <li>性能优化</li>
                </ul>
              </a-card>
            </a-col>
          </a-row>
        </div>

        <div class="next-steps">
          <h3>下一步计划</h3>
          <a-timeline size="small">
            <a-timeline-item color="green">
              <template #dot>
                <CheckCircleOutlined style="color: #52c41a;" />
              </template>
              组件开发完成
            </a-timeline-item>
            <a-timeline-item color="green">
              <template #dot>
                <CheckCircleOutlined style="color: #52c41a;" />
              </template>
              集成到主项目
            </a-timeline-item>
            <a-timeline-item color="blue">
              <template #dot>
                <ClockCircleOutlined style="color: #1890ff;" />
              </template>
              API接口对接
            </a-timeline-item>
            <a-timeline-item color="blue">
              <template #dot>
                <ClockCircleOutlined style="color: #1890ff;" />
              </template>
              用户反馈收集
            </a-timeline-item>
            <a-timeline-item color="gray">
              <template #dot>
                <ExclamationCircleOutlined style="color: #8c8c8c;" />
              </template>
              功能持续优化
            </a-timeline-item>
          </a-timeline>
        </div>
      </div>
    </a-card>

    <!-- 测试数据 -->
    <LeadDetailDrawer ref="originalDetail" />
    <LeadDetailEnhanced ref="enhancedDetail" @refresh="handleRefresh" />
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { message } from 'ant-design-vue';
import {
  EyeOutlined,
  ThunderboltOutlined,
  MobileOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons-vue';
import LeadDetailDrawer from './components/lead-detail-drawer.vue';
import LeadDetailEnhanced from './components/lead-detail-enhanced.vue';

// 组件引用
const originalDetail = ref();
const enhancedDetail = ref();

// 测试数据
const testLeadData = {
  leadId: 'DEMO_001',
  customerName: '张三(演示)',
  customerPhone: '13800138000',
  customerEmail: '<EMAIL>',
  customerAddress: '北京市朝阳区演示地址',
  customerAge: 35,
  genderName: '男',
  leadSource: 1,
  leadSourceName: '网络推广',
  leadStatus: 2,
  leadStatusName: '跟进中',
  leadQuality: 3,
  leadQualityName: '高质量',
  assignedEmployeeName: '演示销售',
  symptom: '演示症状1,演示症状2',
  remark: '这是一个演示用的线索数据，用于展示新组件的功能特性。',
  createTime: '2024-07-31 10:00:00',
  updateTime: '2024-07-31 15:30:00',
  // 工商信息
  companyName: '演示科技有限公司',
  creditCode: '91110000DEMO123456',
  legalPerson: '张三',
  registeredCapital: '1000万元',
  establishDate: '2020-01-01',
  companyStatus: '存续',
  industry: '软件和信息技术服务业',
  companyType: '有限责任公司'
};

// 方法
const showOriginalDetail = () => {
  originalDetail.value.showDrawer(testLeadData.leadId);
};

const showEnhancedDetail = () => {
  enhancedDetail.value.showDrawer(testLeadData);
};

const handleRefresh = () => {
  message.success('数据已刷新');
};
</script>

<style scoped>
.integration-demo {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.demo-card {
  max-width: 1200px;
  margin: 0 auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.version-comparison {
  margin: 24px 0;
}

.version-card {
  height: 100%;
  transition: all 0.3s;
}

.version-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.version-card.enhanced {
  border-color: #1890ff;
}

.version-features {
  min-height: 180px;
  margin-bottom: 16px;
}

.version-features p {
  margin: 8px 0;
  font-size: 14px;
}

.demo-content {
  margin-top: 32px;
}

.integration-details {
  margin: 24px 0;
}

.feature-highlights {
  margin: 24px 0;
}

.feature-card {
  height: 100%;
}

.feature-card ul {
  margin: 0;
  padding-left: 16px;
}

.feature-card li {
  margin: 4px 0;
  font-size: 13px;
}

.next-steps {
  margin: 24px 0;
}

h3 {
  color: #262626;
  margin-bottom: 16px;
}

@media (max-width: 768px) {
  .integration-demo {
    padding: 16px;
  }
  
  .version-comparison .ant-col {
    margin-bottom: 16px;
  }
  
  .feature-highlights .ant-col {
    margin-bottom: 16px;
  }
}
</style>

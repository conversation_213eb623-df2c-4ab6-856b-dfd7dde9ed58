<template>
  <a-drawer
    :title="null"
    :open="visible"
    :width="1000"
    placement="right"
    @close="onClose"
    class="lead-detail-enhanced-drawer"
  >
    <a-spin :spinning="loading">
      <div v-if="leadData" class="lead-detail-enhanced-container" id="lead-detail-enhanced-watermark-container">
        <!-- 头部信息区 -->
        <div class="header-section">
          <div class="header-main">
            <div class="lead-title-area">
              <div class="lead-avatar">
                <a-avatar :size="40" :style="{ backgroundColor: getAvatarColor(leadData.customerName) }">
                  {{ getAvatarText(leadData.customerName) }}
                </a-avatar>
              </div>
              <div class="lead-info">
                <h2 class="lead-name">{{ leadData.customerName }}</h2>
                <div class="lead-meta">
                  <a-tag :color="LEAD_STATUS_ENUM[leadData.leadStatus]?.color" class="status-tag">
                    {{ leadData.leadStatusName }}
                  </a-tag>
                  <a-tag v-if="leadData.leadQuality" :color="LEAD_QUALITY_ENUM[leadData.leadQuality]?.color" class="quality-tag">
                    {{ leadData.leadQualityName }}
                  </a-tag>
                  <span class="lead-id">ID: {{ leadData.leadId }}</span>
                </div>
              </div>
            </div>
            <div class="header-actions">
              <a-button type="primary" size="small" @click="showEditModal">
                <template #icon><EditOutlined /></template>
                编辑
              </a-button>
              <a-button size="small" @click="showFollowModal">
                <template #icon><PlusOutlined /></template>
                跟进
              </a-button>
              <a-button size="small" @click="showAppointmentModal">
                <template #icon><CalendarOutlined /></template>
                预约
              </a-button>
              <!-- 简化的更多操作菜单 - 只保留核心功能 -->
              <a-dropdown>
                <a-button size="small">
                  <template #icon><MoreOutlined /></template>
                </a-button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="showEditModal">
                      <EditOutlined />
                      编辑线索
                    </a-menu-item>
                    <a-menu-item @click="showFollowModal">
                      <MessageOutlined />
                      添加跟进
                    </a-menu-item>
                    <a-menu-item @click="showAppointmentModal">
                      <CalendarOutlined />
                      创建预约
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </div>
          </div>
          
          <!-- 基本信息卡片 -->
          <div class="basic-info-compact">
            <div class="info-grid">
              <div class="info-item">
                <div class="info-icon">
                  <GlobalOutlined />
                </div>
                <div class="info-content">
                  <span class="info-label">线索来源</span>
                  <span class="info-value">{{ getLeadSourceName(leadData.leadSource) }}</span>
                </div>
              </div>

              <div class="info-item">
                <div class="info-icon">
                  <PhoneOutlined />
                </div>
                <div class="info-content">
                  <span class="info-label">手机号码</span>
                  <span class="info-value">{{ leadData.customerPhone || '-' }}</span>
                </div>
              </div>

              <div class="info-item">
                <div class="info-icon">
                  <UserOutlined />
                </div>
                <div class="info-content">
                  <span class="info-label">负责人</span>
                  <span class="info-value">{{ leadData.assignedEmployeeName || '待分配' }}</span>
                </div>
              </div>

              <div class="info-item">
                <div class="info-icon">
                  <ClockCircleOutlined />
                </div>
                <div class="info-content">
                  <span class="info-label">创建时间</span>
                  <span class="info-value">{{ dayjs(leadData.createTime).format('MM-DD HH:mm') }}</span>
                </div>
              </div>

              <div class="info-item" v-if="leadData.customerWechat">
                <div class="info-icon">
                  <WechatOutlined />
                </div>
                <div class="info-content">
                  <span class="info-label">微信号</span>
                  <span class="info-value">{{ leadData.customerWechat }}</span>
                </div>
              </div>

              <div class="info-item" v-if="leadData.age">
                <div class="info-icon">
                  <IdcardOutlined />
                </div>
                <div class="info-content">
                  <span class="info-label">年龄</span>
                  <span class="info-value">{{ leadData.age }}岁</span>
                </div>
              </div>

              <div class="info-item" v-if="leadData.symptom">
                <div class="info-icon">
                  <MedicineBoxOutlined />
                </div>
                <div class="info-content">
                  <span class="info-label">症状</span>
                  <div class="info-value">
                    <a-tag v-for="symptom in leadData.symptom.split(',')" :key="symptom" size="small" class="symptom-tag">
                      <DictLabel :dict-code="DICT_CODE_ENUM.SYMPTOM_TYPE" :data-value="symptom" />
                    </a-tag>
                  </div>
                </div>
              </div>

              <div class="info-item" v-if="leadData.remark">
                <div class="info-icon">
                  <FileTextOutlined />
                </div>
                <div class="info-content">
                  <span class="info-label">备注</span>
                  <span class="info-value">{{ leadData.remark }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 客户状态组件 -->
          <div class="customer-status-section">
            <!-- <div class="status-group">
              <span class="status-group-label">客户状态</span>
              <div class="status-content">
                <div class="current-status">
                  <a-tag :color="getCustomerStatusColor(getCustomerStatusFromLead())" size="large">
                    {{ getCustomerStatusName(getCustomerStatusFromLead()) }}
                  </a-tag>
                </div>
                <div class="status-progress">
                  <a-progress
                    :percent="getStatusProgress()"
                    :stroke-color="getProgressColor()"
                    size="small"
                    :show-info="false"
                  />
                  <span class="progress-text">{{ getProgressText() }}</span>
                </div>
                <div class="status-actions">
                  <a-button type="link" size="small" @click="makeCall" v-if="leadData?.customerPhone">
                    <template #icon><PhoneOutlined /></template>
                    拨打电话
                  </a-button>
                  <a-button type="link" size="small" @click="sendWechat" v-if="leadData?.customerWechat">
                    <template #icon><WechatOutlined /></template>
                    微信联系
                  </a-button>
                </div>
              </div>
            </div> -->

            <!-- <div class="action-group">
              <span class="action-group-label">业务</span>
              <div class="action-buttons">
                <a-button type="text" size="small" @click="scheduleCallback" class="action-btn">
                  <template #icon><ClockCircleOutlined /></template>
                  安排回访
                </a-button>
                <a-button type="text" size="small" @click="createTask" class="action-btn">
                  <template #icon><CheckSquareOutlined /></template>
                  创建任务
                </a-button>
                <a-button type="text" size="small" @click="addToGroup" class="action-btn">
                  <template #icon><TeamOutlined /></template>
                  加入群组
                </a-button>
              </div>
            </div> -->
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-section">
          <!-- 跟进输入框 -->
          <div class="follow-input-section">
            <div class="follow-input-header">
              <a-avatar size="small" :src="currentUserAvatar">{{ currentUserName }}</a-avatar>
              <span class="follow-placeholder">写跟进...</span>
            </div>
          </div>

          <!-- 标签页区域 -->
          <div class="tabs-section">
            <a-tabs v-model:activeKey="activeTab" type="card" class="enhanced-tabs">
              <!-- 活动标签页 -->
              <a-tab-pane key="activity" tab="活动">
                <div class="tab-header">
                  <div class="tab-filters">
                    <span class="filter-label">显示：</span>
                    <a-button
                      type="text"
                      size="small"
                      :class="{ active: activityFilter === 'follow' }"
                      @click="activityFilter = 'follow'"
                    >
                      跟进记录
                    </a-button>
                    <a-button
                      type="text"
                      size="small"
                      :class="{ active: activityFilter === 'dynamic' }"
                      @click="activityFilter = 'dynamic'"
                    >
                      线索动态
                    </a-button>
                    <a-button
                      type="text"
                      size="small"
                      :class="{ active: showAIAnalysis }"
                      @click="toggleAIAnalysis"
                    >
                      <RobotOutlined />
                      AI分析
                    </a-button>
                  </div>
                  <a-button type="text" size="small">
                    <template #icon><FilterOutlined /></template>
                    筛选
                  </a-button>
                </div>

                <!-- AI分析面板 -->
                <AIAnalysisPanel
                  v-if="showAIAnalysis"
                  ref="aiAnalysisPanel"
                  :lead-data="leadData"
                  :follow-list="followList"
                  :chat-records="chatRecords"
                />

                <!-- 活动时间轴 -->
                <div class="activity-timeline">
                  <!-- 快速跟进输入 -->
                  <div class="quick-follow-input">
                    <a-avatar :size="32" :src="currentUserAvatar">{{ currentUserName }}</a-avatar>
                    <div class="follow-input-wrapper">
                      <a-textarea
                        v-model:value="quickFollowContent"
                        placeholder="快速记录跟进..."
                        :auto-size="{ minRows: 1, maxRows: 3 }"
                        class="follow-textarea"
                      />
                      <div class="follow-input-actions">
                        <a-button type="text" size="small" @click="addQuickFollow" :disabled="!quickFollowContent.trim()">
                          <template #icon><SendOutlined /></template>
                          发送
                        </a-button>
                      </div>
                    </div>
                  </div>

                  <!-- 时间轴列表 - 跟进记录 -->
                  <div v-if="activityFilter === 'follow'" v-for="(item, index) in followList" :key="item.followId" class="timeline-item">
                    <div class="timeline-date">
                      <div class="date-day">{{ dayjs(item.createTime).format('DD') }}</div>
                      <div class="date-month">{{ dayjs(item.createTime).format('YYYY.MM') }}</div>
                    </div>
                    <div class="timeline-content">
                      <div class="timeline-header">
                        <a-avatar :size="32" :style="{ backgroundColor: getAvatarColor(item.employeeName || item.followUserName) }">
                          {{ getAvatarText(item.employeeName || item.followUserName) }}
                        </a-avatar>
                        <div class="timeline-meta">
                          <div class="meta-line-1">
                            <span class="user-name">{{ item.employeeName || item.followUserName || '系统' }}</span>
                            <a-tag size="small" :color="getFollowTypeColor(item.followType)">
                              {{ getFollowTypeName(item.followType) }}
                            </a-tag>
                            <a-tag v-if="item.followResultType" size="small" :color="getFollowResultColor(item.followResultType)">
                              {{ getFollowResultName(item.followResultType) }}
                            </a-tag>
                          </div>
                          <div class="meta-line-2">
                            <span class="timeline-time">{{ dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
                            <div class="ai-badge" v-if="shouldShowAIAnalysis(item)">
                              <RobotOutlined />
                              <span>AI分析</span>
                            </div>
                          </div>
                        </div>
                        <a-dropdown>
                          <a-button type="text" size="small">
                            <template #icon><MoreOutlined /></template>
                          </a-button>
                          <template #overlay>
                            <a-menu>
                              <a-menu-item @click="editFollow(item)">
                                <EditOutlined />
                                编辑
                              </a-menu-item>
                              <a-menu-item @click="copyFollow(item)">
                                <CopyOutlined />
                                复制
                              </a-menu-item>
                              <a-menu-divider />
                              <a-menu-item @click="deleteFollow(item)" danger>
                                <DeleteOutlined />
                                删除
                              </a-menu-item>
                            </a-menu>
                          </template>
                        </a-dropdown>
                      </div>

                      <div class="timeline-body">
                        <div class="follow-content">{{ item.followContent }}</div>

                        <!-- 附件展示 -->
                        <div class="follow-attachments" v-if="item.attachments && item.attachments.length">
                          <div v-for="attachment in item.attachments" :key="attachment.id" class="attachment-item">
                            <PaperClipOutlined />
                            <span>{{ attachment.name }}</span>
                          </div>
                        </div>

                        <!-- 下次跟进时间 -->
                        <div class="follow-next-time" v-if="item.nextFollowTime">
                          <ClockCircleOutlined />
                          <span>下次联系：{{ dayjs(item.nextFollowTime).format('MM-DD HH:mm') }}</span>
                          <a-tag size="small" color="orange">待跟进</a-tag>
                        </div>

                        <!-- 互动按钮 -->
                        <div class="follow-actions">
                          <a-button type="text" size="small" @click="likeFollow(item)" :class="{ active: item.isLiked }">
                            <template #icon><LikeOutlined /></template>
                            {{ item.likeCount || 0 }}
                          </a-button>
                          <a-button type="text" size="small" @click="showComments(item)">
                            <template #icon><CommentOutlined /></template>
                            评论
                          </a-button>
                          <a-button type="text" size="small" @click="shareFollow(item)">
                            <template #icon><ShareAltOutlined /></template>
                            分享
                          </a-button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 加载更多 -->
                  <div class="timeline-load-more" v-if="hasMoreFollows">
                    <a-button type="text" @click="loadMoreFollows" :loading="loadingMore">
                      加载更多跟进记录
                    </a-button>
                  </div>

                  <div class="timeline-end" v-else>
                    <span>没有更多记录了</span>
                  </div>

                  <!-- 线索动态时间轴 -->
                  <div v-if="activityFilter === 'dynamic'" class="dynamic-timeline">
                    <div v-for="(operation, index) in filteredOperations" :key="operation.id" class="timeline-item">
                      <div class="timeline-date">
                        <div class="date-day">{{ dayjs(operation.createTime).format('DD') }}</div>
                        <div class="date-month">{{ dayjs(operation.createTime).format('YYYY.MM') }}</div>
                      </div>
                      <div class="timeline-content">
                        <div class="timeline-header">
                          <a-avatar :size="32" :style="{ backgroundColor: getOperationColor(operation.type) }">
                            <component :is="getOperationIcon(operation.type)" />
                          </a-avatar>
                          <div class="timeline-meta">
                            <div class="meta-line-1">
                              <span class="user-name">{{ operation.operatorName }}</span>
                              <a-tag size="small" :color="getOperationColor(operation.type)">
                                {{ operation.actionName }}
                              </a-tag>
                            </div>
                            <div class="meta-line-2">
                              <span class="timeline-time">{{ dayjs(operation.createTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
                            </div>
                          </div>
                        </div>
                        <div class="timeline-body">
                          <div class="operation-content">{{ operation.content }}</div>
                          <div v-if="operation.changes && operation.changes.length" class="operation-changes">
                            <div v-for="change in operation.changes" :key="change.field" class="change-item">
                              <span class="change-field">{{ change.fieldName }}:</span>
                              <span class="change-old">{{ change.oldValue }}</span>
                              <span class="change-arrow">→</span>
                              <span class="change-new">{{ change.newValue }}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="empty-state" v-if="!filteredOperations.length">
                      <a-empty description="暂无线索动态" />
                    </div>
                  </div>
                </div>
              </a-tab-pane>

              <!-- 详细资料标签页 -->
              <a-tab-pane key="details" tab="详细资料">
                <div class="details-content">
                  <a-card title="客户详细信息" size="small" class="detail-section">
                    <a-descriptions :column="2" size="small">
                      <a-descriptions-item label="客户姓名">{{ leadData.customerName }}</a-descriptions-item>
                      <a-descriptions-item label="性别">{{ leadData.genderName || '-' }}</a-descriptions-item>
                      <a-descriptions-item label="年龄">{{ leadData.age || '-' }}岁</a-descriptions-item>
                      <a-descriptions-item label="手机号码">{{ leadData.customerPhone }}</a-descriptions-item>
                      <a-descriptions-item label="微信号">{{ leadData.customerWechat || '-' }}</a-descriptions-item>
                      <a-descriptions-item label="邮箱">{{ leadData.customerEmail || '-' }}</a-descriptions-item>
                      <a-descriptions-item label="地址">{{ leadData.customerAddress || '-' }}</a-descriptions-item>
                      <a-descriptions-item label="职业">{{ leadData.customerJob || '-' }}</a-descriptions-item>
                    </a-descriptions>
                  </a-card>

                  <a-card title="线索信息" size="small" class="detail-section">
                    <a-descriptions :column="2" size="small">
                      <a-descriptions-item label="线索来源">{{ getLeadSourceName(leadData.leadSource) }}</a-descriptions-item>
                      <a-descriptions-item label="线索状态">
                        <a-tag :color="LEAD_STATUS_ENUM[leadData.leadStatus]?.color">
                          {{ leadData.leadStatusName }}
                        </a-tag>
                      </a-descriptions-item>
                      <a-descriptions-item label="线索质量">
                        <a-tag :color="LEAD_QUALITY_ENUM[leadData.leadQuality]?.color">
                          {{ leadData.leadQualityName }}
                        </a-tag>
                      </a-descriptions-item>
                      <a-descriptions-item label="负责人">{{ leadData.assignedEmployeeName || '待分配' }}</a-descriptions-item>
                      <a-descriptions-item label="创建时间">{{ dayjs(leadData.createTime).format('YYYY-MM-DD HH:mm:ss') }}</a-descriptions-item>
                      <a-descriptions-item label="更新时间">{{ dayjs(leadData.updateTime).format('YYYY-MM-DD HH:mm:ss') }}</a-descriptions-item>
                    </a-descriptions>
                  </a-card>

                  <a-card title="症状信息" size="small" class="detail-section" v-if="leadData.symptom">
                    <div class="symptom-tags">
                      <a-tag v-for="symptom in leadData.symptom.split(',')" :key="symptom" color="blue">
                        <DictLabel :dict-code="DICT_CODE_ENUM.SYMPTOM_TYPE" :data-value="symptom" />
                      </a-tag>
                    </div>
                  </a-card>

                  <a-card title="备注信息" size="small" class="detail-section" v-if="leadData.remark">
                    <p class="remark-content">{{ leadData.remark }}</p>
                  </a-card>
                </div>
              </a-tab-pane>

              <!-- 聊天记录标签页 -->
              <a-tab-pane key="chat" tab="聊天记录">
                <div class="chat-content">
                  <div class="chat-container">
                    <div class="chat-messages" ref="chatMessagesRef">
                      <div
                        v-for="message in chatRecords"
                        :key="message.time"
                        :class="['chat-message', message.type === 'customer' ? 'customer' : 'staff']"
                      >
                        <div class="message-avatar">
                          <a-avatar :size="32" :style="{ backgroundColor: message.type === 'customer' ? '#1890ff' : '#52c41a' }">
                            {{ message.type === 'customer' ? '客' : '员' }}
                          </a-avatar>
                        </div>
                        <div class="message-content">
                          <div class="message-header">
                            <span class="sender-name">{{ message.sender }}</span>
                            <span class="send-time">{{ dayjs(message.time).format('YYYY-MM-DD HH:mm:ss') }}</span>
                          </div>
                          <div class="message-body">
                            <div class="text-message" v-html="formatChatContent(message.content)"></div>
                          </div>
                        </div>
                      </div>
                    </div>

                    

                    <div class="empty-state" v-if="!chatRecords.length">
                      <a-empty description="暂无聊天记录" />
                    </div>
                  </div>
                </div>
              </a-tab-pane>

              <!-- 附件标签页 -->
              <a-tab-pane key="attachments" tab="附件">
                <div class="attachments-content">
                  <div class="attachment-header">
                    <a-upload
                      :file-list="attachmentList"
                      :before-upload="beforeUpload"
                      @change="handleAttachmentChange"
                      multiple
                      :show-upload-list="false"
                    >
                      <a-button type="primary">
                        <template #icon><UploadOutlined /></template>
                        上传附件
                      </a-button>
                    </a-upload>
                    <span class="upload-tip">支持上传图片、文档、音频、视频等文件</span>
                  </div>

                  <div class="attachment-list" v-if="attachmentList.length">
                    <div class="attachment-grid">
                      <div v-for="file in attachmentList" :key="file.uid" class="attachment-item">
                        <div class="attachment-preview">
                          <img v-if="isImage(file)" :src="file.url" :alt="file.name" class="preview-image" />
                          <div v-else class="file-icon">
                            <FileOutlined v-if="isDocument(file)" />
                            <VideoCameraOutlined v-else-if="isVideo(file)" />
                            <AudioOutlined v-else-if="isAudio(file)" />
                            <FileOutlined v-else />
                          </div>
                        </div>
                        <div class="attachment-info">
                          <div class="file-name" :title="file.name">{{ file.name }}</div>
                          <div class="file-meta">
                            <span class="file-size">{{ formatFileSize(file.size) }}</span>
                            <span class="upload-time">{{ dayjs(file.uploadTime).format('MM-DD HH:mm') }}</span>
                          </div>
                        </div>
                        <div class="attachment-actions">
                          <a-button type="text" size="small" @click="previewFile(file)">
                            <template #icon><EyeOutlined /></template>
                          </a-button>
                          <a-button type="text" size="small" @click="downloadFile(file)">
                            <template #icon><DownloadOutlined /></template>
                          </a-button>
                          <a-button type="text" size="small" @click="deleteFile(file)" danger>
                            <template #icon><DeleteOutlined /></template>
                          </a-button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="empty-state" v-else>
                    <a-empty description="暂无附件">
                      <a-upload
                        :before-upload="beforeUpload"
                        @change="handleAttachmentChange"
                        multiple
                        :show-upload-list="false"
                      >
                        <a-button type="primary">
                          <template #icon><UploadOutlined /></template>
                          上传第一个附件
                        </a-button>
                      </a-upload>
                    </a-empty>
                  </div>
                </div>
              </a-tab-pane>

              <!-- 操作记录标签页 -->
              <a-tab-pane key="operations" tab="操作记录">
                <div class="operations-content">
                  <div class="operation-filters">
                    <a-radio-group v-model:value="operationFilter" button-style="solid" size="small">
                      <a-radio-button value="all">全部</a-radio-button>
                      <a-radio-button value="create">创建</a-radio-button>
                      <a-radio-button value="update">更新</a-radio-button>
                      <a-radio-button value="assign">分配</a-radio-button>
                      <a-radio-button value="status">状态变更</a-radio-button>
                    </a-radio-group>
                  </div>

                  <div class="operation-timeline">
                    <a-timeline>
                      <a-timeline-item v-for="operation in filteredOperations" :key="operation.id" :color="getOperationColor(operation.type)">
                        <template #dot>
                          <component :is="getOperationIcon(operation.type)" />
                        </template>
                        <div class="operation-item">
                          <div class="operation-header">
                            <span class="operation-user">{{ operation.operatorName }}</span>
                            <span class="operation-action">{{ operation.actionName }}</span>
                            <span class="operation-time">{{ dayjs(operation.createTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
                          </div>
                          <div class="operation-content" v-if="operation.content">
                            {{ operation.content }}
                          </div>
                          <div class="operation-changes" v-if="operation.changes && operation.changes.length">
                            <div v-for="change in operation.changes" :key="change.field" class="change-item">
                              <span class="change-field">{{ change.fieldName }}:</span>
                              <span class="change-old">{{ change.oldValue || '-' }}</span>
                              <ArrowRightOutlined class="change-arrow" />
                              <span class="change-new">{{ change.newValue || '-' }}</span>
                            </div>
                          </div>
                        </div>
                      </a-timeline-item>
                    </a-timeline>
                  </div>

                  <div class="empty-state" v-if="!operationList.length">
                    <a-empty description="暂无操作记录" />
                  </div>
                </div>
              </a-tab-pane>

              <!-- 打印记录标签页 - 暂时注释 -->
              <!-- <a-tab-pane key="prints" tab="打印记录">
                <div class="prints-content">
                  <div class="print-header">
                    <a-button type="primary" @click="printLeadDetail">
                      <template #icon><PrinterOutlined /></template>
                      打印线索详情
                    </a-button>
                    <a-button @click="exportLeadDetail">
                      <template #icon><ExportOutlined /></template>
                      导出PDF
                    </a-button>
                  </div>

                  <div class="print-history" v-if="printList.length">
                    <a-table
                      :columns="printColumns"
                      :data-source="printList"
                      :pagination="false"
                      size="small"
                    >
                      <template #bodyCell="{ column, record }">
                        <template v-if="column.key === 'printType'">
                          <a-tag :color="getPrintTypeColor(record.printType)">
                            {{ record.printTypeName }}
                          </a-tag>
                        </template>
                        <template v-else-if="column.key === 'printTime'">
                          {{ dayjs(record.printTime).format('YYYY-MM-DD HH:mm:ss') }}
                        </template>
                        <template v-else-if="column.key === 'actions'">
                          <a-button type="text" size="small" @click="reprintDocument(record)">
                            <template #icon><RedoOutlined /></template>
                            重新打印
                          </a-button>
                          <a-button type="text" size="small" @click="downloadPrint(record)">
                            <template #icon><DownloadOutlined /></template>
                            下载
                          </a-button>
                        </template>
                      </template>
                    </a-table>
                  </div>

                  <div class="empty-state" v-else>
                    <a-empty description="暂无打印记录">
                      <a-button type="primary" @click="printLeadDetail">
                        <template #icon><PrinterOutlined /></template>
                        开始打印
                      </a-button>
                    </a-empty>
                  </div>
                </div>
              </a-tab-pane> -->
            </a-tabs>
          </div>
        </div>
      </div>
    </a-spin>

    <!-- 编辑线索弹窗 -->
    <LeadFormModal ref="leadFormModal" @refresh="handleRefresh" />

    <!-- 跟进记录弹窗 -->
    <LeadFollowEnhancedModal ref="leadFollowModal" @refresh="handleFollowRefresh" />

    <!-- 预约弹窗 -->
    <LeadAppointmentModal ref="leadAppointmentModal" @refresh="handleRefresh" />
  </a-drawer>
</template>

<script setup>
import { ref, computed, nextTick, onUnmounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import {
  EditOutlined,
  MoreOutlined,
  UserAddOutlined,
  SwapOutlined,
  DeleteOutlined,
  MailOutlined,
  MessageOutlined,
  RobotOutlined,
  FilterOutlined,
  ClockCircleOutlined,
  LikeOutlined,
  DislikeOutlined,
  CommentOutlined,
  PlusOutlined,
  CalendarOutlined,
  StarOutlined,
  ShareAltOutlined,
  GlobalOutlined,
  PhoneOutlined,
  UserOutlined,
  WechatOutlined,
  IdcardOutlined,
  MedicineBoxOutlined,
  FileTextOutlined,
  CheckSquareOutlined,
  TeamOutlined,
  SendOutlined,
  CopyOutlined,
  PaperClipOutlined,
  SearchOutlined,
  UploadOutlined,
  FileOutlined,
  VideoCameraOutlined,
  AudioOutlined,
  EyeOutlined,
  DownloadOutlined,
  ArrowRightOutlined,
  PrinterOutlined,
  ExportOutlined,
  RedoOutlined
} from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import { leadApi } from '/@/api/business/hospital/lead-api';
import { leadFollowApi } from '/@/api/business/hospital/lead-follow-api';
import { lead360Api } from '/@/api/business/hospital/lead-360-api';
import { appointmentApi } from '/@/api/business/hospital/appointment-api';
import { LEAD_STATUS_ENUM, LEAD_QUALITY_ENUM } from '/@/constants/business/hospital/lead-const';
import { CUSTOMER_STATUS_ENUM } from '/@/constants/business/hospital/customer-const';
import { DICT_CODE_ENUM } from '/@/constants/support/dict-const';
import { useUserStore } from '/@/store/modules/system/user';
import DictLabel from '/@/components/support/dict-label/index.vue';
import AIAnalysisPanel from './ai-analysis-panel.vue';
import LeadFormModal from './lead-form-modal.vue';
import LeadFollowEnhancedModal from './lead-follow-enhanced-modal.vue';
import LeadAppointmentModal from './lead-appointment-modal.vue';
import watermark from '/@/lib/smart-watermark';

// 组件事件
const emit = defineEmits(['refresh']);

// 响应式数据
const visible = ref(false);
const loading = ref(false);
const activeTab = ref('activity');
const activityFilter = ref('follow');
const leadData = ref(null);
const followList = ref([]);
const quickFollowContent = ref('');
const hasMoreFollows = ref(false);
const loadingMore = ref(false);
const attachmentList = ref([]);
const operationList = ref([]);
const operationFilter = ref('all');
// const printList = ref([]);
const showAIAnalysis = ref(false);
const aiAnalysisPanel = ref(null);

// 聊天记录相关数据
const chatRecords = ref([]);
const newChatMessage = ref('');
const chatMessagesRef = ref();

// 弹窗组件引用
const leadFormModal = ref();
const leadFollowModal = ref();
const leadAppointmentModal = ref();

// 用户信息
const userStore = useUserStore();
const currentUserName = computed(() => userStore.actualName);
const currentUserAvatar = computed(() => userStore.avatar);

// 计算属性

const filteredOperations = computed(() => {
  if (operationFilter.value === 'all') {
    return operationList.value;
  }
  return operationList.value.filter(op => op.type === operationFilter.value);
});

// 打印相关配置 - 暂时注释
/*
const printColumns = [
  { title: '打印类型', dataIndex: 'printTypeName', key: 'printType' },
  { title: '打印人', dataIndex: 'printerName', key: 'printerName' },
  { title: '打印时间', dataIndex: 'printTime', key: 'printTime' },
  { title: '操作', key: 'actions', width: 150 }
];
*/

// 显示抽屉
const showDrawer = async (leadIdOrData) => {
  console.log('🔍 [DEBUG] showDrawer 开始执行, 参数:', leadIdOrData);
  visible.value = true;
  activeTab.value = 'activity';

  try {
    // 如果传入的是对象，直接使用；如果是ID，则加载数据
    if (typeof leadIdOrData === 'object' && leadIdOrData !== null) {
      console.log('🔍 [DEBUG] 传入的是对象，直接使用leadData');
      leadData.value = leadIdOrData;

      // 并行加载相关数据，但单独处理每个API的错误
      await Promise.allSettled([
        loadFollowList(leadIdOrData.leadId),
        loadChatRecords(leadIdOrData.leadId),
        loadOperationList(leadIdOrData.leadId)
      ]);
    } else {
      console.log('🔍 [DEBUG] 传入的是leadId，需要加载详情:', leadIdOrData);
      await loadLeadDetail(leadIdOrData);

      // 并行加载相关数据，但单独处理每个API的错误
      await Promise.allSettled([
        loadFollowList(leadIdOrData),
        loadChatRecords(leadIdOrData),
        loadOperationList(leadIdOrData)
      ]);
    }

    // 设置水印
    console.log('🔍 [DEBUG] 开始设置水印');
    setTimeout(() => {
      const userStore = useUserStore();
      console.log('🔍 [DEBUG] 设置水印, 用户名:', userStore.actualName);
      watermark.set('lead-detail-enhanced-watermark-container', userStore.actualName);
    }, 100);

    console.log('✅ [DEBUG] showDrawer 执行完成');
  } catch (error) {
    console.error('❌ [DEBUG] showDrawer 执行失败:', error);
    message.error('加载线索详情失败，请稍后重试');
  }
};

// 关闭抽屉
const onClose = () => {
  visible.value = false;
  leadData.value = null;
  followList.value = [];

  // 清除水印
  watermark.clear();
};

// 加载线索详情
const loadLeadDetail = async (leadId) => {
  console.log('🔍 [DEBUG] loadLeadDetail 开始执行, leadId:', leadId);
  try {
    loading.value = true;
    const res = await leadApi.getDetail(leadId);
    console.log('🔍 [DEBUG] leadApi.getDetail 响应:', res);
    if (res.ok) {
      leadData.value = res.data;
      console.log('🔍 [DEBUG] 线索详情加载成功:', res.data);
    } else {
      message.error(res.msg || '加载线索详情失败');
      console.error('❌ [DEBUG] 线索详情加载失败:', res);
    }
  } catch (error) {
    message.error('加载线索详情失败');
    console.error('❌ [DEBUG] 线索详情加载异常:', error);
  } finally {
    loading.value = false;
  }
};

// 加载跟进记录
const followPageNum = ref(1);
const followPageSize = ref(10);

const loadFollowList = async (leadId, isLoadMore = false) => {
  try {
    const currentLeadId = leadId || leadData.value?.leadId;
    if (!currentLeadId) {
      console.log('🔍 [DEBUG] loadFollowList: leadId为空，跳过加载');
      return;
    }

    console.log('🔍 [DEBUG] loadFollowList 开始执行, leadId:', currentLeadId, 'isLoadMore:', isLoadMore);

    if (!isLoadMore) {
      followPageNum.value = 1;
      loadingMore.value = false;
    } else {
      loadingMore.value = true;
    }

    const res = await leadFollowApi.queryByLeadId(currentLeadId);
    console.log('🔍 [DEBUG] leadFollowApi.queryByLeadId 响应:', res);

    if (res.ok) {
      const newData = res.data || [];
      console.log('🔍 [DEBUG] 跟进记录加载成功，数据量:', newData.length);

      if (isLoadMore) {
        followList.value = [...followList.value, ...newData];
      } else {
        followList.value = newData;
      }

      // 判断是否还有更多数据
      hasMoreFollows.value = newData.length >= followPageSize.value;
    } else {
      console.error('❌ [DEBUG] 跟进记录加载失败:', res.msg);
      message.error(res.msg || '加载跟进记录失败');
    }
  } catch (error) {
    console.error('❌ [DEBUG] 加载跟进记录异常:', error);
    message.error('加载跟进记录失败：' + (error.message || '网络错误'));
  } finally {
    loadingMore.value = false;
  }
};

// 获取线索来源中文名称
const getLeadSourceName = (leadSource) => {
  const sourceMap = {
    'BAIDU': '百度',
    'DOUYIN': '抖音',
    'KUAISHOU': '快手',
    'SHANGWUTONG': '商务通',
    'WECHAT': '微信推广',
    'FRIEND_REFERRAL': '朋友介绍',
    'PHONE_INQUIRY': '电话咨询',
    'ONLINE_PROMOTION': '网络推广',
    'OTHER': '其他'
  };
  return sourceMap[leadSource] || leadSource;
};

// 获取头像文字
const getAvatarText = (name) => {
  if (!name) return '';
  return name.length > 1 ? name.slice(-2) : name;
};

// 获取头像颜色
const getAvatarColor = (name) => {
  const colors = [
    '#f56a00', '#7265e6', '#ffbf00', '#00a2ae', '#87d068',
    '#108ee9', '#f50', '#2db7f5', '#52c41a', '#fa8c16'
  ];
  if (!name) return colors[0];
  const index = name.charCodeAt(0) % colors.length;
  return colors[index];
};

// 操作方法
const showEditModal = () => {
  if (leadData.value && leadFormModal.value) {
    leadFormModal.value.showModal(leadData.value);
  }
};

const showFollowModal = () => {
  console.log('🔍 [DEBUG] showFollowModal 开始执行');
  console.log('🔍 [DEBUG] leadData.value:', leadData.value);
  console.log('🔍 [DEBUG] leadFollowModal.value:', leadFollowModal.value);

  if (!leadData.value) {
    console.log('❌ [DEBUG] leadData.value 为空');
    message.warning('请先选择线索');
    return;
  }
  if (!leadFollowModal.value) {
    console.log('❌ [DEBUG] leadFollowModal.value 为空');
    message.error('跟进组件未加载');
    return;
  }
  try {
    console.log('🔍 [DEBUG] 调用 leadFollowModal.showModal, leadId:', leadData.value.leadId);
    leadFollowModal.value.showModal(leadData.value.leadId);
    console.log('✅ [DEBUG] leadFollowModal.showModal 调用成功');
  } catch (error) {
    console.error('❌ [DEBUG] 调用跟进组件失败:', error);
    message.error('打开跟进弹窗失败');
  }
};

const showAppointmentModal = () => {
  if (leadData.value && leadAppointmentModal.value) {
    leadAppointmentModal.value.showModal(leadData.value);
  }
};

// 移除的操作方法 - 简化菜单后不再需要
/*
const showAssignModal = () => {
  message.info('分配员工功能待实现');
};

const convertToCustomer = () => {
  message.info('转为客户功能待实现');
};

const toggleStar = () => {
  message.info('关注功能待实现');
};

const showShareModal = () => {
  message.info('分享功能待实现');
};

const showDeleteConfirm = () => {
  message.info('删除功能待实现');
};
*/

const makeCall = () => {
  if (leadData.value?.customerPhone) {
    window.open(`tel:${leadData.value.customerPhone}`);
  } else {
    message.warning('客户电话号码为空');
  }
};



const sendWechat = () => {
  if (leadData.value?.customerWechat) {
    message.info(`微信号：${leadData.value.customerWechat}`);
  } else {
    message.warning('客户微信号为空');
  }
};

// 客户状态相关方法
const getCustomerStatusFromLead = () => {
  // 根据线索状态推断客户状态
  if (!leadData.value?.leadStatus) return 1; // 默认潜在客户

  const leadStatus = leadData.value.leadStatus;
  switch (leadStatus) {
    case 1: // 新线索
      return 1; // 潜在客户
    case 2: // 跟进中
      return 2; // 意向客户
    case 3: // 已预约
    case 4: // 已到院
      return 2; // 意向客户
    case 5: // 已转化
      return 3; // 成交客户
    case 6: // 爽约
    case 7: // 已关闭
      return 4; // 流失客户
    default:
      return 1; // 潜在客户
  }
};

const getCustomerStatusName = (status) => {
  const statusMap = {
    1: '潜在客户',
    2: '意向客户',
    3: '成交客户',
    4: '流失客户'
  };
  return statusMap[status] || '潜在客户';
};

const getCustomerStatusColor = (status) => {
  const colorMap = {
    1: 'blue',     // 潜在客户
    2: 'orange',   // 意向客户
    3: 'green',    // 成交客户
    4: 'red',      // 流失客户
  };
  return colorMap[status] || 'blue';
};

const getStatusProgress = () => {
  const status = getCustomerStatusFromLead();
  const progressMap = {
    1: 25,  // 潜在客户
    2: 60,  // 意向客户
    3: 100, // 成交客户
    4: 0,   // 流失客户
  };
  return progressMap[status] || 25;
};

const getProgressColor = () => {
  const status = getCustomerStatusFromLead();
  const colorMap = {
    1: '#1890ff',  // 潜在客户 - 蓝色
    2: '#fa8c16',  // 意向客户 - 橙色
    3: '#52c41a',  // 成交客户 - 绿色
    4: '#ff4d4f',  // 流失客户 - 红色
  };
  return colorMap[status] || '#1890ff';
};

const getProgressText = () => {
  const status = getCustomerStatusFromLead();
  const textMap = {
    1: '初步接触',
    2: '深度沟通',
    3: '成功转化',
    4: '已流失',
  };
  return textMap[status] || '初步接触';
};

const scheduleCallback = () => {
  message.info('安排回访功能待实现');
};

const createTask = () => {
  message.info('创建任务功能待实现');
};

const addToGroup = () => {
  message.info('加入群组功能待实现');
};

// 跟进相关方法
const addQuickFollow = async () => {
  if (!quickFollowContent.value.trim()) return;

  try {
    const followData = {
      leadId: leadData.value.leadId,
      followType: 4, // 其他方式
      followContent: quickFollowContent.value.trim(),
      followResult: 4, // 仅记录跟进
      nextFollowTime: null
    };

    const res = await leadFollowApi.add(followData);
    if (res.ok) {
      message.success('跟进记录添加成功');
      quickFollowContent.value = '';
      await loadFollowList(leadData.value.leadId);
    } else {
      message.error(res.msg || '添加跟进记录失败');
    }
  } catch (error) {
    console.error('添加跟进记录失败:', error);
    message.error('添加跟进记录失败');
  }
};

const getFollowTypeColor = (type) => {
  const colorMap = {
    1: 'blue',    // 电话
    2: 'green',   // 微信
    3: 'orange',  // 面谈
    4: 'purple',  // 其他
  };
  return colorMap[type] || 'default';
};

const getFollowTypeName = (type) => {
  const typeMap = {
    1: '电话',
    2: '微信',
    3: '面谈',
    4: '其他',
  };
  return typeMap[type] || '未知';
};

const getFollowResultName = (resultType) => {
  const resultMap = {
    1: '客户有预约意向',
    2: '需要再次跟进',
    3: '客户无意向/无效线索',
    4: '仅记录跟进'
  };
  return resultMap[resultType] || '';
};

const getFollowResultColor = (resultType) => {
  const colorMap = {
    1: 'green',   // 客户有预约意向
    2: 'orange',  // 需要再次跟进
    3: 'red',     // 客户无意向/无效线索
    4: 'blue',    // 仅记录跟进
  };
  return colorMap[resultType] || 'default';
};

const shouldShowAIAnalysis = (item) => {
  // 根据跟进内容长度和类型判断是否显示AI分析
  return item.followContent && item.followContent.length > 20;
};

const editFollow = (item) => {
  if (leadFollowModal.value) {
    leadFollowModal.value.showEditModal(item);
  }
};

const copyFollow = (item) => {
  if (item.followContent) {
    navigator.clipboard.writeText(item.followContent);
    message.success('跟进内容已复制');
  } else {
    message.warning('没有可复制的内容');
  }
};

const deleteFollow = (item) => {
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除这条跟进记录吗？删除后无法恢复。',
    okText: '确定',
    cancelText: '取消',
    onOk: async () => {
      try {
        const res = await leadFollowApi.delete(item.followId);
        if (res.ok) {
          message.success('删除成功');
          await loadFollowList(leadData.value.leadId);
        } else {
          message.error(res.msg || '删除失败');
        }
      } catch (error) {
        console.error('删除跟进记录失败:', error);
        message.error('删除失败');
      }
    }
  });
};

const likeFollow = (item) => {
  message.info('点赞功能待实现');
};

const showComments = (item) => {
  message.info('评论功能待实现');
};

const shareFollow = (item) => {
  message.info('分享功能待实现');
};

const loadMoreFollows = async () => {
  if (loadingMore.value) return;

  followPageNum.value += 1;
  await loadFollowList(leadData.value?.leadId, true);
};

// 聊天记录相关方法
const loadChatRecords = async (leadId) => {
  try {
    const currentLeadId = leadId || leadData.value?.leadId;
    if (!currentLeadId) {
      console.log('🔍 [DEBUG] loadChatRecords: leadId为空，跳过加载');
      return;
    }

    console.log('🔍 [DEBUG] loadChatRecords 开始执行, leadId:', currentLeadId);

    const res = await lead360Api.getLead360View(currentLeadId);
    console.log('🔍 [DEBUG] lead360Api.getLead360View 响应:', res);

    if (res.ok && res.data?.chatRecords) {
      chatRecords.value = res.data.chatRecords;
      console.log('🔍 [DEBUG] 聊天记录加载成功，数据量:', res.data.chatRecords.length);

      // 滚动到底部
      nextTick(() => {
        scrollToBottom();
      });
    } else {
      console.log('🔍 [DEBUG] 聊天记录为空或加载失败:', res.msg);
      // 聊天记录为空不显示错误，这是正常情况
      chatRecords.value = [];
    }
  } catch (error) {
    console.error('❌ [DEBUG] 加载聊天记录异常:', error);
    message.error('加载聊天记录失败：' + (error.message || '网络错误'));
  }
};

const sendChatMessage = async () => {
  if (!newChatMessage.value.trim()) return;

  try {
    const res = await lead360Api.addChatRecord(leadData.value.leadId, {
      sender: currentUserName.value || '当前用户',
      content: newChatMessage.value,
      type: 'staff'
    });

    if (res.ok) {
      newChatMessage.value = '';
      // 重新加载聊天记录
      await loadChatRecords(leadData.value.leadId);
    } else {
      message.error(res.msg || '发送失败');
    }
  } catch (error) {
    console.error('发送消息失败:', error);
    message.error('发送失败');
  }
};

const scrollToBottom = () => {
  if (chatMessagesRef.value) {
    chatMessagesRef.value.scrollTop = chatMessagesRef.value.scrollHeight;
  }
};

// 格式化聊天内容，支持换行
const formatChatContent = (content) => {
  if (!content) return '';
  // 将换行符转换为HTML换行标签
  return content.replace(/\n/g, '<br>').replace(/\r\n/g, '<br>').replace(/\r/g, '<br>');
};

// 加载操作记录
const loadOperationList = async (leadId) => {
  console.log('🔍 [DEBUG] loadOperationList 开始执行, leadId:', leadId);
  const operations = [];

  // 尝试加载状态历史记录 - 静默处理，不显示错误消息
  try {
    console.log('🔍 [DEBUG] 开始加载状态历史记录');

    // 临时禁用message.error，避免显示"查询失败"
    const originalMessageError = message.error;
    message.error = () => {}; // 临时禁用错误提示

    try {
      const statusRes = await leadApi.getStatusHistory(leadId);
      console.log('🔍 [DEBUG] 状态历史记录响应:', statusRes);

      if (statusRes.ok && statusRes.data !== null && statusRes.data !== undefined) {
        // 处理状态历史记录，即使是空数组也要处理
        const statusHistoryArray = Array.isArray(statusRes.data) ? statusRes.data : [];
        console.log('🔍 [DEBUG] 状态历史记录数组:', statusHistoryArray);
        const statusOperations = statusHistoryArray.map(history => ({
          id: `status_${history.historyId}`,
          type: getOperationTypeFromTrigger(history.triggerType),
          operatorName: history.createName,
          actionName: getActionNameFromTrigger(history.triggerType),
          createTime: history.createTime,
          content: history.remark || `${history.fromStatusName} → ${history.toStatusName}`,
          changes: [{
            field: 'status',
            fieldName: '状态',
            oldValue: history.fromStatusName,
            newValue: history.toStatusName
          }]
        }));
        operations.push(...statusOperations);
        console.log('🔍 [DEBUG] 状态操作记录处理完成:', statusOperations);
      } else {
        console.log('ℹ️ [DEBUG] 状态历史记录API返回失败或无数据:', statusRes.msg || '无数据');
      }
    } finally {
      // 恢复message.error
      message.error = originalMessageError;
    }
  } catch (error) {
    console.warn('⚠️ [DEBUG] 状态历史记录API调用异常:', error.message);
    // 不显示错误消息，因为这不是关键功能
  }

  // 如果没有状态历史记录，创建一个基础的创建记录
  if (operations.length === 0) {
    operations.push({
      id: 'create_record',
      type: 'create',
      operatorName: '系统',
      actionName: '创建线索',
      createTime: leadData.value?.createTime || new Date().toISOString(),
      content: '创建了新的线索记录'
    });
  }

    // 尝试加载预约记录（如果API存在）
    try {
      console.log('🔍 [DEBUG] 开始加载预约记录');
      const appointmentRes = await appointmentApi.queryByLeadId(leadId);
      console.log('🔍 [DEBUG] 预约记录响应:', appointmentRes);

      if (appointmentRes.ok && appointmentRes.data) {
        const appointmentOperations = appointmentRes.data.map(appointment => ({
          id: `appointment_${appointment.appointmentId}`,
          type: 'appointment',
          operatorName: appointment.createName,
          actionName: '创建预约',
          createTime: appointment.createTime,
          content: `预约时间：${appointment.appointmentDate} ${appointment.appointmentTime}${appointment.remark ? '，备注：' + appointment.remark : ''}`,
          changes: [{
            field: 'appointment',
            fieldName: '预约',
            oldValue: '',
            newValue: `${appointment.appointmentDate} ${appointment.appointmentTime}`
          }]
        }));
        operations.push(...appointmentOperations);
        console.log('🔍 [DEBUG] 预约操作记录处理完成:', appointmentOperations);
      }
    } catch (appointmentError) {
      console.log('⚠️ [DEBUG] 预约记录加载失败，跳过:', appointmentError.message);
    }

  // 按创建时间倒序排列
  operationList.value = operations.sort((a, b) => new Date(b.createTime) - new Date(a.createTime));
  console.log('🔍 [DEBUG] 最终操作记录:', operationList.value);
  console.log('✅ [DEBUG] 操作记录加载成功，共', operationList.value.length, '条记录');
};

// 辅助方法：将触发类型转换为操作类型
const getOperationTypeFromTrigger = (triggerType) => {
  const typeMap = {
    'FOLLOW_ADD': 'update',
    'APPOINTMENT_CREATE': 'update',
    'ARRIVAL_CONFIRM': 'status',
    'CONVERSION_COMPLETE': 'status',
    'MANUAL_CLOSE': 'status',
    'MANUAL': 'update'
  };
  return typeMap[triggerType] || 'update';
};

// 辅助方法：将触发类型转换为操作名称
const getActionNameFromTrigger = (triggerType) => {
  const actionMap = {
    'FOLLOW_ADD': '添加跟进记录',
    'APPOINTMENT_CREATE': '创建预约',
    'ARRIVAL_CONFIRM': '确认到院',
    'CONVERSION_COMPLETE': '完成转化',
    'MANUAL_CLOSE': '手动关闭',
    'MANUAL': '手动操作'
  };
  return actionMap[triggerType] || '状态变更';
};

// 附件相关方法
const beforeUpload = (file) => {
  const isValidType = ['image/', 'application/', 'text/', 'audio/', 'video/'].some(type =>
    file.type.startsWith(type)
  );
  if (!isValidType) {
    message.error('不支持的文件类型');
    return false;
  }
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('文件大小不能超过10MB');
    return false;
  }
  return false; // 阻止自动上传
};

const handleAttachmentChange = (info) => {
  message.info('文件上传功能待实现');
};

const isImage = (file) => {
  return file.type?.startsWith('image/');
};

const isDocument = (file) => {
  return file.type?.includes('document') || file.type?.includes('pdf') || file.type?.includes('text');
};

const isVideo = (file) => {
  return file.type?.startsWith('video/');
};

const isAudio = (file) => {
  return file.type?.startsWith('audio/');
};

const formatFileSize = (size) => {
  if (!size) return '0 B';
  const units = ['B', 'KB', 'MB', 'GB'];
  let index = 0;
  while (size >= 1024 && index < units.length - 1) {
    size /= 1024;
    index++;
  }
  return `${size.toFixed(1)} ${units[index]}`;
};

const previewFile = (file) => {
  message.info('文件预览功能待实现');
};

const downloadFile = (file) => {
  message.info('文件下载功能待实现');
};

const deleteFile = (file) => {
  message.info('删除文件功能待实现');
};

// 操作记录相关方法
const getOperationColor = (type) => {
  const colorMap = {
    'create': 'green',
    'update': 'blue',
    'assign': 'orange',
    'status': 'purple',
    'delete': 'red'
  };
  return colorMap[type] || 'default';
};

const getOperationIcon = (type) => {
  const iconMap = {
    'create': 'PlusOutlined',
    'update': 'EditOutlined',
    'assign': 'UserAddOutlined',
    'status': 'SwapOutlined',
    'delete': 'DeleteOutlined'
  };
  return iconMap[type] || 'FileOutlined';
};

// 打印相关方法 - 暂时注释
/*
const printLeadDetail = () => {
  message.info('打印功能待实现');
};

const exportLeadDetail = () => {
  message.info('导出PDF功能待实现');
};

const getPrintTypeColor = (type) => {
  const colorMap = {
    'detail': 'blue',
    'summary': 'green',
    'report': 'orange'
  };
  return colorMap[type] || 'default';
};

const reprintDocument = (record) => {
  message.info('重新打印功能待实现');
};

const downloadPrint = (record) => {
  message.info('下载打印文件功能待实现');
};
*/

// AI分析相关方法
const toggleAIAnalysis = () => {
  showAIAnalysis.value = !showAIAnalysis.value;
  if (showAIAnalysis.value && aiAnalysisPanel.value) {
    // 如果显示AI分析面板，触发分析
    nextTick(() => {
      aiAnalysisPanel.value?.startAnalysis();
    });
  }
};

const refreshAIAnalysis = () => {
  if (aiAnalysisPanel.value) {
    aiAnalysisPanel.value.refreshAnalysis();
  }
};

// 刷新处理方法
const handleRefresh = async () => {
  if (leadData.value?.leadId) {
    await loadLeadDetail(leadData.value.leadId);
  }
  emit('refresh');
};

const handleFollowRefresh = () => {
  if (leadData.value?.leadId) {
    loadFollowList(leadData.value.leadId);
  }
  emit('refresh');
};

// 组件卸载时清除水印
onUnmounted(() => {
  watermark.clear();
});

// 暴露方法
defineExpose({
  showDrawer
});
</script>

<style scoped>
.lead-detail-enhanced-drawer {
  /* 自定义抽屉样式 */
}

.lead-detail-enhanced-container {
  height: calc(100vh - 55px);
  display: flex;
  flex-direction: column;
}

/* 头部信息区 */
.header-section {
  flex-shrink: 0;
  padding: 20px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.header-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.lead-title-area {
  display: flex;
  align-items: center;
  gap: 16px;
}

.lead-avatar {
  flex-shrink: 0;
}

.lead-info {
  flex: 1;
  min-width: 0;
}

.lead-name {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #262626;
  line-height: 1.2;
}

.lead-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.status-tag, .quality-tag {
  font-size: 12px;
}

.lead-id {
  color: #8c8c8c;
  font-size: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 基本信息卡片 */
.basic-info-compact {
  background: #fafafa;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  min-width: 0;
}

.info-icon {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1890ff;
  margin-top: 2px;
}

.info-content {
  flex: 1;
  min-width: 0;
}

.info-label {
  display: block;
  color: #8c8c8c;
  font-size: 12px;
  line-height: 1.2;
  margin-bottom: 4px;
}

.info-value {
  color: #262626;
  font-size: 13px;
  font-weight: 500;
  line-height: 1.4;
  word-break: break-all;
}

.symptom-tag {
  margin: 2px 4px 2px 0;
}

/* 客户状态组件 */
.customer-status-section {
  margin-bottom: 16px;
}

.status-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.status-group-label {
  font-size: 12px;
  color: #8c8c8c;
  font-weight: 500;
}

.status-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.current-status {
  display: flex;
  align-items: center;
}

.status-progress {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-text {
  font-size: 12px;
  color: #666;
  min-width: 60px;
}

.status-actions {
  display: flex;
  gap: 8px;
  margin-top: 4px;
}

/* 快捷操作按钮组 */
.quick-actions {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
}

.action-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-group-label {
  font-size: 12px;
  color: #8c8c8c;
  font-weight: 500;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.action-btn {
  padding: 4px 8px;
  height: auto;
  color: #1890ff;
  border-radius: 4px;
  transition: all 0.2s;
}

.action-btn:hover {
  background: #e6f7ff;
  color: #1890ff;
}

/* 内容区域 */
.content-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

/* 跟进输入框 */
.follow-input-section {
  margin-bottom: 16px;
}

.follow-input-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #fafafa;
  border-radius: 6px;
  cursor: pointer;
}

.follow-placeholder {
  color: #8c8c8c;
  font-size: 14px;
}

/* 标签页区域 */
.tabs-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.enhanced-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.enhanced-tabs .ant-tabs-content-holder {
  flex: 1;
  overflow: hidden;
}

.enhanced-tabs .ant-tabs-tabpane {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 标签页头部 */
.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.tab-filters {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  color: #8c8c8c;
  font-size: 14px;
}

.tab-filters .ant-btn.active {
  color: #1890ff;
  background: #e6f7ff;
}

.ai-analysis {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #1890ff;
  font-size: 13px;
  margin-left: 8px;
}

/* 活动时间轴 */
.activity-timeline {
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;
}

/* 快速跟进输入 */
.quick-follow-input {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px dashed #d9d9d9;
}

.follow-input-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.follow-textarea {
  border: none;
  box-shadow: none;
  background: transparent;
  resize: none;
}

.follow-textarea:focus {
  border: none;
  box-shadow: none;
}

.follow-input-actions {
  display: flex;
  justify-content: flex-end;
}

.timeline-item {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  position: relative;
}

.timeline-date {
  flex-shrink: 0;
  text-align: center;
  width: 50px;
}

.date-day {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  line-height: 1;
}

.date-month {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 2px;
}

.timeline-content {
  flex: 1;
  background: #fff;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  position: relative;
}

.timeline-content::before {
  content: '';
  position: absolute;
  left: -8px;
  top: 20px;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 8px solid #f0f0f0;
}

.timeline-content::after {
  content: '';
  position: absolute;
  left: -7px;
  top: 20px;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 8px solid #fff;
}

.timeline-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 12px;
}

.timeline-meta {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.meta-line-1 {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.meta-line-2 {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: space-between;
}

.user-name {
  font-weight: 500;
  color: #262626;
  font-size: 14px;
}

.timeline-time {
  color: #8c8c8c;
  font-size: 12px;
}

.ai-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  color: #1890ff;
  font-size: 11px;
  background: #e6f7ff;
  padding: 2px 6px;
  border-radius: 4px;
}

.timeline-body {
  margin-left: 44px;
}

.follow-content {
  color: #262626;
  line-height: 1.6;
  margin-bottom: 12px;
  white-space: pre-wrap;
}

.follow-attachments {
  margin-bottom: 12px;
}

.attachment-item {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: #f5f5f5;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
  margin-right: 8px;
  margin-bottom: 4px;
}

.follow-next-time {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #fa8c16;
  font-size: 13px;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: #fff7e6;
  border-radius: 4px;
  border-left: 3px solid #fa8c16;
}

.follow-actions {
  display: flex;
  gap: 16px;
}

.follow-actions .ant-btn {
  padding: 0;
  height: auto;
  color: #8c8c8c;
  font-size: 13px;
  transition: all 0.2s;
}

.follow-actions .ant-btn:hover {
  color: #1890ff;
}

.follow-actions .ant-btn.active {
  color: #1890ff;
}

.timeline-load-more {
  text-align: center;
  padding: 20px 0;
}

.timeline-load-more .ant-btn {
  color: #1890ff;
}

.timeline-end {
  text-align: center;
  color: #8c8c8c;
  font-size: 14px;
  padding: 20px 0;
}

.dynamic-timeline {
  margin-top: 16px;
}

.operation-content {
  color: #666;
  margin-bottom: 8px;
}

.operation-changes {
  background: #f8f9fa;
  border-radius: 4px;
  padding: 8px;
  margin-top: 8px;
}

.change-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.change-field {
  font-weight: 500;
  color: #666;
}

.change-old {
  color: #ff4d4f;
  text-decoration: line-through;
}

.change-arrow {
  color: #1890ff;
}

.change-new {
  color: #52c41a;
  font-weight: 500;
}

/* 详细资料标签页 */
.details-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
}

.detail-section {
  margin-bottom: 16px;
}

.detail-section:last-child {
  margin-bottom: 0;
}

.symptom-tags .ant-tag {
  margin: 4px 8px 4px 0;
}

.remark-content {
  line-height: 1.6;
  color: #262626;
  margin: 0;
}

/* 聊天记录标签页 */
.chat-content {
  flex: 1;
  overflow: hidden;
  padding: 16px 0;
}

.chat-container {
  height: 500px;
  display: flex;
  flex-direction: column;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 16px;
  background: #fafafa;
}

.chat-message {
  display: flex;
  margin-bottom: 16px;
  align-items: flex-start;
}

.chat-message.customer {
  flex-direction: row-reverse;
}

.chat-message.customer .message-content {
  margin-right: 8px;
  margin-left: 0;
}

.chat-message.staff .message-content {
  margin-left: 8px;
}

.message-avatar {
  flex-shrink: 0;
}

.message-content {
  max-width: 70%;
}

.message-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.chat-message.customer .message-header {
  justify-content: flex-end;
}

.sender-name {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.send-time {
  font-size: 11px;
  color: #999;
}

.message-body {
  background: #fff;
  padding: 8px 12px;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.chat-message.customer .message-body {
  background: #1890ff;
  color: #fff;
}

.text-message {
  line-height: 1.4;
  word-break: break-word;
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.chat-input {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

/* 附件标签页 */
.attachments-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
}

.attachment-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.upload-tip {
  color: #8c8c8c;
  font-size: 13px;
}

.attachment-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.attachment-item {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 12px;
  transition: all 0.2s;
}

.attachment-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.attachment-preview {
  width: 100%;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  border-radius: 6px;
  margin-bottom: 8px;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.file-icon {
  font-size: 32px;
  color: #8c8c8c;
}

.attachment-info {
  margin-bottom: 8px;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #8c8c8c;
}

.attachment-actions {
  display: flex;
  justify-content: center;
  gap: 8px;
}

/* 操作记录标签页 */
.operations-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
}

.operation-filters {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.operation-timeline {
  flex: 1;
}

.operation-item {
  margin-bottom: 8px;
}

.operation-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.operation-user {
  font-weight: 500;
  color: #262626;
}

.operation-action {
  color: #1890ff;
}

.operation-time {
  color: #8c8c8c;
  font-size: 12px;
  margin-left: auto;
}

.operation-content {
  color: #666;
  font-size: 13px;
  margin-bottom: 8px;
}

.operation-changes {
  background: #fafafa;
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 3px solid #1890ff;
}

.change-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
  font-size: 12px;
}

.change-item:last-child {
  margin-bottom: 0;
}

.change-field {
  color: #8c8c8c;
  min-width: 60px;
}

.change-old {
  color: #ff4d4f;
  text-decoration: line-through;
}

.change-arrow {
  color: #8c8c8c;
}

.change-new {
  color: #52c41a;
  font-weight: 500;
}

/* 打印记录标签页 - 暂时注释 */
/*
.prints-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
}

.print-header {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.print-history {
  flex: 1;
}
*/

/* 通用空状态 */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .lead-detail-enhanced-drawer {
    width: 90vw !important;
  }

  .info-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  }

  .attachment-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}

@media (max-width: 768px) {
  .lead-detail-enhanced-drawer {
    width: 100vw !important;
  }

  .header-section {
    padding: 16px 0;
  }

  .header-main {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .lead-title-area {
    width: 100%;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-start;
    flex-wrap: wrap;
  }

  .basic-info-compact {
    padding: 12px;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .quick-actions {
    flex-direction: column;
    gap: 16px;
  }

  .action-buttons {
    justify-content: flex-start;
  }

  .follow-input-header {
    padding: 8px 12px;
  }

  .tab-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .tab-filters {
    flex-wrap: wrap;
  }

  .timeline-item {
    gap: 8px;
  }

  .timeline-date {
    width: 35px;
  }

  .date-day {
    font-size: 16px;
  }

  .timeline-content {
    padding: 12px;
  }

  .timeline-header {
    gap: 8px;
  }

  .timeline-meta {
    gap: 2px;
  }

  .meta-line-1,
  .meta-line-2 {
    flex-wrap: wrap;
    gap: 4px;
  }

  .timeline-body {
    margin-left: 0;
  }

  .follow-actions {
    gap: 12px;
    flex-wrap: wrap;
  }

  .attachment-grid {
    grid-template-columns: 1fr;
  }

  .attachment-item {
    padding: 8px;
  }

  .attachment-preview {
    height: 100px;
  }

  .operation-header {
    flex-wrap: wrap;
    gap: 4px;
  }

  .operation-time {
    margin-left: 0;
    width: 100%;
    margin-top: 4px;
  }

  .change-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .change-field {
    min-width: auto;
  }

  .print-header {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .lead-name {
    font-size: 18px;
  }

  .lead-meta {
    gap: 4px;
  }

  .info-item {
    gap: 8px;
  }

  .info-content {
    gap: 2px;
  }

  .action-group-label {
    font-size: 11px;
  }

  .action-btn {
    font-size: 12px;
    padding: 2px 6px;
  }

  .timeline-date {
    width: 30px;
  }

  .date-day {
    font-size: 14px;
  }

  .date-month {
    font-size: 10px;
  }

  .timeline-content {
    padding: 8px;
  }

  .timeline-content::before,
  .timeline-content::after {
    display: none;
  }

  .user-name {
    font-size: 13px;
  }

  .timeline-time {
    font-size: 11px;
  }

  .follow-content {
    font-size: 13px;
  }

  .follow-actions .ant-btn {
    font-size: 12px;
  }

  .attachment-preview {
    height: 80px;
  }

  .file-icon {
    font-size: 24px;
  }

  .file-name {
    font-size: 13px;
  }

  .file-meta {
    font-size: 11px;
  }
}

/* 横屏适配 */
@media (max-height: 600px) and (orientation: landscape) {
  .header-section {
    padding: 12px 0;
  }

  .basic-info-compact {
    padding: 8px 12px;
  }

  .quick-actions {
    gap: 12px;
  }

  .timeline-content {
    padding: 8px;
  }

  .attachment-preview {
    height: 60px;
  }
}

/* 高分辨率屏幕优化 */
@media (min-width: 1400px) {
  .lead-detail-enhanced-drawer {
    width: 1200px !important;
  }

  .info-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .attachment-grid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  }
}

/* 打印样式 */
@media print {
  .header-actions,
  .quick-actions,
  .follow-input-section,
  .tab-header,
  .follow-actions,
  .attachment-actions,
  .print-header {
    display: none !important;
  }

  .lead-detail-enhanced-container {
    height: auto !important;
  }

  .content-section {
    flex: none !important;
  }

  .activity-timeline {
    overflow: visible !important;
  }

  .timeline-content {
    border: 1px solid #ddd !important;
    break-inside: avoid;
  }
}
</style>

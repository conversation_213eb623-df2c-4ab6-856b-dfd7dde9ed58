<template>
  <div class="ai-analysis-panel">
    <div class="ai-header">
      <div class="ai-title">
        <RobotOutlined class="ai-icon" />
        <span>AI员工销售分析</span>
        <a-tag color="blue" size="small">Beta</a-tag>
      </div>
      <a-button type="text" size="small" @click="refreshAnalysis" :loading="analyzing">
        <template #icon><ReloadOutlined /></template>
        刷新分析
      </a-button>
    </div>

    <div class="ai-content" v-if="analysisData">
      <!-- 客户意向分析 -->
      <div class="analysis-section">
        <h4 class="section-title">
          <HeartOutlined />
          客户意向分析
        </h4>
        <div class="intention-score">
          <div class="score-circle">
            <a-progress 
              type="circle" 
              :percent="analysisData.intentionScore" 
              :size="80"
              :stroke-color="getIntentionColor(analysisData.intentionScore)"
            />
          </div>
          <div class="score-desc">
            <div class="score-label">意向度评分</div>
            <div class="score-level">{{ getIntentionLevel(analysisData.intentionScore) }}</div>
          </div>
        </div>
        <div class="intention-factors">
          <div class="factor-item" v-for="factor in analysisData.intentionFactors" :key="factor.type">
            <span class="factor-label">{{ factor.label }}:</span>
            <a-tag :color="factor.positive ? 'green' : 'orange'" size="small">
              {{ factor.value }}
            </a-tag>
          </div>
        </div>
      </div>

      <!-- 沟通建议 -->
      <div class="analysis-section communication-suggestions">
        <h4 class="section-title">
          <BulbOutlined />
          沟通建议
        </h4>
        <div class="suggestions">
          <div class="suggestion-item" v-for="(suggestion, index) in analysisData.suggestions" :key="index">
            <div class="suggestion-icon">
              <CheckCircleOutlined v-if="suggestion.priority === 'high'" style="color: #52c41a;" />
              <ExclamationCircleOutlined v-else-if="suggestion.priority === 'medium'" style="color: #fa8c16;" />
              <InfoCircleOutlined v-else style="color: #1890ff;" />
            </div>
            <div class="suggestion-content">
              <div class="suggestion-title">{{ suggestion.title }}</div>
              <div class="suggestion-desc">{{ suggestion.description }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 关键词分析 -->
      <div class="analysis-section">
        <h4 class="section-title">
          <TagsOutlined />
          关键词分析
        </h4>
        <div class="keywords">
          <a-tag 
            v-for="keyword in analysisData.keywords" 
            :key="keyword.word"
            :color="getKeywordColor(keyword.sentiment)"
            class="keyword-tag"
          >
            {{ keyword.word }}
            <span class="keyword-count">({{ keyword.count }})</span>
          </a-tag>
        </div>
      </div>

      <!-- 下次跟进建议 */
      <div class="analysis-section">
        <h4 class="section-title">
          <ClockCircleOutlined />
          下次跟进建议
        </h4>
        <div class="follow-suggestion">
          <div class="follow-time">
            <CalendarOutlined />
            <span>建议时间：{{ analysisData.nextFollowTime }}</span>
          </div>
          <div class="follow-method">
            <PhoneOutlined />
            <span>建议方式：{{ analysisData.suggestedMethod }}</span>
          </div>
          <div class="follow-content">
            <MessageOutlined />
            <span>建议话术：{{ analysisData.suggestedScript }}</span>
          </div>
        </div>
      </div>

      <!-- 风险提醒 -->
      <div class="analysis-section" v-if="analysisData.risks && analysisData.risks.length">
        <h4 class="section-title">
          <WarningOutlined />
          风险提醒
        </h4>
        <div class="risks">
          <a-alert 
            v-for="risk in analysisData.risks" 
            :key="risk.type"
            :message="risk.title"
            :description="risk.description"
            :type="risk.level"
            show-icon
            class="risk-item"
          />
        </div>
      </div>
    </div>

    <div class="ai-loading" v-else-if="analyzing">
      <a-spin size="large">
        <template #indicator>
          <RobotOutlined style="font-size: 24px;" spin />
        </template>
      </a-spin>
      <div class="loading-text">AI正在分析中...</div>
    </div>

    <div class="ai-empty" v-else>
      <a-empty description="暂无分析数据">
        <a-button type="primary" @click="startAnalysis">
          <template #icon><RobotOutlined /></template>
          开始AI分析
        </a-button>
      </a-empty>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import {
  RobotOutlined,
  ReloadOutlined,
  HeartOutlined,
  BulbOutlined,
  TagsOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  CalendarOutlined,
  PhoneOutlined,
  MessageOutlined
} from '@ant-design/icons-vue';

// Props
const props = defineProps({
  leadData: {
    type: Object,
    required: true
  },
  followList: {
    type: Array,
    default: () => []
  },
  chatRecords: {
    type: Array,
    default: () => []
  }
});

// 响应式数据
const analyzing = ref(false);
const analysisData = ref(null);

// 方法
const startAnalysis = async () => {
  analyzing.value = true;
  try {
    console.log('开始AI分析，数据:', {
      leadData: props.leadData,
      followList: props.followList,
      chatRecords: props.chatRecords
    });

    // 检查数据是否存在
    if (!props.leadData) {
      throw new Error('缺少线索数据');
    }

    // 模拟AI分析过程
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 基于真实数据进行AI分析
    const analysisResult = analyzeRealData();
    analysisData.value = analysisResult;

    message.success('AI分析完成');
  } catch (error) {
    console.error('AI分析失败:', error);
    message.error(`AI分析失败：${error.message || '请重试'}`);
  } finally {
    analyzing.value = false;
  }
};

// 基于真实数据的AI分析方法
const analyzeRealData = () => {
  const followList = props.followList || [];
  const chatRecords = props.chatRecords || [];
  const leadData = props.leadData || {};

  // 分析跟进记录和聊天记录内容
  const followContent = followList.map(f => f.followContent || '').filter(content => content.trim());
  const chatContent = chatRecords.map(c => c.content || '').filter(content => content.trim());
  const allContent = [...followContent, ...chatContent];

  // 计算意向度评分
  const intentionScore = calculateIntentionScore(allContent, followList, leadData);

  // 分析意向因素
  const intentionFactors = analyzeIntentionFactors(followList, leadData);

  // 生成沟通建议
  const suggestions = generateSuggestions(allContent, followList, leadData);

  // 提取关键词
  const keywords = extractKeywords(allContent);

  // 分析风险
  const risks = analyzeRisks(allContent, followList, leadData);

  // 建议下次跟进时间和方式
  const followUpSuggestion = suggestNextFollowUp(followList, leadData);

  return {
    intentionScore,
    intentionFactors,
    suggestions,
    keywords,
    nextFollowTime: followUpSuggestion.time,
    suggestedMethod: followUpSuggestion.method,
    suggestedScript: followUpSuggestion.script,
    risks
  };
};

const refreshAnalysis = () => {
  if (analysisData.value) {
    startAnalysis();
  }
};

// 计算意向度评分
const calculateIntentionScore = (allContent, followList, leadData) => {
  let score = 50; // 基础分数

  // 根据跟进频率调整分数
  if (followList.length >= 5) score += 15;
  else if (followList.length >= 3) score += 10;
  else if (followList.length >= 1) score += 5;

  // 根据最近跟进时间调整分数
  if (followList.length > 0) {
    const lastFollow = followList[0];
    const daysSinceLastFollow = dayjs().diff(dayjs(lastFollow.createTime), 'day');
    if (daysSinceLastFollow <= 1) score += 10;
    else if (daysSinceLastFollow <= 3) score += 5;
    else if (daysSinceLastFollow > 7) score -= 10;
  }

  // 根据内容关键词调整分数
  const positiveKeywords = ['感兴趣', '考虑', '了解', '咨询', '预约', '到院'];
  const negativeKeywords = ['不需要', '太贵', '再说', '考虑考虑', '没时间'];

  allContent.forEach(content => {
    positiveKeywords.forEach(keyword => {
      if (content.includes(keyword)) score += 3;
    });
    negativeKeywords.forEach(keyword => {
      if (content.includes(keyword)) score -= 5;
    });
  });

  // 根据线索状态调整分数
  if (leadData.leadStatus === 'APPOINTMENT_CONFIRMED') score += 20;
  else if (leadData.leadStatus === 'FOLLOW_UP') score += 10;
  else if (leadData.leadStatus === 'LOST') score = Math.min(score, 30);

  return Math.max(0, Math.min(100, score));
};

// 分析意向因素
const analyzeIntentionFactors = (followList, leadData) => {
  const factors = [];

  // 响应速度分析
  const avgResponseTime = followList.length > 1 ? '及时' : '一般';
  factors.push({
    type: 'response',
    label: '响应速度',
    value: avgResponseTime,
    positive: avgResponseTime === '及时'
  });

  // 沟通频率分析
  const communicationFreq = followList.length >= 3 ? '频繁' : followList.length >= 1 ? '正常' : '较少';
  factors.push({
    type: 'communication',
    label: '沟通频率',
    value: communicationFreq,
    positive: communicationFreq !== '较少'
  });

  // 兴趣程度分析
  const interestLevel = leadData.leadStatus === 'APPOINTMENT_CONFIRMED' ? '很高' :
                       leadData.leadStatus === 'FOLLOW_UP' ? '中等' : '待确认';
  factors.push({
    type: 'interest',
    label: '兴趣程度',
    value: interestLevel,
    positive: interestLevel !== '待确认'
  });

  // 决策时间分析
  const decisionTime = followList.length >= 5 ? '较快' : '正常';
  factors.push({
    type: 'timeline',
    label: '决策速度',
    value: decisionTime,
    positive: decisionTime === '较快'
  });

  return factors;
};

// 生成沟通建议
const generateSuggestions = (allContent, followList, leadData) => {
  const suggestions = [];

  // 基于跟进频率的建议
  if (followList.length === 0) {
    suggestions.push({
      priority: 'high',
      title: '立即建立联系',
      description: '尚未有跟进记录，建议立即联系客户了解需求'
    });
  } else if (followList.length < 3) {
    suggestions.push({
      priority: 'medium',
      title: '增加跟进频率',
      description: '建议增加跟进频率，保持与客户的良好沟通'
    });
  }

  // 基于内容分析的建议
  const contentText = allContent.join(' ');
  if (contentText.includes('价格') || contentText.includes('费用')) {
    suggestions.push({
      priority: 'high',
      title: '重点关注价格敏感度',
      description: '客户对价格较为关注，建议准备多套价格方案'
    });
  }

  if (contentText.includes('效果') || contentText.includes('治疗')) {
    suggestions.push({
      priority: 'medium',
      title: '强调治疗效果',
      description: '客户关注治疗效果，可分享成功案例增强信心'
    });
  }

  // 基于线索状态的建议
  if (leadData.leadStatus === 'NEW') {
    suggestions.push({
      priority: 'high',
      title: '快速响应新线索',
      description: '新线索需要在24小时内完成首次联系'
    });
  }

  return suggestions.slice(0, 3); // 最多返回3个建议
};

// 提取关键词
const extractKeywords = (allContent) => {
  const keywords = [];
  const keywordMap = new Map();

  // 预定义的关键词及其情感倾向
  const predefinedKeywords = {
    '价格': 'negative',
    '费用': 'negative',
    '效果': 'positive',
    '治疗': 'positive',
    '考虑': 'neutral',
    '了解': 'positive',
    '咨询': 'positive',
    '预约': 'positive',
    '时间': 'neutral',
    '方便': 'positive'
  };

  allContent.forEach(content => {
    Object.keys(predefinedKeywords).forEach(keyword => {
      const count = (content.match(new RegExp(keyword, 'g')) || []).length;
      if (count > 0) {
        keywordMap.set(keyword, (keywordMap.get(keyword) || 0) + count);
      }
    });
  });

  keywordMap.forEach((count, word) => {
    keywords.push({
      word,
      count,
      sentiment: predefinedKeywords[word] || 'neutral'
    });
  });

  return keywords.sort((a, b) => b.count - a.count).slice(0, 6);
};

// 分析风险
const analyzeRisks = (allContent, followList, leadData) => {
  const risks = [];
  const contentText = allContent.join(' ');

  // 价格风险
  if (contentText.includes('太贵') || contentText.includes('价格高')) {
    risks.push({
      type: 'budget',
      level: 'warning',
      title: '价格敏感风险',
      description: '客户对价格较为敏感，存在因价格问题流失的风险'
    });
  }

  // 沟通风险
  if (followList.length > 0) {
    const lastFollow = followList[0];
    const daysSinceLastFollow = dayjs().diff(dayjs(lastFollow.createTime), 'day');
    if (daysSinceLastFollow > 7) {
      risks.push({
        type: 'communication',
        level: 'danger',
        title: '沟通中断风险',
        description: '超过一周未跟进，客户可能已失去兴趣'
      });
    }
  }

  // 竞争风险
  if (contentText.includes('其他医院') || contentText.includes('比较')) {
    risks.push({
      type: 'competition',
      level: 'warning',
      title: '竞争对手风险',
      description: '客户可能在比较多家医院，需要突出我们的优势'
    });
  }

  return risks;
};

// 建议下次跟进
const suggestNextFollowUp = (followList, leadData) => {
  const now = dayjs();
  let suggestedTime = '明天上午';
  let method = '电话沟通';
  let script = '您好，我是XX医院的客服，想了解一下您的身体情况...';

  if (followList.length === 0) {
    suggestedTime = '今天下午';
    method = '电话沟通';
    script = '您好，感谢您对我们医院的关注，我想了解一下您的具体需求...';
  } else if (followList.length >= 3) {
    suggestedTime = '3天后';
    method = '微信沟通';
    script = '您好，关于之前讨论的治疗方案，您还有什么疑问吗？';
  }

  return { time: suggestedTime, method, script };
};

const getIntentionColor = (score) => {
  if (score >= 80) return '#52c41a';
  if (score >= 60) return '#fa8c16';
  return '#ff4d4f';
};

const getIntentionLevel = (score) => {
  if (score >= 80) return '高意向';
  if (score >= 60) return '中等意向';
  if (score >= 40) return '低意向';
  return '无意向';
};

const getKeywordColor = (sentiment) => {
  const colorMap = {
    'positive': 'green',
    'negative': 'red',
    'neutral': 'blue'
  };
  return colorMap[sentiment] || 'default';
};

// 组件挂载时自动开始分析
onMounted(() => {
  if (props.followList.length > 0) {
    startAnalysis();
  }
});

// 暴露方法
defineExpose({
  startAnalysis,
  refreshAnalysis
});
</script>

<style scoped>
.ai-analysis-panel {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 16px;
  color: white;
  margin-bottom: 16px;
}

.ai-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.ai-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
}

.ai-icon {
  font-size: 18px;
  color: #fff;
}

.ai-content {
  color: #333;
}

.analysis-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
}

.analysis-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.intention-score {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 12px;
}

.score-desc {
  flex: 1;
}

.score-label {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 4px;
}

.score-level {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.intention-factors {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.factor-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
}

.factor-label {
  color: #666;
}

.suggestions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.suggestion-item {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.suggestion-icon {
  flex-shrink: 0;
  margin-top: 2px;
}

.suggestion-content {
  flex: 1;
}

.suggestion-title {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.suggestion-desc {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

.keywords {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.keyword-tag {
  margin: 0;
}

.keyword-count {
  font-size: 11px;
  opacity: 0.8;
}

.follow-suggestion {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.follow-time,
.follow-method,
.follow-content {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  font-size: 13px;
  color: #666;
}

.risks {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.risk-item {
  margin: 0;
}

.ai-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: white;
}

.loading-text {
  margin-top: 16px;
  font-size: 14px;
}

.ai-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.ai-empty .ant-empty {
  color: white;
}

.ai-empty .ant-empty-description {
  color: rgba(255, 255, 255, 0.8);
}
</style>

<template>
  <div class="lead-detail-test">
    <div class="test-header">
      <h2>线索详情增强组件测试</h2>
      <a-space>
        <a-button type="primary" @click="openEnhancedDetail">
          打开增强版详情
        </a-button>
        <a-button @click="openOriginalDetail">
          打开原版详情
        </a-button>
        <a-button @click="generateTestData">
          生成测试数据
        </a-button>
      </a-space>
    </div>

    <div class="test-content">
      <a-card title="测试数据" size="small">
        <a-descriptions :column="2" size="small">
          <a-descriptions-item label="线索ID">{{ testLeadData.leadId }}</a-descriptions-item>
          <a-descriptions-item label="客户姓名">{{ testLeadData.customerName }}</a-descriptions-item>
          <a-descriptions-item label="手机号码">{{ testLeadData.customerPhone }}</a-descriptions-item>
          <a-descriptions-item label="线索状态">{{ testLeadData.leadStatusName }}</a-descriptions-item>
          <a-descriptions-item label="负责人">{{ testLeadData.assignedEmployeeName }}</a-descriptions-item>
          <a-descriptions-item label="跟进记录数">{{ testFollowList.length }}</a-descriptions-item>
        </a-descriptions>
      </a-card>

      <a-card title="功能测试清单" size="small" style="margin-top: 16px;">
        <a-checkbox-group v-model:value="checkedFeatures" style="width: 100%;">
          <a-row>
            <a-col :span="8" v-for="feature in testFeatures" :key="feature.key">
              <a-checkbox :value="feature.key">{{ feature.label }}</a-checkbox>
            </a-col>
          </a-row>
        </a-checkbox-group>
      </a-card>

      <a-card title="性能监控" size="small" style="margin-top: 16px;">
        <a-descriptions :column="3" size="small">
          <a-descriptions-item label="组件加载时间">{{ performanceData.loadTime }}ms</a-descriptions-item>
          <a-descriptions-item label="数据渲染时间">{{ performanceData.renderTime }}ms</a-descriptions-item>
          <a-descriptions-item label="内存使用">{{ performanceData.memoryUsage }}MB</a-descriptions-item>
        </a-descriptions>
      </a-card>
    </div>

    <!-- 增强版线索详情 -->
    <LeadDetailEnhanced
      v-model:visible="enhancedVisible"
      :lead-data="testLeadData"
      @refresh="handleRefresh"
    />

    <!-- 原版线索详情（用于对比） -->
    <LeadDetailDrawer
      v-model:visible="originalVisible"
      :lead-data="testLeadData"
      @refresh="handleRefresh"
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { message } from 'ant-design-vue';
import LeadDetailEnhanced from './lead-detail-enhanced.vue';
import LeadDetailDrawer from './lead-detail-drawer.vue';

// 响应式数据
const enhancedVisible = ref(false);
const originalVisible = ref(false);
const checkedFeatures = ref([]);

// 测试数据
const testLeadData = reactive({
  leadId: 'L20240731001',
  customerName: '张三',
  customerPhone: '13800138000',
  customerEmail: '<EMAIL>',
  customerAddress: '北京市朝阳区xxx街道xxx号',
  customerAge: 35,
  genderName: '男',
  leadSource: 1,
  leadSourceName: '网络推广',
  leadStatus: 2,
  leadStatusName: '跟进中',
  leadQuality: 3,
  leadQualityName: '高质量',
  assignedEmployeeName: '李销售',
  symptom: '头痛,失眠,焦虑',
  remark: '客户对我们的产品很感兴趣，预算充足，决策周期较短。',
  createTime: '2024-07-30 10:30:00',
  updateTime: '2024-07-31 14:20:00',
  // 工商信息
  companyName: '北京某某科技有限公司',
  creditCode: '91110000123456789X',
  legalPerson: '张三',
  registeredCapital: '1000万元',
  establishDate: '2020-01-15',
  companyStatus: '存续',
  industry: '软件和信息技术服务业',
  companyType: '有限责任公司',
  companyAddress: '北京市朝阳区xxx科技园',
  companyPhone: '010-12345678',
  companyEmail: '<EMAIL>',
  companyWebsite: 'https://www.company.com',
  businessScope: '技术开发、技术推广、技术转让、技术咨询、技术服务；销售自行开发的产品；计算机系统服务；应用软件服务；软件开发；软件咨询；模型设计；包装装潢设计；教育咨询（中介服务除外）；经济贸易咨询；文化咨询；体育咨询；公共关系服务；会议服务；投资咨询；企业策划、设计；设计、制作、代理、发布广告；市场调查；企业管理咨询；组织文化艺术交流活动（不含营业性演出）；文艺创作；承办展览展示活动；会议服务；影视策划；翻译服务。'
});

const testFollowList = ref([
  {
    id: 1,
    followType: 1,
    followTypeName: '电话沟通',
    followMethod: 1,
    followMethodName: '主动外呼',
    content: '与客户进行了初步沟通，了解了基本需求。客户对产品表现出浓厚兴趣，询问了价格和实施周期。',
    nextFollowTime: '2024-08-01 14:00:00',
    createTime: '2024-07-31 10:30:00',
    employeeName: '李销售',
    employeeAvatar: '/avatar1.jpg'
  },
  {
    id: 2,
    followType: 2,
    followTypeName: '微信沟通',
    followMethod: 2,
    followMethodName: '客户主动',
    content: '客户通过微信发来了详细的需求文档，包含了具体的功能要求和预算范围。',
    nextFollowTime: '2024-08-02 09:00:00',
    createTime: '2024-07-30 16:45:00',
    employeeName: '李销售',
    employeeAvatar: '/avatar1.jpg'
  }
]);

// 测试功能清单
const testFeatures = [
  { key: 'header', label: '头部信息展示' },
  { key: 'basicInfo', label: '基本信息卡片' },
  { key: 'quickActions', label: '快捷操作按钮' },
  { key: 'timeline', label: '跟进记录时间轴' },
  { key: 'quickFollow', label: '快速跟进输入' },
  { key: 'aiAnalysis', label: 'AI分析功能' },
  { key: 'detailsTab', label: '详细资料标签页' },
  { key: 'businessTab', label: '工商信息标签页' },
  { key: 'attachmentsTab', label: '附件标签页' },
  { key: 'operationsTab', label: '操作记录标签页' },
  { key: 'printsTab', label: '打印记录标签页' },
  { key: 'responsive', label: '响应式布局' },
  { key: 'performance', label: '加载性能' },
  { key: 'interaction', label: '交互流畅度' },
  { key: 'dataUpdate', label: '数据更新机制' }
];

// 性能监控数据
const performanceData = reactive({
  loadTime: 0,
  renderTime: 0,
  memoryUsage: 0
});

// 方法
const openEnhancedDetail = () => {
  const startTime = performance.now();
  enhancedVisible.value = true;
  
  // 模拟性能监控
  setTimeout(() => {
    performanceData.loadTime = Math.round(performance.now() - startTime);
    performanceData.renderTime = Math.round(Math.random() * 50 + 20);
    performanceData.memoryUsage = Math.round(Math.random() * 10 + 15);
  }, 100);
};

const openOriginalDetail = () => {
  originalVisible.value = true;
};

const generateTestData = () => {
  // 生成随机测试数据
  const names = ['张三', '李四', '王五', '赵六', '钱七'];
  const companies = ['科技有限公司', '贸易有限公司', '咨询有限公司', '服务有限公司'];
  const symptoms = ['头痛', '失眠', '焦虑', '抑郁', '疲劳'];
  
  testLeadData.customerName = names[Math.floor(Math.random() * names.length)];
  testLeadData.customerPhone = '138' + Math.floor(Math.random() * *********).toString().padStart(8, '0');
  testLeadData.companyName = '北京某某' + companies[Math.floor(Math.random() * companies.length)];
  testLeadData.symptom = symptoms.slice(0, Math.floor(Math.random() * 3) + 1).join(',');
  
  // 生成随机跟进记录
  const followCount = Math.floor(Math.random() * 5) + 2;
  testFollowList.value = Array.from({ length: followCount }, (_, index) => ({
    id: index + 1,
    followType: Math.floor(Math.random() * 3) + 1,
    followTypeName: ['电话沟通', '微信沟通', '面谈'][Math.floor(Math.random() * 3)],
    followMethod: Math.floor(Math.random() * 2) + 1,
    followMethodName: ['主动外呼', '客户主动'][Math.floor(Math.random() * 2)],
    content: `第${index + 1}次跟进记录，客户反馈良好，继续跟进中...`,
    nextFollowTime: new Date(Date.now() + (index + 1) * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),
    createTime: new Date(Date.now() - (followCount - index) * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),
    employeeName: '李销售',
    employeeAvatar: '/avatar1.jpg'
  }));
  
  message.success('测试数据已生成');
};

const handleRefresh = () => {
  message.success('数据已刷新');
};

// 初始化
generateTestData();
</script>

<style scoped>
.lead-detail-test {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-header h2 {
  margin: 0;
  color: #262626;
}

.test-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ant-checkbox-group .ant-col {
  margin-bottom: 8px;
}

@media (max-width: 768px) {
  .lead-detail-test {
    padding: 16px;
  }
  
  .test-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .test-header h2 {
    font-size: 18px;
  }
}
</style>

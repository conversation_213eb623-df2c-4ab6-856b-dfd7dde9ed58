# AI 分析流程完整文档

## 概述

AI 分析系统是线索详情增强版的核心功能之一，通过分析线索数据、跟进记录和聊天记录，为销售人员提供智能化的客户意向分析、沟通建议和风险预警。

## 1. 数据输入

### 1.1 输入数据源
- **leadData**: 线索基础数据
  - 客户基本信息（姓名、电话、年龄等）
  - 线索状态（NEW/FOLLOW_UP/APPOINTMENT_CONFIRMED/LOST等）
  - 线索质量等级（A级/B级/C级）
  - 创建时间、负责人等

- **followList**: 跟进记录列表
  - 跟进内容（followContent）
  - 跟进时间（createTime）
  - 跟进人员信息

- **chatRecords**: 聊天记录列表
  - 聊天内容（content）
  - 聊天时间
  - 发送方信息

### 1.2 数据预处理
```javascript
// 提取所有文本内容
const followContent = followList.map(f => f.followContent || '').filter(content => content.trim());
const chatContent = chatRecords.map(c => c.content || '').filter(content => content.trim());
const allContent = [...followContent, ...chatContent];
```

## 2. 核心分析算法

### 2.1 意向度评分算法 (calculateIntentionScore)

**基础评分机制：**
- 起始分数：50分
- 最终分数范围：0-100分

**评分因子：**

1. **跟进频率加分**
   - ≥5次跟进：+15分
   - ≥3次跟进：+10分
   - ≥1次跟进：+5分

2. **最近跟进时间加分**
   - 1天内：+10分
   - 3天内：+5分
   - 超过7天：-10分

3. **内容关键词分析**
   - 正面关键词（每个+3分）：感兴趣、考虑、了解、咨询、预约、到院
   - 负面关键词（每个-5分）：不需要、太贵、再说、考虑考虑、没时间

4. **线索状态加分**
   - APPOINTMENT_CONFIRMED（预约确认）：+20分
   - FOLLOW_UP（跟进中）：+10分
   - LOST（流失）：最高30分

**算法实现：**
```javascript
const calculateIntentionScore = (allContent, followList, leadData) => {
  let score = 50; // 基础分数
  
  // 跟进频率评分
  if (followList.length >= 5) score += 15;
  else if (followList.length >= 3) score += 10;
  else if (followList.length >= 1) score += 5;
  
  // 时间因子评分
  if (followList.length > 0) {
    const daysSinceLastFollow = dayjs().diff(dayjs(lastFollow.createTime), 'day');
    if (daysSinceLastFollow <= 1) score += 10;
    else if (daysSinceLastFollow <= 3) score += 5;
    else if (daysSinceLastFollow > 7) score -= 10;
  }
  
  // 关键词评分
  // ... 详细实现见源码
  
  return Math.max(0, Math.min(100, score));
};
```

### 2.2 意向因素分析 (analyzeIntentionFactors)

**分析维度：**

1. **响应速度**
   - 及时：跟进记录 > 1次
   - 一般：跟进记录 ≤ 1次

2. **沟通频率**
   - 频繁：≥3次跟进
   - 正常：≥1次跟进
   - 较少：0次跟进

3. **兴趣程度**
   - 很高：APPOINTMENT_CONFIRMED状态
   - 中等：FOLLOW_UP状态
   - 待确认：其他状态

4. **决策速度**
   - 较快：≥5次跟进
   - 正常：<5次跟进

### 2.3 沟通建议生成 (generateSuggestions)

**建议类型：**

1. **基于跟进频率的建议**
   - 无跟进记录：立即建立联系（高优先级）
   - 跟进次数<3：增加跟进频率（中优先级）

2. **基于内容分析的建议**
   - 包含"价格"/"费用"：重点关注价格敏感度（高优先级）
   - 包含"效果"/"治疗"：强调治疗效果（中优先级）

3. **基于线索状态的建议**
   - NEW状态：快速响应新线索（高优先级）

**优先级分类：**
- high：红色，需要立即处理
- medium：橙色，重要但不紧急
- low：蓝色，一般关注

### 2.4 关键词提取算法 (extractKeywords)

**预定义关键词库：**
```javascript
const predefinedKeywords = {
  '价格': 'negative',    // 价格敏感
  '费用': 'negative',    // 成本关注
  '效果': 'positive',    // 效果关注
  '治疗': 'positive',    // 治疗意向
  '考虑': 'neutral',     // 中性态度
  '了解': 'positive',    // 积极了解
  '咨询': 'positive',    // 主动咨询
  '预约': 'positive',    // 预约意向
  '时间': 'neutral',     // 时间安排
  '方便': 'positive'     // 配合度高
};
```

**提取流程：**
1. 遍历所有文本内容
2. 统计预定义关键词出现频次
3. 按频次降序排列
4. 返回前6个高频关键词

### 2.5 风险分析算法 (analyzeRisks)

**风险类型：**

1. **价格敏感风险**
   - 触发条件：内容包含"太贵"或"价格高"
   - 风险等级：warning（警告）
   - 描述：客户对价格较为敏感，存在因价格问题流失的风险

2. **沟通中断风险**
   - 触发条件：超过7天未跟进
   - 风险等级：danger（危险）
   - 描述：超过一周未跟进，客户可能已失去兴趣

3. **竞争对手风险**
   - 触发条件：内容包含"其他医院"或"比较"
   - 风险等级：warning（警告）
   - 描述：客户可能在比较多家医院，需要突出我们的优势

### 2.6 下次跟进建议算法 (suggestNextFollowUp)

**建议逻辑：**

1. **无跟进记录**
   - 时间：今天下午
   - 方式：电话沟通
   - 话术：您好，感谢您对我们医院的关注，我想了解一下您的具体需求...

2. **跟进次数≥3次**
   - 时间：3天后
   - 方式：微信沟通
   - 话术：您好，关于之前讨论的治疗方案，您还有什么疑问吗？

3. **默认情况**
   - 时间：明天上午
   - 方式：电话沟通
   - 话术：您好，我是XX医院的客服，想了解一下您的身体情况...

## 3. 分析结果输出

### 3.1 意向度评分显示
- 圆形进度条显示分数（0-100）
- 颜色编码：
  - ≥80分：绿色（高意向）
  - ≥60分：橙色（中等意向）
  - ≥40分：红色（低意向）
  - <40分：红色（无意向）

### 3.2 意向因素标签
- 绿色标签：正面因素
- 橙色标签：负面因素
- 显示具体评估结果

### 3.3 沟通建议列表
- 图标区分优先级
- 标题+详细描述
- 最多显示3条建议

### 3.4 关键词云
- 颜色编码情感倾向
- 显示出现频次
- 最多显示6个关键词

### 3.5 下次跟进建议
- 建议时间
- 建议方式
- 建议话术

### 3.6 风险提醒
- Alert组件显示
- 不同风险等级对应不同颜色
- 风险标题+详细描述

## 4. 技术实现

### 4.1 组件架构
- **ai-analysis-panel.vue**: 主分析面板组件
- **lead-detail-enhanced.vue**: 集成AI分析功能

### 4.2 数据流
1. 父组件传递数据（leadData, followList, chatRecords）
2. AI分析面板接收数据并触发分析
3. 执行各项分析算法
4. 渲染分析结果

### 4.3 性能优化
- 模拟2秒分析时间，提升用户体验
- 组件挂载时自动分析（如有跟进记录）
- 支持手动刷新分析

## 5. 使用场景

### 5.1 销售人员使用
- 快速了解客户意向程度
- 获取针对性沟通建议
- 识别潜在风险点
- 优化跟进策略

### 5.2 管理人员使用
- 评估销售团队工作质量
- 识别高价值客户
- 制定培训计划
- 优化销售流程

## 6. 扩展性

### 6.1 算法优化
- 可调整评分权重
- 增加新的分析维度
- 优化关键词库
- 引入机器学习模型

### 6.2 功能扩展
- 增加行业对比分析
- 添加客户画像分析
- 集成外部数据源
- 支持自定义分析规则

## 7. 注意事项

### 7.1 数据质量
- 确保跟进记录内容完整
- 聊天记录格式规范
- 线索状态及时更新

### 7.2 算法局限性
- 基于规则的分析，非机器学习
- 依赖预定义关键词库
- 需要足够的历史数据支撑

### 7.3 隐私保护
- 客户数据加密存储
- 分析结果仅内部使用
- 遵循数据保护法规

/**
 * 待办事项 API
 *
 * <AUTHOR>
 * @Date 2025-08-01 15:00:00
 * @Wechat zhuoda1024
 * @Email <EMAIL>
 * @Copyright 1024创新实验室 （ https://1024lab.net ），Since 2012
 */

import { postRequest, getRequest } from '/@/lib/axios'

export const todoApi = {
  /**
   * 分页查询待办事项
   */
  queryPage: (params: any) => postRequest('/api/hospital/todo/queryPage', params),

  /**
   * 获取待办详情
   */
  getDetail: (todoId: number) => getRequest(`/api/hospital/todo/detail/${todoId}`),

  /**
   * 添加待办事项
   */
  add: (params: any) => postRequest('/api/hospital/todo/add', params),

  /**
   * 更新待办事项
   */
  update: (params: any) => postRequest('/api/hospital/todo/update', params),

  /**
   * 删除待办事项
   */
  delete: (todoId: number) => postRequest(`/api/hospital/todo/delete/${todoId}`),

  /**
   * 待办操作（开始、暂停、完成、取消、重新分配）
   */
  operation: (params: any) => postRequest('/api/hospital/todo/operation', params),

  /**
   * 获取今日待办事项
   */
  getTodayTodos: () => getRequest('/api/hospital/todo/today'),

  /**
   * 获取我的今日待办事项
   */
  getMyTodayTodos: () => getRequest('/api/hospital/todo/my/today'),

  /**
   * 获取逾期待办事项
   */
  getOverdueTodos: () => getRequest('/api/hospital/todo/overdue'),

  /**
   * 获取我的逾期待办事项
   */
  getMyOverdueTodos: () => getRequest('/api/hospital/todo/my/overdue'),

  /**
   * 获取高优先级待办事项
   */
  getHighPriorityTodos: () => getRequest('/api/hospital/todo/high-priority'),

  /**
   * 获取我的高优先级待办事项
   */
  getMyHighPriorityTodos: () => getRequest('/api/hospital/todo/my/high-priority'),

  /**
   * 批量更新待办状态
   */
  batchUpdateStatus: (params: any) => postRequest('/api/hospital/todo/batch/updateStatus', params)
}

// 待办类型枚举
export const TodoTypeEnum = {
  LEAD_FOLLOW: 'lead_follow',
  APPOINTMENT_REMIND: 'appointment_remind',
  VISIT_CONFIRM: 'visit_confirm',
  DIAGNOSIS_PENDING: 'diagnosis_pending',
  PRESCRIPTION_PENDING: 'prescription_pending',
  FOLLOW_UP_REMIND: 'follow_up_remind',
  TREATMENT_FOLLOW: 'treatment_follow',
  SATISFACTION_SURVEY: 'satisfaction_survey',
  PAYMENT_REMIND: 'payment_remind',
  INSURANCE_PROCESS: 'insurance_process'
}

// 待办类型描述映射
export const TodoTypeDescMap = {
  [TodoTypeEnum.LEAD_FOLLOW]: '线索跟进',
  [TodoTypeEnum.APPOINTMENT_REMIND]: '预约提醒',
  [TodoTypeEnum.VISIT_CONFIRM]: '到诊确认',
  [TodoTypeEnum.DIAGNOSIS_PENDING]: '待诊断',
  [TodoTypeEnum.PRESCRIPTION_PENDING]: '待开药',
  [TodoTypeEnum.FOLLOW_UP_REMIND]: '回访提醒',
  [TodoTypeEnum.TREATMENT_FOLLOW]: '治疗跟进',
  [TodoTypeEnum.SATISFACTION_SURVEY]: '满意度调查',
  [TodoTypeEnum.PAYMENT_REMIND]: '付款提醒',
  [TodoTypeEnum.INSURANCE_PROCESS]: '保险处理'
}

// 待办状态枚举
export const TodoStatusEnum = {
  PENDING: 1,      // 待处理
  PROCESSING: 2,   // 处理中
  COMPLETED: 3,    // 已完成
  CANCELLED: 4,    // 已取消
  EXPIRED: 5       // 已逾期
}

// 待办状态描述映射
export const TodoStatusDescMap = {
  [TodoStatusEnum.PENDING]: '待处理',
  [TodoStatusEnum.PROCESSING]: '处理中',
  [TodoStatusEnum.COMPLETED]: '已完成',
  [TodoStatusEnum.CANCELLED]: '已取消',
  [TodoStatusEnum.EXPIRED]: '已逾期'
}

// 优先级枚举
export const TodoPriorityEnum = {
  HIGH: 1,    // 高
  MEDIUM: 2,  // 中
  LOW: 3      // 低
}

// 优先级描述映射
export const TodoPriorityDescMap = {
  [TodoPriorityEnum.HIGH]: '高',
  [TodoPriorityEnum.MEDIUM]: '中',
  [TodoPriorityEnum.LOW]: '低'
}

// 优先级颜色映射
export const TodoPriorityColorMap = {
  [TodoPriorityEnum.HIGH]: 'red',
  [TodoPriorityEnum.MEDIUM]: 'orange',
  [TodoPriorityEnum.LOW]: 'blue'
}

// 待办表单接口
export interface TodoFormData {
  todoId?: number
  todoTitle: string
  todoContent?: string
  todoType: string
  priorityLevel: number
  urgencyLevel?: number
  businessType?: string
  businessId?: number
  customerName?: string
  customerPhone?: string
  dueTime?: string
  remindTime?: string
  estimatedDuration?: number
  assignedEmployeeId?: number
  assignedEmployeeName?: string
  assignedDepartmentId?: number
  assignedDepartmentName?: string
  tags?: string
  attachments?: string
  remarks?: string
}

// 待办查询表单接口
export interface TodoQueryForm {
  pageNum: number
  pageSize: number
  todoTitle?: string
  todoType?: string
  todoStatus?: number
  priorityLevel?: number
  urgencyLevel?: number
  businessType?: string
  businessId?: number
  customerName?: string
  customerPhone?: string
  assignedEmployeeId?: number
  assignedDepartmentId?: number
  dueTimeBegin?: string
  dueTimeEnd?: string
  createTimeBegin?: string
  createTimeEnd?: string
  isOverdue?: boolean
  autoGenerated?: boolean
  tags?: string
}

// 待办操作表单接口
export interface TodoOperationForm {
  todoId: number
  operationType: 'start' | 'pause' | 'complete' | 'cancel' | 'reassign'
  completionNote?: string
  cancelReason?: string
  newAssigneeId?: number
  newAssigneeName?: string
  newDepartmentId?: number
  newDepartmentName?: string
}

// 待办VO接口
export interface TodoVO {
  todoId: number
  todoNo: string
  todoTitle: string
  todoContent?: string
  todoType: string
  todoTypeDesc: string
  todoStatus: number
  todoStatusDesc: string
  priorityLevel: number
  priorityLevelDesc: string
  priorityColor: string
  urgencyLevel?: number
  businessType?: string
  businessId?: number
  customerName?: string
  customerPhone?: string
  dueTime?: string
  remindTime?: string
  estimatedDuration?: number
  assignedEmployeeId?: number
  assignedEmployeeName?: string
  assignedDepartmentId?: number
  assignedDepartmentName?: string
  startTime?: string
  completeTime?: string
  completionNote?: string
  cancelReason?: string
  autoGenerated: boolean
  generationRule?: string
  tags?: string
  attachments?: string
  remarks?: string
  isOverdue: boolean
  overdueDays: number
  remainingHours: number
  processingDuration?: number
  availableActions: string[]
  createTime: string
  createUserId?: number
  updateTime?: string
  updateUserId?: number
}

<template>
  <div class="todo-dashboard">
    <!-- 统计卡片 -->
    <a-row :gutter="16" class="stats-row">
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic
            title="今日待办"
            :value="todayTodos.length"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix>
              <ClockCircleOutlined />
            </template>
          </a-statistic>
          <div class="stat-footer">
            <a-button type="link" size="small" @click="viewTodayTodos">查看详情</a-button>
          </div>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic
            title="逾期待办"
            :value="overdueTodos.length"
            :value-style="{ color: '#ff4d4f' }"
          >
            <template #prefix>
              <ExclamationCircleOutlined />
            </template>
          </a-statistic>
          <div class="stat-footer">
            <a-button type="link" size="small" @click="viewOverdueTodos">查看详情</a-button>
          </div>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic
            title="高优先级"
            :value="highPriorityTodos.length"
            :value-style="{ color: '#fa8c16' }"
          >
            <template #prefix>
              <WarningOutlined />
            </template>
          </a-statistic>
          <div class="stat-footer">
            <a-button type="link" size="small" @click="viewHighPriorityTodos">查看详情</a-button>
          </div>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic
            title="处理中"
            :value="processingCount"
            :value-style="{ color: '#52c41a' }"
          >
            <template #prefix>
              <SyncOutlined />
            </template>
          </a-statistic>
          <div class="stat-footer">
            <a-button type="link" size="small" @click="viewProcessingTodos">查看详情</a-button>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 快速操作 -->
    <a-card title="快速操作" size="small" class="quick-actions-card">
      <a-space size="large">
        <a-button type="primary" @click="onAddTodo">
          <template #icon><PlusOutlined /></template>
          新增待办
        </a-button>
        <a-button @click="onRefreshAll">
          <template #icon><ReloadOutlined /></template>
          刷新数据
        </a-button>
        <a-button @click="goToTodoList">
          <template #icon><UnorderedListOutlined /></template>
          待办列表
        </a-button>
      </a-space>
    </a-card>

    <!-- 今日待办 -->
    <a-card title="今日待办" size="small" class="todo-section-card">
      <template #extra>
        <a-space>
          <a-button type="link" size="small" @click="viewTodayTodos">查看全部</a-button>
          <a-button type="link" size="small" @click="loadTodayTodos">
            <ReloadOutlined />
          </a-button>
        </a-space>
      </template>
      
      <div v-if="todayTodosLoading" class="loading-container">
        <a-spin />
      </div>
      
      <div v-else-if="todayTodos.length === 0" class="empty-container">
        <a-empty description="今日暂无待办事项" />
      </div>
      
      <a-list v-else :data-source="todayTodos.slice(0, 5)" item-layout="horizontal">
        <template #renderItem="{ item }">
          <a-list-item>
            <template #actions>
              <a-space>
                <a-button v-if="item.availableActions.includes('start')" type="link" size="small" @click="onStart(item)">
                  开始
                </a-button>
                <a-button v-if="item.availableActions.includes('complete')" type="link" size="small" @click="onComplete(item)">
                  完成
                </a-button>
                <a-button type="link" size="small" @click="onView(item)">查看</a-button>
              </a-space>
            </template>
            
            <a-list-item-meta>
              <template #title>
                <a-space>
                  <span>{{ item.todoTitle }}</span>
                  <a-tag :color="item.priorityColor" size="small">{{ item.priorityLevelDesc }}</a-tag>
                  <a-tag size="small">{{ item.todoTypeDesc }}</a-tag>
                </a-space>
              </template>
              <template #description>
                <div class="todo-description">
                  <div>{{ item.todoContent || '无描述' }}</div>
                  <div class="todo-meta">
                    <span>分配给: {{ item.assignedEmployeeName }}</span>
                    <span v-if="item.dueTime">
                      截止: {{ dayjs(item.dueTime).format('HH:mm') }}
                    </span>
                    <span v-if="item.customerName">客户: {{ item.customerName }}</span>
                  </div>
                </div>
              </template>
            </a-list-item-meta>
          </a-list-item>
        </template>
      </a-list>
    </a-card>

    <!-- 逾期待办 -->
    <a-card v-if="overdueTodos.length > 0" title="逾期待办" size="small" class="todo-section-card overdue-card">
      <template #extra>
        <a-space>
          <a-button type="link" size="small" @click="viewOverdueTodos">查看全部</a-button>
          <a-button type="link" size="small" @click="loadOverdueTodos">
            <ReloadOutlined />
          </a-button>
        </a-space>
      </template>
      
      <a-list :data-source="overdueTodos.slice(0, 3)" item-layout="horizontal">
        <template #renderItem="{ item }">
          <a-list-item>
            <template #actions>
              <a-space>
                <a-button v-if="item.availableActions.includes('start')" type="link" size="small" @click="onStart(item)">
                  开始
                </a-button>
                <a-button v-if="item.availableActions.includes('complete')" type="link" size="small" @click="onComplete(item)">
                  完成
                </a-button>
                <a-button type="link" size="small" @click="onView(item)">查看</a-button>
              </a-space>
            </template>
            
            <a-list-item-meta>
              <template #title>
                <a-space>
                  <span>{{ item.todoTitle }}</span>
                  <a-tag color="red" size="small">逾期{{ item.overdueDays }}天</a-tag>
                  <a-tag :color="item.priorityColor" size="small">{{ item.priorityLevelDesc }}</a-tag>
                </a-space>
              </template>
              <template #description>
                <div class="todo-description">
                  <div>{{ item.todoContent || '无描述' }}</div>
                  <div class="todo-meta">
                    <span>分配给: {{ item.assignedEmployeeName }}</span>
                    <span class="overdue-time">
                      截止: {{ dayjs(item.dueTime).format('MM-DD HH:mm') }}
                    </span>
                    <span v-if="item.customerName">客户: {{ item.customerName }}</span>
                  </div>
                </div>
              </template>
            </a-list-item-meta>
          </a-list-item>
        </template>
      </a-list>
    </a-card>

    <!-- 待办详情抽屉 -->
    <TodoDetailDrawer
      v-model:visible="detailDrawerVisible"
      :todo-id="currentTodoId"
      @refresh="onRefreshAll"
    />

    <!-- 待办表单抽屉 -->
    <TodoFormDrawer
      v-model:visible="formDrawerVisible"
      :todo-id="currentTodoId"
      @refresh="onRefreshAll"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  WarningOutlined,
  SyncOutlined,
  PlusOutlined,
  ReloadOutlined,
  UnorderedListOutlined
} from '@ant-design/icons-vue'
import { todoApi, type TodoVO } from '/@/api/business/hospital/todo-api'
import TodoDetailDrawer from './components/TodoDetailDrawer.vue'
import TodoFormDrawer from './components/TodoFormDrawer.vue'

const router = useRouter()

// 数据状态
const todayTodos = ref<TodoVO[]>([])
const overdueTodos = ref<TodoVO[]>([])
const highPriorityTodos = ref<TodoVO[]>([])
const todayTodosLoading = ref(false)

// 抽屉状态
const detailDrawerVisible = ref(false)
const formDrawerVisible = ref(false)
const currentTodoId = ref<number | null>(null)

// 计算属性
const processingCount = computed(() => {
  return todayTodos.value.filter(todo => todo.todoStatus === 2).length
})

// 加载今日待办
const loadTodayTodos = async () => {
  try {
    todayTodosLoading.value = true
    const { data } = await todoApi.getMyTodayTodos()
    todayTodos.value = data
  } catch (error) {
    console.error('加载今日待办失败:', error)
  } finally {
    todayTodosLoading.value = false
  }
}

// 加载逾期待办
const loadOverdueTodos = async () => {
  try {
    const { data } = await todoApi.getMyOverdueTodos()
    overdueTodos.value = data
  } catch (error) {
    console.error('加载逾期待办失败:', error)
  }
}

// 加载高优先级待办
const loadHighPriorityTodos = async () => {
  try {
    const { data } = await todoApi.getMyHighPriorityTodos()
    highPriorityTodos.value = data
  } catch (error) {
    console.error('加载高优先级待办失败:', error)
  }
}

// 刷新所有数据
const onRefreshAll = async () => {
  await Promise.all([
    loadTodayTodos(),
    loadOverdueTodos(),
    loadHighPriorityTodos()
  ])
}

// 事件处理
const onAddTodo = () => {
  currentTodoId.value = null
  formDrawerVisible.value = true
}

const onView = (todo: TodoVO) => {
  currentTodoId.value = todo.todoId
  detailDrawerVisible.value = true
}

const onStart = async (todo: TodoVO) => {
  try {
    await todoApi.operation({
      todoId: todo.todoId,
      operationType: 'start'
    })
    message.success('开始处理成功')
    onRefreshAll()
  } catch (error) {
    console.error('开始处理失败:', error)
  }
}

const onComplete = (todo: TodoVO) => {
  Modal.confirm({
    title: '确认完成',
    content: `确定要完成待办"${todo.todoTitle}"吗？`,
    onOk: async () => {
      try {
        await todoApi.operation({
          todoId: todo.todoId,
          operationType: 'complete'
        })
        message.success('完成待办成功')
        onRefreshAll()
      } catch (error) {
        console.error('完成待办失败:', error)
      }
    }
  })
}

// 导航方法
const goToTodoList = () => {
  router.push('/business/hospital/todo/list')
}

const viewTodayTodos = () => {
  router.push({
    path: '/business/hospital/todo/list',
    query: { filter: 'today' }
  })
}

const viewOverdueTodos = () => {
  router.push({
    path: '/business/hospital/todo/list',
    query: { filter: 'overdue' }
  })
}

const viewHighPriorityTodos = () => {
  router.push({
    path: '/business/hospital/todo/list',
    query: { filter: 'high-priority' }
  })
}

const viewProcessingTodos = () => {
  router.push({
    path: '/business/hospital/todo/list',
    query: { filter: 'processing' }
  })
}

// 初始化
onMounted(() => {
  onRefreshAll()
})
</script>

<style scoped>
.todo-dashboard {
  padding: 16px;
}

.stats-row {
  margin-bottom: 16px;
}

.stat-card {
  text-align: center;
}

.stat-footer {
  margin-top: 8px;
  border-top: 1px solid #f0f0f0;
  padding-top: 8px;
}

.quick-actions-card,
.todo-section-card {
  margin-bottom: 16px;
}

.overdue-card {
  border-color: #ff4d4f;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
}

.empty-container {
  text-align: center;
  padding: 20px 0;
}

.todo-description {
  color: #666;
}

.todo-meta {
  margin-top: 4px;
  font-size: 12px;
  color: #999;
}

.todo-meta span {
  margin-right: 12px;
}

.overdue-time {
  color: #ff4d4f !important;
}
</style>

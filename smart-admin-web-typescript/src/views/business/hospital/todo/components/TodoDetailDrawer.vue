<template>
  <a-drawer
    v-model:visible="visible"
    title="待办详情"
    width="600"
    :closable="true"
    :mask-closable="false"
    @close="onClose"
  >
    <div v-if="loading" class="loading-container">
      <a-spin size="large" />
    </div>
    
    <div v-else-if="todoDetail" class="todo-detail">
      <!-- 基本信息 -->
      <a-card title="基本信息" size="small" class="detail-card">
        <a-descriptions :column="2" bordered size="small">
          <a-descriptions-item label="待办编号">{{ todoDetail.todoNo }}</a-descriptions-item>
          <a-descriptions-item label="待办类型">
            <a-tag>{{ todoDetail.todoTypeDesc }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="待办标题" :span="2">{{ todoDetail.todoTitle }}</a-descriptions-item>
          <a-descriptions-item label="待办内容" :span="2">
            <div class="content-text">{{ todoDetail.todoContent || '无' }}</div>
          </a-descriptions-item>
          <a-descriptions-item label="优先级">
            <a-tag :color="todoDetail.priorityColor">{{ todoDetail.priorityLevelDesc }}</a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="待办状态">
            <a-tag :color="getStatusColor(todoDetail.todoStatus)">{{ todoDetail.todoStatusDesc }}</a-tag>
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 客户信息 -->
      <a-card v-if="todoDetail.customerName" title="客户信息" size="small" class="detail-card">
        <a-descriptions :column="2" bordered size="small">
          <a-descriptions-item label="客户姓名">{{ todoDetail.customerName }}</a-descriptions-item>
          <a-descriptions-item label="客户电话">{{ todoDetail.customerPhone || '无' }}</a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 分配信息 -->
      <a-card title="分配信息" size="small" class="detail-card">
        <a-descriptions :column="2" bordered size="small">
          <a-descriptions-item label="分配员工">{{ todoDetail.assignedEmployeeName }}</a-descriptions-item>
          <a-descriptions-item label="分配部门">{{ todoDetail.assignedDepartmentName || '无' }}</a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 时间信息 -->
      <a-card title="时间信息" size="small" class="detail-card">
        <a-descriptions :column="2" bordered size="small">
          <a-descriptions-item label="截止时间">
            <span :class="{ 'text-red': todoDetail.isOverdue, 'text-orange': todoDetail.remainingHours <= 2 }">
              {{ todoDetail.dueTime ? dayjs(todoDetail.dueTime).format('YYYY-MM-DD HH:mm') : '无' }}
            </span>
          </a-descriptions-item>
          <a-descriptions-item label="提醒时间">
            {{ todoDetail.remindTime ? dayjs(todoDetail.remindTime).format('YYYY-MM-DD HH:mm') : '无' }}
          </a-descriptions-item>
          <a-descriptions-item label="预估时长">
            {{ todoDetail.estimatedDuration ? `${todoDetail.estimatedDuration}分钟` : '无' }}
          </a-descriptions-item>
          <a-descriptions-item label="逾期状态">
            <a-tag v-if="todoDetail.isOverdue" color="red">逾期{{ todoDetail.overdueDays }}天</a-tag>
            <a-tag v-else-if="todoDetail.remainingHours <= 2" color="orange">{{ todoDetail.remainingHours }}小时后到期</a-tag>
            <span v-else>正常</span>
          </a-descriptions-item>
          <a-descriptions-item label="开始时间">
            {{ todoDetail.startTime ? dayjs(todoDetail.startTime).format('YYYY-MM-DD HH:mm') : '未开始' }}
          </a-descriptions-item>
          <a-descriptions-item label="完成时间">
            {{ todoDetail.completeTime ? dayjs(todoDetail.completeTime).format('YYYY-MM-DD HH:mm') : '未完成' }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 业务关联 -->
      <a-card v-if="todoDetail.businessType" title="业务关联" size="small" class="detail-card">
        <a-descriptions :column="2" bordered size="small">
          <a-descriptions-item label="业务类型">{{ getBusinessTypeDesc(todoDetail.businessType) }}</a-descriptions-item>
          <a-descriptions-item label="业务ID">
            <a-button type="link" size="small" @click="onViewBusiness">{{ todoDetail.businessId }}</a-button>
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 处理信息 -->
      <a-card v-if="todoDetail.completionNote || todoDetail.cancelReason" title="处理信息" size="small" class="detail-card">
        <a-descriptions :column="1" bordered size="small">
          <a-descriptions-item v-if="todoDetail.completionNote" label="完成备注">
            <div class="content-text">{{ todoDetail.completionNote }}</div>
          </a-descriptions-item>
          <a-descriptions-item v-if="todoDetail.cancelReason" label="取消原因">
            <div class="content-text">{{ todoDetail.cancelReason }}</div>
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 其他信息 -->
      <a-card title="其他信息" size="small" class="detail-card">
        <a-descriptions :column="2" bordered size="small">
          <a-descriptions-item label="自动生成">
            <a-tag :color="todoDetail.autoGenerated ? 'blue' : 'default'">
              {{ todoDetail.autoGenerated ? '是' : '否' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="生成规则">{{ todoDetail.generationRule || '无' }}</a-descriptions-item>
          <a-descriptions-item label="标签">{{ todoDetail.tags || '无' }}</a-descriptions-item>
          <a-descriptions-item label="附件">{{ todoDetail.attachments || '无' }}</a-descriptions-item>
          <a-descriptions-item label="备注" :span="2">
            <div class="content-text">{{ todoDetail.remarks || '无' }}</div>
          </a-descriptions-item>
          <a-descriptions-item label="创建时间">
            {{ dayjs(todoDetail.createTime).format('YYYY-MM-DD HH:mm') }}
          </a-descriptions-item>
          <a-descriptions-item label="更新时间">
            {{ todoDetail.updateTime ? dayjs(todoDetail.updateTime).format('YYYY-MM-DD HH:mm') : '无' }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>
    </div>

    <!-- 操作按钮 -->
    <template #footer>
      <a-space>
        <a-button v-if="todoDetail?.availableActions?.includes('start')" type="primary" @click="onStart">
          开始处理
        </a-button>
        <a-button v-if="todoDetail?.availableActions?.includes('pause')" @click="onPause">
          暂停处理
        </a-button>
        <a-button v-if="todoDetail?.availableActions?.includes('complete')" type="primary" @click="onComplete">
          完成
        </a-button>
        <a-button v-if="todoDetail?.availableActions?.includes('cancel')" danger @click="onCancel">
          取消
        </a-button>
        <a-button v-if="todoDetail?.availableActions?.includes('reassign')" @click="onReassign">
          重新分配
        </a-button>
        <a-button v-if="todoDetail?.availableActions?.includes('edit')" @click="onEdit">
          编辑
        </a-button>
        <a-button @click="onClose">关闭</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { message, Modal } from 'ant-design-vue'
import dayjs from 'dayjs'
import { todoApi, type TodoVO } from '/@/api/business/hospital/todo-api'

interface Props {
  visible: boolean
  todoId: number | null
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'refresh'): void
  (e: 'edit', todoId: number): void
  (e: 'reassign', todoId: number): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)
const loading = ref(false)
const todoDetail = ref<TodoVO | null>(null)

// 监听visible变化
watch(() => props.visible, (newVal) => {
  visible.value = newVal
  if (newVal && props.todoId) {
    loadTodoDetail()
  }
})

watch(visible, (newVal) => {
  emit('update:visible', newVal)
})

// 加载待办详情
const loadTodoDetail = async () => {
  if (!props.todoId) return
  
  try {
    loading.value = true
    const { data } = await todoApi.getDetail(props.todoId)
    todoDetail.value = data
  } catch (error) {
    console.error('加载待办详情失败:', error)
    message.error('加载待办详情失败')
  } finally {
    loading.value = false
  }
}

// 获取状态颜色
const getStatusColor = (status: number) => {
  const colorMap: Record<number, string> = {
    1: 'blue',    // 待处理
    2: 'orange',  // 处理中
    3: 'green',   // 已完成
    4: 'red',     // 已取消
    5: 'volcano'  // 已逾期
  }
  return colorMap[status] || 'default'
}

// 获取业务类型描述
const getBusinessTypeDesc = (businessType: string) => {
  const typeMap: Record<string, string> = {
    'lead': '线索',
    'appointment': '预约',
    'patient': '患者',
    'follow_up': '回访'
  }
  return typeMap[businessType] || businessType
}

// 事件处理
const onClose = () => {
  visible.value = false
  todoDetail.value = null
}

const onStart = async () => {
  try {
    await todoApi.operation({
      todoId: props.todoId!,
      operationType: 'start'
    })
    message.success('开始处理成功')
    loadTodoDetail()
    emit('refresh')
  } catch (error) {
    console.error('开始处理失败:', error)
  }
}

const onPause = async () => {
  try {
    await todoApi.operation({
      todoId: props.todoId!,
      operationType: 'pause'
    })
    message.success('暂停处理成功')
    loadTodoDetail()
    emit('refresh')
  } catch (error) {
    console.error('暂停处理失败:', error)
  }
}

const onComplete = () => {
  Modal.confirm({
    title: '确认完成',
    content: '确定要完成这个待办事项吗？',
    onOk: async () => {
      try {
        await todoApi.operation({
          todoId: props.todoId!,
          operationType: 'complete'
        })
        message.success('完成待办成功')
        loadTodoDetail()
        emit('refresh')
      } catch (error) {
        console.error('完成待办失败:', error)
      }
    }
  })
}

const onCancel = () => {
  Modal.confirm({
    title: '确认取消',
    content: '确定要取消这个待办事项吗？',
    onOk: async () => {
      try {
        await todoApi.operation({
          todoId: props.todoId!,
          operationType: 'cancel'
        })
        message.success('取消待办成功')
        loadTodoDetail()
        emit('refresh')
      } catch (error) {
        console.error('取消待办失败:', error)
      }
    }
  })
}

const onEdit = () => {
  emit('edit', props.todoId!)
  onClose()
}

const onReassign = () => {
  emit('reassign', props.todoId!)
  onClose()
}

const onViewBusiness = () => {
  if (!todoDetail.value) return
  
  const { businessType, businessId } = todoDetail.value
  
  // 根据业务类型跳转到对应页面
  switch (businessType) {
    case 'lead':
      // 跳转到线索详情
      window.open(`#/business/hospital/lead/detail/${businessId}`)
      break
    case 'appointment':
      // 跳转到预约详情
      window.open(`#/business/hospital/appointment/detail/${businessId}`)
      break
    case 'patient':
      // 跳转到患者详情
      window.open(`#/business/hospital/patient/detail/${businessId}`)
      break
    case 'follow_up':
      // 跳转到回访详情
      window.open(`#/business/hospital/followup/detail/${businessId}`)
      break
    default:
      message.info('暂不支持查看该业务类型')
  }
}
</script>

<style scoped>
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.todo-detail {
  padding: 0;
}

.detail-card {
  margin-bottom: 16px;
}

.detail-card:last-child {
  margin-bottom: 0;
}

.content-text {
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 100px;
  overflow-y: auto;
}

.text-red {
  color: #ff4d4f;
}

.text-orange {
  color: #fa8c16;
}
</style>

<template>
  <a-drawer
    v-model:visible="visible"
    :title="isEdit ? '编辑待办' : '新增待办'"
    width="600"
    :closable="true"
    :mask-closable="false"
    @close="onClose"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      layout="vertical"
      @finish="onSubmit"
    >
      <!-- 基本信息 -->
      <a-card title="基本信息" size="small" class="form-card">
        <a-row :gutter="16">
          <a-col :span="24">
            <a-form-item label="待办标题" name="todoTitle">
              <a-input v-model:value="formData.todoTitle" placeholder="请输入待办标题" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="待办类型" name="todoType">
              <a-select v-model:value="formData.todoType" placeholder="请选择待办类型">
                <a-select-option value="lead_follow">线索跟进</a-select-option>
                <a-select-option value="appointment_remind">预约提醒</a-select-option>
                <a-select-option value="visit_confirm">到诊确认</a-select-option>
                <a-select-option value="follow_up_remind">回访提醒</a-select-option>
                <a-select-option value="diagnosis_pending">待诊断</a-select-option>
                <a-select-option value="prescription_pending">待开药</a-select-option>
                <a-select-option value="treatment_follow">治疗跟进</a-select-option>
                <a-select-option value="satisfaction_survey">满意度调查</a-select-option>
                <a-select-option value="payment_remind">付款提醒</a-select-option>
                <a-select-option value="insurance_process">保险处理</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="优先级" name="priorityLevel">
              <a-select v-model:value="formData.priorityLevel" placeholder="请选择优先级">
                <a-select-option :value="1">高</a-select-option>
                <a-select-option :value="2">中</a-select-option>
                <a-select-option :value="3">低</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="待办内容" name="todoContent">
              <a-textarea
                v-model:value="formData.todoContent"
                placeholder="请输入待办内容"
                :rows="3"
                :max-length="500"
                show-count
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-card>

      <!-- 客户信息 -->
      <a-card title="客户信息" size="small" class="form-card">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="客户姓名" name="customerName">
              <a-input v-model:value="formData.customerName" placeholder="请输入客户姓名" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="客户电话" name="customerPhone">
              <a-input v-model:value="formData.customerPhone" placeholder="请输入客户电话" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-card>

      <!-- 分配信息 -->
      <a-card title="分配信息" size="small" class="form-card">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="分配员工" name="assignedEmployeeId">
              <a-select
                v-model:value="formData.assignedEmployeeId"
                placeholder="请选择分配员工"
                show-search
                :filter-option="filterEmployee"
                @change="onEmployeeChange"
              >
                <a-select-option v-for="employee in employeeList" :key="employee.employeeId" :value="employee.employeeId">
                  {{ employee.actualName }} - {{ employee.departmentName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="分配部门" name="assignedDepartmentName">
              <a-input v-model:value="formData.assignedDepartmentName" placeholder="自动填充" disabled />
            </a-form-item>
          </a-col>
        </a-row>
      </a-card>

      <!-- 时间信息 -->
      <a-card title="时间信息" size="small" class="form-card">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="截止时间" name="dueTime">
              <a-date-picker
                v-model:value="formData.dueTime"
                show-time
                format="YYYY-MM-DD HH:mm"
                placeholder="请选择截止时间"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="提醒时间" name="remindTime">
              <a-date-picker
                v-model:value="formData.remindTime"
                show-time
                format="YYYY-MM-DD HH:mm"
                placeholder="请选择提醒时间"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="预估时长(分钟)" name="estimatedDuration">
              <a-input-number
                v-model:value="formData.estimatedDuration"
                placeholder="请输入预估时长"
                :min="1"
                :max="480"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="紧急程度" name="urgencyLevel">
              <a-select v-model:value="formData.urgencyLevel" placeholder="请选择紧急程度">
                <a-select-option :value="1">紧急</a-select-option>
                <a-select-option :value="2">一般</a-select-option>
                <a-select-option :value="3">不急</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
      </a-card>

      <!-- 其他信息 -->
      <a-card title="其他信息" size="small" class="form-card">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="标签" name="tags">
              <a-input v-model:value="formData.tags" placeholder="请输入标签，多个用逗号分隔" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="附件" name="attachments">
              <a-input v-model:value="formData.attachments" placeholder="请输入附件信息" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="备注" name="remarks">
              <a-textarea
                v-model:value="formData.remarks"
                placeholder="请输入备注信息"
                :rows="2"
                :max-length="200"
                show-count
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-card>
    </a-form>

    <!-- 操作按钮 -->
    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" :loading="submitLoading" @click="onSubmit">
          {{ isEdit ? '更新' : '保存' }}
        </a-button>
      </a-space>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import dayjs, { type Dayjs } from 'dayjs'
import { todoApi, type TodoFormData } from '/@/api/business/hospital/todo-api'
import { employeeApi } from '/@/api/system/employee-api'

interface Props {
  visible: boolean
  todoId: number | null
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'refresh'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)
const formRef = ref()
const submitLoading = ref(false)
const employeeList = ref([])

// 是否编辑模式
const isEdit = computed(() => !!props.todoId)

// 表单数据
const formData = reactive<TodoFormData & { dueTime?: Dayjs; remindTime?: Dayjs }>({
  todoTitle: '',
  todoContent: '',
  todoType: '',
  priorityLevel: 2,
  urgencyLevel: 2,
  customerName: '',
  customerPhone: '',
  assignedEmployeeId: undefined,
  assignedEmployeeName: '',
  assignedDepartmentId: undefined,
  assignedDepartmentName: '',
  dueTime: undefined,
  remindTime: undefined,
  estimatedDuration: undefined,
  tags: '',
  attachments: '',
  remarks: ''
})

// 表单验证规则
const formRules = {
  todoTitle: [
    { required: true, message: '请输入待办标题', trigger: 'blur' },
    { max: 100, message: '待办标题不能超过100个字符', trigger: 'blur' }
  ],
  todoType: [
    { required: true, message: '请选择待办类型', trigger: 'change' }
  ],
  priorityLevel: [
    { required: true, message: '请选择优先级', trigger: 'change' }
  ],
  assignedEmployeeId: [
    { required: true, message: '请选择分配员工', trigger: 'change' }
  ]
}

// 监听visible变化
watch(() => props.visible, (newVal) => {
  visible.value = newVal
  if (newVal) {
    if (props.todoId) {
      loadTodoDetail()
    } else {
      resetForm()
    }
  }
})

watch(visible, (newVal) => {
  emit('update:visible', newVal)
})

// 加载待办详情
const loadTodoDetail = async () => {
  if (!props.todoId) return
  
  try {
    const { data } = await todoApi.getDetail(props.todoId)
    
    // 填充表单数据
    Object.assign(formData, {
      todoTitle: data.todoTitle,
      todoContent: data.todoContent,
      todoType: data.todoType,
      priorityLevel: data.priorityLevel,
      urgencyLevel: data.urgencyLevel,
      customerName: data.customerName,
      customerPhone: data.customerPhone,
      assignedEmployeeId: data.assignedEmployeeId,
      assignedEmployeeName: data.assignedEmployeeName,
      assignedDepartmentId: data.assignedDepartmentId,
      assignedDepartmentName: data.assignedDepartmentName,
      dueTime: data.dueTime ? dayjs(data.dueTime) : undefined,
      remindTime: data.remindTime ? dayjs(data.remindTime) : undefined,
      estimatedDuration: data.estimatedDuration,
      tags: data.tags,
      attachments: data.attachments,
      remarks: data.remarks
    })
  } catch (error) {
    console.error('加载待办详情失败:', error)
    message.error('加载待办详情失败')
  }
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(formData, {
    todoTitle: '',
    todoContent: '',
    todoType: '',
    priorityLevel: 2,
    urgencyLevel: 2,
    customerName: '',
    customerPhone: '',
    assignedEmployeeId: undefined,
    assignedEmployeeName: '',
    assignedDepartmentId: undefined,
    assignedDepartmentName: '',
    dueTime: undefined,
    remindTime: undefined,
    estimatedDuration: undefined,
    tags: '',
    attachments: '',
    remarks: ''
  })
}

// 查询员工列表
const queryEmployeeList = async () => {
  try {
    const { data } = await employeeApi.queryAllEmployee(false)
    employeeList.value = data
  } catch (error) {
    console.error('查询员工列表失败:', error)
  }
}

// 员工筛选
const filterEmployee = (input: string, option: any) => {
  return option.children.toLowerCase().includes(input.toLowerCase())
}

// 员工变化处理
const onEmployeeChange = (employeeId: number) => {
  const employee = employeeList.value.find((emp: any) => emp.employeeId === employeeId)
  if (employee) {
    formData.assignedEmployeeName = employee.actualName
    formData.assignedDepartmentId = employee.departmentId
    formData.assignedDepartmentName = employee.departmentName
  }
}

// 提交表单
const onSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    submitLoading.value = true
    
    const submitData = {
      ...formData,
      dueTime: formData.dueTime?.format('YYYY-MM-DD HH:mm:ss'),
      remindTime: formData.remindTime?.format('YYYY-MM-DD HH:mm:ss')
    }
    
    if (isEdit.value) {
      submitData.todoId = props.todoId!
      await todoApi.update(submitData)
      message.success('更新待办成功')
    } else {
      await todoApi.add(submitData)
      message.success('新增待办成功')
    }
    
    emit('refresh')
    onClose()
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitLoading.value = false
  }
}

// 关闭抽屉
const onClose = () => {
  visible.value = false
  resetForm()
}

// 初始化
onMounted(() => {
  queryEmployeeList()
})
</script>

<style scoped>
.form-card {
  margin-bottom: 16px;
}

.form-card:last-child {
  margin-bottom: 0;
}
</style>

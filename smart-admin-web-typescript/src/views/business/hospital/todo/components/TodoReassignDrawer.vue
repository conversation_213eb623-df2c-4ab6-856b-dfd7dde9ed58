<template>
  <a-drawer
    v-model:visible="visible"
    title="重新分配待办"
    width="500"
    :closable="true"
    :mask-closable="false"
    @close="onClose"
  >
    <div v-if="loading" class="loading-container">
      <a-spin size="large" />
    </div>
    
    <div v-else>
      <!-- 当前分配信息 -->
      <a-card v-if="todoDetail" title="当前分配信息" size="small" class="current-info-card">
        <a-descriptions :column="1" bordered size="small">
          <a-descriptions-item label="待办标题">{{ todoDetail.todoTitle }}</a-descriptions-item>
          <a-descriptions-item label="当前分配员工">{{ todoDetail.assignedEmployeeName }}</a-descriptions-item>
          <a-descriptions-item label="当前分配部门">{{ todoDetail.assignedDepartmentName || '无' }}</a-descriptions-item>
        </a-descriptions>
      </a-card>

      <!-- 重新分配表单 -->
      <a-card title="重新分配" size="small" class="reassign-form-card">
        <a-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          layout="vertical"
          @finish="onSubmit"
        >
          <a-form-item label="新分配员工" name="newAssigneeId">
            <a-select
              v-model:value="formData.newAssigneeId"
              placeholder="请选择新的分配员工"
              show-search
              :filter-option="filterEmployee"
              @change="onEmployeeChange"
            >
              <a-select-option v-for="employee in employeeList" :key="employee.employeeId" :value="employee.employeeId">
                {{ employee.actualName }} - {{ employee.departmentName }}
              </a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item label="新分配部门" name="newDepartmentName">
            <a-input v-model:value="formData.newDepartmentName" placeholder="自动填充" disabled />
          </a-form-item>

          <a-form-item label="分配原因" name="reassignReason">
            <a-textarea
              v-model:value="formData.reassignReason"
              placeholder="请输入重新分配的原因"
              :rows="3"
              :max-length="200"
              show-count
            />
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 操作按钮 -->
    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" :loading="submitLoading" @click="onSubmit">
          确认分配
        </a-button>
      </a-space>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { todoApi, type TodoVO } from '/@/api/business/hospital/todo-api'
import { employeeApi } from '/@/api/system/employee-api'

interface Props {
  visible: boolean
  todoId: number | null
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'refresh'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)
const loading = ref(false)
const formRef = ref()
const submitLoading = ref(false)
const todoDetail = ref<TodoVO | null>(null)
const employeeList = ref([])

// 表单数据
const formData = reactive({
  newAssigneeId: undefined as number | undefined,
  newAssigneeName: '',
  newDepartmentId: undefined as number | undefined,
  newDepartmentName: '',
  reassignReason: ''
})

// 表单验证规则
const formRules = {
  newAssigneeId: [
    { required: true, message: '请选择新的分配员工', trigger: 'change' }
  ],
  reassignReason: [
    { required: true, message: '请输入重新分配的原因', trigger: 'blur' },
    { max: 200, message: '分配原因不能超过200个字符', trigger: 'blur' }
  ]
}

// 监听visible变化
watch(() => props.visible, (newVal) => {
  visible.value = newVal
  if (newVal && props.todoId) {
    loadTodoDetail()
    resetForm()
  }
})

watch(visible, (newVal) => {
  emit('update:visible', newVal)
})

// 加载待办详情
const loadTodoDetail = async () => {
  if (!props.todoId) return
  
  try {
    loading.value = true
    const { data } = await todoApi.getDetail(props.todoId)
    todoDetail.value = data
  } catch (error) {
    console.error('加载待办详情失败:', error)
    message.error('加载待办详情失败')
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(formData, {
    newAssigneeId: undefined,
    newAssigneeName: '',
    newDepartmentId: undefined,
    newDepartmentName: '',
    reassignReason: ''
  })
}

// 查询员工列表
const queryEmployeeList = async () => {
  try {
    const { data } = await employeeApi.queryAllEmployee(false)
    employeeList.value = data
  } catch (error) {
    console.error('查询员工列表失败:', error)
  }
}

// 员工筛选
const filterEmployee = (input: string, option: any) => {
  return option.children.toLowerCase().includes(input.toLowerCase())
}

// 员工变化处理
const onEmployeeChange = (employeeId: number) => {
  const employee = employeeList.value.find((emp: any) => emp.employeeId === employeeId)
  if (employee) {
    formData.newAssigneeName = employee.actualName
    formData.newDepartmentId = employee.departmentId
    formData.newDepartmentName = employee.departmentName
  }
}

// 提交表单
const onSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    // 检查是否分配给同一个人
    if (formData.newAssigneeId === todoDetail.value?.assignedEmployeeId) {
      message.warning('不能分配给当前处理人')
      return
    }
    
    submitLoading.value = true
    
    await todoApi.operation({
      todoId: props.todoId!,
      operationType: 'reassign',
      newAssigneeId: formData.newAssigneeId!,
      newAssigneeName: formData.newAssigneeName,
      newDepartmentId: formData.newDepartmentId,
      newDepartmentName: formData.newDepartmentName
    })
    
    message.success('重新分配成功')
    emit('refresh')
    onClose()
  } catch (error) {
    console.error('重新分配失败:', error)
  } finally {
    submitLoading.value = false
  }
}

// 关闭抽屉
const onClose = () => {
  visible.value = false
  todoDetail.value = null
  resetForm()
}

// 初始化
onMounted(() => {
  queryEmployeeList()
})
</script>

<style scoped>
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.current-info-card {
  margin-bottom: 16px;
}

.reassign-form-card {
  margin-bottom: 0;
}
</style>

<template>
  <div class="todo-list-container">
    <!-- 搜索表单 -->
    <a-card size="small" :bordered="false" class="search-card">
      <a-form ref="searchFormRef" :model="searchForm" layout="inline">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="待办标题" name="todoTitle">
              <a-input v-model:value="searchForm.todoTitle" placeholder="请输入待办标题" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="待办类型" name="todoType">
              <a-select v-model:value="searchForm.todoType" placeholder="请选择待办类型" allow-clear>
                <a-select-option value="lead_follow">线索跟进</a-select-option>
                <a-select-option value="appointment_remind">预约提醒</a-select-option>
                <a-select-option value="visit_confirm">到诊确认</a-select-option>
                <a-select-option value="follow_up_remind">回访提醒</a-select-option>
                <a-select-option value="diagnosis_pending">待诊断</a-select-option>
                <a-select-option value="prescription_pending">待开药</a-select-option>
                <a-select-option value="treatment_follow">治疗跟进</a-select-option>
                <a-select-option value="satisfaction_survey">满意度调查</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="待办状态" name="todoStatus">
              <a-select v-model:value="searchForm.todoStatus" placeholder="请选择待办状态" allow-clear>
                <a-select-option :value="1">待处理</a-select-option>
                <a-select-option :value="2">处理中</a-select-option>
                <a-select-option :value="3">已完成</a-select-option>
                <a-select-option :value="4">已取消</a-select-option>
                <a-select-option :value="5">已逾期</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="优先级" name="priorityLevel">
              <a-select v-model:value="searchForm.priorityLevel" placeholder="请选择优先级" allow-clear>
                <a-select-option :value="1">高</a-select-option>
                <a-select-option :value="2">中</a-select-option>
                <a-select-option :value="3">低</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="分配员工" name="assignedEmployeeId">
              <a-select v-model:value="searchForm.assignedEmployeeId" placeholder="请选择分配员工" allow-clear show-search>
                <a-select-option v-for="employee in employeeList" :key="employee.employeeId" :value="employee.employeeId">
                  {{ employee.actualName }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="客户姓名" name="customerName">
              <a-input v-model:value="searchForm.customerName" placeholder="请输入客户姓名" allow-clear />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="截止时间" name="dueTimeRange">
              <a-range-picker v-model:value="searchForm.dueTimeRange" show-time format="YYYY-MM-DD HH:mm" />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item>
              <a-space>
                <a-button type="primary" @click="onSearch">
                  <template #icon><SearchOutlined /></template>
                  查询
                </a-button>
                <a-button @click="onReset">
                  <template #icon><ReloadOutlined /></template>
                  重置
                </a-button>
              </a-space>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-card>

    <!-- 操作按钮 -->
    <a-card size="small" :bordered="false" class="action-card">
      <a-space>
        <a-button type="primary" @click="onAdd">
          <template #icon><PlusOutlined /></template>
          新增待办
        </a-button>
        <a-button @click="onBatchComplete" :disabled="!hasSelected">
          <template #icon><CheckOutlined /></template>
          批量完成
        </a-button>
        <a-button @click="onBatchCancel" :disabled="!hasSelected">
          <template #icon><CloseOutlined /></template>
          批量取消
        </a-button>
        <a-button @click="onRefresh">
          <template #icon><ReloadOutlined /></template>
          刷新
        </a-button>
      </a-space>
    </a-card>

    <!-- 数据表格 -->
    <a-card size="small" :bordered="false">
      <a-table
        :columns="columns"
        :data-source="tableData"
        :loading="tableLoading"
        :pagination="pagination"
        :row-selection="rowSelection"
        :scroll="{ x: 1500 }"
        @change="onTableChange"
        row-key="todoId"
      >
        <!-- 待办类型 -->
        <template #todoType="{ record }">
          <a-tag>{{ record.todoTypeDesc }}</a-tag>
        </template>

        <!-- 优先级 -->
        <template #priorityLevel="{ record }">
          <a-tag :color="record.priorityColor">{{ record.priorityLevelDesc }}</a-tag>
        </template>

        <!-- 待办状态 -->
        <template #todoStatus="{ record }">
          <a-tag :color="getStatusColor(record.todoStatus)">{{ record.todoStatusDesc }}</a-tag>
        </template>

        <!-- 逾期状态 -->
        <template #overdue="{ record }">
          <a-tag v-if="record.isOverdue" color="red">逾期{{ record.overdueDays }}天</a-tag>
          <a-tag v-else-if="record.remainingHours <= 2" color="orange">{{ record.remainingHours }}小时后到期</a-tag>
          <span v-else>正常</span>
        </template>

        <!-- 截止时间 -->
        <template #dueTime="{ record }">
          <span :class="{ 'text-red': record.isOverdue, 'text-orange': record.remainingHours <= 2 }">
            {{ dayjs(record.dueTime).format('YYYY-MM-DD HH:mm') }}
          </span>
        </template>

        <!-- 操作 -->
        <template #action="{ record }">
          <a-space>
            <a-button v-if="record.availableActions.includes('start')" type="link" size="small" @click="onStart(record)">
              开始
            </a-button>
            <a-button v-if="record.availableActions.includes('pause')" type="link" size="small" @click="onPause(record)">
              暂停
            </a-button>
            <a-button v-if="record.availableActions.includes('complete')" type="link" size="small" @click="onComplete(record)">
              完成
            </a-button>
            <a-button v-if="record.availableActions.includes('cancel')" type="link" size="small" danger @click="onCancel(record)">
              取消
            </a-button>
            <a-button v-if="record.availableActions.includes('reassign')" type="link" size="small" @click="onReassign(record)">
              重新分配
            </a-button>
            <a-button v-if="record.availableActions.includes('edit')" type="link" size="small" @click="onEdit(record)">
              编辑
            </a-button>
            <a-button type="link" size="small" @click="onView(record)">
              查看
            </a-button>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 待办详情抽屉 -->
    <TodoDetailDrawer
      v-model:visible="detailDrawerVisible"
      :todo-id="currentTodoId"
      @refresh="onRefresh"
    />

    <!-- 待办表单抽屉 -->
    <TodoFormDrawer
      v-model:visible="formDrawerVisible"
      :todo-id="currentTodoId"
      @refresh="onRefresh"
    />

    <!-- 重新分配抽屉 -->
    <TodoReassignDrawer
      v-model:visible="reassignDrawerVisible"
      :todo-id="currentTodoId"
      @refresh="onRefresh"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { message, Modal } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  CheckOutlined,
  CloseOutlined
} from '@ant-design/icons-vue'
import { todoApi } from '/@/api/business/hospital/todo-api'
import { employeeApi } from '/@/api/system/employee-api'
import TodoDetailDrawer from './components/TodoDetailDrawer.vue'
import TodoFormDrawer from './components/TodoFormDrawer.vue'
import TodoReassignDrawer from './components/TodoReassignDrawer.vue'

// 搜索表单
const searchFormRef = ref()
const searchForm = reactive({
  todoTitle: '',
  todoType: undefined,
  todoStatus: undefined,
  priorityLevel: undefined,
  assignedEmployeeId: undefined,
  customerName: '',
  dueTimeRange: undefined
})

// 表格数据
const tableData = ref([])
const tableLoading = ref(false)
const selectedRowKeys = ref([])

// 分页
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条数据`
})

// 员工列表
const employeeList = ref([])

// 抽屉状态
const detailDrawerVisible = ref(false)
const formDrawerVisible = ref(false)
const reassignDrawerVisible = ref(false)
const currentTodoId = ref<number | null>(null)

// 计算属性
const hasSelected = computed(() => selectedRowKeys.value.length > 0)

// 表格列配置
const columns = [
  { title: '待办编号', dataIndex: 'todoNo', width: 120, fixed: 'left' },
  { title: '待办标题', dataIndex: 'todoTitle', width: 200, ellipsis: true },
  { title: '待办类型', dataIndex: 'todoType', width: 100, slots: { customRender: 'todoType' } },
  { title: '优先级', dataIndex: 'priorityLevel', width: 80, slots: { customRender: 'priorityLevel' } },
  { title: '状态', dataIndex: 'todoStatus', width: 80, slots: { customRender: 'todoStatus' } },
  { title: '客户姓名', dataIndex: 'customerName', width: 100 },
  { title: '分配员工', dataIndex: 'assignedEmployeeName', width: 100 },
  { title: '截止时间', dataIndex: 'dueTime', width: 150, slots: { customRender: 'dueTime' } },
  { title: '逾期状态', dataIndex: 'overdue', width: 120, slots: { customRender: 'overdue' } },
  { title: '创建时间', dataIndex: 'createTime', width: 150, customRender: ({ text }: any) => dayjs(text).format('YYYY-MM-DD HH:mm') },
  { title: '操作', key: 'action', width: 200, fixed: 'right', slots: { customRender: 'action' } }
]

// 行选择配置
const rowSelection = {
  selectedRowKeys: selectedRowKeys,
  onChange: (keys: any[]) => {
    selectedRowKeys.value = keys
  }
}

// 获取状态颜色
const getStatusColor = (status: number) => {
  const colorMap: Record<number, string> = {
    1: 'blue',    // 待处理
    2: 'orange',  // 处理中
    3: 'green',   // 已完成
    4: 'red',     // 已取消
    5: 'volcano'  // 已逾期
  }
  return colorMap[status] || 'default'
}

// 查询数据
const queryData = async () => {
  try {
    tableLoading.value = true
    const params = {
      ...searchForm,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      dueTimeBegin: searchForm.dueTimeRange?.[0]?.format('YYYY-MM-DD HH:mm:ss'),
      dueTimeEnd: searchForm.dueTimeRange?.[1]?.format('YYYY-MM-DD HH:mm:ss')
    }
    delete params.dueTimeRange

    const { data } = await todoApi.queryPage(params)
    tableData.value = data.list
    pagination.total = data.total
  } catch (error) {
    console.error('查询待办列表失败:', error)
  } finally {
    tableLoading.value = false
  }
}

// 查询员工列表
const queryEmployeeList = async () => {
  try {
    const { data } = await employeeApi.queryAllEmployee(false)
    employeeList.value = data
  } catch (error) {
    console.error('查询员工列表失败:', error)
  }
}

// 事件处理
const onSearch = () => {
  pagination.current = 1
  queryData()
}

const onReset = () => {
  searchFormRef.value?.resetFields()
  pagination.current = 1
  queryData()
}

const onRefresh = () => {
  queryData()
}

const onTableChange = ({ current, pageSize }: any) => {
  pagination.current = current
  pagination.pageSize = pageSize
  queryData()
}

const onAdd = () => {
  currentTodoId.value = null
  formDrawerVisible.value = true
}

const onEdit = (record: any) => {
  currentTodoId.value = record.todoId
  formDrawerVisible.value = true
}

const onView = (record: any) => {
  currentTodoId.value = record.todoId
  detailDrawerVisible.value = true
}

const onStart = async (record: any) => {
  try {
    await todoApi.operation({
      todoId: record.todoId,
      operationType: 'start'
    })
    message.success('开始处理成功')
    queryData()
  } catch (error) {
    console.error('开始处理失败:', error)
  }
}

const onPause = async (record: any) => {
  try {
    await todoApi.operation({
      todoId: record.todoId,
      operationType: 'pause'
    })
    message.success('暂停处理成功')
    queryData()
  } catch (error) {
    console.error('暂停处理失败:', error)
  }
}

const onComplete = (record: any) => {
  Modal.confirm({
    title: '确认完成',
    content: '确定要完成这个待办事项吗？',
    onOk: async () => {
      try {
        await todoApi.operation({
          todoId: record.todoId,
          operationType: 'complete'
        })
        message.success('完成待办成功')
        queryData()
      } catch (error) {
        console.error('完成待办失败:', error)
      }
    }
  })
}

const onCancel = (record: any) => {
  Modal.confirm({
    title: '确认取消',
    content: '确定要取消这个待办事项吗？',
    onOk: async () => {
      try {
        await todoApi.operation({
          todoId: record.todoId,
          operationType: 'cancel'
        })
        message.success('取消待办成功')
        queryData()
      } catch (error) {
        console.error('取消待办失败:', error)
      }
    }
  })
}

const onReassign = (record: any) => {
  currentTodoId.value = record.todoId
  reassignDrawerVisible.value = true
}

const onBatchComplete = () => {
  Modal.confirm({
    title: '批量完成',
    content: `确定要完成选中的 ${selectedRowKeys.value.length} 个待办事项吗？`,
    onOk: async () => {
      try {
        await todoApi.batchUpdateStatus({
          todoIds: selectedRowKeys.value,
          status: 3 // 已完成
        })
        message.success('批量完成成功')
        selectedRowKeys.value = []
        queryData()
      } catch (error) {
        console.error('批量完成失败:', error)
      }
    }
  })
}

const onBatchCancel = () => {
  Modal.confirm({
    title: '批量取消',
    content: `确定要取消选中的 ${selectedRowKeys.value.length} 个待办事项吗？`,
    onOk: async () => {
      try {
        await todoApi.batchUpdateStatus({
          todoIds: selectedRowKeys.value,
          status: 4 // 已取消
        })
        message.success('批量取消成功')
        selectedRowKeys.value = []
        queryData()
      } catch (error) {
        console.error('批量取消失败:', error)
      }
    }
  })
}

// 初始化
onMounted(() => {
  queryData()
  queryEmployeeList()
})
</script>

<style scoped>
.todo-list-container {
  padding: 16px;
}

.search-card,
.action-card {
  margin-bottom: 16px;
}

.text-red {
  color: #ff4d4f;
}

.text-orange {
  color: #fa8c16;
}
</style>
